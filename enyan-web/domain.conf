
############################################################################################################
server {
    listen      80;
    server_name e.endao.click;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name e.endao.click;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/e_endao_click/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/e_endao_click/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name e.edbook.cc;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name e.edbook.cc;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/e_edbook_cc/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/e_edbook_cc/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name ************;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name ************;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate 需要先 cat certificate.crt ca_bundle.crt >> certificate.crt
    #ssl_certificate /etc/nginx/ssl/************/certificate.crt;
    #ssl_certificate_key /etc/nginx/ssl/************/private.key;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name edbook.life;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name edbook.life;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/edbook_life/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/edbook_life/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name ea1.en-dao.net;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name ea1.en-dao.net;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/ea1_en_dao_net/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/ea1_en_dao_net/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name ep1.en-dao.top;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name ep1.en-dao.top;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/ep1_en_dao_top/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/ep1_en_dao_top/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name ei1.en-dao.com;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name ei1.en-dao.com;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/ei1_en_dao_com/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/ei1_en_dao_com/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name e.hisbook.cc;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name e.hisbook.cc;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/e.hisbook.cc/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/e.hisbook.cc/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name i.hisbook.cc;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name i.hisbook.cc;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/i.hisbook.cc/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/i.hisbook.cc/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
### 用于Jeremy的转向
############################################################################################################
server {
    listen      80;
    server_name cf.edbook.cloud; ### 用于Jeremy的转向
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name cf.edbook.cloud;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/cf_edbook_cloud/cf_edbook_cloud.pem;
    #ssl_certificate_key /etc/nginx/ssl/cf_edbook_cloud/cf_edbook_cloud.key;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
### 用于Jeremy的转向
############################################################################################################
server {
    listen      80;
    server_name a.bookapp.cc;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name a.bookapp.cc;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/a.bookapp.cc/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/a.bookapp.cc/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name i.bookapp.cc;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name i.bookapp.cc;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/i.bookapp.cc/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/i.bookapp.cc/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}
############################################################################################################
server {
    listen      80;
    server_name e.bookapp.cc;
    rewrite ^ https://$http_host$request_uri? permanent;
}

server {
    listen      443 ssl;
    listen      [::]:443;
    server_name e.bookapp.cc;

    root /usr/share/nginx/html;

    # enable ssl
    #ssl on;                                #如果强制HTTPs访问，这行要打开
    ssl_protocols   SSLv2 SSLv3 TLSv1.2;    # 指定密码为openssl支持的格式
    ssl_prefer_server_ciphers   on;         # 依赖SSLv3和TLSv1协议的服务器密码将优先于客户端密码
    ssl_ciphers HIGH:!aNULL:!MD5;         # 密码加密方式

    # config ssl certificate
    #ssl_certificate /etc/nginx/ssl/e.bookapp.cc/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/e.bookapp.cc/key.pem;

    client_max_body_size 200m;
    underscores_in_headers on; #默认的情况下nginx引用header变量时不能使用带下划线的变量，修改这个

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header access_token $http_access_token;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        proxy_connect_timeout      240;
        proxy_send_timeout         240;
        proxy_read_timeout         240;
        # note, there is not SSL here! plain HTTP is used
        proxy_pass http://tomcat;
    }
}