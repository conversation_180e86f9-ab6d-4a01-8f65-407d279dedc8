db.getCollectionInfos();

db.getCollection('enyanReaderHighlights').dataSize();
db.getCollection('enyanReaderHighlights').find({bookId:1368, type:0});
############################################################################
#书籍的明细
#select * from enyan_order_detail where user_email = "<EMAIL>"
#<EMAIL>
#购买信息
#select * from purchase where user_id = 15;

#书籍的明细
select * from enyan_order_detail where user_email = "<EMAIL>";
#购买信息
select * from purchase where user_id = 27;

select version();

#重置某个email的drm信息
update purchase set license_uuid = null; #where user_id = 27;
update enyan_order_detail set drmInfo =  JSON_REMOVE(drmInfo, '$.lInfo.lId'); #WHERE user_email = "<EMAIL>";
select * from enyan_order_detail; #where user_email = "<EMAIL>";

########################################################################################################################
#重新上传所有的书籍
#1、重置 publication
DROP TABLE IF EXISTS `publication`;
create table publication
(
    id     int auto_increment
        primary key,
    uuid   varchar(255) not null,
    title  varchar(255) not null,
    status varchar(255) not null
)ENGINE=InnoDB DEFAULT CHARSET=utf8;
create index uuid_index on publication (uuid);
#2、先再次上传书籍。清空 OrderDetail drmInfo信息：
 update enyan_order_detail set drmInfo = '';

#3、删除purchase、license、license_status、license_view数据：drop create
DROP TABLE IF EXISTS `purchase`;
create table purchase
(
    id               int auto_increment
        primary key,
    uuid             varchar(255) not null,
    publication_id   int          not null,
    user_id          int          not null,
    license_uuid     varchar(255) null,
    type             varchar(32)  not null,
    transaction_date datetime     null,
    start_date       datetime     null,
    end_date         datetime     null,
    status           varchar(255) not null,
    constraint purchase_user_id_publication_id_uindex
        unique (user_id, publication_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;
create index idx_purchase on purchase (license_uuid);

DROP TABLE IF EXISTS `license_status`;
create table license_status
(
    id                   int auto_increment
        primary key,
    status               int          not null,
    license_updated      datetime     not null,
    status_updated       datetime     not null,
    device_count         int          null,
    potential_rights_end datetime     null,
    license_ref          varchar(255) not null,
    rights_end           datetime     null
)ENGINE=InnoDB DEFAULT CHARSET=utf8;
create index license_ref_index on license_status (license_ref);

DROP TABLE IF EXISTS `license_view`;
create table license_view
(
    id           int auto_increment
        primary key,
    uuid         varchar(255) not null,
    device_count int          not null,
    status       varchar(255) not null,
    message      varchar(255) not null
)ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `license`;
create table license
(
    id           varchar(255)  not null
        primary key,
    user_id      varchar(255)  not null,
    provider     varchar(255)  not null,
    issued       datetime      not null,
    updated      datetime      null,
    rights_print int           null,
    rights_copy  int           null,
    rights_start datetime      null,
    rights_end   datetime      null,
    content_fk   varchar(255)  not null,
    lsd_status   int default 0 null
)ENGINE=InnoDB DEFAULT CHARSET=utf8;
#4、重置 https://ebook.endao.co/test/resetOrderDetailAndPurchaseInfo
########################################################################################################################
#根据email查询笔记信息
select user_email as email,content as 划线内容,chapter_name as 章节名称,FROM_UNIXTIME(create_time/1000,'%Y-%m-%d %H:%i:%s') 创建时间 from enyan_user_highlights
    where user_email='<EMAIL>' and is_deleted = 0;

select count(*),user_email from enyan_user_highlights where  is_deleted = 0  group by user_email ;
select count(*),book_id from enyan_user_highlights where  is_deleted = 0  group by book_id ;
delete from enyan_user_highlights where user_email in ("<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>");

select book_id,book_title from enyan_book where book_id in (22,49,50,51);

select * from enyan_user_highlights where book_id = 49;

update enyan_user_highlights set update_time = (unix_timestamp() * 1000) where book_id = 50;

#delete from enyan_user_highlights where is_deleted = 1

#查询支付信息
select * from order;

########################################################################################################################
## 21:Not HK;  22: HK;
select * from enyan_order where is_paid = 1 and pay_info->'$.charge.payType' = 2 and pay_info->'$.charge.country' != 'HK';

select * from enyan_order where is_paid = 1 and pay_info->'$.charge.payType' != 1;

#信用卡区分 HK及非HK
update enyan_order set pay_info = JSON_SET(pay_info,'$.charge.payType',21) where is_paid = 1 and pay_info->'$.charge.payType' = 2 and pay_info->'$.charge.country' != 'HK';
update enyan_order set pay_info = JSON_SET(pay_info,'$.charge.payType',22) where is_paid = 1 and pay_info->'$.charge.payType' = 2 and pay_info->'$.charge.country' = 'HK';

#信用卡恢复
update enyan_order set pay_info = JSON_SET(pay_info,'$.charge.payType',2) where is_paid = 1 and pay_info->'$.charge.payType' = 21;
update enyan_order set pay_info = JSON_SET(pay_info,'$.charge.payType',2) where is_paid = 1 and pay_info->'$.charge.payType' = 22;

########################################################################################################################
## 21:Not HK;  22: HK; 先创建，最好再call
drop procedure if exists procUpdatePayTypeToHK;
create PROCEDURE procUpdatePayTypeToHK()
BEGIN
    DECLARE cur_order_num varchar(40);
    DECLARE done INT DEFAULT FALSE;
    DECLARE order_cur cursor for select order_num from enyan_order where is_paid = 1 and pay_info->'$.charge.payType' = 2 and pay_info->'$.charge.country' = 'HK';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = true;
    #SET cur_order_num = 'E11851104678';
    open order_cur;
    read_loop:LOOP
        fetch order_cur into cur_order_num;
        if done then
            leave read_loop;
        end if;
        #update enyan_order_detail set publisher_id = publisher_id + 1 where order_num = cur_order_num;
        update enyan_order_detail set pay_type = 22 where order_num = cur_order_num;
        #select cur_order_num;
        #select * from enyan_order_detail where order_num = cur_order_num;
        #update enyan_order_detail set pay_type = 22 where order_num = cur_order_num;
        #select * from  enyan_order_detail where order_num = cur_order_num;
    end LOOP;
    close order_cur;
END

call procUpdatePayTypeToHK();

########################################################################################################################
## 21:Not HK;  22: HK; 先创建，最好再call
drop procedure if exists procUpdatePayTypeToNotHK;
create PROCEDURE procUpdatePayTypeToNotHK()
BEGIN
    DECLARE cur_order_num varchar(40);
    DECLARE done INT DEFAULT FALSE;
    DECLARE order_cur cursor for select order_num from enyan_order where is_paid = 1 and pay_info->'$.charge.payType' = 2 and pay_info->'$.charge.country' != 'HK';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = true;
    #SET cur_order_num = 'E11851104678';
    open order_cur;
    read_loop:LOOP
        fetch order_cur into cur_order_num;
        if done then
            leave read_loop;
        end if;
        #update enyan_order_detail set publisher_id = publisher_id + 1 where order_num = cur_order_num;
        update enyan_order_detail set pay_type = 21 where order_num = cur_order_num;
        #select cur_order_num;
        #select * from enyan_order_detail where order_num = cur_order_num;
        #update enyan_order_detail set pay_type = 22 where order_num = cur_order_num;
        #select * from  enyan_order_detail where order_num = cur_order_num;
    end LOOP;
    close order_cur;
END

call procUpdatePayTypeToNotHK();
########################################################################################################################
## 设置所有书籍的折扣 可以提前设置，因为task里可以自动启用
update enyan_book set discount_single_is_valid=1 , discount_single_description='6折' , discount_single_value=60 ,
                      discount_single_start_time = '2020-06-18',discount_single_end_time='2020-06-26' where price > 0;
########################################################################################################################
## 更新书籍
select * from enyan_book where book_title='合乎聖經的講道：釋經講道信息的發展與表達'; #book_drm_ref=74, book_id=68

select * from publication where title='合乎圣经的讲道-繁-200506'; # new_publication_ID=74

select * from publication where title='合乎圣经的讲道-繁-200430'; # old_publication_ID=71

select * from enyan_order_detail where  book_id=68 and user_email='<EMAIL>'; #purchaseID=932, pubID=74，uID=16

update enyan_order_detail set drmInfo=JSON_SET(drmInfo,'$.lInfo.pubId',74,'$.lInfo.lId','') where book_id = 68 ;#and user_email='<EMAIL>';

update purchase set publication_id = 74, license_uuid = null where  publication_id=71 ;#and user_id=16;

########################################################################################################################
#https://www.cnblogs.com/xiaoboluo768/p/6222862.html
show global variables like "%timeout%";

########################################################################################################################
select * from enyan_order where order_num = 'E11271138666';

#获取drm信息  {"lInfo":{"lId":"8ec18703-7c0c-49ef-b148-a0d79039d66b","pId":2018,"pubId":28,"uId":615}}, pId=2018
select * from enyan_order_detail where order_num = 'E11271138666';

#根据purchase 信息  user_id; license_uuid=8ec18703-7c0c-49ef-b148-a0d79039d66b
select * from purchase where id=2018;

# 可以获取license的信息； content_fk=e1d6bd32-6cc9-4e14-8955-7a31c97d0e34, user_id = 61fb4d2c-a3f9-473c-9960-776d773f9da7
select * from license where id='8ec18703-7c0c-49ef-b148-a0d79039d66b';

#select * from license_view where uuid='61fb4d2c-a3f9-473c-9960-776d773f9da7'; 暂时没找到别的方法

#可以获取license的状态；id=1439
select * from license_status where license_ref='8ec18703-7c0c-49ef-b148-a0d79039d66b';

#可以获取设备ID信息，设备名称
select * from event where license_status_fk=1439;

#直接删除license是不可以的
delete from license_status where license_ref='8ec18703-7c0c-49ef-b148-a0d79039d66b';
delete from license where id='8ec18703-7c0c-49ef-b148-a0d79039d66b';
delete from purchase where id=2018;
delete from event where license_status_fk=1439;
delete from enyan_order where order_num = 'E11271138666';
delete from enyan_order_detail where order_num = 'E11271138666';

########################################################################################################################
#下次尝试直接修改参数值
#表purchase：
#	type：LOAN； start_date: 后退一天
#表 license_status:
#	status: 1:ready; 2:active（买与租借都是这个标识）;4: revoke(回收)；8:return;
#表 license：
#	rights_start
#	rights_end(可以直接修改这个结束日期，完善会员租借自动续费功能)
#表 event:
#	type: 1:注册；6:renew；3:return
# // Publication status
# const (
# 	StatusDraft      string = "draft"
# 	StatusEncrypting string = "encrypting"
# 	StatusError      string = "error"
# 	StatusOk         string = "ok"
# )
#
# // License status
# const (
# 	StatusDraft      string = "draft"
# 	StatusEncrypting string = "encrypting"
# 	StatusError      string = "error"
# 	StatusOk         string = "ok"
# )
#
# // Purchase status
# const (
# 	StatusToBeRenewed  string = "to-be-renewed"
# 	StatusToBeReturned string = "to-be-returned"
# 	StatusError        string = "error"
# 	StatusOk           string = "ok"
# )
#
# // Enumeration of PurchaseType
# const (
# 	BUY  string = "BUY"
# 	LOAN string = "LOAN"
# )
#
# / List of status values as strings
# const (
# 	STATUS_READY     = "ready"
# 	STATUS_ACTIVE    = "active"
# 	STATUS_REVOKED   = "revoked"
# 	STATUS_RETURNED  = "returned"
# 	STATUS_CANCELLED = "cancelled"
# 	STATUS_EXPIRED   = "expired"
# 	EVENT_RENEWED    = "renewed"
# )
#
# // List of status values as int
# const (
# 	STATUS_READY_INT     = 0
# 	STATUS_ACTIVE_INT    = 1
# 	STATUS_REVOKED_INT   = 2
# 	STATUS_RETURNED_INT  = 3
# 	STATUS_CANCELLED_INT = 4
# 	STATUS_EXPIRED_INT   = 5
# 	EVENT_RENEWED_INT    = 6
# )


#设置书籍到期
update license set rights_end = now() where user_id in (select uuid from user where email='<EMAIL>') and license.content_fk='';
update license set rights_end = now() where id = '3159fa7-6cfa-4200-80f7-0ffb0c967c8a';

update license set issued = now() where id = '93e331d2-d7d6-4bfe-a541-1839dd4fee82';#license的值
update license_status set potential_rights_end = now() where license_ref = '93e331d2-d7d6-4bfe-a541-1839dd4fee82';#license的值
########################################################################################################################
#如果服务器的证书更新了，需要更新各个license的日期
# （新下载的书籍【即便之前已经下载过，也包含在内】的license的日期必须晚于证书日期，这个应该是DRM的bug；不过当前设备之前下载的书籍是可以一直阅读的）
update license set issued = now();
update license_status set potential_rights_end = now();
########################################################################################################################
#导出注册用户
select distinct(user_email) as '电子书email' from enyan_order;
select count(1) from oc_customer;
select count(distinct user_email) from enyan_order where order_total > 0;
########################################################################################################################
#导出购买书籍的用户
select user_email,book_title from enyan_order_detail where book_id = 110;

########################################################################################################################
#根据设备名称获取信息
select * from user_info where info_text->'$.devices[*].deviceName' like '%Note%';

########################################################################################################################
#取消书籍
#步骤1：根据email获取书籍的lId
#SELECT * FROM enyan_order_detail WHERE user_email = '<EMAIL>';#bookId=135; lId=d66e408e-e3fd-4e1c-a52d-cd4b9212ae1a
SELECT * FROM enyan_book_buy WHERE user_email = '<EMAIL>';#bookId=135; lId=d66e408e-e3fd-4e1c-a52d-cd4b9212ae1a
#步骤2
#方法1：根据书籍内容 把书籍到期
update license set rights_end = now() where user_id in (select uuid from user where email='<EMAIL>') and license.content_fk='';
#方法2：根据license（lId） 把书籍到期
update license set rights_end = now() where id = '3159fa7-6cfa-4200-80f7-0ffb0c967c8a';
update license_status set status = 4, rights_end = now() where license_ref = '3159fa7-6cfa-4200-80f7-0ffb0c967c8a';#status状态在上边

#步骤3：删除书籍
#delete FROM enyan_order_detail WHERE user_email = '<EMAIL>' and book_id=135;
########################################################################################################################
#灵修计划开始的书籍统计
select from_unixtime(plan.start_from/1000, '%Y-%m-%d') as '开始时间',count(*) as '开始计划数',plan.name from enyan_plan plan where plan.book_id = 4 group by from_unixtime(plan.start_from/1000, '%Y-%m-%d') order by from_unixtime(plan.start_from/1000, '%Y-%m-%d') ;
#最后更新的书籍统计
select from_unixtime(plan.update_time/1000, '%Y-%m-%d') as '最后使用时间',count(*) as '使用人数',plan.name as '书籍名称' from enyan_plan plan where plan.book_id = 139 group by from_unixtime(plan.update_time/1000, '%Y-%m-%d') order by from_unixtime(plan.update_time/1000, '%Y-%m-%d');
#按照书籍统计
select from_unixtime(plan.update_time/1000, '%Y-%m-%d') as '最后使用时间',count(*) as '使用人数',plan.name as '书籍名称' from enyan_plan plan where plan.update_time > unix_timestamp('2022-10-01')*1000 group by from_unixtime(plan.update_time/1000, '%Y-%m-%d'), plan.name order by from_unixtime(plan.update_time/1000, '%Y-%m-%d');
########################################################################################################################
#合计金额
select user_email as '用户email',sum(order_total) as '合计金额' from enyan_order group by user_email order by sum(order_total) desc ;

########################################################################################################################
#数据库表迁移
insert into enyan_book_buy(order_num,user_id,user_email,book_id,book_title,publisher_id,purchased_at,purchased_day,drmInfo)
select order_num,user_id,user_email,book_id,book_title,publisher_id,purchased_at,purchased_day,drmInfo from enyan_order_detail;

########################################################################################################################
#关联设置合作方信息，email必须都为小写
#update user_info set info_text = JSON_SET(info_text,'$.pub',9) where username = '<EMAIL>';
#<EMAIL> <EMAIL>
select * from user_info where user_info.info_text->'$.pub' = 98;
select * from user_info where username = '<EMAIL>';
#insert into user_info value ('<EMAIL>','{"pub":6}');
update user_info set info_text = '{"pub":9}' where username = '<EMAIL>';

insert into user_info value ('<EMAIL>','{"pubs":["98"]}');
update oc_customer set customer_group_id = 4, approved = 1 where email in ('<EMAIL>','<EMAIL>');
########################################################################################################################
#设置推荐语为书籍描述的前99字
update enyan_book set recommended_caption = left(book_description,99) where recommended_caption is null or recommended_caption = '';

########################################################################################################################
#统计笔记信息
select count(1),book_id  from enyan_reader_highlights where creation_date > date('2021-04-27') and book_id in (156,179,178,149,173,193,221,225) group by book_id;

select count(1)  from enyan_reader_highlights where is_deleted = 0 and annotation != '' and creation_date > date('2021-04-27') and book_id in (156,179,178,149,173,193,221,225) ;

########################################################################################################################
#修改书籍名称并进行拼接
update enyan_book set book_title = concat('测试用书·请勿购买 ',book_id)  where book_id = 1;

########################################################################################################################
#用户统计-月
select DATE_FORMAT(date_added,'%Y%m') months,count(email) count from oc_customer group by months;
#用户统计-天
select DATE_FORMAT(date_added,'%Y%m%d') months,count(email) count from oc_customer group by DATE_FORMAT(date_added,'%Y%m%d');
#用户统计-周
select DATE_FORMAT(date_added,'%Y%u') months,count(email) count from oc_customer group by DATE_FORMAT(date_added,'%Y%u');

#未激活名单
select email,date_added from oc_customer where approved = 0;
########################################################################################################################
#修改数据编码为utf8mb4，以兼容emoj的字段
ALTER TABLE oc_customer CONVERT TO CHARACTER SET utf8mb4;

########################################################################################################################
#导出相应书籍的兑换码使用情形：
# 205 归纳研经-兑换码-简体；206 归纳研经-兑换码-繁体；
# 110 耶稣就是基督-兑换码-繁体； 111 耶稣就是基督-兑换码-简体；
# 125 神学阅读-兑换码-繁体；126 神学阅读-兑换码-简体；
#118 心理学与基督教：五种观点(简)；119 心理學與基督教：五種觀點(繁)
#321 专文写作格式指南-简体；332 专文写作格式指南-繁体
select user_email,code,if(status=1,"Yes","No") as '状态' from enyan_redeem_code where JSON_CONTAINS( `note`->'$.booksToRedeemList[*].bookId' ,  '119', '$');
select user_email,code,if(status=1,"Yes","No") as 'Status',JSON_EXTRACT(`note`,'$.emailToRedeem') as 'Redeem email' from enyan_redeem_code where JSON_CONTAINS( `note`->'$.booksToRedeemList[*].bookId' ,  '119', '$');

########################################################################################################################
#清空测试服务器的书籍信息
update enyan_order set is_deleted = 1 where user_email = '<EMAIL>';
update enyan_order_detail set is_deleted = 1 where user_email = '<EMAIL>';
update enyan_book_buy set is_deleted = 1 where user_email = '<EMAIL>';

########################################################################################################################
#订单中是否有一本书
select * from enyan_order where user_email = '<EMAIL>' and is_paid = 1 and JSON_CONTAINS( `order_detail`->'$.cartDiscountInfoList[*].productInfoList[*].code' ,  '328', '$');

########################################################################################################################
#划线笔记用户统计
select count(1) as cnt,user_email from enyan_reader_highlights where is_deleted = 0 group by user_email ORDER BY cnt DESC  limit 100 ;

########################################################################################################################
#查看license基本信息，用于筛选书籍阅读设备大于某数值的信息
select pu.title,u.email,st.device_count,st.license_ref,st.license_updated, st.id from license_status st,license l, user u,publication pu where st.device_count > 10 and st.license_ref = l.id and l.user_id = u.uuid and l.content_fk = pu.uuid order by st.device_count desc ;
select * from event where license_status_fk = 2249; #48 Robin;49 picture

########################################################################################################################
#更新导入的兑换码的结束日期
update enyan_redeem_code set end_at = '2022-09-11 00:00:00' where create_time='20220802140223';

########################################################################################################################
#导出书籍信息
select book_id as '书籍ID',book_title as '书名',publisher_name as '出版社',word_count as '字数' from enyan_book;

########################################################################################################################
#查询license信息
select * from enyan_book_buy where user_email = '<EMAIL>' and drmInfo->'$.lInfo.lId' like '%40%';
select * from enyan_book_buy where length(drmInfo->'$.lInfo.lId') > 38;
select user_email,length(drmInfo->'$.lInfo.lId') from enyan_book_buy where user_email = '<EMAIL>' ;
########################################################################################################################
#将不合规的license清空（中间处理先租后买数据时，可能修改错了license）
select * from enyan_book_buy where length(drmInfo->'$.lInfo.lId') > 38;
update enyan_book_buy set drmInfo =  JSON_REMOVE(drmInfo, '$.lInfo.lId') where length(drmInfo->'$.lInfo.lId') > 38;
########################################################################################################################
#更换用户的email
#password= DRMUtil.getDefaultHintPasswd('email')
update enyan_order set user_email ='<EMAIL>' where user_email = '<EMAIL>' ;
update enyan_order_detail set user_email ='<EMAIL>' where user_email = '<EMAIL>' ;
update enyan_book_buy set user_email ='<EMAIL>' where user_email = '<EMAIL>' ;
update user set email ='<EMAIL>', password = '95bf717b75feccb775cc50badce30826ad9f5f89e5a89564a74b3564dd7f65fa' where email = '<EMAIL>';
update user_info set username ='<EMAIL>' where username = '<EMAIL>';
#重置用户的登录信息及密码
update oc_customer set email = '<EMAIL>', password = 'e1c21037a34f95b877b1bd6e88dbf8a5d891c48b',salt = 'wwwEnyanE' where email = '<EMAIL>' ;
########################################################################################################################
#获取出版社信息
select publisher_name as '出版社',concat('https://ebook.endao.co/category-0-grid-0-',publisher_id,'-0-0-0-0-0-0') as '访问链接' from enyan_publisher where publisher_id > 0;
########################################################################################################################
#先租后买支付国家及人次
select pay_country as '国家缩写',count(1) as '人次' from enyan_rent_detail group by pay_country;
########################################################################################################################
#替换书籍的字符串
UPDATE `enyan_book` SET book_cover = REPLACE (book_cover, 'https://ehome.endao.co/book_image/', 'https://d1.endao.cloud/root/tmp/book_image/' ),
                        book_cover_app = REPLACE (book_cover_app, 'https://ehome.endao.co/book_image/', 'https://d1.endao.cloud/root/tmp/book_image/' ),
                        book_sample = REPLACE (book_sample, 'https://ehome.endao.co/book_image/', 'https://d1.endao.cloud/root/tmp/book_image/' ) ;

########################################################################################################################
#统计优惠码使用情况
select order_id ,order_num,user_email,order_detail->'$.couponCode',order_detail->'$.amountCoupon', purchased_at from enyan_order where is_paid = 1 and order_detail->'$.couponCode' is not null ;
########################################################################################################################
#使用IF进行数据更新
update enyan_comment set comment_count = comment_count - 1, can_show = IF(comment_count = 0 and is_deleted = 1, 0, can_show) where data_id = 1;
update enyan_comment set comment_count = comment_count - 1, can_show = CASE WHEN comment_count > 0 THEN 1 ELSE 0 END where data_id = 1;
########################################################################################################################
#将被迁移至的账号的信息注销
update enyan_book_buy set user_email = 'ljt1047181587@outlook.com_revoke_0' where user_email = '<EMAIL>';
update enyan_order set user_email = 'ljt1047181587@outlook.com_revoke_0' where user_email = '<EMAIL>';
update enyan_order_detail set user_email = 'ljt1047181587@outlook.com_revoke_0' where user_email = '<EMAIL>';
delete from user_info where username = '<EMAIL>';
delete from user where email = '<EMAIL>';

#将老信息的账号替换为新信息
update enyan_book_buy set user_email = '<EMAIL>' where user_email = '<EMAIL>';
update enyan_order set user_email = '<EMAIL>' where user_email = '<EMAIL>';
update enyan_order_detail set user_email = '<EMAIL>' where user_email = '<EMAIL>';
########################################################################################################################
#查找参数信息
show global variables like '%timeout%';
show global variables like '%connections%';
########################################################################################################################
#截取先租后买的订单号以方便测试
update enyan_rent set order_num = concat(left(order_num,11),"0",rent_id) where rent_id < 10;
update enyan_rent set order_num = concat(left(order_num,11),rent_id) where rent_id >= 10 and rent_id <= 99;