log4j.rootLogger=ERROR,Console,File

log4j.appender.Console=org.apache.log4j.ConsoleAppender
log4j.appender.Console.Target=System.out
log4j.appender.Console.layout=org.apache.log4j.PatternLayout
log4j.appender.Console.layout.ConversionPattern=[%p][%d{yyyy-MM-dd HH\:mm\:ss,SSS}][%c]%LL - %m%n

log4j.appender.File=org.apache.log4j.RollingFileAppender
log4j.appender.File.File=${logback.file.path}/enyan.log
log4j.appender.File.MaxFileSize=10MB
log4j.appender.File.Threshold=ALL
log4j.appender.File.layout=org.apache.log4j.PatternLayout
log4j.appender.File.layout.ConversionPattern=[%p][%d{yyyy-MM-dd HH\:mm\:ss,SSS}][%c]%m%n

#下面是配置将日志信息插入数据库，
#配置输出目标为数据库（假如要将日志在控制台输出，配置为log4j.appender. stdout =org.apache.log4j.ConsoleAppender；将日志写入文件，配置为log4j.appender.logfile=org.apache.log4j.DailyRollingFileAppender
#这样的配置在许多地方都要有，需要可查有关资料）,当然你也可以自己扩展org.apache.log4j.jdbc.JDBCAppender这个类，只需要在这里配置就可以了例如我们配置我自己扩展的MyJDBCAppender，配置为#log4j.appender.db=com.neam.commons.MyJDBCAppender
log4j.appender.datasource=org.apache.log4j.jdbc.JDBCAppender
log4j.appender.datasource.layout=org.apache.log4j.PatternLayout
log4j.appender.datasource.driver=com.mysql.jdbc.Driver
#定义什么级别的错误将写入到数据库中
log4j.appender.datasource.BufferSize=1
#设置缓存大小，就是当有1条日志信息是才忘数据库插一次
log4j.appender.datasource.URL=${jdbc_url}
log4j.appender.datasource.user=${jdbc_username}
log4j.appender.datasource.password=${jdbc_password}
log4j.appender.datasource.sql=insert into enyan_logs (class,method,create_time,log_level,log_line,msg) values ('%C','%M','%d{yyyy-MM-dd HH:mm:ss}','%p','%l','%m')

log4j.logger.com.aaron=${logback.level}
log4j.logger.org.springframework = ERROR