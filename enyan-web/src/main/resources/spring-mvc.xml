<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:util="http://www.springframework.org/schema/util"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
    http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.2.xsd
    http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd">


	<!-- REST中根据URL后缀自动判定Content-Type及相应的View -->
	<bean id="contentNegotiationManager" class="org.springframework.web.accept.ContentNegotiationManagerFactoryBean">
		<property name="ignoreAcceptHeader" value="true"/>
		<property name="defaultContentType" value="application/json"/>
		<property name="mediaTypes">
			<value>
				json=application/json
				xml=application/xml
			</value>
		</property>
	</bean>

	<mvc:annotation-driven conversion-service="conversionService" content-negotiation-manager="contentNegotiationManager">
		<mvc:message-converters>
            <bean class="org.springframework.http.converter.ByteArrayHttpMessageConverter"/>
			<!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
				<constructor-arg value="UTF-8"/>
			</bean>
			<bean class="com.aaron.a4f.converter.AaronJsonConverter">
				<property name="supportedMediaTypes">
					<list>
						<value>application/json;charset=UTF-8</value>
					</list>
				</property>
				<!--<property name="objectMapper">
					<util:constant static-field="com.aaron.a4j.util.JsonMapperHolder.objectMapper"/>
				</property>-->
			</bean>
		</mvc:message-converters>
	</mvc:annotation-driven>


    <!-- 如果使用了RESTful形式的拦截，那么对于静态资源的处理上，就需要加上此句，静态资源（没有映射的）就会 -->
    <mvc:default-servlet-handler />


	<!--静态资源映射-->
	<!--本项目把静态资源放在了WEB-INF的statics目录下，资源映射如下-->
	<!--<mvc:resources mapping="/css/**" location="/WEB-INF/statics/css/"/>
	<mvc:resources mapping="/js/**" location="/WEB-INF/statics/js/"/>
	<mvc:resources mapping="/image/**" location="/WEB-INF/statics/image/"/>-->

	<!--但是项目部署到linux下发现WEB-INF的静态资源会出现无法解析的情况，但是本地tomcat访问正常，因此建议还是直接把静态资源放在webapp的statics下，映射配置如下-->
	<!--<mvc:resources mapping="/css/**" location="/statics/css/"/>-->
	<!--<mvc:resources mapping="/js/**" location="/statics/js/"/>-->
	<!--<mvc:resources mapping="/image/**" location="/statics/images/"/>-->

	<!--<mvc:resources mapping="/pdf/**" location="/uploadFiles/"/>-->

	<!-- 配置注解驱动 可以将request参数与绑定到controller参数上 -->


	<!-- springmvc文件上传需要配置的节点-->
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="209715000"/><!--200M-->
		<property name="defaultEncoding" value="UTF-8"/>
		<property name="resolveLazily" value="true"/>
	</bean>

    <bean id="conversionService" class="org.springframework.format.support.FormattingConversionServiceFactoryBean">
        <property name="converters">
            <set>
<!--                <bean class="com.aaron.a4f.converter.DateConverter"></bean> 配合注解 @DateTimeFormat 已经可以自动处理-->
            </set>
        </property>
    </bean>
    <!-- 自动扫描该包，使SpringMVC认为包下用了@controller注解的类是控制器 -->
    <context:component-scan base-package="com.aaron.spring.controller">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

	<context:component-scan base-package="com.aaron.spring.api">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

    <!-- +++ Add component scan for Swagger configuration +++ -->
    <context:component-scan base-package="com.aaron.config" />

	<bean id="localeResolver" class="org.springframework.web.servlet.i18n.CookieLocaleResolver" >
		<property name="defaultLocale" value="zh_CN" />
		<property name="cookieName" value="myAppLocaleCookie"/>
		<property name="cookieMaxAge" value="3600"/>
	</bean>

	<mvc:interceptors>
		<!-- 国际化操作拦截器 如果采用基于（请求/Session/Cookie）则必需配置 org.springframework.web.servlet.i18n.LocaleChangeInterceptor-->
		<bean class="com.aaron.spring.interceptor.EnyanLocaleChangeInterceptor">
			<property name="paramName" value="locale" />
		</bean>
		<mvc:interceptor>
			<mvc:mapping path="/api/**"/>
			<bean class="com.aaron.spring.interceptor.APIInterceptor" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/front/**"/>
			<bean class="com.aaron.spring.interceptor.FrontInterceptor" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/**"/>
			<mvc:exclude-mapping path="/css/**"/>
			<mvc:exclude-mapping path="/images/**"/>
			<mvc:exclude-mapping path="/img/**"/>
			<mvc:exclude-mapping path="/js/**"/>
			<mvc:exclude-mapping path="/statics/**"/>
			<mvc:exclude-mapping path="/store/**"/>
			<mvc:exclude-mapping path="/rent/**"/>
			<mvc:exclude-mapping path="/api/**"/>
			<bean class="com.aaron.spring.interceptor.IPInterceptor" />
		</mvc:interceptor>
        <!--用于获取访问设备 Device currentDevice = DeviceUtils.getCurrentDevice(request);
		<bean class="org.springframework.mobile.device.DeviceResolverHandlerInterceptor" />-->

	</mvc:interceptors>

    <!-- +++ Add MVC resource handler for Swagger UI +++ -->
    <!-- Swagger UI (Springfox 3.x) -->
    <mvc:resources mapping="/swagger-ui/**" location="classpath:/META-INF/resources/webjars/springfox-swagger-ui/" />

    <!-- Handles HTTP GET requests for /resources/** by efficiently serving up static resources in the ${webappRoot}/resources directory -->
    <mvc:resources mapping="/resources/**" location="/resources/" />

</beans>