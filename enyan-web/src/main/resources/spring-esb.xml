<?xml version="1.0" encoding="utf-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <!--
    <bean id="httpRequestExecutor" class="com.gillion.esb.api.client.executor.http.HttpRequestExecutor">
        <property name="executorConfiguration">
            <bean class="com.gillion.esb.api.client.executor.http.HttpRequestExecutorConfiguration">
                <property name="connectTimeout" value="120000"/>
                <property name="readTimeout" value="120000"/>
                <property name="url" value="http://10.138.46.31:29998/esb"/>
            </bean>
        </property>
    </bean>
    <bean id="clientConfiguration" class="com.gillion.esb.api.configuration.SimpleClientConfiguration">
        <property name="reqSysCode" value="S25"/>
        <property name="clientPlugins">
            <list>
                <bean class="com.gillion.esb.api.plugins.transaction.TransactionClientPlugin"></bean>
            </list>
        </property>
        <property name="clientRequestExecutor" ref="httpRequestExecutor">
        </property>
    </bean>

    <bean id="esbClient" class="com.gillion.esb.api.client.ESBPoolClient">
        <property name="clientConfiguration" ref="clientConfiguration"/>
        <property name="maxTotal" value="100"/>
        <property name="maxIdle" value="50"/>
    </bean>-->

</beans>