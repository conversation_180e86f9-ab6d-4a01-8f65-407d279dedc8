<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:util="http://www.springframework.org/schema/util"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
    http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.2.xsd
    http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd">

   	<mvc:annotation-driven />
	<context:component-scan base-package="com.aaron.spring.controller" />

	<bean id="mappingJacksonHttpMessageConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
		<property name="supportedMediaTypes">
			<list>
				<value>text/html;charset=UTF-8</value>
			</list>
		</property>
	</bean>

	<bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
		<property name="messageConverters">
			<list>
				<ref bean="mappingJacksonHttpMessageConverter" />
			</list>
		</property>
	</bean>
	<!-- 
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver" 
		  p:prefix="/" p:suffix=".jsp" /> -->
		  
	<!--<bean class="org.springframework.web.servlet.handler.SimpleUrlHandlerMapping">    
            <property name="mappings">    
              <props>    
                    <prop key="index.do">indexController</prop>    
                </props>    
            </property>    
    </bean>    
     用于页面跳转,根据请求的不同跳转到不同页面，如请求index.do则跳转到/WEB-INF/view/index.jsp页面  
    <bean id="indexController" class="org.springframework.web.servlet.mvc.UrlFilenameViewController"/>  --> 
    	  
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
	        <!-- Example: a logical view name of 'showMessage' is mapped to '/WEB-INF/jsp/showMessage.jsp' -->
	        <!-- 返回的视图模型数据需要经过jstl来处理    
            <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"></property> -->  
	        <property name="prefix" value="/WEB-INF/view/"/>
	        <property name="suffix" value=".jsp"/>
	</bean>

	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="defaultEncoding">
			<value>UTF-8</value>
		</property>
		<property name="maxUploadSize">
			<value>10485760</value><!-- 10M -->
		</property>
		<property name="maxInMemorySize">
			<value>4096</value>
		</property>
	</bean>

</beans>