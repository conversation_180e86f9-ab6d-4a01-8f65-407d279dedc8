hibernate.dialect=org.hibernate.dialect.OracleDialect
dialect=mysql
driverClassName=${driverClassName}
validationQuery=select 1
jdbc_url=${jdbc_url}
jdbc_username=${jdbc_username}
jdbc_password=${jdbc_password}

dataSource.maxPoolSize=${dataSource.maxPoolSize}

logback.appenderRef=${logback.appenderRef}
logback.file.path=${logback.file.path}
logback.level=${logback.level}

#mybatis
mybatis.logImpl=${mybatis.logImpl}

#ssl.mysql
ssl.mysql.trustStore=${ssl.mysql.trustStore}
ssl.mysql.trustStorePassword=${ssl.mysql.trustStorePassword}

#MongoDB
mongodb.host=${spring.data.mongodb.host}
mongodb.port=${spring.data.mongodb.port}
mongodb.database=${spring.data.mongodb.database}
mongodb.username=${spring.data.mongodb.username}
mongodb.password=${spring.data.mongodb.password}

#elasticsearch
elasticsearch.host=${spring.data.elasticsearch.host}
elasticsearch.port=${spring.data.elasticsearch.port}
elasticsearch.protocol=${spring.data.elasticsearch.protocol}
elasticsearch.database=${spring.data.elasticsearch.database}
elasticsearch.username=${spring.data.elasticsearch.username}
elasticsearch.password=${spring.data.elasticsearch.password}

# Swagger 配置 - 支持逗号分隔的多个包路径
# 请确保这些包路径下包含您的控制器类
swagger.base.package=com.aaron.spring.api.v4

# Swagger 调试日志级别 (可选)
#logging.level.com.aaron.config.SwaggerConfig=DEBUG
#swagger.base-package=com.aaron.spring.api.v4