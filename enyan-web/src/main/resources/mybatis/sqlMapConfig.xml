<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
  PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
	<settings>
		<setting name="cacheEnabled" value="true" />
		<setting name="lazyLoadingEnabled" value="true" />
		<setting name="multipleResultSetsEnabled" value="true" />
		<setting name="useColumnLabel" value="true" />
		<setting name="useGeneratedKeys" value="false" />
		<setting name="autoMappingBehavior" value="PARTIAL" />
		<setting name="defaultExecutorType" value="SIMPLE" /><!-- SIMPLE REUSE BATCH -->
		<!-- <setting name="defaultExecutorType" value="BATCH" /> -->
		<setting name="defaultStatementTimeout" value="25000" />
		<setting name="safeRowBoundsEnabled" value="false" />
		<setting name="mapUnderscoreToCamelCase" value="true" /><!--开启下划线到驼峰的自动转换. 作用：将数据库字段根据驼峰规则自动注入到对象属性-->
		<setting name="localCacheScope" value="SESSION" />
		<!-- <setting name="jdbcTypeForNull" value="OTHER" /> -->
		<setting name="jdbcTypeForNull" value="NULL" />
		<setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString" />
    	<setting name="logImpl" value="${mybatis.logImpl}"/>
	</settings>
	<typeAliases>
		<!-- =========================================================== -->
	    <!-- security模块                                                                                                                                                                             -->
	    <!-- =========================================================== -->
		<!--<typeAlias alias="operationLog" type="com.haier.openplatform.log.domain.OperationLog" />
		<typeAlias alias="operationLogSearchModel" type="com.haier.openplatform.bms.security.model.OperationLogSearchModel"/>
		<typeAlias alias="uploadFile" type="com.haier.openplatform.bms.security.domain.UploadFile"/>
		<typeAlias alias="fileSearchModel" type="com.haier.openplatform.bms.security.model.FileSearchModel"/>-->
	</typeAliases>

	<typeHandlers>
	  <typeHandler handler="com.aaron.mybatis.SerializableTypeHandler"/>
	</typeHandlers>
</configuration>
