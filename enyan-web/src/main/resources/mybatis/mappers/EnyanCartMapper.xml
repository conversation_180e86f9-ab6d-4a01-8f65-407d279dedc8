<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanCartMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanCart">
    <id column="cart_id" jdbcType="VARCHAR" property="cartId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="add_at" jdbcType="TIMESTAMP" property="addAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    cart_id, user_email, book_id, quantity, add_at
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanCartExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_cart
    where cart_id = #{cartId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from enyan_cart
    where cart_id = #{cartId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanCartExample">
    delete from enyan_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanCart">
    <selectKey keyProperty="cartId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_cart (user_email, book_id, quantity, 
      add_at)
    values (#{userEmail,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, #{quantity,jdbcType=INTEGER}, 
      #{addAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanCart">
    <selectKey keyProperty="cartId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_cart
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="addAt != null">
        add_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="addAt != null">
        #{addAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanCartExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_cart
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_cart
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_cart
    <set>
      <if test="record.cartId != null">
        cart_id = #{record.cartId,jdbcType=VARCHAR},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.addAt != null">
        add_at = #{record.addAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_cart
    set cart_id = #{record.cartId,jdbcType=VARCHAR},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      quantity = #{record.quantity,jdbcType=INTEGER},
      add_at = #{record.addAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanCart">
    update enyan_cart
    <set>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="addAt != null">
        add_at = #{addAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where cart_id = #{cartId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanCart">
    update enyan_cart
    set user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      quantity = #{quantity,jdbcType=INTEGER},
      add_at = #{addAt,jdbcType=TIMESTAMP}
    where cart_id = #{cartId,jdbcType=VARCHAR}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>