<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanCouponMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanCoupon">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="coupon_name" jdbcType="VARCHAR" property="couponName" />
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode" />
    <result column="coupon_value" jdbcType="INTEGER" property="couponValue" />
    <result column="buy_max" jdbcType="INTEGER" property="buyMax" />
    <result column="use_max" jdbcType="INTEGER" property="useMax" />
    <result column="min_limit_value" jdbcType="INTEGER" property="minLimitValue" />
    <result column="buy_count" jdbcType="INTEGER" property="buyCount" />
    <result column="coupon_type" jdbcType="INTEGER" property="couponType" />
    <result column="coupon_status" jdbcType="INTEGER" property="couponStatus" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanCoupon">
    <result column="book_string" jdbcType="LONGVARCHAR" property="bookString" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, coupon_name, coupon_code, coupon_value, buy_max, use_max, min_limit_value, 
    buy_count, coupon_type, coupon_status, begin_time, end_time, create_at, is_deleted
  </sql>
  <sql id="Blob_Column_List">
    book_string
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanCouponExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanCouponExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_coupon
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_coupon
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanCouponExample">
    delete from enyan_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanCoupon">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_coupon (coupon_name, coupon_code, coupon_value, 
      buy_max, use_max, min_limit_value, 
      buy_count, coupon_type, coupon_status, 
      begin_time, end_time, create_at, 
      is_deleted, book_string)
    values (#{couponName,jdbcType=VARCHAR}, #{couponCode,jdbcType=VARCHAR}, #{couponValue,jdbcType=INTEGER}, 
      #{buyMax,jdbcType=INTEGER}, #{useMax,jdbcType=INTEGER}, #{minLimitValue,jdbcType=INTEGER}, 
      #{buyCount,jdbcType=INTEGER}, #{couponType,jdbcType=INTEGER}, #{couponStatus,jdbcType=INTEGER}, 
      #{beginTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{createAt,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER}, #{bookString,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanCoupon">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponName != null">
        coupon_name,
      </if>
      <if test="couponCode != null">
        coupon_code,
      </if>
      <if test="couponValue != null">
        coupon_value,
      </if>
      <if test="buyMax != null">
        buy_max,
      </if>
      <if test="useMax != null">
        use_max,
      </if>
      <if test="minLimitValue != null">
        min_limit_value,
      </if>
      <if test="buyCount != null">
        buy_count,
      </if>
      <if test="couponType != null">
        coupon_type,
      </if>
      <if test="couponStatus != null">
        coupon_status,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="bookString != null">
        book_string,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponName != null">
        #{couponName,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponValue != null">
        #{couponValue,jdbcType=INTEGER},
      </if>
      <if test="buyMax != null">
        #{buyMax,jdbcType=INTEGER},
      </if>
      <if test="useMax != null">
        #{useMax,jdbcType=INTEGER},
      </if>
      <if test="minLimitValue != null">
        #{minLimitValue,jdbcType=INTEGER},
      </if>
      <if test="buyCount != null">
        #{buyCount,jdbcType=INTEGER},
      </if>
      <if test="couponType != null">
        #{couponType,jdbcType=INTEGER},
      </if>
      <if test="couponStatus != null">
        #{couponStatus,jdbcType=INTEGER},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="bookString != null">
        #{bookString,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanCouponExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_coupon
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_coupon
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_coupon
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.couponName != null">
        coupon_name = #{record.couponName,jdbcType=VARCHAR},
      </if>
      <if test="record.couponCode != null">
        coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.couponValue != null">
        coupon_value = #{record.couponValue,jdbcType=INTEGER},
      </if>
      <if test="record.buyMax != null">
        buy_max = #{record.buyMax,jdbcType=INTEGER},
      </if>
      <if test="record.useMax != null">
        use_max = #{record.useMax,jdbcType=INTEGER},
      </if>
      <if test="record.minLimitValue != null">
        min_limit_value = #{record.minLimitValue,jdbcType=INTEGER},
      </if>
      <if test="record.buyCount != null">
        buy_count = #{record.buyCount,jdbcType=INTEGER},
      </if>
      <if test="record.couponType != null">
        coupon_type = #{record.couponType,jdbcType=INTEGER},
      </if>
      <if test="record.couponStatus != null">
        coupon_status = #{record.couponStatus,jdbcType=INTEGER},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.bookString != null">
        book_string = #{record.bookString,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_coupon
    set data_id = #{record.dataId,jdbcType=BIGINT},
      coupon_name = #{record.couponName,jdbcType=VARCHAR},
      coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      coupon_value = #{record.couponValue,jdbcType=INTEGER},
      buy_max = #{record.buyMax,jdbcType=INTEGER},
      use_max = #{record.useMax,jdbcType=INTEGER},
      min_limit_value = #{record.minLimitValue,jdbcType=INTEGER},
      buy_count = #{record.buyCount,jdbcType=INTEGER},
      coupon_type = #{record.couponType,jdbcType=INTEGER},
      coupon_status = #{record.couponStatus,jdbcType=INTEGER},
      begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      book_string = #{record.bookString,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_coupon
    set data_id = #{record.dataId,jdbcType=BIGINT},
      coupon_name = #{record.couponName,jdbcType=VARCHAR},
      coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      coupon_value = #{record.couponValue,jdbcType=INTEGER},
      buy_max = #{record.buyMax,jdbcType=INTEGER},
      use_max = #{record.useMax,jdbcType=INTEGER},
      min_limit_value = #{record.minLimitValue,jdbcType=INTEGER},
      buy_count = #{record.buyCount,jdbcType=INTEGER},
      coupon_type = #{record.couponType,jdbcType=INTEGER},
      coupon_status = #{record.couponStatus,jdbcType=INTEGER},
      begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanCoupon">
    update enyan_coupon
    <set>
      <if test="couponName != null">
        coupon_name = #{couponName,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        coupon_code = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponValue != null">
        coupon_value = #{couponValue,jdbcType=INTEGER},
      </if>
      <if test="buyMax != null">
        buy_max = #{buyMax,jdbcType=INTEGER},
      </if>
      <if test="useMax != null">
        use_max = #{useMax,jdbcType=INTEGER},
      </if>
      <if test="minLimitValue != null">
        min_limit_value = #{minLimitValue,jdbcType=INTEGER},
      </if>
      <if test="buyCount != null">
        buy_count = #{buyCount,jdbcType=INTEGER},
      </if>
      <if test="couponType != null">
        coupon_type = #{couponType,jdbcType=INTEGER},
      </if>
      <if test="couponStatus != null">
        coupon_status = #{couponStatus,jdbcType=INTEGER},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="bookString != null">
        book_string = #{bookString,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanCoupon">
    update enyan_coupon
    set coupon_name = #{couponName,jdbcType=VARCHAR},
      coupon_code = #{couponCode,jdbcType=VARCHAR},
      coupon_value = #{couponValue,jdbcType=INTEGER},
      buy_max = #{buyMax,jdbcType=INTEGER},
      use_max = #{useMax,jdbcType=INTEGER},
      min_limit_value = #{minLimitValue,jdbcType=INTEGER},
      buy_count = #{buyCount,jdbcType=INTEGER},
      coupon_type = #{couponType,jdbcType=INTEGER},
      coupon_status = #{couponStatus,jdbcType=INTEGER},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      book_string = #{bookString,jdbcType=LONGVARCHAR}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanCoupon">
    update enyan_coupon
    set coupon_name = #{couponName,jdbcType=VARCHAR},
      coupon_code = #{couponCode,jdbcType=VARCHAR},
      coupon_value = #{couponValue,jdbcType=INTEGER},
      buy_max = #{buyMax,jdbcType=INTEGER},
      use_max = #{useMax,jdbcType=INTEGER},
      min_limit_value = #{minLimitValue,jdbcType=INTEGER},
      buy_count = #{buyCount,jdbcType=INTEGER},
      coupon_type = #{couponType,jdbcType=INTEGER},
      coupon_status = #{couponStatus,jdbcType=INTEGER},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>