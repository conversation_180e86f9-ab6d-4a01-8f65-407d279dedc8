<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanPublisherMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanPublisher">
    <id column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="publisher_name" jdbcType="VARCHAR" property="publisherName" />
    <result column="publisher_name_tc" jdbcType="VARCHAR" property="publisherNameTc" />
    <result column="publisher_name_en" jdbcType="VARCHAR" property="publisherNameEn" />
    <result column="publisher_avatar" jdbcType="VARCHAR" property="publisherAvatar" />
    <result column="start_time" jdbcType="DATE" property="startTime" />
    <result column="vendor_percent" jdbcType="INTEGER" property="vendorPercent" />
    <result column="bank_national" jdbcType="INTEGER" property="bankNational" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_address" jdbcType="VARCHAR" property="bankAddress" />
    <result column="bank_code" jdbcType="VARCHAR" property="bankCode" />
    <result column="bank_title" jdbcType="VARCHAR" property="bankTitle" />
    <result column="bank_num" jdbcType="VARCHAR" property="bankNum" />
    <result column="fund_total" jdbcType="DECIMAL" property="fundTotal" />
    <result column="fund_withdrawn" jdbcType="DECIMAL" property="fundWithdrawn" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanPublisher">
    <result column="misc_config" jdbcType="LONGVARCHAR" property="miscConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    publisher_id, publisher_name, publisher_name_tc, publisher_name_en, publisher_avatar, 
    start_time, vendor_percent, bank_national, bank_name, bank_address, bank_code, bank_title, 
    bank_num, fund_total, fund_withdrawn
  </sql>
  <sql id="Blob_Column_List">
    misc_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanPublisherExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_publisher
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanPublisherExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_publisher
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_publisher
    where publisher_id = #{publisherId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_publisher
    where publisher_id = #{publisherId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanPublisherExample">
    delete from enyan_publisher
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanPublisher">
    <selectKey keyProperty="publisherId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_publisher (publisher_name, publisher_name_tc, publisher_name_en, 
      publisher_avatar, start_time, vendor_percent, 
      bank_national, bank_name, bank_address, 
      bank_code, bank_title, bank_num, 
      fund_total, fund_withdrawn, misc_config
      )
    values (#{publisherName,jdbcType=VARCHAR}, #{publisherNameTc,jdbcType=VARCHAR}, #{publisherNameEn,jdbcType=VARCHAR}, 
      #{publisherAvatar,jdbcType=VARCHAR}, #{startTime,jdbcType=DATE}, #{vendorPercent,jdbcType=INTEGER}, 
      #{bankNational,jdbcType=INTEGER}, #{bankName,jdbcType=VARCHAR}, #{bankAddress,jdbcType=VARCHAR}, 
      #{bankCode,jdbcType=VARCHAR}, #{bankTitle,jdbcType=VARCHAR}, #{bankNum,jdbcType=VARCHAR}, 
      #{fundTotal,jdbcType=DECIMAL}, #{fundWithdrawn,jdbcType=DECIMAL}, #{miscConfig,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanPublisher">
    <selectKey keyProperty="publisherId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_publisher
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="publisherName != null">
        publisher_name,
      </if>
      <if test="publisherNameTc != null">
        publisher_name_tc,
      </if>
      <if test="publisherNameEn != null">
        publisher_name_en,
      </if>
      <if test="publisherAvatar != null">
        publisher_avatar,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="vendorPercent != null">
        vendor_percent,
      </if>
      <if test="bankNational != null">
        bank_national,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankAddress != null">
        bank_address,
      </if>
      <if test="bankCode != null">
        bank_code,
      </if>
      <if test="bankTitle != null">
        bank_title,
      </if>
      <if test="bankNum != null">
        bank_num,
      </if>
      <if test="fundTotal != null">
        fund_total,
      </if>
      <if test="fundWithdrawn != null">
        fund_withdrawn,
      </if>
      <if test="miscConfig != null">
        misc_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="publisherName != null">
        #{publisherName,jdbcType=VARCHAR},
      </if>
      <if test="publisherNameTc != null">
        #{publisherNameTc,jdbcType=VARCHAR},
      </if>
      <if test="publisherNameEn != null">
        #{publisherNameEn,jdbcType=VARCHAR},
      </if>
      <if test="publisherAvatar != null">
        #{publisherAvatar,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="vendorPercent != null">
        #{vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="bankNational != null">
        #{bankNational,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAddress != null">
        #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankTitle != null">
        #{bankTitle,jdbcType=VARCHAR},
      </if>
      <if test="bankNum != null">
        #{bankNum,jdbcType=VARCHAR},
      </if>
      <if test="fundTotal != null">
        #{fundTotal,jdbcType=DECIMAL},
      </if>
      <if test="fundWithdrawn != null">
        #{fundWithdrawn,jdbcType=DECIMAL},
      </if>
      <if test="miscConfig != null">
        #{miscConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanPublisherExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_publisher
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_publisher
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_publisher
    <set>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.publisherName != null">
        publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherNameTc != null">
        publisher_name_tc = #{record.publisherNameTc,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherNameEn != null">
        publisher_name_en = #{record.publisherNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherAvatar != null">
        publisher_avatar = #{record.publisherAvatar,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=DATE},
      </if>
      <if test="record.vendorPercent != null">
        vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="record.bankNational != null">
        bank_national = #{record.bankNational,jdbcType=INTEGER},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAddress != null">
        bank_address = #{record.bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.bankCode != null">
        bank_code = #{record.bankCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bankTitle != null">
        bank_title = #{record.bankTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.bankNum != null">
        bank_num = #{record.bankNum,jdbcType=VARCHAR},
      </if>
      <if test="record.fundTotal != null">
        fund_total = #{record.fundTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.fundWithdrawn != null">
        fund_withdrawn = #{record.fundWithdrawn,jdbcType=DECIMAL},
      </if>
      <if test="record.miscConfig != null">
        misc_config = #{record.miscConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_publisher
    set publisher_id = #{record.publisherId,jdbcType=BIGINT},
      publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      publisher_name_tc = #{record.publisherNameTc,jdbcType=VARCHAR},
      publisher_name_en = #{record.publisherNameEn,jdbcType=VARCHAR},
      publisher_avatar = #{record.publisherAvatar,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=DATE},
      vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      bank_national = #{record.bankNational,jdbcType=INTEGER},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_address = #{record.bankAddress,jdbcType=VARCHAR},
      bank_code = #{record.bankCode,jdbcType=VARCHAR},
      bank_title = #{record.bankTitle,jdbcType=VARCHAR},
      bank_num = #{record.bankNum,jdbcType=VARCHAR},
      fund_total = #{record.fundTotal,jdbcType=DECIMAL},
      fund_withdrawn = #{record.fundWithdrawn,jdbcType=DECIMAL},
      misc_config = #{record.miscConfig,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_publisher
    set publisher_id = #{record.publisherId,jdbcType=BIGINT},
      publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      publisher_name_tc = #{record.publisherNameTc,jdbcType=VARCHAR},
      publisher_name_en = #{record.publisherNameEn,jdbcType=VARCHAR},
      publisher_avatar = #{record.publisherAvatar,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=DATE},
      vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      bank_national = #{record.bankNational,jdbcType=INTEGER},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_address = #{record.bankAddress,jdbcType=VARCHAR},
      bank_code = #{record.bankCode,jdbcType=VARCHAR},
      bank_title = #{record.bankTitle,jdbcType=VARCHAR},
      bank_num = #{record.bankNum,jdbcType=VARCHAR},
      fund_total = #{record.fundTotal,jdbcType=DECIMAL},
      fund_withdrawn = #{record.fundWithdrawn,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanPublisher">
    update enyan_publisher
    <set>
      <if test="publisherName != null">
        publisher_name = #{publisherName,jdbcType=VARCHAR},
      </if>
      <if test="publisherNameTc != null">
        publisher_name_tc = #{publisherNameTc,jdbcType=VARCHAR},
      </if>
      <if test="publisherNameEn != null">
        publisher_name_en = #{publisherNameEn,jdbcType=VARCHAR},
      </if>
      <if test="publisherAvatar != null">
        publisher_avatar = #{publisherAvatar,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=DATE},
      </if>
      <if test="vendorPercent != null">
        vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="bankNational != null">
        bank_national = #{bankNational,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAddress != null">
        bank_address = #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        bank_code = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankTitle != null">
        bank_title = #{bankTitle,jdbcType=VARCHAR},
      </if>
      <if test="bankNum != null">
        bank_num = #{bankNum,jdbcType=VARCHAR},
      </if>
      <if test="fundTotal != null">
        fund_total = #{fundTotal,jdbcType=DECIMAL},
      </if>
      <if test="fundWithdrawn != null">
        fund_withdrawn = #{fundWithdrawn,jdbcType=DECIMAL},
      </if>
      <if test="miscConfig != null">
        misc_config = #{miscConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where publisher_id = #{publisherId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanPublisher">
    update enyan_publisher
    set publisher_name = #{publisherName,jdbcType=VARCHAR},
      publisher_name_tc = #{publisherNameTc,jdbcType=VARCHAR},
      publisher_name_en = #{publisherNameEn,jdbcType=VARCHAR},
      publisher_avatar = #{publisherAvatar,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=DATE},
      vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      bank_national = #{bankNational,jdbcType=INTEGER},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_address = #{bankAddress,jdbcType=VARCHAR},
      bank_code = #{bankCode,jdbcType=VARCHAR},
      bank_title = #{bankTitle,jdbcType=VARCHAR},
      bank_num = #{bankNum,jdbcType=VARCHAR},
      fund_total = #{fundTotal,jdbcType=DECIMAL},
      fund_withdrawn = #{fundWithdrawn,jdbcType=DECIMAL},
      misc_config = #{miscConfig,jdbcType=LONGVARCHAR}
    where publisher_id = #{publisherId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanPublisher">
    update enyan_publisher
    set publisher_name = #{publisherName,jdbcType=VARCHAR},
      publisher_name_tc = #{publisherNameTc,jdbcType=VARCHAR},
      publisher_name_en = #{publisherNameEn,jdbcType=VARCHAR},
      publisher_avatar = #{publisherAvatar,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=DATE},
      vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      bank_national = #{bankNational,jdbcType=INTEGER},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_address = #{bankAddress,jdbcType=VARCHAR},
      bank_code = #{bankCode,jdbcType=VARCHAR},
      bank_title = #{bankTitle,jdbcType=VARCHAR},
      bank_num = #{bankNum,jdbcType=VARCHAR},
      fund_total = #{fundTotal,jdbcType=DECIMAL},
      fund_withdrawn = #{fundWithdrawn,jdbcType=DECIMAL}
    where publisher_id = #{publisherId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>