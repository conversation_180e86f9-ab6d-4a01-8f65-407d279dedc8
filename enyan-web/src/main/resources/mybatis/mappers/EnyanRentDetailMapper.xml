<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanRentDetailMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanRentDetail">
    <id column="detail_id" jdbcType="BIGINT" property="detailId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="rent_type" jdbcType="INTEGER" property="rentType" />
    <result column="rent_lang" jdbcType="INTEGER" property="rentLang" />
    <result column="is_auto" jdbcType="INTEGER" property="isAuto" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="rent_months" jdbcType="INTEGER" property="rentMonths" />
    <result column="vendor_percent" jdbcType="INTEGER" property="vendorPercent" />
    <result column="income_vendor" jdbcType="DECIMAL" property="incomeVendor" />
    <result column="income_plat" jdbcType="DECIMAL" property="incomePlat" />
    <result column="income_total" jdbcType="DECIMAL" property="incomeTotal" />
    <result column="income_real" jdbcType="DECIMAL" property="incomeReal" />
    <result column="pay_fee" jdbcType="DECIMAL" property="payFee" />
    <result column="net_sales" jdbcType="DECIMAL" property="netSales" />
    <result column="order_currency" jdbcType="INTEGER" property="orderCurrency" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="pay_country" jdbcType="VARCHAR" property="payCountry" />
    <result column="order_from" jdbcType="INTEGER" property="orderFrom" />
    <result column="purchased_at" jdbcType="TIMESTAMP" property="purchasedAt" />
    <result column="from_at" jdbcType="TIMESTAMP" property="fromAt" />
    <result column="expired_at" jdbcType="TIMESTAMP" property="expiredAt" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    detail_id, order_num, trade_no, user_email, rent_type, rent_lang, is_auto, publisher_id, 
    rent_months, vendor_percent, income_vendor, income_plat, income_total, income_real, 
    pay_fee, net_sales, order_currency, pay_type, pay_country, order_from, purchased_at, 
    from_at, expired_at, create_at, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanRentDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_rent_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_rent_detail
    where detail_id = #{detailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_rent_detail
    where detail_id = #{detailId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanRentDetailExample">
    delete from enyan_rent_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanRentDetail">
    <selectKey keyProperty="detailId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_rent_detail (order_num, trade_no, user_email, 
      rent_type, rent_lang, is_auto, 
      publisher_id, rent_months, vendor_percent, 
      income_vendor, income_plat, income_total, 
      income_real, pay_fee, net_sales, 
      order_currency, pay_type, pay_country, 
      order_from, purchased_at, from_at, 
      expired_at, create_at, is_deleted
      )
    values (#{orderNum,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR}, #{userEmail,jdbcType=VARCHAR}, 
      #{rentType,jdbcType=INTEGER}, #{rentLang,jdbcType=INTEGER}, #{isAuto,jdbcType=INTEGER}, 
      #{publisherId,jdbcType=BIGINT}, #{rentMonths,jdbcType=INTEGER}, #{vendorPercent,jdbcType=INTEGER}, 
      #{incomeVendor,jdbcType=DECIMAL}, #{incomePlat,jdbcType=DECIMAL}, #{incomeTotal,jdbcType=DECIMAL}, 
      #{incomeReal,jdbcType=DECIMAL}, #{payFee,jdbcType=DECIMAL}, #{netSales,jdbcType=DECIMAL}, 
      #{orderCurrency,jdbcType=INTEGER}, #{payType,jdbcType=INTEGER}, #{payCountry,jdbcType=VARCHAR}, 
      #{orderFrom,jdbcType=INTEGER}, #{purchasedAt,jdbcType=TIMESTAMP}, #{fromAt,jdbcType=TIMESTAMP}, 
      #{expiredAt,jdbcType=TIMESTAMP}, #{createAt,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanRentDetail">
    <selectKey keyProperty="detailId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_rent_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="rentType != null">
        rent_type,
      </if>
      <if test="rentLang != null">
        rent_lang,
      </if>
      <if test="isAuto != null">
        is_auto,
      </if>
      <if test="publisherId != null">
        publisher_id,
      </if>
      <if test="rentMonths != null">
        rent_months,
      </if>
      <if test="vendorPercent != null">
        vendor_percent,
      </if>
      <if test="incomeVendor != null">
        income_vendor,
      </if>
      <if test="incomePlat != null">
        income_plat,
      </if>
      <if test="incomeTotal != null">
        income_total,
      </if>
      <if test="incomeReal != null">
        income_real,
      </if>
      <if test="payFee != null">
        pay_fee,
      </if>
      <if test="netSales != null">
        net_sales,
      </if>
      <if test="orderCurrency != null">
        order_currency,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="payCountry != null">
        pay_country,
      </if>
      <if test="orderFrom != null">
        order_from,
      </if>
      <if test="purchasedAt != null">
        purchased_at,
      </if>
      <if test="fromAt != null">
        from_at,
      </if>
      <if test="expiredAt != null">
        expired_at,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="rentType != null">
        #{rentType,jdbcType=INTEGER},
      </if>
      <if test="rentLang != null">
        #{rentLang,jdbcType=INTEGER},
      </if>
      <if test="isAuto != null">
        #{isAuto,jdbcType=INTEGER},
      </if>
      <if test="publisherId != null">
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="rentMonths != null">
        #{rentMonths,jdbcType=INTEGER},
      </if>
      <if test="vendorPercent != null">
        #{vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="incomeVendor != null">
        #{incomeVendor,jdbcType=DECIMAL},
      </if>
      <if test="incomePlat != null">
        #{incomePlat,jdbcType=DECIMAL},
      </if>
      <if test="incomeTotal != null">
        #{incomeTotal,jdbcType=DECIMAL},
      </if>
      <if test="incomeReal != null">
        #{incomeReal,jdbcType=DECIMAL},
      </if>
      <if test="payFee != null">
        #{payFee,jdbcType=DECIMAL},
      </if>
      <if test="netSales != null">
        #{netSales,jdbcType=DECIMAL},
      </if>
      <if test="orderCurrency != null">
        #{orderCurrency,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="payCountry != null">
        #{payCountry,jdbcType=VARCHAR},
      </if>
      <if test="orderFrom != null">
        #{orderFrom,jdbcType=INTEGER},
      </if>
      <if test="purchasedAt != null">
        #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="fromAt != null">
        #{fromAt,jdbcType=TIMESTAMP},
      </if>
      <if test="expiredAt != null">
        #{expiredAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanRentDetailExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_rent_detail
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_rent_detail
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_rent_detail
    <set>
      <if test="record.detailId != null">
        detail_id = #{record.detailId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNum != null">
        order_num = #{record.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.rentType != null">
        rent_type = #{record.rentType,jdbcType=INTEGER},
      </if>
      <if test="record.rentLang != null">
        rent_lang = #{record.rentLang,jdbcType=INTEGER},
      </if>
      <if test="record.isAuto != null">
        is_auto = #{record.isAuto,jdbcType=INTEGER},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.rentMonths != null">
        rent_months = #{record.rentMonths,jdbcType=INTEGER},
      </if>
      <if test="record.vendorPercent != null">
        vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="record.incomeVendor != null">
        income_vendor = #{record.incomeVendor,jdbcType=DECIMAL},
      </if>
      <if test="record.incomePlat != null">
        income_plat = #{record.incomePlat,jdbcType=DECIMAL},
      </if>
      <if test="record.incomeTotal != null">
        income_total = #{record.incomeTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.incomeReal != null">
        income_real = #{record.incomeReal,jdbcType=DECIMAL},
      </if>
      <if test="record.payFee != null">
        pay_fee = #{record.payFee,jdbcType=DECIMAL},
      </if>
      <if test="record.netSales != null">
        net_sales = #{record.netSales,jdbcType=DECIMAL},
      </if>
      <if test="record.orderCurrency != null">
        order_currency = #{record.orderCurrency,jdbcType=INTEGER},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=INTEGER},
      </if>
      <if test="record.payCountry != null">
        pay_country = #{record.payCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.orderFrom != null">
        order_from = #{record.orderFrom,jdbcType=INTEGER},
      </if>
      <if test="record.purchasedAt != null">
        purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fromAt != null">
        from_at = #{record.fromAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiredAt != null">
        expired_at = #{record.expiredAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_rent_detail
    set detail_id = #{record.detailId,jdbcType=BIGINT},
      order_num = #{record.orderNum,jdbcType=VARCHAR},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      rent_type = #{record.rentType,jdbcType=INTEGER},
      rent_lang = #{record.rentLang,jdbcType=INTEGER},
      is_auto = #{record.isAuto,jdbcType=INTEGER},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      rent_months = #{record.rentMonths,jdbcType=INTEGER},
      vendor_percent = #{record.vendorPercent,jdbcType=INTEGER},
      income_vendor = #{record.incomeVendor,jdbcType=DECIMAL},
      income_plat = #{record.incomePlat,jdbcType=DECIMAL},
      income_total = #{record.incomeTotal,jdbcType=DECIMAL},
      income_real = #{record.incomeReal,jdbcType=DECIMAL},
      pay_fee = #{record.payFee,jdbcType=DECIMAL},
      net_sales = #{record.netSales,jdbcType=DECIMAL},
      order_currency = #{record.orderCurrency,jdbcType=INTEGER},
      pay_type = #{record.payType,jdbcType=INTEGER},
      pay_country = #{record.payCountry,jdbcType=VARCHAR},
      order_from = #{record.orderFrom,jdbcType=INTEGER},
      purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      from_at = #{record.fromAt,jdbcType=TIMESTAMP},
      expired_at = #{record.expiredAt,jdbcType=TIMESTAMP},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanRentDetail">
    update enyan_rent_detail
    <set>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="rentType != null">
        rent_type = #{rentType,jdbcType=INTEGER},
      </if>
      <if test="rentLang != null">
        rent_lang = #{rentLang,jdbcType=INTEGER},
      </if>
      <if test="isAuto != null">
        is_auto = #{isAuto,jdbcType=INTEGER},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="rentMonths != null">
        rent_months = #{rentMonths,jdbcType=INTEGER},
      </if>
      <if test="vendorPercent != null">
        vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      </if>
      <if test="incomeVendor != null">
        income_vendor = #{incomeVendor,jdbcType=DECIMAL},
      </if>
      <if test="incomePlat != null">
        income_plat = #{incomePlat,jdbcType=DECIMAL},
      </if>
      <if test="incomeTotal != null">
        income_total = #{incomeTotal,jdbcType=DECIMAL},
      </if>
      <if test="incomeReal != null">
        income_real = #{incomeReal,jdbcType=DECIMAL},
      </if>
      <if test="payFee != null">
        pay_fee = #{payFee,jdbcType=DECIMAL},
      </if>
      <if test="netSales != null">
        net_sales = #{netSales,jdbcType=DECIMAL},
      </if>
      <if test="orderCurrency != null">
        order_currency = #{orderCurrency,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="payCountry != null">
        pay_country = #{payCountry,jdbcType=VARCHAR},
      </if>
      <if test="orderFrom != null">
        order_from = #{orderFrom,jdbcType=INTEGER},
      </if>
      <if test="purchasedAt != null">
        purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="fromAt != null">
        from_at = #{fromAt,jdbcType=TIMESTAMP},
      </if>
      <if test="expiredAt != null">
        expired_at = #{expiredAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where detail_id = #{detailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanRentDetail">
    update enyan_rent_detail
    set order_num = #{orderNum,jdbcType=VARCHAR},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      user_email = #{userEmail,jdbcType=VARCHAR},
      rent_type = #{rentType,jdbcType=INTEGER},
      rent_lang = #{rentLang,jdbcType=INTEGER},
      is_auto = #{isAuto,jdbcType=INTEGER},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      rent_months = #{rentMonths,jdbcType=INTEGER},
      vendor_percent = #{vendorPercent,jdbcType=INTEGER},
      income_vendor = #{incomeVendor,jdbcType=DECIMAL},
      income_plat = #{incomePlat,jdbcType=DECIMAL},
      income_total = #{incomeTotal,jdbcType=DECIMAL},
      income_real = #{incomeReal,jdbcType=DECIMAL},
      pay_fee = #{payFee,jdbcType=DECIMAL},
      net_sales = #{netSales,jdbcType=DECIMAL},
      order_currency = #{orderCurrency,jdbcType=INTEGER},
      pay_type = #{payType,jdbcType=INTEGER},
      pay_country = #{payCountry,jdbcType=VARCHAR},
      order_from = #{orderFrom,jdbcType=INTEGER},
      purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      from_at = #{fromAt,jdbcType=TIMESTAMP},
      expired_at = #{expiredAt,jdbcType=TIMESTAMP},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where detail_id = #{detailId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>