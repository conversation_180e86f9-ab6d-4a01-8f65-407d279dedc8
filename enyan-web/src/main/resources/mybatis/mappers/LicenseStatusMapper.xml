<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.LicenseStatusMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.LicenseStatus">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="license_updated" jdbcType="TIMESTAMP" property="licenseUpdated" />
    <result column="status_updated" jdbcType="TIMESTAMP" property="statusUpdated" />
    <result column="device_count" jdbcType="INTEGER" property="deviceCount" />
    <result column="potential_rights_end" jdbcType="TIMESTAMP" property="potentialRightsEnd" />
    <result column="license_ref" jdbcType="VARCHAR" property="licenseRef" />
    <result column="rights_end" jdbcType="TIMESTAMP" property="rightsEnd" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, status, license_updated, status_updated, device_count, potential_rights_end, 
    license_ref, rights_end
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.LicenseStatusExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from license_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from license_status
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from license_status
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.LicenseStatusExample">
    delete from license_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.LicenseStatus">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into license_status (status, license_updated, status_updated, 
      device_count, potential_rights_end, license_ref, 
      rights_end)
    values (#{status,jdbcType=INTEGER}, #{licenseUpdated,jdbcType=TIMESTAMP}, #{statusUpdated,jdbcType=TIMESTAMP}, 
      #{deviceCount,jdbcType=INTEGER}, #{potentialRightsEnd,jdbcType=TIMESTAMP}, #{licenseRef,jdbcType=VARCHAR}, 
      #{rightsEnd,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.LicenseStatus">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into license_status
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        status,
      </if>
      <if test="licenseUpdated != null">
        license_updated,
      </if>
      <if test="statusUpdated != null">
        status_updated,
      </if>
      <if test="deviceCount != null">
        device_count,
      </if>
      <if test="potentialRightsEnd != null">
        potential_rights_end,
      </if>
      <if test="licenseRef != null">
        license_ref,
      </if>
      <if test="rightsEnd != null">
        rights_end,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="licenseUpdated != null">
        #{licenseUpdated,jdbcType=TIMESTAMP},
      </if>
      <if test="statusUpdated != null">
        #{statusUpdated,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceCount != null">
        #{deviceCount,jdbcType=INTEGER},
      </if>
      <if test="potentialRightsEnd != null">
        #{potentialRightsEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="licenseRef != null">
        #{licenseRef,jdbcType=VARCHAR},
      </if>
      <if test="rightsEnd != null">
        #{rightsEnd,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.LicenseStatusExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from license_status
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          license_status
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update license_status
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.licenseUpdated != null">
        license_updated = #{record.licenseUpdated,jdbcType=TIMESTAMP},
      </if>
      <if test="record.statusUpdated != null">
        status_updated = #{record.statusUpdated,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deviceCount != null">
        device_count = #{record.deviceCount,jdbcType=INTEGER},
      </if>
      <if test="record.potentialRightsEnd != null">
        potential_rights_end = #{record.potentialRightsEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.licenseRef != null">
        license_ref = #{record.licenseRef,jdbcType=VARCHAR},
      </if>
      <if test="record.rightsEnd != null">
        rights_end = #{record.rightsEnd,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update license_status
    set id = #{record.id,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      license_updated = #{record.licenseUpdated,jdbcType=TIMESTAMP},
      status_updated = #{record.statusUpdated,jdbcType=TIMESTAMP},
      device_count = #{record.deviceCount,jdbcType=INTEGER},
      potential_rights_end = #{record.potentialRightsEnd,jdbcType=TIMESTAMP},
      license_ref = #{record.licenseRef,jdbcType=VARCHAR},
      rights_end = #{record.rightsEnd,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.LicenseStatus">
    update license_status
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="licenseUpdated != null">
        license_updated = #{licenseUpdated,jdbcType=TIMESTAMP},
      </if>
      <if test="statusUpdated != null">
        status_updated = #{statusUpdated,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceCount != null">
        device_count = #{deviceCount,jdbcType=INTEGER},
      </if>
      <if test="potentialRightsEnd != null">
        potential_rights_end = #{potentialRightsEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="licenseRef != null">
        license_ref = #{licenseRef,jdbcType=VARCHAR},
      </if>
      <if test="rightsEnd != null">
        rights_end = #{rightsEnd,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.LicenseStatus">
    update license_status
    set status = #{status,jdbcType=INTEGER},
      license_updated = #{licenseUpdated,jdbcType=TIMESTAMP},
      status_updated = #{statusUpdated,jdbcType=TIMESTAMP},
      device_count = #{deviceCount,jdbcType=INTEGER},
      potential_rights_end = #{potentialRightsEnd,jdbcType=TIMESTAMP},
      license_ref = #{licenseRef,jdbcType=VARCHAR},
      rights_end = #{rightsEnd,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>