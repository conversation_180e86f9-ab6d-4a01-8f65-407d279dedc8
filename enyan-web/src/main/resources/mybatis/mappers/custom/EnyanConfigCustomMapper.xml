<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanConfigCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanConfig">
    <id column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="config_description" jdbcType="VARCHAR" property="configDescription" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="config_value" jdbcType="VARCHAR" property="configValue" />
    <result column="is_show" jdbcType="TINYINT" property="isShow" />
  </resultMap>

  <insert id="add" parameterType="com.aaron.spring.model.EnyanConfig">
    insert into enyan_config (config_id,config_description, config_name, config_value, is_show)
    values (#{configId,jdbcType=BIGINT},#{configDescription,jdbcType=VARCHAR}, #{configName,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR},
      #{isShow,jdbcType=TINYINT})
  </insert>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>