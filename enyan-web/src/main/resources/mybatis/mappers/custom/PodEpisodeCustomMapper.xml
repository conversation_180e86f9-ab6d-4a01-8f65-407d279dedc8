<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.PodEpisodeCustomMapper">
  <sql id="Base_Column_List">
    episode_id, podcast_id, topic_id, title, description, audio_file_url, duration_seconds, 
    is_published, publication_date, episode_number, episode_count, listen_count, like_count, 
    is_deleted, created_at, cover_image_url, cover_image_url2
  </sql>
  
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.PodEpisode">
    <id column="episode_id" jdbcType="BIGINT" property="episodeId" />
    <result column="podcast_id" jdbcType="BIGINT" property="podcastId" />
    <result column="topic_id" jdbcType="BIGINT" property="topicId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="audio_file_url" jdbcType="VARCHAR" property="audioFileUrl" />
    <result column="duration_seconds" jdbcType="INTEGER" property="durationSeconds" />
    <result column="is_published" jdbcType="INTEGER" property="isPublished" />
    <result column="publication_date" jdbcType="TIMESTAMP" property="publicationDate" />
    <result column="episode_number" jdbcType="INTEGER" property="episodeNumber" />
    <result column="episode_count" jdbcType="INTEGER" property="episodeCount" />
    <result column="listen_count" jdbcType="INTEGER" property="listenCount" />
    <result column="like_count" jdbcType="INTEGER" property="likeCount" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="cover_image_url" jdbcType="VARCHAR" property="coverImageUrl" />
    <result column="cover_image_url2" jdbcType="VARCHAR" property="coverImageUrl2" />
  </resultMap>
  
  <!-- 逻辑删除播客单集 -->
  <update id="updateEpisodeToDeletedById" parameterType="java.lang.Long">
    UPDATE pod_episode
    SET is_deleted = 1
    WHERE episode_id = #{episodeId,jdbcType=BIGINT}
  </update>

  
  <!-- 增加播客单集点赞量 -->
  <update id="updateEpisodeLikeCountById" parameterType="java.lang.Long">
    UPDATE pod_episode
    SET like_count = like_count + 1
    WHERE episode_id = #{episodeId,jdbcType=BIGINT}
  </update>
  
  <!-- 更新指定播客下所有单集的封面图片 -->
  <update id="updateEpisodesCoverImageByPodcastId">
    UPDATE pod_episode
    SET cover_image_url = #{coverImageUrl,jdbcType=VARCHAR},
        cover_image_url2 = #{coverImageUrl2,jdbcType=VARCHAR},
        updated_at = NOW()
    WHERE podcast_id = #{podcastId,jdbcType=BIGINT}
    AND is_deleted = 0
  </update>
  
  <!-- 记录开始播放 -->
  <update id="recordEpisodePlayStart">
    UPDATE pod_user_episode_interaction 
    SET last_played_at = NOW()
    WHERE user_email = #{email} 
    AND episode_id = #{episodeId};
    
    INSERT INTO pod_user_episode_interaction 
      (user_email, episode_id, last_played_at, created_at)
    SELECT 
      #{email}, 
      #{episodeId}, 
      NOW(), 
      NOW()
    WHERE NOT EXISTS (
      SELECT 1 
      FROM pod_user_episode_interaction 
      WHERE user_email = #{email} 
      AND episode_id = #{episodeId}
    );
  </update>
  
  <!-- 增加单集播放次数 -->
  <update id="incrementEpisodeListenCount">
    UPDATE pod_episode
    SET listen_count = listen_count + 1
    WHERE episode_id = #{episodeId}
  </update>
  
  <!-- 记录播放停止 -->
  <update id="recordEpisodePlayStop">
    UPDATE pod_user_episode_interaction uei
    JOIN pod_episode e ON uei.episode_id = e.episode_id
    SET uei.is_completed = CASE 
        WHEN TIMESTAMPDIFF(SECOND, uei.last_played_at, NOW()) >= e.duration_seconds THEN 1
        ELSE 0 
      END
    WHERE uei.episode_id = #{episodeId}
    AND uei.user_email = #{email}
  </update>
  
  <!-- 更新单集累计播放时长 -->
  <update id="updateCumulativePlaybackSeconds">
    UPDATE pod_episode e
    JOIN pod_user_episode_interaction i ON e.episode_id = i.episode_id
    SET e.cumulative_playback_seconds = 
        e.cumulative_playback_seconds + 
        TIMESTAMPDIFF(SECOND, i.last_played_at, NOW())
    WHERE e.episode_id = #{episodeId}
    AND (i.user_email = #{email})
  </update>
  
  <!-- 检查用户是否已经开始播放某个单集  -->
  <select id="hasUserStartedPlaying" resultType="boolean">
    SELECT CASE 
      WHEN COUNT(1) > 0 THEN TRUE 
      ELSE FALSE 
    END
    FROM pod_user_episode_interaction
    WHERE user_email = #{email}
    AND episode_id = #{episodeId}
    AND is_completed = 0
  </select>
  

  
  <!-- 查询总记录数 -->
  <select id="queryCount" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM pod_episode
    <where>
      <if test="record.topicId != null">
        AND topic_id = #{record.topicId}
      </if>
      AND is_published = 1
      AND is_deleted = 0
    </where>
  </select>

    <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>
