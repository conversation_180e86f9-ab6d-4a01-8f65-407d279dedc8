<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.StuEnrollCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.StuEnroll">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="stu_id" jdbcType="INTEGER" property="stuId" />
    <result column="last_name" jdbcType="VARCHAR" property="lastName" />
    <result column="first_name" jdbcType="VARCHAR" property="firstName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="enroll_at" jdbcType="TIMESTAMP" property="enrollAt" />
    <result column="enroll_status" jdbcType="INTEGER" property="enrollStatus" />
    <result column="enroll_code" jdbcType="VARCHAR" property="enrollCode" />
    <result column="checkin" jdbcType="VARCHAR" property="checkin" />
    <result column="terms" jdbcType="INTEGER" property="terms" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, stu_id, last_name, first_name, email, enroll_at, enroll_status, enroll_code,
    checkin, terms
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.StuEnrollExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from stu_enroll
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stu_enroll
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stu_enroll
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.StuEnrollExample">
    delete from stu_enroll
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.StuEnroll">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stu_enroll (stu_id, last_name, first_name,
    email, enroll_at, enroll_status,
    enroll_code, checkin, terms
    )
    values (#{stuId,jdbcType=INTEGER}, #{lastName,jdbcType=VARCHAR}, #{firstName,jdbcType=VARCHAR},
    #{email,jdbcType=VARCHAR}, #{enrollAt,jdbcType=TIMESTAMP}, #{enrollStatus,jdbcType=INTEGER},
    #{enrollCode,jdbcType=VARCHAR}, #{checkin,jdbcType=VARCHAR}, #{terms,jdbcType=INTEGER}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.StuEnroll">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stu_enroll
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="stuId != null">
        stu_id,
      </if>
      <if test="lastName != null">
        last_name,
      </if>
      <if test="firstName != null">
        first_name,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="enrollAt != null">
        enroll_at,
      </if>
      <if test="enrollStatus != null">
        enroll_status,
      </if>
      <if test="enrollCode != null">
        enroll_code,
      </if>
      <if test="checkin != null">
        checkin,
      </if>
      <if test="terms != null">
        terms,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="stuId != null">
        #{stuId,jdbcType=INTEGER},
      </if>
      <if test="lastName != null">
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="enrollAt != null">
        #{enrollAt,jdbcType=TIMESTAMP},
      </if>
      <if test="enrollStatus != null">
        #{enrollStatus,jdbcType=INTEGER},
      </if>
      <if test="enrollCode != null">
        #{enrollCode,jdbcType=VARCHAR},
      </if>
      <if test="checkin != null">
        #{checkin,jdbcType=VARCHAR},
      </if>
      <if test="terms != null">
        #{terms,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.StuEnrollExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
      DISTINCT
      <include refid="Base_Column_List"/>
      from stu_enroll
      <if test="_parameter != null">
        <include refid="Example_Where_Clause"/>
      </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">

      stu_enroll
      <if test="_parameter != null" >
        <include refid="Example_Where_Clause" />
      </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stu_enroll
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.stuId != null">
        stu_id = #{record.stuId,jdbcType=INTEGER},
      </if>
      <if test="record.lastName != null">
        last_name = #{record.lastName,jdbcType=VARCHAR},
      </if>
      <if test="record.firstName != null">
        first_name = #{record.firstName,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.enrollAt != null">
        enroll_at = #{record.enrollAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enrollStatus != null">
        enroll_status = #{record.enrollStatus,jdbcType=INTEGER},
      </if>
      <if test="record.enrollCode != null">
        enroll_code = #{record.enrollCode,jdbcType=VARCHAR},
      </if>
      <if test="record.checkin != null">
        checkin = #{record.checkin,jdbcType=VARCHAR},
      </if>
      <if test="record.terms != null">
        terms = #{record.terms,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stu_enroll
    set data_id = #{record.dataId,jdbcType=BIGINT},
    stu_id = #{record.stuId,jdbcType=INTEGER},
    last_name = #{record.lastName,jdbcType=VARCHAR},
    first_name = #{record.firstName,jdbcType=VARCHAR},
    email = #{record.email,jdbcType=VARCHAR},
    enroll_at = #{record.enrollAt,jdbcType=TIMESTAMP},
    enroll_status = #{record.enrollStatus,jdbcType=INTEGER},
    enroll_code = #{record.enrollCode,jdbcType=VARCHAR},
    checkin = #{record.checkin,jdbcType=VARCHAR},
    terms = #{record.terms,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.StuEnroll">
    update stu_enroll
    <set>
      <if test="stuId != null">
        stu_id = #{stuId,jdbcType=INTEGER},
      </if>
      <if test="lastName != null">
        last_name = #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        first_name = #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="enrollAt != null">
        enroll_at = #{enrollAt,jdbcType=TIMESTAMP},
      </if>
      <if test="enrollStatus != null">
        enroll_status = #{enrollStatus,jdbcType=INTEGER},
      </if>
      <if test="enrollCode != null">
        enroll_code = #{enrollCode,jdbcType=VARCHAR},
      </if>
      <if test="checkin != null">
        checkin = #{checkin,jdbcType=VARCHAR},
      </if>
      <if test="terms != null">
        terms = #{terms,jdbcType=INTEGER},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.StuEnroll">
    update stu_enroll
    set stu_id = #{stuId,jdbcType=INTEGER},
        last_name = #{lastName,jdbcType=VARCHAR},
        first_name = #{firstName,jdbcType=VARCHAR},
        email = #{email,jdbcType=VARCHAR},
        enroll_at = #{enrollAt,jdbcType=TIMESTAMP},
        enroll_status = #{enrollStatus,jdbcType=INTEGER},
        enroll_code = #{enrollCode,jdbcType=VARCHAR},
        checkin = #{checkin,jdbcType=VARCHAR},
        terms = #{terms,jdbcType=INTEGER}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>