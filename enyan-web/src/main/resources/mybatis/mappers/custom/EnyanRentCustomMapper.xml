<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanRentCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanRent">
    <id column="rent_id" jdbcType="BIGINT" property="rentId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="rent_type" jdbcType="INTEGER" property="rentType" />
    <result column="rent_lang" jdbcType="INTEGER" property="rentLang" />
    <result column="is_valid" jdbcType="INTEGER" property="isValid" />
    <result column="rent_status" jdbcType="INTEGER" property="rentStatus" />
    <result column="is_auto" jdbcType="INTEGER" property="isAuto" />
    <result column="is_paid" jdbcType="INTEGER" property="isPaid" />
    <result column="leave_buy" jdbcType="INTEGER" property="leaveBuy" />
    <result column="to_order_num" jdbcType="VARCHAR" property="toOrderNum" />
    <result column="base_license" jdbcType="VARCHAR" property="baseLicense" />
    <result column="rent_price" jdbcType="DECIMAL" property="rentPrice" />
    <result column="total_fee" jdbcType="DECIMAL" property="totalFee" />
    <result column="total_months" jdbcType="INTEGER" property="totalMonths" />
    <result column="order_from" jdbcType="INTEGER" property="orderFrom" />
    <result column="begin_at" jdbcType="TIMESTAMP" property="beginAt" />
    <result column="leave_at" jdbcType="TIMESTAMP" property="leaveAt" />
    <result column="expired_at" jdbcType="TIMESTAMP" property="expiredAt" />
    <result column="valueInt" jdbcType="INTEGER" property="valueInt" />
    <result column="valueString" jdbcType="VARCHAR" property="valueString" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanRent">
    <result column="rent_detail" jdbcType="LONGVARCHAR" property="rentDetail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    rent_id, order_num, user_email, publisher_id, rent_type, rent_lang, is_valid, rent_status,
    is_auto, is_paid, leave_buy, to_order_num, base_license, rent_price, total_fee, total_months,
    order_from, begin_at, leave_at, expired_at, create_at, is_deleted
  </sql>
  <sql id="Blob_Column_List">
    rent_detail
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanRentExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_rent
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanRentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_rent
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_rent
    where rent_id = #{rentId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_rent
    where rent_id = #{rentId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanRentExample">
    delete from enyan_rent
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanRent">
    <selectKey keyProperty="rentId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_rent (order_num, user_email, publisher_id, 
      rent_type, rent_lang, is_valid, 
      rent_status, is_auto, is_paid, 
      is_auto_leave, leave_buy, to_order_num, 
      total_fee, total_months, credit_info, 
      order_from, begin_at, leave_at, 
      expired_at, create_at, rent_detail
      )
    values (#{orderNum,jdbcType=VARCHAR}, #{userEmail,jdbcType=VARCHAR}, #{publisherId,jdbcType=BIGINT}, 
      #{rentType,jdbcType=INTEGER}, #{rentLang,jdbcType=INTEGER}, #{isValid,jdbcType=INTEGER}, 
      #{rentStatus,jdbcType=INTEGER}, #{isAuto,jdbcType=INTEGER}, #{isPaid,jdbcType=INTEGER}, 
      #{isAutoLeave,jdbcType=INTEGER}, #{leaveBuy,jdbcType=INTEGER}, #{toOrderNum,jdbcType=VARCHAR}, 
      #{totalFee,jdbcType=DECIMAL}, #{totalMonths,jdbcType=INTEGER}, #{creditInfo,jdbcType=VARCHAR}, 
      #{orderFrom,jdbcType=INTEGER}, #{beginAt,jdbcType=TIMESTAMP}, #{leaveAt,jdbcType=TIMESTAMP}, 
      #{expiredAt,jdbcType=TIMESTAMP}, #{createAt,jdbcType=TIMESTAMP}, #{rentDetail,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanRent">
    <selectKey keyProperty="rentId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_rent
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="publisherId != null">
        publisher_id,
      </if>
      <if test="rentType != null">
        rent_type,
      </if>
      <if test="rentLang != null">
        rent_lang,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="rentStatus != null">
        rent_status,
      </if>
      <if test="isAuto != null">
        is_auto,
      </if>
      <if test="isPaid != null">
        is_paid,
      </if>
      <if test="isAutoLeave != null">
        is_auto_leave,
      </if>
      <if test="leaveBuy != null">
        leave_buy,
      </if>
      <if test="toOrderNum != null">
        to_order_num,
      </if>
      <if test="totalFee != null">
        total_fee,
      </if>
      <if test="totalMonths != null">
        total_months,
      </if>
      <if test="creditInfo != null">
        credit_info,
      </if>
      <if test="orderFrom != null">
        order_from,
      </if>
      <if test="beginAt != null">
        begin_at,
      </if>
      <if test="leaveAt != null">
        leave_at,
      </if>
      <if test="expiredAt != null">
        expired_at,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="rentDetail != null">
        rent_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="rentType != null">
        #{rentType,jdbcType=INTEGER},
      </if>
      <if test="rentLang != null">
        #{rentLang,jdbcType=INTEGER},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=INTEGER},
      </if>
      <if test="rentStatus != null">
        #{rentStatus,jdbcType=INTEGER},
      </if>
      <if test="isAuto != null">
        #{isAuto,jdbcType=INTEGER},
      </if>
      <if test="isPaid != null">
        #{isPaid,jdbcType=INTEGER},
      </if>
      <if test="isAutoLeave != null">
        #{isAutoLeave,jdbcType=INTEGER},
      </if>
      <if test="leaveBuy != null">
        #{leaveBuy,jdbcType=INTEGER},
      </if>
      <if test="toOrderNum != null">
        #{toOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="totalFee != null">
        #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="totalMonths != null">
        #{totalMonths,jdbcType=INTEGER},
      </if>
      <if test="creditInfo != null">
        #{creditInfo,jdbcType=VARCHAR},
      </if>
      <if test="orderFrom != null">
        #{orderFrom,jdbcType=INTEGER},
      </if>
      <if test="beginAt != null">
        #{beginAt,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveAt != null">
        #{leaveAt,jdbcType=TIMESTAMP},
      </if>
      <if test="expiredAt != null">
        #{expiredAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="rentDetail != null">
        #{rentDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanRentExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_rent
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_rent
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_rent
    <set>
      <if test="record.rentId != null">
        rent_id = #{record.rentId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNum != null">
        order_num = #{record.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.rentType != null">
        rent_type = #{record.rentType,jdbcType=INTEGER},
      </if>
      <if test="record.rentLang != null">
        rent_lang = #{record.rentLang,jdbcType=INTEGER},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=INTEGER},
      </if>
      <if test="record.rentStatus != null">
        rent_status = #{record.rentStatus,jdbcType=INTEGER},
      </if>
      <if test="record.isAuto != null">
        is_auto = #{record.isAuto,jdbcType=INTEGER},
      </if>
      <if test="record.isPaid != null">
        is_paid = #{record.isPaid,jdbcType=INTEGER},
      </if>
      <if test="record.isAutoLeave != null">
        is_auto_leave = #{record.isAutoLeave,jdbcType=INTEGER},
      </if>
      <if test="record.leaveBuy != null">
        leave_buy = #{record.leaveBuy,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderNum != null">
        to_order_num = #{record.toOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.totalFee != null">
        total_fee = #{record.totalFee,jdbcType=DECIMAL},
      </if>
      <if test="record.totalMonths != null">
        total_months = #{record.totalMonths,jdbcType=INTEGER},
      </if>
      <if test="record.creditInfo != null">
        credit_info = #{record.creditInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderFrom != null">
        order_from = #{record.orderFrom,jdbcType=INTEGER},
      </if>
      <if test="record.beginAt != null">
        begin_at = #{record.beginAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.leaveAt != null">
        leave_at = #{record.leaveAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiredAt != null">
        expired_at = #{record.expiredAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rentDetail != null">
        rent_detail = #{record.rentDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_rent
    set rent_id = #{record.rentId,jdbcType=BIGINT},
      order_num = #{record.orderNum,jdbcType=VARCHAR},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      rent_type = #{record.rentType,jdbcType=INTEGER},
      rent_lang = #{record.rentLang,jdbcType=INTEGER},
      is_valid = #{record.isValid,jdbcType=INTEGER},
      rent_status = #{record.rentStatus,jdbcType=INTEGER},
      is_auto = #{record.isAuto,jdbcType=INTEGER},
      is_paid = #{record.isPaid,jdbcType=INTEGER},
      is_auto_leave = #{record.isAutoLeave,jdbcType=INTEGER},
      leave_buy = #{record.leaveBuy,jdbcType=INTEGER},
      to_order_num = #{record.toOrderNum,jdbcType=VARCHAR},
      total_fee = #{record.totalFee,jdbcType=DECIMAL},
      total_months = #{record.totalMonths,jdbcType=INTEGER},
      credit_info = #{record.creditInfo,jdbcType=VARCHAR},
      order_from = #{record.orderFrom,jdbcType=INTEGER},
      begin_at = #{record.beginAt,jdbcType=TIMESTAMP},
      leave_at = #{record.leaveAt,jdbcType=TIMESTAMP},
      expired_at = #{record.expiredAt,jdbcType=TIMESTAMP},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      rent_detail = #{record.rentDetail,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_rent
    set rent_id = #{record.rentId,jdbcType=BIGINT},
      order_num = #{record.orderNum,jdbcType=VARCHAR},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      rent_type = #{record.rentType,jdbcType=INTEGER},
      rent_lang = #{record.rentLang,jdbcType=INTEGER},
      is_valid = #{record.isValid,jdbcType=INTEGER},
      rent_status = #{record.rentStatus,jdbcType=INTEGER},
      is_auto = #{record.isAuto,jdbcType=INTEGER},
      is_paid = #{record.isPaid,jdbcType=INTEGER},
      is_auto_leave = #{record.isAutoLeave,jdbcType=INTEGER},
      leave_buy = #{record.leaveBuy,jdbcType=INTEGER},
      to_order_num = #{record.toOrderNum,jdbcType=VARCHAR},
      total_fee = #{record.totalFee,jdbcType=DECIMAL},
      total_months = #{record.totalMonths,jdbcType=INTEGER},
      credit_info = #{record.creditInfo,jdbcType=VARCHAR},
      order_from = #{record.orderFrom,jdbcType=INTEGER},
      begin_at = #{record.beginAt,jdbcType=TIMESTAMP},
      leave_at = #{record.leaveAt,jdbcType=TIMESTAMP},
      expired_at = #{record.expiredAt,jdbcType=TIMESTAMP},
      create_at = #{record.createAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanRent">
    update enyan_rent
    <set>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="rentType != null">
        rent_type = #{rentType,jdbcType=INTEGER},
      </if>
      <if test="rentLang != null">
        rent_lang = #{rentLang,jdbcType=INTEGER},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=INTEGER},
      </if>
      <if test="rentStatus != null">
        rent_status = #{rentStatus,jdbcType=INTEGER},
      </if>
      <if test="isAuto != null">
        is_auto = #{isAuto,jdbcType=INTEGER},
      </if>
      <if test="isPaid != null">
        is_paid = #{isPaid,jdbcType=INTEGER},
      </if>
      <if test="isAutoLeave != null">
        is_auto_leave = #{isAutoLeave,jdbcType=INTEGER},
      </if>
      <if test="leaveBuy != null">
        leave_buy = #{leaveBuy,jdbcType=INTEGER},
      </if>
      <if test="toOrderNum != null">
        to_order_num = #{toOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="totalFee != null">
        total_fee = #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="totalMonths != null">
        total_months = #{totalMonths,jdbcType=INTEGER},
      </if>
      <if test="creditInfo != null">
        credit_info = #{creditInfo,jdbcType=VARCHAR},
      </if>
      <if test="orderFrom != null">
        order_from = #{orderFrom,jdbcType=INTEGER},
      </if>
      <if test="beginAt != null">
        begin_at = #{beginAt,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveAt != null">
        leave_at = #{leaveAt,jdbcType=TIMESTAMP},
      </if>
      <if test="expiredAt != null">
        expired_at = #{expiredAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="rentDetail != null">
        rent_detail = #{rentDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where rent_id = #{rentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanRent">
    update enyan_rent
    set order_num = #{orderNum,jdbcType=VARCHAR},
      user_email = #{userEmail,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      rent_type = #{rentType,jdbcType=INTEGER},
      rent_lang = #{rentLang,jdbcType=INTEGER},
      is_valid = #{isValid,jdbcType=INTEGER},
      rent_status = #{rentStatus,jdbcType=INTEGER},
      is_auto = #{isAuto,jdbcType=INTEGER},
      is_paid = #{isPaid,jdbcType=INTEGER},
      is_auto_leave = #{isAutoLeave,jdbcType=INTEGER},
      leave_buy = #{leaveBuy,jdbcType=INTEGER},
      to_order_num = #{toOrderNum,jdbcType=VARCHAR},
      total_fee = #{totalFee,jdbcType=DECIMAL},
      total_months = #{totalMonths,jdbcType=INTEGER},
      credit_info = #{creditInfo,jdbcType=VARCHAR},
      order_from = #{orderFrom,jdbcType=INTEGER},
      begin_at = #{beginAt,jdbcType=TIMESTAMP},
      leave_at = #{leaveAt,jdbcType=TIMESTAMP},
      expired_at = #{expiredAt,jdbcType=TIMESTAMP},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      rent_detail = #{rentDetail,jdbcType=LONGVARCHAR}
    where rent_id = #{rentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanRent">
    update enyan_rent
    set order_num = #{orderNum,jdbcType=VARCHAR},
      user_email = #{userEmail,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      rent_type = #{rentType,jdbcType=INTEGER},
      rent_lang = #{rentLang,jdbcType=INTEGER},
      is_valid = #{isValid,jdbcType=INTEGER},
      rent_status = #{rentStatus,jdbcType=INTEGER},
      is_auto = #{isAuto,jdbcType=INTEGER},
      is_paid = #{isPaid,jdbcType=INTEGER},
      is_auto_leave = #{isAutoLeave,jdbcType=INTEGER},
      leave_buy = #{leaveBuy,jdbcType=INTEGER},
      to_order_num = #{toOrderNum,jdbcType=VARCHAR},
      total_fee = #{totalFee,jdbcType=DECIMAL},
      total_months = #{totalMonths,jdbcType=INTEGER},
      credit_info = #{creditInfo,jdbcType=VARCHAR},
      order_from = #{orderFrom,jdbcType=INTEGER},
      begin_at = #{beginAt,jdbcType=TIMESTAMP},
      leave_at = #{leaveAt,jdbcType=TIMESTAMP},
      expired_at = #{expiredAt,jdbcType=TIMESTAMP},
      create_at = #{createAt,jdbcType=TIMESTAMP}
    where rent_id = #{rentId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>

  <select id="queryRecordBaseInfoByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from enyan_rent
    where rent_id = #{rentId,jdbcType=BIGINT}
  </select>

</mapper>