<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanCommentMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanComment">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="sex" jdbcType="INTEGER" property="sex" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="star" jdbcType="VARCHAR" property="star" />
    <result column="comment_count" jdbcType="INTEGER" property="commentCount" />
    <result column="like_count" jdbcType="INTEGER" property="likeCount" />
    <result column="can_show" jdbcType="INTEGER" property="canShow" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, email, nick_name, sex, book_id, title, content, star, comment_count, like_count, 
    can_show, parent_id, create_at, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanCommentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_comment
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_comment
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanCommentExample">
    delete from enyan_comment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanComment">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_comment (email, nick_name, sex, 
      book_id, title, content, 
      star, comment_count, like_count, 
      can_show, parent_id, create_at, 
      is_deleted)
    values (#{email,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, #{sex,jdbcType=INTEGER}, 
      #{bookId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{star,jdbcType=VARCHAR}, #{commentCount,jdbcType=INTEGER}, #{likeCount,jdbcType=INTEGER}, 
      #{canShow,jdbcType=INTEGER}, #{parentId,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanComment">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="email != null">
        email,
      </if>
      <if test="nickName != null">
        nick_name,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="star != null">
        star,
      </if>
      <if test="commentCount != null">
        comment_count,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="canShow != null">
        can_show,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=INTEGER},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="star != null">
        #{star,jdbcType=VARCHAR},
      </if>
      <if test="commentCount != null">
        #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="canShow != null">
        #{canShow,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanCommentExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_comment
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_comment
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_comment
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.nickName != null">
        nick_name = #{record.nickName,jdbcType=VARCHAR},
      </if>
      <if test="record.sex != null">
        sex = #{record.sex,jdbcType=INTEGER},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.star != null">
        star = #{record.star,jdbcType=VARCHAR},
      </if>
      <if test="record.commentCount != null">
        comment_count = #{record.commentCount,jdbcType=INTEGER},
      </if>
      <if test="record.likeCount != null">
        like_count = #{record.likeCount,jdbcType=INTEGER},
      </if>
      <if test="record.canShow != null">
        can_show = #{record.canShow,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_comment
    set data_id = #{record.dataId,jdbcType=BIGINT},
      email = #{record.email,jdbcType=VARCHAR},
      nick_name = #{record.nickName,jdbcType=VARCHAR},
      sex = #{record.sex,jdbcType=INTEGER},
      book_id = #{record.bookId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      star = #{record.star,jdbcType=VARCHAR},
      comment_count = #{record.commentCount,jdbcType=INTEGER},
      like_count = #{record.likeCount,jdbcType=INTEGER},
      can_show = #{record.canShow,jdbcType=INTEGER},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanComment">
    update enyan_comment
    <set>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        nick_name = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=INTEGER},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="star != null">
        star = #{star,jdbcType=VARCHAR},
      </if>
      <if test="commentCount != null">
        comment_count = #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="canShow != null">
        can_show = #{canShow,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanComment">
    update enyan_comment
    set email = #{email,jdbcType=VARCHAR},
      nick_name = #{nickName,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=INTEGER},
      book_id = #{bookId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      star = #{star,jdbcType=VARCHAR},
      comment_count = #{commentCount,jdbcType=INTEGER},
      like_count = #{likeCount,jdbcType=INTEGER},
      can_show = #{canShow,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>