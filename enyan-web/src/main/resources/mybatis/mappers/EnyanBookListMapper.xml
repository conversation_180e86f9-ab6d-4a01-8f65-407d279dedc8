<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanBookListMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBookList">
    <id column="set_id" jdbcType="BIGINT" property="setId" />
    <result column="set_name" jdbcType="VARCHAR" property="setName" />
    <result column="set_name_tc" jdbcType="VARCHAR" property="setNameTc" />
    <result column="set_name_en" jdbcType="VARCHAR" property="setNameEn" />
    <result column="banner_url" jdbcType="VARCHAR" property="bannerUrl" />
    <result column="set_abstract" jdbcType="VARCHAR" property="setAbstract" />
    <result column="book_web" jdbcType="VARCHAR" property="bookWeb" />
    <result column="book_id_text" jdbcType="VARCHAR" property="bookIdText" />
    <result column="discount_value" jdbcType="INTEGER" property="discountValue" />
    <result column="is_discount_valid" jdbcType="INTEGER" property="isDiscountValid" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="price_discount" jdbcType="DECIMAL" property="priceDiscount" />
    <result column="is_index" jdbcType="INTEGER" property="isIndex" />
    <result column="show_order" jdbcType="INTEGER" property="showOrder" />
    <result column="book_cover" jdbcType="VARCHAR" property="bookCover" />
    <result column="is_valid" jdbcType="INTEGER" property="isValid" />
    <result column="is_show" jdbcType="INTEGER" property="isShow" />
    <result column="can_all_buy" jdbcType="INTEGER" property="canAllBuy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    set_id, set_name, set_name_tc, set_name_en, banner_url, set_abstract, book_web, book_id_text, 
    discount_value, is_discount_valid, price, price_discount, is_index, show_order, book_cover, 
    is_valid, is_show, can_all_buy
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanBookListExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_book_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_book_list
    where set_id = #{setId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_book_list
    where set_id = #{setId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanBookListExample">
    delete from enyan_book_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanBookList">
    <selectKey keyProperty="setId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book_list (set_name, set_name_tc, set_name_en, 
      banner_url, set_abstract, book_web, 
      book_id_text, discount_value, is_discount_valid, 
      price, price_discount, is_index, 
      show_order, book_cover, is_valid, 
      is_show, can_all_buy)
    values (#{setName,jdbcType=VARCHAR}, #{setNameTc,jdbcType=VARCHAR}, #{setNameEn,jdbcType=VARCHAR}, 
      #{bannerUrl,jdbcType=VARCHAR}, #{setAbstract,jdbcType=VARCHAR}, #{bookWeb,jdbcType=VARCHAR}, 
      #{bookIdText,jdbcType=VARCHAR}, #{discountValue,jdbcType=INTEGER}, #{isDiscountValid,jdbcType=INTEGER}, 
      #{price,jdbcType=DECIMAL}, #{priceDiscount,jdbcType=DECIMAL}, #{isIndex,jdbcType=INTEGER}, 
      #{showOrder,jdbcType=INTEGER}, #{bookCover,jdbcType=VARCHAR}, #{isValid,jdbcType=INTEGER}, 
      #{isShow,jdbcType=INTEGER}, #{canAllBuy,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanBookList">
    <selectKey keyProperty="setId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="setName != null">
        set_name,
      </if>
      <if test="setNameTc != null">
        set_name_tc,
      </if>
      <if test="setNameEn != null">
        set_name_en,
      </if>
      <if test="bannerUrl != null">
        banner_url,
      </if>
      <if test="setAbstract != null">
        set_abstract,
      </if>
      <if test="bookWeb != null">
        book_web,
      </if>
      <if test="bookIdText != null">
        book_id_text,
      </if>
      <if test="discountValue != null">
        discount_value,
      </if>
      <if test="isDiscountValid != null">
        is_discount_valid,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="priceDiscount != null">
        price_discount,
      </if>
      <if test="isIndex != null">
        is_index,
      </if>
      <if test="showOrder != null">
        show_order,
      </if>
      <if test="bookCover != null">
        book_cover,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="isShow != null">
        is_show,
      </if>
      <if test="canAllBuy != null">
        can_all_buy,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="setName != null">
        #{setName,jdbcType=VARCHAR},
      </if>
      <if test="setNameTc != null">
        #{setNameTc,jdbcType=VARCHAR},
      </if>
      <if test="setNameEn != null">
        #{setNameEn,jdbcType=VARCHAR},
      </if>
      <if test="bannerUrl != null">
        #{bannerUrl,jdbcType=VARCHAR},
      </if>
      <if test="setAbstract != null">
        #{setAbstract,jdbcType=VARCHAR},
      </if>
      <if test="bookWeb != null">
        #{bookWeb,jdbcType=VARCHAR},
      </if>
      <if test="bookIdText != null">
        #{bookIdText,jdbcType=VARCHAR},
      </if>
      <if test="discountValue != null">
        #{discountValue,jdbcType=INTEGER},
      </if>
      <if test="isDiscountValid != null">
        #{isDiscountValid,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceDiscount != null">
        #{priceDiscount,jdbcType=DECIMAL},
      </if>
      <if test="isIndex != null">
        #{isIndex,jdbcType=INTEGER},
      </if>
      <if test="showOrder != null">
        #{showOrder,jdbcType=INTEGER},
      </if>
      <if test="bookCover != null">
        #{bookCover,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=INTEGER},
      </if>
      <if test="isShow != null">
        #{isShow,jdbcType=INTEGER},
      </if>
      <if test="canAllBuy != null">
        #{canAllBuy,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanBookListExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_book_list
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_book_list
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_book_list
    <set>
      <if test="record.setId != null">
        set_id = #{record.setId,jdbcType=BIGINT},
      </if>
      <if test="record.setName != null">
        set_name = #{record.setName,jdbcType=VARCHAR},
      </if>
      <if test="record.setNameTc != null">
        set_name_tc = #{record.setNameTc,jdbcType=VARCHAR},
      </if>
      <if test="record.setNameEn != null">
        set_name_en = #{record.setNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.bannerUrl != null">
        banner_url = #{record.bannerUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.setAbstract != null">
        set_abstract = #{record.setAbstract,jdbcType=VARCHAR},
      </if>
      <if test="record.bookWeb != null">
        book_web = #{record.bookWeb,jdbcType=VARCHAR},
      </if>
      <if test="record.bookIdText != null">
        book_id_text = #{record.bookIdText,jdbcType=VARCHAR},
      </if>
      <if test="record.discountValue != null">
        discount_value = #{record.discountValue,jdbcType=INTEGER},
      </if>
      <if test="record.isDiscountValid != null">
        is_discount_valid = #{record.isDiscountValid,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.priceDiscount != null">
        price_discount = #{record.priceDiscount,jdbcType=DECIMAL},
      </if>
      <if test="record.isIndex != null">
        is_index = #{record.isIndex,jdbcType=INTEGER},
      </if>
      <if test="record.showOrder != null">
        show_order = #{record.showOrder,jdbcType=INTEGER},
      </if>
      <if test="record.bookCover != null">
        book_cover = #{record.bookCover,jdbcType=VARCHAR},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=INTEGER},
      </if>
      <if test="record.isShow != null">
        is_show = #{record.isShow,jdbcType=INTEGER},
      </if>
      <if test="record.canAllBuy != null">
        can_all_buy = #{record.canAllBuy,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_book_list
    set set_id = #{record.setId,jdbcType=BIGINT},
      set_name = #{record.setName,jdbcType=VARCHAR},
      set_name_tc = #{record.setNameTc,jdbcType=VARCHAR},
      set_name_en = #{record.setNameEn,jdbcType=VARCHAR},
      banner_url = #{record.bannerUrl,jdbcType=VARCHAR},
      set_abstract = #{record.setAbstract,jdbcType=VARCHAR},
      book_web = #{record.bookWeb,jdbcType=VARCHAR},
      book_id_text = #{record.bookIdText,jdbcType=VARCHAR},
      discount_value = #{record.discountValue,jdbcType=INTEGER},
      is_discount_valid = #{record.isDiscountValid,jdbcType=INTEGER},
      price = #{record.price,jdbcType=DECIMAL},
      price_discount = #{record.priceDiscount,jdbcType=DECIMAL},
      is_index = #{record.isIndex,jdbcType=INTEGER},
      show_order = #{record.showOrder,jdbcType=INTEGER},
      book_cover = #{record.bookCover,jdbcType=VARCHAR},
      is_valid = #{record.isValid,jdbcType=INTEGER},
      is_show = #{record.isShow,jdbcType=INTEGER},
      can_all_buy = #{record.canAllBuy,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanBookList">
    update enyan_book_list
    <set>
      <if test="setName != null">
        set_name = #{setName,jdbcType=VARCHAR},
      </if>
      <if test="setNameTc != null">
        set_name_tc = #{setNameTc,jdbcType=VARCHAR},
      </if>
      <if test="setNameEn != null">
        set_name_en = #{setNameEn,jdbcType=VARCHAR},
      </if>
      <if test="bannerUrl != null">
        banner_url = #{bannerUrl,jdbcType=VARCHAR},
      </if>
      <if test="setAbstract != null">
        set_abstract = #{setAbstract,jdbcType=VARCHAR},
      </if>
      <if test="bookWeb != null">
        book_web = #{bookWeb,jdbcType=VARCHAR},
      </if>
      <if test="bookIdText != null">
        book_id_text = #{bookIdText,jdbcType=VARCHAR},
      </if>
      <if test="discountValue != null">
        discount_value = #{discountValue,jdbcType=INTEGER},
      </if>
      <if test="isDiscountValid != null">
        is_discount_valid = #{isDiscountValid,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceDiscount != null">
        price_discount = #{priceDiscount,jdbcType=DECIMAL},
      </if>
      <if test="isIndex != null">
        is_index = #{isIndex,jdbcType=INTEGER},
      </if>
      <if test="showOrder != null">
        show_order = #{showOrder,jdbcType=INTEGER},
      </if>
      <if test="bookCover != null">
        book_cover = #{bookCover,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=INTEGER},
      </if>
      <if test="isShow != null">
        is_show = #{isShow,jdbcType=INTEGER},
      </if>
      <if test="canAllBuy != null">
        can_all_buy = #{canAllBuy,jdbcType=INTEGER},
      </if>
    </set>
    where set_id = #{setId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanBookList">
    update enyan_book_list
    set set_name = #{setName,jdbcType=VARCHAR},
      set_name_tc = #{setNameTc,jdbcType=VARCHAR},
      set_name_en = #{setNameEn,jdbcType=VARCHAR},
      banner_url = #{bannerUrl,jdbcType=VARCHAR},
      set_abstract = #{setAbstract,jdbcType=VARCHAR},
      book_web = #{bookWeb,jdbcType=VARCHAR},
      book_id_text = #{bookIdText,jdbcType=VARCHAR},
      discount_value = #{discountValue,jdbcType=INTEGER},
      is_discount_valid = #{isDiscountValid,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      price_discount = #{priceDiscount,jdbcType=DECIMAL},
      is_index = #{isIndex,jdbcType=INTEGER},
      show_order = #{showOrder,jdbcType=INTEGER},
      book_cover = #{bookCover,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=INTEGER},
      is_show = #{isShow,jdbcType=INTEGER},
      can_all_buy = #{canAllBuy,jdbcType=INTEGER}
    where set_id = #{setId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>