<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanBalanceHistoryMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBalanceHistory">
    <id column="balance_history_id" jdbcType="BIGINT" property="balanceHistoryId" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="income_vendor_total" jdbcType="DECIMAL" property="incomeVendorTotal" />
    <result column="balance_day" jdbcType="INTEGER" property="balanceDay" />
    <result column="balance_at" jdbcType="TIMESTAMP" property="balanceAt" />
    <result column="is_counted" jdbcType="TINYINT" property="isCounted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanBalanceHistory">
    <result column="balance_detail" jdbcType="LONGVARCHAR" property="balanceDetail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    balance_history_id, publisher_id, income_vendor_total, balance_day, balance_at, is_counted
  </sql>
  <sql id="Blob_Column_List">
    balance_detail
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanBalanceHistoryExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_balance_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanBalanceHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_balance_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_balance_history
    where balance_history_id = #{balanceHistoryId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_balance_history
    where balance_history_id = #{balanceHistoryId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanBalanceHistoryExample">
    delete from enyan_balance_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanBalanceHistory">
    <selectKey keyProperty="balanceHistoryId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_balance_history (publisher_id, income_vendor_total, balance_day, 
      balance_at, is_counted, balance_detail
      )
    values (#{publisherId,jdbcType=BIGINT}, #{incomeVendorTotal,jdbcType=DECIMAL}, #{balanceDay,jdbcType=INTEGER}, 
      #{balanceAt,jdbcType=TIMESTAMP}, #{isCounted,jdbcType=TINYINT}, #{balanceDetail,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanBalanceHistory">
    <selectKey keyProperty="balanceHistoryId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_balance_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="publisherId != null">
        publisher_id,
      </if>
      <if test="incomeVendorTotal != null">
        income_vendor_total,
      </if>
      <if test="balanceDay != null">
        balance_day,
      </if>
      <if test="balanceAt != null">
        balance_at,
      </if>
      <if test="isCounted != null">
        is_counted,
      </if>
      <if test="balanceDetail != null">
        balance_detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="publisherId != null">
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="incomeVendorTotal != null">
        #{incomeVendorTotal,jdbcType=DECIMAL},
      </if>
      <if test="balanceDay != null">
        #{balanceDay,jdbcType=INTEGER},
      </if>
      <if test="balanceAt != null">
        #{balanceAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isCounted != null">
        #{isCounted,jdbcType=TINYINT},
      </if>
      <if test="balanceDetail != null">
        #{balanceDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanBalanceHistoryExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_balance_history
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_balance_history
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_balance_history
    <set>
      <if test="record.balanceHistoryId != null">
        balance_history_id = #{record.balanceHistoryId,jdbcType=BIGINT},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.incomeVendorTotal != null">
        income_vendor_total = #{record.incomeVendorTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.balanceDay != null">
        balance_day = #{record.balanceDay,jdbcType=INTEGER},
      </if>
      <if test="record.balanceAt != null">
        balance_at = #{record.balanceAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isCounted != null">
        is_counted = #{record.isCounted,jdbcType=TINYINT},
      </if>
      <if test="record.balanceDetail != null">
        balance_detail = #{record.balanceDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_balance_history
    set balance_history_id = #{record.balanceHistoryId,jdbcType=BIGINT},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      income_vendor_total = #{record.incomeVendorTotal,jdbcType=DECIMAL},
      balance_day = #{record.balanceDay,jdbcType=INTEGER},
      balance_at = #{record.balanceAt,jdbcType=TIMESTAMP},
      is_counted = #{record.isCounted,jdbcType=TINYINT},
      balance_detail = #{record.balanceDetail,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_balance_history
    set balance_history_id = #{record.balanceHistoryId,jdbcType=BIGINT},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      income_vendor_total = #{record.incomeVendorTotal,jdbcType=DECIMAL},
      balance_day = #{record.balanceDay,jdbcType=INTEGER},
      balance_at = #{record.balanceAt,jdbcType=TIMESTAMP},
      is_counted = #{record.isCounted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanBalanceHistory">
    update enyan_balance_history
    <set>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="incomeVendorTotal != null">
        income_vendor_total = #{incomeVendorTotal,jdbcType=DECIMAL},
      </if>
      <if test="balanceDay != null">
        balance_day = #{balanceDay,jdbcType=INTEGER},
      </if>
      <if test="balanceAt != null">
        balance_at = #{balanceAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isCounted != null">
        is_counted = #{isCounted,jdbcType=TINYINT},
      </if>
      <if test="balanceDetail != null">
        balance_detail = #{balanceDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where balance_history_id = #{balanceHistoryId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanBalanceHistory">
    update enyan_balance_history
    set publisher_id = #{publisherId,jdbcType=BIGINT},
      income_vendor_total = #{incomeVendorTotal,jdbcType=DECIMAL},
      balance_day = #{balanceDay,jdbcType=INTEGER},
      balance_at = #{balanceAt,jdbcType=TIMESTAMP},
      is_counted = #{isCounted,jdbcType=TINYINT},
      balance_detail = #{balanceDetail,jdbcType=LONGVARCHAR}
    where balance_history_id = #{balanceHistoryId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanBalanceHistory">
    update enyan_balance_history
    set publisher_id = #{publisherId,jdbcType=BIGINT},
      income_vendor_total = #{incomeVendorTotal,jdbcType=DECIMAL},
      balance_day = #{balanceDay,jdbcType=INTEGER},
      balance_at = #{balanceAt,jdbcType=TIMESTAMP},
      is_counted = #{isCounted,jdbcType=TINYINT}
    where balance_history_id = #{balanceHistoryId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>