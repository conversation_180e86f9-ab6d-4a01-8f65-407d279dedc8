<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EmailHistoryMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EmailHistory">
    <id column="email_id" jdbcType="BIGINT" property="emailId" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="email_type" jdbcType="INTEGER" property="emailType" />
    <result column="repeat_times" jdbcType="INTEGER" property="repeatTimes" />
    <result column="send_at" jdbcType="BIGINT" property="sendAt" />
    <result column="year_month_day" jdbcType="INTEGER" property="yearMonthDay" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    email_id, email, email_type, repeat_times, send_at, year_month_day
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EmailHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from email_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from email_history
    where email_id = #{emailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from email_history
    where email_id = #{emailId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EmailHistoryExample">
    delete from email_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EmailHistory">
    <selectKey keyProperty="emailId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into email_history (email, email_type, repeat_times, 
      send_at, year_month_day)
    values (#{email,jdbcType=VARCHAR}, #{emailType,jdbcType=INTEGER}, #{repeatTimes,jdbcType=INTEGER}, 
      #{sendAt,jdbcType=BIGINT}, #{yearMonthDay,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EmailHistory">
    <selectKey keyProperty="emailId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into email_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="email != null">
        email,
      </if>
      <if test="emailType != null">
        email_type,
      </if>
      <if test="repeatTimes != null">
        repeat_times,
      </if>
      <if test="sendAt != null">
        send_at,
      </if>
      <if test="yearMonthDay != null">
        year_month_day,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="emailType != null">
        #{emailType,jdbcType=INTEGER},
      </if>
      <if test="repeatTimes != null">
        #{repeatTimes,jdbcType=INTEGER},
      </if>
      <if test="sendAt != null">
        #{sendAt,jdbcType=BIGINT},
      </if>
      <if test="yearMonthDay != null">
        #{yearMonthDay,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EmailHistoryExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from email_history
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          email_history
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update email_history
    <set>
      <if test="record.emailId != null">
        email_id = #{record.emailId,jdbcType=BIGINT},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.emailType != null">
        email_type = #{record.emailType,jdbcType=INTEGER},
      </if>
      <if test="record.repeatTimes != null">
        repeat_times = #{record.repeatTimes,jdbcType=INTEGER},
      </if>
      <if test="record.sendAt != null">
        send_at = #{record.sendAt,jdbcType=BIGINT},
      </if>
      <if test="record.yearMonthDay != null">
        year_month_day = #{record.yearMonthDay,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update email_history
    set email_id = #{record.emailId,jdbcType=BIGINT},
      email = #{record.email,jdbcType=VARCHAR},
      email_type = #{record.emailType,jdbcType=INTEGER},
      repeat_times = #{record.repeatTimes,jdbcType=INTEGER},
      send_at = #{record.sendAt,jdbcType=BIGINT},
      year_month_day = #{record.yearMonthDay,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EmailHistory">
    update email_history
    <set>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="emailType != null">
        email_type = #{emailType,jdbcType=INTEGER},
      </if>
      <if test="repeatTimes != null">
        repeat_times = #{repeatTimes,jdbcType=INTEGER},
      </if>
      <if test="sendAt != null">
        send_at = #{sendAt,jdbcType=BIGINT},
      </if>
      <if test="yearMonthDay != null">
        year_month_day = #{yearMonthDay,jdbcType=INTEGER},
      </if>
    </set>
    where email_id = #{emailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EmailHistory">
    update email_history
    set email = #{email,jdbcType=VARCHAR},
      email_type = #{emailType,jdbcType=INTEGER},
      repeat_times = #{repeatTimes,jdbcType=INTEGER},
      send_at = #{sendAt,jdbcType=BIGINT},
      year_month_day = #{yearMonthDay,jdbcType=INTEGER}
    where email_id = #{emailId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>