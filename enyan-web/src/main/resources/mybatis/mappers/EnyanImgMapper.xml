<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanImgMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanImg">
    <id column="img_id" jdbcType="BIGINT" property="imgId" />
    <result column="img_description" jdbcType="VARCHAR" property="imgDescription" />
    <result column="img_name" jdbcType="VARCHAR" property="imgName" />
    <result column="image_type" jdbcType="INTEGER" property="imageType" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    img_id, img_description, img_name, image_type, book_id
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanImgExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_img
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_img
    where img_id = #{imgId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_img
    where img_id = #{imgId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanImgExample">
    delete from enyan_img
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanImg">
    <selectKey keyProperty="imgId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_img (img_description, img_name, image_type, 
      book_id)
    values (#{imgDescription,jdbcType=VARCHAR}, #{imgName,jdbcType=VARCHAR}, #{imageType,jdbcType=INTEGER}, 
      #{bookId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanImg">
    <selectKey keyProperty="imgId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_img
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imgDescription != null">
        img_description,
      </if>
      <if test="imgName != null">
        img_name,
      </if>
      <if test="imageType != null">
        image_type,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imgDescription != null">
        #{imgDescription,jdbcType=VARCHAR},
      </if>
      <if test="imgName != null">
        #{imgName,jdbcType=VARCHAR},
      </if>
      <if test="imageType != null">
        #{imageType,jdbcType=INTEGER},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanImgExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_img
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_img
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_img
    <set>
      <if test="record.imgId != null">
        img_id = #{record.imgId,jdbcType=BIGINT},
      </if>
      <if test="record.imgDescription != null">
        img_description = #{record.imgDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.imgName != null">
        img_name = #{record.imgName,jdbcType=VARCHAR},
      </if>
      <if test="record.imageType != null">
        image_type = #{record.imageType,jdbcType=INTEGER},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_img
    set img_id = #{record.imgId,jdbcType=BIGINT},
      img_description = #{record.imgDescription,jdbcType=VARCHAR},
      img_name = #{record.imgName,jdbcType=VARCHAR},
      image_type = #{record.imageType,jdbcType=INTEGER},
      book_id = #{record.bookId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanImg">
    update enyan_img
    <set>
      <if test="imgDescription != null">
        img_description = #{imgDescription,jdbcType=VARCHAR},
      </if>
      <if test="imgName != null">
        img_name = #{imgName,jdbcType=VARCHAR},
      </if>
      <if test="imageType != null">
        image_type = #{imageType,jdbcType=INTEGER},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
    </set>
    where img_id = #{imgId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanImg">
    update enyan_img
    set img_description = #{imgDescription,jdbcType=VARCHAR},
      img_name = #{imgName,jdbcType=VARCHAR},
      image_type = #{imageType,jdbcType=INTEGER},
      book_id = #{bookId,jdbcType=BIGINT}
    where img_id = #{imgId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>