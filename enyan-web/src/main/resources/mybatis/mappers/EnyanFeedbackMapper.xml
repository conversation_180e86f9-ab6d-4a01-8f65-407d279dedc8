<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanFeedbackMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanFeedback">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="feed_email" jdbcType="VARCHAR" property="feedEmail" />
    <result column="device_from" jdbcType="VARCHAR" property="deviceFrom" />
    <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="done_type" jdbcType="INTEGER" property="doneType" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, type, feed_email, device_from, device_version, content, done_type, create_at, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanFeedbackExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_feedback
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_feedback
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_feedback
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanFeedbackExample">
    delete from enyan_feedback
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanFeedback">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_feedback (type, feed_email, device_from, 
      device_version, content, done_type, 
      create_at, is_deleted)
    values (#{type,jdbcType=INTEGER}, #{feedEmail,jdbcType=VARCHAR}, #{deviceFrom,jdbcType=VARCHAR}, 
      #{deviceVersion,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{doneType,jdbcType=INTEGER}, 
      #{createAt,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanFeedback">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_feedback
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="feedEmail != null">
        feed_email,
      </if>
      <if test="deviceFrom != null">
        device_from,
      </if>
      <if test="deviceVersion != null">
        device_version,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="doneType != null">
        done_type,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="feedEmail != null">
        #{feedEmail,jdbcType=VARCHAR},
      </if>
      <if test="deviceFrom != null">
        #{deviceFrom,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="doneType != null">
        #{doneType,jdbcType=INTEGER},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanFeedbackExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_feedback
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_feedback
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_feedback
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.feedEmail != null">
        feed_email = #{record.feedEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceFrom != null">
        device_from = #{record.deviceFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceVersion != null">
        device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.doneType != null">
        done_type = #{record.doneType,jdbcType=INTEGER},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_feedback
    set data_id = #{record.dataId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      feed_email = #{record.feedEmail,jdbcType=VARCHAR},
      device_from = #{record.deviceFrom,jdbcType=VARCHAR},
      device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      done_type = #{record.doneType,jdbcType=INTEGER},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanFeedback">
    update enyan_feedback
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="feedEmail != null">
        feed_email = #{feedEmail,jdbcType=VARCHAR},
      </if>
      <if test="deviceFrom != null">
        device_from = #{deviceFrom,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        device_version = #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="doneType != null">
        done_type = #{doneType,jdbcType=INTEGER},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanFeedback">
    update enyan_feedback
    set type = #{type,jdbcType=INTEGER},
      feed_email = #{feedEmail,jdbcType=VARCHAR},
      device_from = #{deviceFrom,jdbcType=VARCHAR},
      device_version = #{deviceVersion,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      done_type = #{doneType,jdbcType=INTEGER},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>