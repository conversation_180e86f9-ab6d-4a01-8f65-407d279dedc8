<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.PodEpisodeMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.PodEpisode">
    <id column="episode_id" property="episodeId" jdbcType="BIGINT" />
    <result column="podcast_id" property="podcastId" jdbcType="BIGINT" />
    <result column="topic_id" property="topicId" jdbcType="BIGINT" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="LONGVARCHAR" />
    <result column="audio_file_url" property="audioFileUrl" jdbcType="VARCHAR" />
    <result column="duration_seconds" property="durationSeconds" jdbcType="INTEGER" />
    <result column="is_published" property="isPublished" jdbcType="INTEGER" />
    <result column="publication_date" property="publicationDate" jdbcType="TIMESTAMP" />
    <result column="episode_number" property="episodeNumber" jdbcType="INTEGER" />
    <result column="episode_count" property="episodeCount" jdbcType="INTEGER" />
    <result column="listen_count" property="listenCount" jdbcType="INTEGER" />
    <result column="like_count" property="likeCount" jdbcType="INTEGER" />
    <result column="is_deleted" property="isDeleted" jdbcType="INTEGER" />
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP" />
    <result column="cover_image_url" property="coverImageUrl" jdbcType="VARCHAR" />
    <result column="cover_image_url2" property="coverImageUrl2" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List">
    episode_id, podcast_id, topic_id, title, description, audio_file_url,
    duration_seconds, is_published, publication_date, episode_number,
    episode_count, listen_count, like_count, is_deleted, created_at, cover_image_url, cover_image_url2
  </sql>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <select id="selectByExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pod_episode
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from pod_episode
    where episode_id = #{episodeId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pod_episode
    where episode_id = #{episodeId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByExample" parameterType="com.aaron.spring.model.PodEpisodeExample">
    delete from pod_episode
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>

  <insert id="insert" parameterType="com.aaron.spring.model.PodEpisode">
    insert into pod_episode (episode_id, podcast_id, topic_id, 
      title, description, audio_file_url, 
      duration_seconds, is_published, publication_date, 
      episode_number, episode_count, listen_count, 
      like_count, is_deleted, created_at, cover_image_url, cover_image_url2)
    values (#{episodeId,jdbcType=BIGINT}, #{podcastId,jdbcType=BIGINT}, #{topicId,jdbcType=BIGINT}, 
      #{title,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}, #{audioFileUrl,jdbcType=VARCHAR}, 
      #{durationSeconds,jdbcType=INTEGER}, #{isPublished,jdbcType=INTEGER}, #{publicationDate,jdbcType=TIMESTAMP}, 
      #{episodeNumber,jdbcType=INTEGER}, #{episodeCount,jdbcType=INTEGER}, #{listenCount,jdbcType=INTEGER}, 
      #{likeCount,jdbcType=INTEGER}, #{isDeleted,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{coverImageUrl,jdbcType=VARCHAR}, #{coverImageUrl2,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.aaron.spring.model.PodEpisode">
    insert into pod_episode
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="episodeId != null" >
        episode_id,
      </if>
      <if test="podcastId != null" >
        podcast_id,
      </if>
      <if test="topicId != null" >
        topic_id,
      </if>
      <if test="title != null" >
        title,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="audioFileUrl != null" >
        audio_file_url,
      </if>
      <if test="durationSeconds != null" >
        duration_seconds,
      </if>
      <if test="isPublished != null" >
        is_published,
      </if>
      <if test="publicationDate != null" >
        publication_date,
      </if>
      <if test="episodeNumber != null" >
        episode_number,
      </if>
      <if test="episodeCount != null" >
        episode_count,
      </if>
      <if test="listenCount != null" >
        listen_count,
      </if>
      <if test="likeCount != null" >
        like_count,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createdAt != null" >
        created_at,
      </if>
      <if test="coverImageUrl != null" >
        cover_image_url,
      </if>
      <if test="coverImageUrl2 != null" >
        cover_image_url2,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="episodeId != null" >
        #{episodeId,jdbcType=BIGINT},
      </if>
      <if test="podcastId != null" >
        #{podcastId,jdbcType=BIGINT},
      </if>
      <if test="topicId != null" >
        #{topicId,jdbcType=BIGINT},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="audioFileUrl != null" >
        #{audioFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="durationSeconds != null" >
        #{durationSeconds,jdbcType=INTEGER},
      </if>
      <if test="isPublished != null" >
        #{isPublished,jdbcType=INTEGER},
      </if>
      <if test="publicationDate != null" >
        #{publicationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="episodeNumber != null" >
        #{episodeNumber,jdbcType=INTEGER},
      </if>
      <if test="episodeCount != null" >
        #{episodeCount,jdbcType=INTEGER},
      </if>
      <if test="listenCount != null" >
        #{listenCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null" >
        #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null" >
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="coverImageUrl != null" >
        #{coverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="coverImageUrl2 != null" >
        #{coverImageUrl2,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="countByExample" parameterType="com.aaron.spring.model.PodEpisodeExample" resultType="java.lang.Long">
    select count(*) from pod_episode
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    update pod_episode
    <set>
      <if test="record.episodeId != null">
        episode_id = #{record.episodeId,jdbcType=BIGINT},
      </if>
      <if test="record.podcastId != null">
        podcast_id = #{record.podcastId,jdbcType=BIGINT},
      </if>
      <if test="record.topicId != null">
        topic_id = #{record.topicId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.audioFileUrl != null">
        audio_file_url = #{record.audioFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.durationSeconds != null">
        duration_seconds = #{record.durationSeconds,jdbcType=INTEGER},
      </if>
      <if test="record.isPublished != null">
        is_published = #{record.isPublished,jdbcType=INTEGER},
      </if>
      <if test="record.publicationDate != null">
        publication_date = #{record.publicationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.episodeNumber != null">
        episode_number = #{record.episodeNumber,jdbcType=INTEGER},
      </if>
      <if test="record.episodeCount != null">
        episode_count = #{record.episodeCount,jdbcType=INTEGER},
      </if>
      <if test="record.listenCount != null">
        listen_count = #{record.listenCount,jdbcType=INTEGER},
      </if>
      <if test="record.likeCount != null">
        like_count = #{record.likeCount,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.coverImageUrl != null">
        cover_image_url = #{record.coverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.coverImageUrl2 != null">
        cover_image_url2 = #{record.coverImageUrl2,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByExample" parameterType="map">
    update pod_episode
    set episode_id = #{record.episodeId,jdbcType=BIGINT},
      podcast_id = #{record.podcastId,jdbcType=BIGINT},
      topic_id = #{record.topicId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=LONGVARCHAR},
      audio_file_url = #{record.audioFileUrl,jdbcType=VARCHAR},
      duration_seconds = #{record.durationSeconds,jdbcType=INTEGER},
      is_published = #{record.isPublished,jdbcType=INTEGER},
      publication_date = #{record.publicationDate,jdbcType=TIMESTAMP},
      episode_number = #{record.episodeNumber,jdbcType=INTEGER},
      episode_count = #{record.episodeCount,jdbcType=INTEGER},
      listen_count = #{record.listenCount,jdbcType=INTEGER},
      like_count = #{record.likeCount,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      cover_image_url = #{record.coverImageUrl,jdbcType=VARCHAR},
      cover_image_url2 = #{record.coverImageUrl2,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.PodEpisode">
    update pod_episode
    <set>
      <if test="podcastId != null">
        podcast_id = #{podcastId,jdbcType=BIGINT},
      </if>
      <if test="topicId != null">
        topic_id = #{topicId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="audioFileUrl != null">
        audio_file_url = #{audioFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="durationSeconds != null">
        duration_seconds = #{durationSeconds,jdbcType=INTEGER},
      </if>
      <if test="isPublished != null">
        is_published = #{isPublished,jdbcType=INTEGER},
      </if>
      <if test="publicationDate != null">
        publication_date = #{publicationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="episodeNumber != null">
        episode_number = #{episodeNumber,jdbcType=INTEGER},
      </if>
      <if test="episodeCount != null">
        episode_count = #{episodeCount,jdbcType=INTEGER},
      </if>
      <if test="listenCount != null">
        listen_count = #{listenCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="coverImageUrl != null">
        cover_image_url = #{coverImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="coverImageUrl2 != null">
        cover_image_url2 = #{coverImageUrl2,jdbcType=VARCHAR},
      </if>
    </set>
    where episode_id = #{episodeId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.PodEpisode">
    update pod_episode
    set podcast_id = #{podcastId,jdbcType=BIGINT},
      topic_id = #{topicId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      description = #{description,jdbcType=LONGVARCHAR},
      audio_file_url = #{audioFileUrl,jdbcType=VARCHAR},
      duration_seconds = #{durationSeconds,jdbcType=INTEGER},
      is_published = #{isPublished,jdbcType=INTEGER},
      publication_date = #{publicationDate,jdbcType=TIMESTAMP},
      episode_number = #{episodeNumber,jdbcType=INTEGER},
      episode_count = #{episodeCount,jdbcType=INTEGER},
      listen_count = #{listenCount,jdbcType=INTEGER},
      like_count = #{likeCount,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      cover_image_url = #{coverImageUrl,jdbcType=VARCHAR},
      cover_image_url2 = #{coverImageUrl2,jdbcType=VARCHAR}
    where episode_id = #{episodeId,jdbcType=BIGINT}
  </update>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>
