publisher.bank.title=account name
pay.ing=Payment is being verified...
passwd.forget=Forgot password
publisher.name.sc=Publisher's name in simplified Chinese
publisher.name.tc=Publisher's name in traditional Chinese
publisher.starttime=billing start time
email.verify=Verify your email
book.shelf.up=on the shelf
book.shelf.down=off the shelf
error.404.order=Order does not exist
error.404.book=Books do not exist or is off the shelf
error.404.blog=Blogs do not exist
error.404.reading=Reading clubs do not exist
error.book.shelf.down=The book is off the shelf
menu.info=Personal Information Management
device.release=Unlink the device
device.description={0} of {1}
AbstractUserDetailsAuthenticationProvider.disabled=disabled
account.disabled=This account has not verified email, please click {0} here {1} to verify
AbstractUserDetailsAuthenticationProvider.expired=account expired
AbstractUserDetailsAuthenticationProvider.badCredentials=Incorrect email address or password, please re-enter! 
error.user.inactive=Please activate your email address first!
book.add=Add books
main.title=Inspirata eBooks Management System
book.list=Book Management
menu.web=Website Management
menu.index=Homepage
home=Homepage
book.edit=Modify Books
category.list=Category Management
publisher.list=Publisher Management
publication.list=Encrypted books management
publication.add=Add encrypted books
publication.edit=Modify encrypted books
publication.title=Encrypted book name
discount.list=Discount Management
category.add=Add a Category
category.edit=Modify Category
discount.add=Add Discounts
discount.edit=Modify Discounts
bookSet.list=Set Management
bookSet.add=Add Set
bookSet.edit=Modify Set
bookList.list=List Management
bookList.add=Add List
bookList.edit=Modify List
publisher.add=Add Publisher
publisher.edit=edit=Modify Publisher
config.list=Configuration Management
order.list=Order Management
order.detail=Order Details
img.add=Add Image
img.edit=Modify Image
img.list=Image list
refund.list=Refund Management
refund.add=录入退款信息
refund.add.redeem=录入兑换码退款
banner.add=Banner添加
banner.edit=Banner修改
banner.list=Banner列表
reading.add=活动添加
reading.edit=活动修改
reading.list=活动列表
daily.words.add=金句添加
daily.words.edit=金句修改
daily.words.list=金句列表
daily.words.reset=重置金句
rent.list=先租后买列表
rent.detail=先租后买明细
feedback.add=反馈添加
feedback.edit=反馈修改
feedback.list=反馈列表
comment.add=评论添加
comment.edit=评论修改
comment.list=评论列表
comment.star.count={0} rating(s)
comment.tab.name=Reviews
comment.none=No ratings yet.
comment.go=Go to rating now

action.enable=Enabled
action.enable.not=Not Enabled

data.empty=Data is not found!

book.file.list=Book file list
shop.title=Inspirata eBooks | eBook platform for Chinese Christians
shop.index=Home
shop.indexEditor=Recommendation
shop.index.publisher=Partner Publishers
shop.nav.book.sc=Simplified Chinese
shop.nav.book.tc=Traditional Chinese
shop.nav.book.eng=English
shop.nav.book.free=Free
shop.nav.book.all=All
shop.nav.inspirata=Inspirata
shop.nav.book.presale=Pre-order
shop.nav.special.offer=SALE
shop.nav.reading=读书伙伴
shop.nav.category.by=Categories
shop.nav.language.by=Books by language
shop.nav.reader=Download
shop.nav.blog=BLOG
shop.nav.rent=先租後買
publisher.bank.name=Bank title
publisher.bank.address=Bank Address
publisher.bank.code=Bank Code
publisher.bank.num=Bank Account Number
shop.nav.lang.sc=Simplified Chinese
shop.nav.lang.tc=Traditional Chinese
shop.search.placehold=Search by title, author or publisher
shop.copyright=Inspirata eBooks
shop.cbcs.title=購買全套房角石
shop.cbcs.ot=房角石舊約書卷
shop.cbcs.nt=房角石新約書卷
shop.cbcs.sc=簡體版
shop.cbcs.tc=繁體版
footer.customer=Service
footer.faq=FAQs
footer.note=notes
footer.gratiscopy=Gratis Copy
footer.refund=Refund Policy
footer.publisher=Publishing Partnership
footer.subscription=Email Subscription
footer.info=Information
footer.conditions=Terms of Use
footer.privacy=Privacy Policy
footer.copyright=Copyright Statement
footer.faith=Statement of Faith
footer.disclaimer=Disclaimers
footer.link.book=Inspirata Bookstore
footer.link.read=Inspirata Reading
footer.link.ebook=Inspirata eBooks
footer.link.ezra=Ezra Hall
footer.aboutus=The Inspirata eBooks is a Chinese eBook reading platform developed by Inspirata Publishing (Hong Kong) Limited.  Through partnerships with authors and publishers, we promote the digitization of Chinese Christian resources, making them accessible anytime and anywhere. 
footer.join=Join Us
footer.follow=Follow Us
footer.catalogue=Publications Catalogue
info.login=Sign in
info.reg=Sign up
login.title=Sign in
info.own=Me
info.account=Account Management
info.myorder=My Orders
info.mywishes=My Wishlist
info.myaccount=My Account
info.logout=Sign out
footer.link.friend=Links
footer.about=About Us
book.category=Categories
welcome=Welcome
banner1.1=Carefully designed, self-developed
banner1.2=Introduce Inspirata eBooks App
banner1.3=The e-book reading platform for Chinese Christians
banner1.4=Download App
banner2.1=--Academic Research and Writing--
banner2.2=The essential tool for theological research and monograph writing
banner2.3=The first comprehensive and specific academic guide in Chinese theology
banner2.4=Buy Now
banner3.1=---Read the Bible for Life & The Reader's Guide--
banner3.2=-The Whole Bible Explained + Year-Round Bible Reading Plan
banner3.3=365 days of companionship, so that every Christian will love reading the Bible from now on
banner3.4=Buy Now
banner4.1=Beginning of the year, stock up on spiritual resources for the whole year
banner4.2=New Year's special offer, 60% off everything
banner4.3=January 24~February 8
banner4.4=Buy Now
banner5.1=Trial of "The Reader's Guide"
banner5.2=May Day special, 60% off everything for a limited time!
banner5.3=Sale time: 5.1 ~ 5.5
banner5.4=Get it now!
search.null=Sorry, no results found, try a different keyword!
order.label=Sort by
order.default=Default
order.new=New releases
order.create=New releases
order.read.desc=View count: High to Low ↓
order.sales.desc=Popularity
order.sales.asc=Sales: Low to High ↑
order.sales=Sales Volume
order.price.desc=Price: High to Low ↓
order.price.asc=Price: Low to High ↑
order.price=Price
print=Show
book.author=Author
book.translator=Translator
book.publisher=Publisher
book.set=Series
book.version=Other Versions
book.version.paper=Print version of this book
book.supply.none=Currently unavailable
buy.now=Buy eBook
buy.done=Purchased
buy.presale=Pre-order eBook
cart.add=Add to cart
cart.success=Added to shopping cart successfully!
how.read=To read the book/sample, you need to download the App. DOWNLOAD &nbsp;&gt;&gt;
buy.paperbook=Purchase paper book
book.description=INTRODUCTION
book.catalogue=Contents
book.info=Information
book.lang=Language
book.format=Ebook format
book.format.1=Reflowable ePub
book.format.2=PDF
book.format.3=Fixed Layout ePub
book.presale=Pre-order
book.title.presale=[Pre-order Sale]
book.currency.label=Multiple currency options available, you can switch the reference currency in the navigation bar.
book.status=Status
book.tts=Text-to-Speech
book.pagination=Paper book page number view
search.label=Search
book.part=Online Preview
login.now=Sign in
reg.title=Sign up
login.error=Username or password is incorrect.
login.stay=Remember me
reg.now=Sign up now
user.nickname=Username
user.email=Email address
passwd.input=Enter password
passwd.login.label=Password
passwd.confirm=Confirm password
passwd.label=Password
my.account=My Account
my.device=My Devices
my.order=My Orders
my.wish=My Wishlist
my.redeemCode=Use Redeem Code
my.rent=My Subscription
my.gift.history=My Gift History
cart.label=Shopping Cart
book.title=Book Title
alert.cartDel=Remove this item from your shopping cart?
alert.confirm=Confirm
alert.delete=Delete
alert.cancel=Cancel
alert.continue=Continue
buy.back=Continue shopping
account.settle=Checkout
account.total=Total
account.discount=Discount Amount
account.amout=Amount
operation.label=Operation
alert.orderCancel=Cancel this order?
alert.orderDel=Delete this order?
order.date=Order Date
order.num=Order Number
order.amount=Amount
order.haspay=Completed payment
order.look=View details
order.invoice=Download Invoice
book.download=Download the book
book.read=Read in App
book.hasBought=Purchased
account.pay=Pay now
order.del=Delete order
order.cancel=Cancel Order
order.total=Total {0} item(s)
nickname.update=Modify username
nickname.current=Current username
nickname.input=Please enter a new username
update.confirm=Update
operation.update=Modify
account.toPay=Payable
account.hasPay=Paid
book.price=Unit price
book.amount=Quantity
book.total=Subtotal
order.submit=Submit eBook Order
order.confirm=Confirm order
pay.label=Checkout
protocal.user=User agreement
read.had=I am aware of the above reminders and have read and accepted the
read.had.top=Please note:
read.had.top.1=You are purchasing an e-book instead of a paper book. Therefore, the book(s) will not be sent by post, and no return or refund will be allowed after successful purchase.
read.had.top.2=All ebooks are DRM encrypted and can only be read within the Inspirata eBook App.
pay.credit=Pay by Credit Card (UnionPay/Visa/Master)
alipay.label=Alipay
alipay.label.hk=Alipay
pay.alipay=AlipayHK
pay.alipay.hk=Pay by Alipay
pay.store=商家
pay.amount=Total
pay.alipay.msg1=Payment method: Use mobile Alipay to scan the code to pay (the final payment amount is in RMB, the exchange rate will be shown in the App)
pay.alipay.msg2=Payment method: Use AlipayHK to scan the code to pay
pay.alipay.msg3=If the page is not updated after successful payment, please click:
pay.alipay.scan1=扫一扫付款
pay.alipay.scan2=打开手机支付宝<br>扫一扫继续付款
pay.alipay.scan3=打开手机AlipayHK<br>扫一扫继续付款
pay.success=Payment made successfully!
wish.cancel=Cancel
back.label=Return
back.index=Return to home page
back.page=Return to previous page
error.404=Oops! The page you are looking for is like a sheep wandering lost......
credit.label=Credit Card
pay.type=Payment Method
passwd.update=Change password
passwdUpdate.input1=Enter the original password
passwdUpdate.input2=Enter a new password
passwdUpdate.input3=Confirm new password
placehoder.passwd=6-20 characters: English letters, numbers and symbols
error.protocol=Please confirm to read the user agreement of Inspirata eBooks
error.credit=Credit card can’t be empty
error.date=Date can’t not be empty
error.cvc=CVC can't be empty
alert.close=Close
email.send=Send verification email
email.check=Email verification
login.do=Sign in
passwd.get=Forgot password?
passwd.reset=Reset password

alert.devicelimit.del=Are you sure you want to unlink this device? Once unlinked, the App installed on this device will not work properly. You can sign in from the device to link the device again.

error.email.null=Email can’t be empty
error.email.invalid=The email address you entered is incorrect! Please re-enter
error.email.notexist=The email address is not registered! Please sign up first!
error.email.toomuch=Please try again after 10 minutes.
success.email.send=The verification email has been sent to {0}
error.passwd.null=Password can't be empty
error.passwd.format=Password must be composed of 6-20 English letters, numbers or symbols
error.passwd.again= The password entered twice does not match, please re-enter!
error.500=There is an error on the page, we are truly sorry ......
error.update=Modification failed
success.update=Modified Successfully
error.update.timeout=Operation timeout!
error.email.exist=An account is already registered with that email address, please sign in.
error.nickname.null=Username can't be empty!
error.email.activefail=Activation failed!
error.redeemCode=This code is invalid, please re-enter!
error.redeemCode.has=This code is no longer valid!
error.redeemCode.expired=This code has expired.

success.active=Activation succeeded!
success.done=Successful!
success.redeemCode=Redeemed successfully!
success.revoke=Successfully cancelled

error.active.timeout=Activation timeout
error.done.timeout=Operation timeout!
email.active=activate email
error.book.select=Please select e-book
error.count.select=Please select the correct number
error.passwd.oldnull=Original password cannot be empty
error.passwd.old.incorect=The original password is wrong, please re-enter it!
update.nickname=Change username
update.passwd=Change password
error.pay.fail=Payment failed
search.tips= Due to the distinction between Simplified and Traditional Chinese for your search terms, this search result may not be complete. Please use Simplified Chinese to search for Simplified Chinese books and Traditional Chinese to search for Traditional Chinese books.
search.tips.null=Warm Tips: Please use Simplified Chinese to search for Simplified Chinese books, and use Traditional Chinese to search for Traditional Chinese books.
error.nonlogin=Sign in!
balance.account=Balance Account
error.reg.agree=You need to accept the above terms and conditions before you can register.
error.pay.agree=You need to accept the above terms and conditions before you can make a payment.
web.nav=Website navigation
nav.reg.login=Register / Sign in
success.cart.add=is successfully added to shopping cart
success.wishlist.add=is successfully added to wishlist
time.hour=Hour
time.min=Minute
time.second=Second
date.day=Time
date.week.start=Start time
date.week.end=End time
date.month=Month
date.year=Year
order.date.day=Date
order.date.week=Week (the first day of the week)
pay.left.1=Remaining payment time
pay.left.21=The order has been successfully submitted! Please finish payment within
pay.left.22=Otherwise the order will be closed automatically.
error.invalid.order=Order has been cancelled
error.nickname.toolong=Username length should be within 14 characters
back.orders=Go back to My order
placehoder.nickname=Username: Up to 14 characters: Chinese characters, English letters, numbers and symbols
search.by.start=Start Date
search.by.end=End Date
search.by.range=Date Interval
search.select=Search
search.by.order=By order
search.by.book=By book title
book.price.fixed=List Price
book.price.selling=Selling Price
income.vendor=Royalty Amount
income.plat=Platform Revenue
income.total=Sales Revenue
sell.info=Sales Details
sell.analyze=Sales Analysis
sell.statistics=Sales Statistics
error.time.startOrEnd.null=Please set the start or end date
book.quantity=Sales Volume
income.sell=Sales Revenue
income.pay.fee=Service Charge
income.net.sales=Net Sales
income.net.sales.revenue=Net Sales Revenue
income.vendor.percent=Royalty Rate
income.gross.profit=Gross Profit
income.gross.profit.margin=Gross Profit Margin
income.from=From
my.balance.account=My bank account
credit.to=Valid until
book.sc=Simplified Chinese
book.tc=Traditional Chinese
book.eng=English
book.esin=ESIN
book.pub.code=publication code
reg.label=Register
login.label=Sign in
nav.money=Billing Currency
nav.language=Interface language
book.publish.at=Publication Date
sell.count=Sales Statistics
search.by.day=Daily Sales
search.by.week=Weekly Sales
search.by.month=Monthly Sales
search.by.year=Yearly Sales
book.keywords=Book keywords
pay.fail=Payment failed
book.product.web=Product Website
book.word.count=Word Count
book.word.show={0} words
label.rate=Exchange Rate
sell.balance=Billing Management

label.month=month
label.sell.look=View sales details
label.status=status
label.counted.0=Settlement in progress
label.counted.1=Settled
label.counted.-1=Unsettled
label.redeemCode=Redeem code
label.free=Free
label.pay.method=Payment method
label.pay.1=Alipay
label.pay.11=Alipay HK
label.pay.2=Credit card
label.pay.3=Free
label.pay.4=Redeem Code
label.pay.5=Cash
label.pay.21=Credit card (non-Hong Kong)
label.pay.22=Credit card (Hong Kong)
label.explain=Please note
label.and=and
label.conditions=Terms of Use
label.refund=Refund Policy
label.privacy=Privacy Policy
label.cart=add to shopping cart
label.wish=add to wishlist

email.order.title=Inspirata eBooks-Order Details
email.gift.title=Your friend has gifted you an e-book. Come read it!
email.active.title=Thank you for signing up for Inspirata eBooks
email.forget.title=Inspirata eBooks-Retrieve Password

error.balance.select=Select the billing month
book.cost=Ebook production fee
vendor.percentage=Proportion of revenue
vendor.percent=Proportion of revenue
sell.balance.confirm=Billing management confirmation
fee.book.cost=eBook production fee
fee.transfer=transfer fee
button.confirm=confirm
button.back=Return
button.cancel=Cancel
button.submit=Submit
success.balance.confirm=You have successfully submitted the settlement application, we will process the settlement application and complete the settlement between the 1st and the 15th of each month.
button.ok=Confirm
button.remove=Remove
button.apply=Apply
button.input=Fill in
button.select.all=Select all
button.select.has=selected
label.counted.-2=Cancelled
balance.history=Billing history
label.time.create=Submitted
info.look=View details
balance.fee=Amount to be settled
error.balance.less=Settlement amount is less than 0
sidebar.service=Support
error.balance.date.less=It's not the billing time yet: Vendor can apply for the first settlement after three natural months in Inspirata e-book platform
tip.alipay=Alipay payment is only available for mainland China users, if you are not a mainland China user, please use credit card payment, thank you!
tip.currency=*Orders are charged in Hong Kong dollars, and other currencies are for reference only. Non-HKD payments may face exchange rate variations and credit card fees.
menu.balance=Billing Management
label.vendor=Publisher
footer.contact=Contact customer service
search.download=Export
search.by.vendor=By publisher

footer.revoke=Account cancellation

coupon.list = Coupon List
coupon.add = Add Coupon
coupon.edit = Edit Coupon

blog.list = Blog List
blog.add = Add Blog
blog.edit = Edit Blog
blog.like.fail = Fail
blog.like.success = Success
blog.likes = Like(s)

redeemCode.list=List of redeem codes
redeemCode.do=Redeem
redeemCode.input=Please enter redeem code
redeemCode.continue=Redeem Now
redeemCode.stop=Cancel
redeemCode.hasbuy.msg=Note: Redeeming will overwrite book(s) you already own (your highlights/notes will be kept). Duplicate book(s) cannot be gifted or refunded.
redeemCode.todo.msg=The redeem code includes the following {0} ebooks:
redeemCode.hasbuy=Purchased

S01=Please sign in first!
S10=Please sign in first!
S11=Please sign in first!
S12=Please sign in first!

discount.n.has=The following items are discounted: 
discount.n.not=The following products are not eligible for the Multi-piece discount.
discount.header=Multi-piece discount
discount.icon=Multi-piece discount

gift.title=Gifting ebook
gift.buy=Gift
gift.explain.choose=Quantity to gift
gift.explain.1=1. You can gift ebooks to friends by purchasing redeem codes. After successful payment, go to "Me > My Gift History" to send the gifts. You can either copy the redeem codes and share them directly with your friends or enter their email addresses, and they will receive gift notification emails.<br/>
gift.explain.2=2. Each code can be redeemed only once, and will expire after successful redeem. Please redeem it on the website via "Me > Use redeem code".<br/>
gift.explain.3=3. The redeemed ebooks can only be read in the Inspirata eBooks App.<br/>
gift.explain.4=4. Once the redeem code is purchased, it is not refundable.<br/>
gift.look=View My gift history
gift.send.title=Gifting ebook
gift.send.bookname=Book title
gift.send.to.email=Gift recipient's email
gift.send.to.text=Write a message
gift.send.default.text=I would love to give this book to you as a gift. Enjoy it! God bless you.
gift.send.tip=I have confirmed the accuracy of the email address
gift.send.tip.text=Tip: You can either copy the redeem code and share it directly with your friend or enter their email address, and they will receive a gift notification email.
gift.send.code=Redeem code
gift.send.code.status=Redeem status
gift.send.code.status.0=Unredeemed
gift.send.code.status.1=Redeemed
gift.send.code.date=Redeem date
gift.send.code.email=Redeem account
gift.send.to.name=Recipient
gift.order.time=Order time
gift.email.input=Please enter the recipient's email
redeem.total=Total {0} redeem code(s)
error.gift.send=Failed to gift redeem code!
fail.redeemCode=Failed to redeem

goods.count=item(s)

coupon.placeholder=Please enter coupon code
coupon.label=Coupon code
coupon.value.label=Coupon discount
coupon.code.label=Coupon code
coupon.use=Use coupon
success.coupon.msg=Coupon discount: {0}
error.coupon.null=Please enter coupon code
error.coupon.invalid=The coupon does not exist
error.coupon.expired=The coupon is no longer valid
error.coupon.only=The coupon can only be used once
error.coupon.only.times=The coupon can only be used {0} time(s)
error.coupon.out=Sorry, the coupon is out of stock
error.coupon.limit=The coupon can only be used for participating ebook(s) over {0}.

gratiscopy.bg=学术赠书项目说明
gratiscopy.bg.1=近年来，香港及台湾两地以外的神学院经常难以找到教学所需的书籍。教材从两地寄出，邮资既昂贵，邮递时间亦很长。
gratiscopy.bg.2=经过长时间的祷告，恩道出版社决定推出“学术赠书”项目。恩道将翻译并出版高质量的教材，在指定的时间内，以ePub电子书的形式免费赠送神学院的教授和讲师。有兴趣的神学生、教牧弟兄姊妹，亦可以合宜的价格购买。
gratiscopy.top=申请须知
gratiscopy.top.1=本项目旨在帮助神学院老师便捷地获取优质学术及教材资源，申请之前请您了解以下信息：
gratiscopy.top.2=申请条件：申请人须是<strong>神学院的教授或讲师</strong>（全球范围内，区域不限）
gratiscopy.top.3=申请数量：每本书仅可申请一次，且只能为自己申请（同一书名的简体版和繁体版只选其一）
gratiscopy.top.4=申请方式：点击书籍信息栏上的"申请免费电子书"按钮，填写相关信息并提交
gratiscopy.top.5=赠书形式：赠书仅限电子版，若需纸书请前往恩道书房网站购买：book.endao.co
gratiscopy.top.6=购书优惠：恩道出版图书在开放申请赠书阶段，购电子书/纸书单本可享55折，纸书10本以上可享5折；申请赠书截止后，购纸书10本以上可享75折，欢迎选购
gratiscopy.top.7=联系我们：如有任何疑问，欢迎来信咨询：<EMAIL>
gratiscopy.top.8=以下可申请书目将不定期更新，敬请关注

subscription.button=Subscribe
subscription.placeholder=Email
subscription.label=Enter your email address to receive important information such as book summaries, new releases, promotions and more from Inspirata eBooks.
subscription.label2=Gmail/Hotmail/Outlook/Protonmail or other secure email is recommended.
subscription.success=Subscribed successfully! Thank you for your support.

reading.index=读书会首页
reading.nav.0=全部活动
reading.nav.1=发现
reading.nav.2=轻松共读
reading.nav.3=互助共读
reading.nav.4=姊妹沙龙
reading.nav.5=公共沙龙
reading.nav.6=读书点滴
reading.nav.7=主题阅读
reading.nav.8=讲座信息
reading.nav.join=加入读书伙伴
reading.top.1.title=读书会简介
reading.top.1.label=恩道读书会，一个专注于操练敬虔思维、追求属灵智慧的读者团契。我们致力于在阅读中培养以圣经真理为基石的思辨和自省的能力，并挖掘可以应用于生活和服侍的智慧。
reading.top.2.title=交流平台简介
reading.top.2.label=我们使用微软的Teams作为读者之间畅谈的平台。诚邀各位读者多走一里路加入我们的Team，让我们一起自由地思考和讨论、激起思想的火花。
reading.more=了解更多 >
reading.buy=购买书籍 >
reading.recent=近期活动
reading.status.0=已经结束
reading.status.1=火热报名中
reading.status.2=活动即将开始

search.button=Search
search.by.book.all=All books
search.by.book.released=Released
search.by.book.unreleased=Unreleased
search.time.select=Select the time range
search.book.input=Search by book title
search.by.time.desc=Reverse chronological order
search.by.book.name=Sort by book title
search.sort.volume.desc=Sort by sales volume, high to low
search.sort.volume.asc=Sort by sales volume, low to high
search.sort.revenue.desc=Sort by sales revenue, high to low
search.sort.revenue.asc=Sort by sales revenue, low to high
search.input=Please enter
search.label.order=Order
search.label.book=Book title
search.time.lang=en
fee.description=① Royalty Amount = Net Sales Revenue * Royalty Percentage  ② Net Sales Revenue = Sales Revenue - Service Charge <br/> ③ Sales Revenue = Selling Price * Sales Volume.

mail.greetings={0}，您好！
mail.signature=<span>若您有任何疑问，请直接回复此邮件。</span><br />Copyright © 2017-2025 恩道电子书 All Rights Reserved
mail.rent.common=若要管理订阅（续订/退订/优惠购书），请<a href="https://e.bookapp.cc/">登录</a>恩道电子书网站，于“我的 > 我的订阅”页面中操作。
mail.rent0101.current=您的先租后买·{0}·{1}（HK$ {2}/月） 将于北京时间{3}到期。
mail.rent0102.current=您的先租后买·{0}·{1}将以HK$ {2}/月的价格自动续订，直到您退订为止。
mail.rent0102.note=下次自动扣费时间：北京时间{0}
mail.rent0102.todo=您必须在每次自动扣费前至少一天退订，以避免被收费。
mail.rent0103.current=您的先租后买·{0}·{1}将于北京时间{2}到期。
mail.rent0103.note=因您已订阅36个月（最大订阅时长），到期后系统将自动退订，请及时使用购书优惠。
mail.rent0201.current=您的先租后买·{0}·{1}已于{2}到期。
mail.rent0201.todo=您在北京时间 {0} 24时前仍可续订，否则将自动退订。
mail.rent0202.reason=您的先租后买·{0}·{1}（HK$ {2}/月）自动扣费失败。
mail.rent0202.current=当前订阅将于{0}到期。
mail.rent0301.reason=您的先租后买·{0}·{1}（HK$ {2}/月）已自动退订。
mail.rent0301.todo=北京时间 {0} 24时前，您仍可续订（从到期时间开始计费）或使用购书优惠（月费抵扣+全套折扣），逾期未使用则视为放弃。
mail.rent0302.reason=因自动扣费失败，您的先租后买·{0}·{1}已自动退订。
mail.rent0302.todo=北京时间 {0} 24时前，您仍可续订（从到期时间开始计费）或使用购书优惠（月费抵扣+全套折扣），逾期未使用则视为放弃。
mail.rent0401.current=您已成功订阅先租后买·{0}，您可前往 <a href="https://e.bookapp.cc/index-Reader">恩道电子书App</a> 阅读所订阅的电子书。
mail.rent0401.note=订单编号: {0}<br/>订单时间: {1}<br/>订阅方式：{2}<br/>本次订阅时长：{3}个月<br/>到期时间：北京时间{4}<br/>实付：HK${5}
mail.rent0402.current=您已成功订阅先租后买·{0}，您可前往 <a href="https://e.bookapp.cc/index-Reader">恩道电子书App</a> 阅读所订阅的电子书。
mail.rent0402.note=订单编号: {0}<br/>订单时间: {1}<br/>订阅方式：{2}<br/>本次订阅时长：{3}个月<br/>下次自动扣费时间：北京时间{4}<br/>实付：HK${5}
mail.rent0501.current=您已成功续订先租后买·{0}，您可前往 <a href="https://e.bookapp.cc/index-Reader">恩道电子书App</a> 阅读所订阅的电子书。
mail.rent0501.note=订单编号: {0}<br/>订单时间: {1}<br/>订阅方式：{2}<br/>本次订阅时长：{3}个月<br/>到期时间：北京时间{4}<br/>实付：HK${5}
mail.rent0502.current=您已成功续订先租后买·{0}，您可前往 <a href="https://e.bookapp.cc/index-Reader">恩道电子书App</a> 阅读所订阅的电子书。
mail.rent0502.note=订单编号: {0}<br/>订单时间: {1}<br/>订阅方式：{2}<br/>本次订阅时长：{3}个月<br/>下次自动扣费时间：北京时间{4}<br/>实付：HK${5}
mail.rent06.current=您的先租后买·{0}·{1}已于{2}到期。
mail.rent06.note=因您已订阅36个月（最大订阅时长），系统已自动退订。
mail.rent06.todo=您可以在北京时间 {0} 24时 前使用购书优惠（月费抵扣+全套折扣），逾期未使用则视为放弃。
mail.rent0701.current=您的先租后买·{0}·{1}购书优惠（月费抵扣+全套折扣）即将到期。
mail.rent0701.todo=北京时间 {0} 24时 前，您仍可续订（从到期时间开始计费）或使用购书优惠，逾期未使用则视为放弃。
mail.rent0702.current=您的先租后买·{0}·{1}购书优惠（月费抵扣+全套折扣）即将到期。
mail.rent0702.todo=请您在北京时间 {0} 24时 前使用购书优惠，逾期未使用则视为放弃。

header.no=No.
header.email=email
header.date.order=Order Date
header.date.start=Start Date
header.date.end=End Date
header.order.no=Order Number
header.book.title=Book Title
header.price.list=List Price
header.price.sell=Selling Price
header.volume.sales=Sales Volume
header.revenue.sales=Sales Revenue
header.pay.method=Payment Method
header.fee.pay=Service Charge
header.revenue.net=Net Sales Revenue
header.amount.royalty=Royalty Amount
header.publisher=Publisher
header.change.volume=Sales Change
header.change.sales=Sales Change(HKD)
header.refund.reason=Reason for Refund
header.refund.date=Date of Refund

invoice.signature=No signature is required for this computer-generated invoice.
invoice.customer=Customer Name:
invoice.date=Invoice Date:
invoice.email=Email:
invoice.payment=Payment Method:
invoice.orderNum=Order Number
invoice.bookTitle=Book Title
invoice.quantity=Quantity
invoice.subtotal=Subtotal (HK$)
invoice.total=Total: