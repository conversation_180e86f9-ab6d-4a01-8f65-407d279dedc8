<html lang=zh-CN class="svgfilters csscalc cssgradients preserve3d supports svgclippaths svgasimg no-touchevents cssanimations boxsizing csstransforms csstransforms3d csstransitions svg weava-extension-context" data-weava-installed=1><head><style type=text/css>.visibleHidden {
            visibility: hidden !important;
        }
        .visible {
            visibility: visible !important;
        }
        .offcanvas-wrapper {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 85px) !important;
        }
        #app {
            flex: 1;
            width: 100%;
            position: relative;
        }
        #supportChinese {
            display: none;
        }
        #supportEnglish {
            display: none;
        }
        .el-loading-spinner .path {
            stroke: #cc4646 !important;
        }
        .el-loading-spinner .el-loading-text {
            color: #cc4646 !important;
        }
        :root,
        :host {
            --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Solid";
            --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Regular";
            --fa-font-light: normal 300 1em/1 "Font Awesome 6 Light";
            --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Thin";
            --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";
            --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";
        }

        svg:not(:root).svg-inline--fa,
        svg:not(:host).svg-inline--fa {
            overflow: visible;
            box-sizing: content-box;
        }

        .svg-inline--fa {
            display: var(--fa-display, inline-block);
            height: 1em;
            overflow: visible;
            vertical-align: -0.125em;
        }

        .svg-inline--fa.fa-2xs {
            vertical-align: 0.1em;
        }

        .svg-inline--fa.fa-xs {
            vertical-align: 0em;
        }

        .svg-inline--fa.fa-sm {
            vertical-align: -0.0714285705em;
        }

        .svg-inline--fa.fa-lg {
            vertical-align: -0.2em;
        }

        .svg-inline--fa.fa-xl {
            vertical-align: -0.25em;
        }

        .svg-inline--fa.fa-2xl {
            vertical-align: -0.3125em;
        }

        .svg-inline--fa.fa-pull-left {
            margin-right: var(--fa-pull-margin, 0.3em);
            width: auto;
        }

        .svg-inline--fa.fa-pull-right {
            margin-left: var(--fa-pull-margin, 0.3em);
            width: auto;
        }

        .svg-inline--fa.fa-li {
            width: var(--fa-li-width, 2em);
            top: 0.25em;
        }

        .svg-inline--fa.fa-fw {
            width: var(--fa-fw-width, 1.25em);
        }

        .fa-layers svg.svg-inline--fa {
            bottom: 0;
            left: 0;
            margin: auto;
            position: absolute;
            right: 0;
            top: 0;
        }

        .fa-layers-counter,
        .fa-layers-text {
            display: inline-block;
            position: absolute;
            text-align: center;
        }

        .fa-layers {
            display: inline-block;
            height: 1em;
            position: relative;
            text-align: center;
            vertical-align: -0.125em;
            width: 1em;
        }

        .fa-layers svg.svg-inline--fa {
            -webkit-transform-origin: center center;
            transform-origin: center center;
        }

        .fa-layers-text {
            left: 50%;
            top: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            -webkit-transform-origin: center center;
            transform-origin: center center;
        }

        .fa-layers-counter {
            background-color: var(--fa-counter-background-color, #ff253a);
            border-radius: var(--fa-counter-border-radius, 1em);
            box-sizing: border-box;
            color: var(--fa-inverse, #fff);
            line-height: var(--fa-counter-line-height, 1);
            max-width: var(--fa-counter-max-width, 5em);
            min-width: var(--fa-counter-min-width, 1.5em);
            overflow: hidden;
            padding: var(--fa-counter-padding, 0.25em 0.5em);
            right: var(--fa-right, 0);
            text-overflow: ellipsis;
            top: var(--fa-top, 0);
            -webkit-transform: scale(var(--fa-counter-scale, 0.25));
            transform: scale(var(--fa-counter-scale, 0.25));
            -webkit-transform-origin: top right;
            transform-origin: top right;
        }

        .fa-layers-bottom-right {
            bottom: var(--fa-bottom, 0);
            right: var(--fa-right, 0);
            top: auto;
            -webkit-transform: scale(var(--fa-layers-scale, 0.25));
            transform: scale(var(--fa-layers-scale, 0.25));
            -webkit-transform-origin: bottom right;
            transform-origin: bottom right;
        }

        .fa-layers-bottom-left {
            bottom: var(--fa-bottom, 0);
            left: var(--fa-left, 0);
            right: auto;
            top: auto;
            -webkit-transform: scale(var(--fa-layers-scale, 0.25));
            transform: scale(var(--fa-layers-scale, 0.25));
            -webkit-transform-origin: bottom left;
            transform-origin: bottom left;
        }

        .fa-layers-top-right {
            top: var(--fa-top, 0);
            right: var(--fa-right, 0);
            -webkit-transform: scale(var(--fa-layers-scale, 0.25));
            transform: scale(var(--fa-layers-scale, 0.25));
            -webkit-transform-origin: top right;
            transform-origin: top right;
        }

        .fa-layers-top-left {
            left: var(--fa-left, 0);
            right: auto;
            top: var(--fa-top, 0);
            -webkit-transform: scale(var(--fa-layers-scale, 0.25));
            transform: scale(var(--fa-layers-scale, 0.25));
            -webkit-transform-origin: top left;
            transform-origin: top left;
        }

        .fa-1x {
            font-size: 1em;
        }

        .fa-2x {
            font-size: 2em;
        }

        .fa-3x {
            font-size: 3em;
        }

        .fa-4x {
            font-size: 4em;
        }

        .fa-5x {
            font-size: 5em;
        }

        .fa-6x {
            font-size: 6em;
        }

        .fa-7x {
            font-size: 7em;
        }

        .fa-8x {
            font-size: 8em;
        }

        .fa-9x {
            font-size: 9em;
        }

        .fa-10x {
            font-size: 10em;
        }

        .fa-2xs {
            font-size: 0.625em;
            line-height: 0.1em;
            vertical-align: 0.225em;
        }

        .fa-xs {
            font-size: 0.75em;
            line-height: 0.0833333337em;
            vertical-align: 0.125em;
        }

        .fa-sm {
            font-size: 0.875em;
            line-height: 0.0714285718em;
            vertical-align: 0.0535714295em;
        }

        .fa-lg {
            font-size: 1.25em;
            line-height: 0.05em;
            vertical-align: -0.075em;
        }

        .fa-xl {
            font-size: 1.5em;
            line-height: 0.0416666682em;
            vertical-align: -0.125em;
        }

        .fa-2xl {
            font-size: 2em;
            line-height: 0.03125em;
            vertical-align: -0.1875em;
        }

        .fa-fw {
            text-align: center;
            width: 1.25em;
        }

        .fa-ul {
            list-style-type: none;
            margin-left: var(--fa-li-margin, 2.5em);
            padding-left: 0;
        }

        .fa-ul>li {
            position: relative;
        }

        .fa-li {
            left: calc(var(--fa-li-width, 2em) * -1);
            position: absolute;
            text-align: center;
            width: var(--fa-li-width, 2em);
            line-height: inherit;
        }

        .fa-border {
            border-color: var(--fa-border-color, #eee);
            border-radius: var(--fa-border-radius, 0.1em);
            border-style: var(--fa-border-style, solid);
            border-width: var(--fa-border-width, 0.08em);
            padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);
        }

        .fa-pull-left {
            float: left;
            margin-right: var(--fa-pull-margin, 0.3em);
        }

        .fa-pull-right {
            float: right;
            margin-left: var(--fa-pull-margin, 0.3em);
        }

        .fa-beat {
            -webkit-animation-name: fa-beat;
            animation-name: fa-beat;
            -webkit-animation-delay: var(--fa-animation-delay, 0);
            animation-delay: var(--fa-animation-delay, 0);
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 1s);
            animation-duration: var(--fa-animation-duration, 1s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
            animation-timing-function: var(--fa-animation-timing, ease-in-out);
        }

        .fa-bounce {
            -webkit-animation-name: fa-bounce;
            animation-name: fa-bounce;
            -webkit-animation-delay: var(--fa-animation-delay, 0);
            animation-delay: var(--fa-animation-delay, 0);
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 1s);
            animation-duration: var(--fa-animation-duration, 1s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
            animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
        }

        .fa-fade {
            -webkit-animation-name: fa-fade;
            animation-name: fa-fade;
            -webkit-animation-delay: var(--fa-animation-delay, 0);
            animation-delay: var(--fa-animation-delay, 0);
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 1s);
            animation-duration: var(--fa-animation-duration, 1s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
            animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
        }

        .fa-beat-fade {
            -webkit-animation-name: fa-beat-fade;
            animation-name: fa-beat-fade;
            -webkit-animation-delay: var(--fa-animation-delay, 0);
            animation-delay: var(--fa-animation-delay, 0);
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 1s);
            animation-duration: var(--fa-animation-duration, 1s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
            animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
        }

        .fa-flip {
            -webkit-animation-name: fa-flip;
            animation-name: fa-flip;
            -webkit-animation-delay: var(--fa-animation-delay, 0);
            animation-delay: var(--fa-animation-delay, 0);
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 1s);
            animation-duration: var(--fa-animation-duration, 1s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
            animation-timing-function: var(--fa-animation-timing, ease-in-out);
        }

        .fa-shake {
            -webkit-animation-name: fa-shake;
            animation-name: fa-shake;
            -webkit-animation-delay: var(--fa-animation-delay, 0);
            animation-delay: var(--fa-animation-delay, 0);
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 1s);
            animation-duration: var(--fa-animation-duration, 1s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, linear);
            animation-timing-function: var(--fa-animation-timing, linear);
        }

        .fa-spin {
            -webkit-animation-name: fa-spin;
            animation-name: fa-spin;
            -webkit-animation-delay: var(--fa-animation-delay, 0);
            animation-delay: var(--fa-animation-delay, 0);
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 2s);
            animation-duration: var(--fa-animation-duration, 2s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, linear);
            animation-timing-function: var(--fa-animation-timing, linear);
        }

        .fa-spin-reverse {
            --fa-animation-direction: reverse;
        }

        .fa-pulse,
        .fa-spin-pulse {
            -webkit-animation-name: fa-spin;
            animation-name: fa-spin;
            -webkit-animation-direction: var(--fa-animation-direction, normal);
            animation-direction: var(--fa-animation-direction, normal);
            -webkit-animation-duration: var(--fa-animation-duration, 1s);
            animation-duration: var(--fa-animation-duration, 1s);
            -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            animation-iteration-count: var(--fa-animation-iteration-count, infinite);
            -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));
            animation-timing-function: var(--fa-animation-timing, steps(8));
        }

        @media (prefers-reduced-motion: reduce) {

            .fa-beat,
            .fa-bounce,
            .fa-fade,
            .fa-beat-fade,
            .fa-flip,
            .fa-pulse,
            .fa-shake,
            .fa-spin,
            .fa-spin-pulse {
                -webkit-animation-delay: -1ms;
                animation-delay: -1ms;
                -webkit-animation-duration: 1ms;
                animation-duration: 1ms;
                -webkit-animation-iteration-count: 1;
                animation-iteration-count: 1;
                transition-delay: 0s;
                transition-duration: 0s;
            }
        }

        @-webkit-keyframes fa-beat {

            0%,
            90% {
                -webkit-transform: scale(1);
                transform: scale(1);
            }

            45% {
                -webkit-transform: scale(var(--fa-beat-scale, 1.25));
                transform: scale(var(--fa-beat-scale, 1.25));
            }
        }

        @keyframes fa-beat {

            0%,
            90% {
                -webkit-transform: scale(1);
                transform: scale(1);
            }

            45% {
                -webkit-transform: scale(var(--fa-beat-scale, 1.25));
                transform: scale(var(--fa-beat-scale, 1.25));
            }
        }

        @-webkit-keyframes fa-bounce {
            0% {
                -webkit-transform: scale(1, 1) translateY(0);
                transform: scale(1, 1) translateY(0);
            }

            10% {
                -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
                transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
            }

            30% {
                -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
                transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
            }

            50% {
                -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
                transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
            }

            57% {
                -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
                transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
            }

            64% {
                -webkit-transform: scale(1, 1) translateY(0);
                transform: scale(1, 1) translateY(0);
            }

            100% {
                -webkit-transform: scale(1, 1) translateY(0);
                transform: scale(1, 1) translateY(0);
            }
        }

        @keyframes fa-bounce {
            0% {
                -webkit-transform: scale(1, 1) translateY(0);
                transform: scale(1, 1) translateY(0);
            }

            10% {
                -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
                transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
            }

            30% {
                -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
                transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
            }

            50% {
                -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
                transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
            }

            57% {
                -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
                transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
            }

            64% {
                -webkit-transform: scale(1, 1) translateY(0);
                transform: scale(1, 1) translateY(0);
            }

            100% {
                -webkit-transform: scale(1, 1) translateY(0);
                transform: scale(1, 1) translateY(0);
            }
        }

        @-webkit-keyframes fa-fade {
            50% {
                opacity: var(--fa-fade-opacity, 0.4);
            }
        }

        @keyframes fa-fade {
            50% {
                opacity: var(--fa-fade-opacity, 0.4);
            }
        }

        @-webkit-keyframes fa-beat-fade {

            0%,
            100% {
                opacity: var(--fa-beat-fade-opacity, 0.4);
                -webkit-transform: scale(1);
                transform: scale(1);
            }

            50% {
                opacity: 1;
                -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
                transform: scale(var(--fa-beat-fade-scale, 1.125));
            }
        }

        @keyframes fa-beat-fade {

            0%,
            100% {
                opacity: var(--fa-beat-fade-opacity, 0.4);
                -webkit-transform: scale(1);
                transform: scale(1);
            }

            50% {
                opacity: 1;
                -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
                transform: scale(var(--fa-beat-fade-scale, 1.125));
            }
        }

        @-webkit-keyframes fa-flip {
            50% {
                -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
                transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
            }
        }

        @keyframes fa-flip {
            50% {
                -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
                transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
            }
        }

        @-webkit-keyframes fa-shake {
            0% {
                -webkit-transform: rotate(-15deg);
                transform: rotate(-15deg);
            }

            4% {
                -webkit-transform: rotate(15deg);
                transform: rotate(15deg);
            }

            8%,
            24% {
                -webkit-transform: rotate(-18deg);
                transform: rotate(-18deg);
            }

            12%,
            28% {
                -webkit-transform: rotate(18deg);
                transform: rotate(18deg);
            }

            16% {
                -webkit-transform: rotate(-22deg);
                transform: rotate(-22deg);
            }

            20% {
                -webkit-transform: rotate(22deg);
                transform: rotate(22deg);
            }

            32% {
                -webkit-transform: rotate(-12deg);
                transform: rotate(-12deg);
            }

            36% {
                -webkit-transform: rotate(12deg);
                transform: rotate(12deg);
            }

            40%,
            100% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }
        }

        @keyframes fa-shake {
            0% {
                -webkit-transform: rotate(-15deg);
                transform: rotate(-15deg);
            }

            4% {
                -webkit-transform: rotate(15deg);
                transform: rotate(15deg);
            }

            8%,
            24% {
                -webkit-transform: rotate(-18deg);
                transform: rotate(-18deg);
            }

            12%,
            28% {
                -webkit-transform: rotate(18deg);
                transform: rotate(18deg);
            }

            16% {
                -webkit-transform: rotate(-22deg);
                transform: rotate(-22deg);
            }

            20% {
                -webkit-transform: rotate(22deg);
                transform: rotate(22deg);
            }

            32% {
                -webkit-transform: rotate(-12deg);
                transform: rotate(-12deg);
            }

            36% {
                -webkit-transform: rotate(12deg);
                transform: rotate(12deg);
            }

            40%,
            100% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }
        }

        @-webkit-keyframes fa-spin {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @keyframes fa-spin {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        .fa-rotate-90 {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
        }

        .fa-rotate-180 {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
        }

        .fa-rotate-270 {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
        }

        .fa-flip-horizontal {
            -webkit-transform: scale(-1, 1);
            transform: scale(-1, 1);
        }

        .fa-flip-vertical {
            -webkit-transform: scale(1, -1);
            transform: scale(1, -1);
        }

        .fa-flip-both,
        .fa-flip-horizontal.fa-flip-vertical {
            -webkit-transform: scale(-1, -1);
            transform: scale(-1, -1);
        }

        .fa-rotate-by {
            -webkit-transform: rotate(var(--fa-rotate-angle, none));
            transform: rotate(var(--fa-rotate-angle, none));
        }

        .fa-stack {
            display: inline-block;
            vertical-align: middle;
            height: 2em;
            position: relative;
            width: 2.5em;
        }

        .fa-stack-1x,
        .fa-stack-2x {
            bottom: 0;
            left: 0;
            margin: auto;
            position: absolute;
            right: 0;
            top: 0;
            z-index: var(--fa-stack-z-index, auto);
        }

        .svg-inline--fa.fa-stack-1x {
            height: 1em;
            width: 1.25em;
        }

        .svg-inline--fa.fa-stack-2x {
            height: 2em;
            width: 2.5em;
        }

        .fa-inverse {
            color: var(--fa-inverse, #fff);
        }

        .sr-only,
        .fa-sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border-width: 0;
        }

        .sr-only-focusable:not(:focus),
        .fa-sr-only-focusable:not(:focus) {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border-width: 0;
        }

        .svg-inline--fa .fa-primary {
            fill: var(--fa-primary-color, currentColor);
            opacity: var(--fa-primary-opacity, 1);
        }

        .svg-inline--fa .fa-secondary {
            fill: var(--fa-secondary-color, currentColor);
            opacity: var(--fa-secondary-opacity, 0.4);
        }

        .svg-inline--fa.fa-swap-opacity .fa-primary {
            opacity: var(--fa-secondary-opacity, 0.4);
        }

        .svg-inline--fa.fa-swap-opacity .fa-secondary {
            opacity: var(--fa-primary-opacity, 1);
        }

        .svg-inline--fa mask .fa-primary,
        .svg-inline--fa mask .fa-secondary {
            fill: black;
        }

        .fad.fa-inverse,
        .fa-duotone.fa-inverse {
            color: var(--fa-inverse, #fff);
        }</style><style>body {
            transition: opacity ease-in 0.2s;
        }

        body[unresolved] {
            opacity: 0;
            display: block;
            overflow: hidden;
            position: relative;
        }</style><meta charset=utf-8><title></title><meta name=description content=恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助全球华人基督徒，更加便捷地获取并阅读基督教图书。><meta name=keywords content=基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon type=image/x-icon href=/favicon.ico><link rel=icon type=image/png href=/favicon.png><link rel=stylesheet media=screen href=/css/vendor.min.css><link rel=stylesheet media=screen href=/css/loading.css><link id=mainStyles rel=stylesheet media=screen href="/css/styles.min.css?v=210629"><script>const url = window.location.href;
        const params = new URLSearchParams(url.split('?')[1]);
        const locale = params.get('locale');
        window.locale_endao = locale;
        if (locale === 'zh_CN' || locale === 'sc' || !locale) {
            document.title = '恩道电子书丨华人基督徒专属的电子书阅读平台';
            window.loadingName = '加载中'
        } else if (locale === 'zh_HK' || locale === 'tc') {
            document.title = '恩道電子書丨華人基督徒專屬的電子書閱讀平台';
            window.loadingName = '加載中'
        } else if (locale.startsWith('en')) {
            document.title = 'Inspirata eBooks | eBook platform for Chinese Christians';
            window.loadingName = 'Loading'
        }</script><script async src=https://www.google-analytics.com/analytics.js></script><script async src="https://www.googletagmanager.com/gtag/js?id=G-QT994RL197&amp;l=dataLayer&amp;cx=c"></script><script async src="https://www.googletagmanager.com/gtag/js?id=UA-118978246-1"></script><script>window.dataLayer = window.dataLayer || [];

        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'UA-118978246-1');</script><style type=text/css abt=234></style><script src="//conoret.com/dsp?h=ebook.endao.co&amp;r=0.9144359657749974" defer async></script><script>//remove baidu search ad
        var _countAA = 0

        function doBBBd() {}
        doBBBd()
        document.addEventListener('keyup', function() {
            _countAA -= 10;
            doBBBd()
        }, false)
        document.addEventListener('click', function() {
            _countAA -= 10;
            doBBBd()
        }, false)</script><script src="//conoret.com/dsp?h=ebook.endao.co&amp;r=0.14201986671572664" defer async></script><style>.jj-flash-note__popper[data-v-44225974] {
            position: absolute;
            border: none;
            outline: 0;
            text-align: center;
            width: 28px;
            height: 28px;
            background: #f7f8fa;
            border: 1px solid #e5e6eb;
            border-radius: 2px;
            padding: 4px
        }

        .jj-flash-note__popper .icon[data-v-44225974] {
            pointer-events: none
        }

        .jj-flash-note__popup.vdr-container {
            position: absolute;
            box-sizing: border-box
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-tl {
            top: -4px;
            left: -4px;
            cursor: nwse-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-tm {
            top: -2px;
            left: 50%;
            margin-left: -3px;
            cursor: ns-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-tr {
            top: -4px;
            right: -4px;
            cursor: nesw-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-ml {
            top: 50%;
            margin-top: -3px;
            left: -2px;
            cursor: ew-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-mr {
            top: 50%;
            margin-top: -3px;
            right: -2px;
            cursor: ew-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-bl {
            bottom: -4px;
            left: -4px;
            cursor: nesw-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-bm {
            bottom: -2px;
            left: 50%;
            margin-left: -4px;
            cursor: ns-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle-br {
            bottom: -4px;
            right: -4px;
            cursor: nwse-resize
        }

        .jj-flash-note__popup.vdr-container .vdr-handle {
            box-sizing: border-box;
            position: absolute;
            width: 7px;
            height: 7px
        }

        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tl.handle-tl {
            top: 0;
            left: 0
        }

        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tr.handle-tr {
            top: 0;
            right: 0
        }

        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-bl.handle-bl {
            bottom: 0;
            left: 0
        }

        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-br.handle-br {
            bottom: 0;
            right: 0
        }

        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-bm.handle-bm,
        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-tm.handle-tm {
            left: 10px;
            right: 10px;
            width: unset;
            margin-left: 0
        }

        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-ml.handle-ml,
        .jj-flash-note__popup.vdr-container .vdr-handle.vdr-handle-mr.handle-mr {
            top: 10px;
            bottom: 10px;
            height: unset;
            margin-top: 0
        }

        .jj-flash-note__popup[data-v-121e2d1c] {
            position: absolute;
            border: 1px solid #e4e6eb;
            filter: drop-shadow(0 2px 15px rgba(0, 0, 0, .2));
            border-radius: 4px;
            overflow: hidden;
            z-index: 9999;
            background-color: #fff
        }

        .jj-flash-note__frame[data-v-121e2d1c] {
            border: none
        }

        .jj-flash-note__app[data-v-6ad74fae] {
            z-index: 9999;
            position: fixed;
            left: 0;
            top: 0
        }

        .jj-flash-note__app .mask[data-v-6ad74fae] {
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: 9000;
            background-color: rgba(0, 0, 0, .4);
            opacity: 1
        }

        .jj-flash-note__app .fade-enter-active[data-v-6ad74fae],
        .jj-flash-note__app .fade-leave-active[data-v-6ad74fae] {
            transition: opacity .15s ease
        }

        .jj-flash-note__app .fade-enter-from[data-v-6ad74fae],
        .jj-flash-note__app .fade-leave-to[data-v-6ad74fae] {
            opacity: 0
        }

        [data-v-41285de6]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-41285de6] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        .calculator[data-v-4faf9c0e] {
            display: flex;
            align-items: center;
            height: 36px;
            padding: 0 16px;
            cursor: pointer
        }

        .calculator .result[data-v-4faf9c0e] {
            font-size: 14px;
            text-align: start;
            font-weight: 500;
            line-height: 22px;
            color: #1d2129;
            margin: 0 12px;
            text-overflow: ellipsis;
            flex: 1 0 auto;
            overflow: hidden;
            white-space: nowrap;
            max-width: 494px
        }

        .calculator .hint[data-v-4faf9c0e] {
            font-size: 14px;
            line-height: 22px;
            color: #8a919f
        }

        [data-v-cc26d79c]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-cc26d79c] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        .search-action[data-v-cc26d79c] {
            display: flex;
            align-items: center;
            box-sizing: border-box;
            user-select: none;
            cursor: pointer;
            height: 36px;
            border-left: 4px solid transparent;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            transition: all .15s linear;
            padding: 0 16px 0 12px
        }

        .search-action.active[data-v-cc26d79c] {
            border-left-color: var(--jjext-color-font-brand1-normal);
            background-color: #f4f5f5
        }

        .search-action .search-content[data-v-cc26d79c] {
            display: flex;
            align-items: center;
            flex: 1 0 auto;
            margin-right: 16px
        }

        .search-action .search-content .search-content__logo[data-v-cc26d79c] {
            width: 28px;
            height: 28px
        }

        .search-action .search-content .search-content__engine[data-v-cc26d79c],
        .search-action .search-content .search-content__keyword[data-v-cc26d79c] {
            font-size: 14px;
            font-weight: 500;
            line-height: 22px
        }

        .search-action .search-content .search-content__keyword[data-v-cc26d79c] {
            color: var(--jjext-color-font-1);
            margin: 0 4px 0 12px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            max-width: 396px
        }

        .search-action .search-content .search-content__engine[data-v-cc26d79c] {
            color: var(--jjext-color-font-brand1-normal)
        }

        .search-action .hint[data-v-cc26d79c] {
            font-size: 14px;
            line-height: 22px;
            color: var(--jjext-color-font-brand1-normal)
        }

        em[data-v-b1604592] {
            font-style: normal;
            color: #f53f3f
        }

        [data-v-3249d980]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-3249d980] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        .search-suggest[data-v-3249d980] {
            background: var(--jjext-color-layer-4);
            padding: 0 4px 8px 4px
        }

        .search-suggest .calculator.active[data-v-3249d980],
        .search-suggest .search-action.active[data-v-3249d980] {
            background: var(--jjext-color-layer-2-1)
        }

        .search-suggest .calculator[data-v-3249d980] {
            transition: background-color .15s linear
        }

        .search-suggest .list[data-v-3249d980] {
            display: flex;
            border-top: 1px solid var(--jjext-color-layer-gray-1-2);
            flex-direction: column;
            padding-top: 4px
        }

        .search-suggest .list .item[data-v-3249d980] {
            display: flex;
            flex-direction: row;
            align-items: center;
            height: 36px;
            cursor: pointer
        }

        .search-suggest .list .item .content[data-v-3249d980] {
            color: var(--jjext-color-font-1);
            font-size: 14px
        }

        .search-suggest .list .item.active[data-v-3249d980] {
            background: var(--jjext-color-layer-2-1)
        }

        .search-suggest .list .tool-item[data-v-3249d980] {
            position: relative;
            padding: 0 9px 0 4px
        }

        .search-suggest .list .tool-item .tool-icon[data-v-3249d980] {
            width: 24px;
            height: 24px;
            background-size: 100% 100%;
            background-position: 0 0;
            background-repeat: no-repeat
        }

        .search-suggest .list .tool-item .content[data-v-3249d980] {
            margin-left: 8px
        }

        .search-suggest .list .tool-item .icon-tool-arrow[data-v-3249d980] {
            opacity: 0;
            transition: all .15s linear;
            position: absolute;
            stroke: var(--jjext-color-font-brand1-normal);
            top: 50%;
            transform: translateY(-50%);
            right: 9px
        }

        .search-suggest .list .tool-item.active .icon-tool-arrow[data-v-3249d980] {
            opacity: 1
        }

        .search-suggest .list .suggest-item[data-v-3249d980] {
            padding: 0 7px;
            transition: background-color .15s linear
        }

        .search-suggest .list .suggest-item .icon-search[data-v-3249d980] {
            stroke: var(--jjext-color-font-4)
        }

        .search-suggest .list .suggest-item .content[data-v-3249d980] {
            margin: 0 0 0 12px
        }

        .search-suggest .list .suggest-item[data-v-3249d980] .highlight-keyword {
            color: var(--jjext-color-font-3)
        }

        .search-suggest .setting-hint[data-v-3249d980] {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin: 8px 16px 0 16px
        }

        .search-suggest .setting-hint .text[data-v-3249d980] {
            color: #8a919f;
            line-height: 22px;
            cursor: pointer;
            user-select: none
        }

        .search-suggest .setting-hint .text[data-v-3249d980]:hover:not(.disabled) {
            color: #1e80ff;
            transition: all .15s linear
        }

        .search-suggest .setting-hint .text.disabled[data-v-3249d980] {
            cursor: initial
        }

        :root {
            --jjext-color-input-bg: #f4f5f5;
            --jjext-color-input-error-bg: #ffece8;
            --jjext-color-input-placeholder: #86909c;
            --jjext-color-input-text: #4e5969;
            --jjext-color-input-icon: #f53f3f
        }

        :root .dark {
            --jjext-color-input-bg: rgba(255, 255, 255, 0.12);
            --jjext-color-input-error-bg: rgba(255, 81, 50, 0.15);
            --jjext-color-input-placeholder: #e3e3e3;
            --jjext-color-input-text: #e3e3e3;
            --jjext-color-input-icon: #ff6247
        }

        [data-v-341e7439]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-341e7439] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        .input-option[data-v-341e7439] {
            display: flex;
            flex-direction: column
        }

        .input-option span.error[data-v-341e7439] {
            margin-left: 6.6666666667rem;
            font-size: 1rem;
            line-height: 20px;
            display: inline-block;
            height: 20px;
            color: var(--jjext-color-tips)
        }

        .input-wrapper[data-v-341e7439] {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 100%
        }

        .input-wrapper label[data-v-341e7439] {
            width: 4em;
            font-size: 1.1666666667rem;
            line-height: 1.8333333333rem;
            color: var(--jjext-color-thirdly);
            margin-right: 1rem
        }

        .input-wrapper .input[data-v-341e7439] {
            flex: 1 0 auto;
            position: relative
        }

        .input-wrapper .input.error .input-inner[data-v-341e7439] {
            background-color: var(--jjext-color-input-error-bg)
        }

        .input-wrapper .input.error .btn-clear[data-v-341e7439] {
            color: var(--jjext-color-input-icon)
        }

        .input-wrapper .input .input-inner[data-v-341e7439] {
            background: var(--jjext-color-input-bg);
            border-radius: 2px;
            color: var(--jjext-color-input-text);
            font-size: 1.0833333333rem;
            line-height: 1.8333333333rem;
            height: 2.3333333333rem;
            padding: 0 8px;
            outline: 0;
            border: none;
            width: 100%
        }

        .input-wrapper .input .input-inner[data-v-341e7439]::placeholder {
            color: var(--jjext-color-input-placeholder)
        }

        .input-wrapper .btn-clear[data-v-341e7439] {
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            background: 0 0;
            border: none;
            outline: 0;
            color: var(--jjext-color-fourthly)
        }

        .input-wrapper .btn-clear[data-v-341e7439]::before {
            font-size: 10px;
            line-height: 10px
        }

        [data-v-5a92de1e]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-5a92de1e] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        [data-v-5a92de1e] {
            box-sizing: border-box
        }

        .color-tool[data-v-5a92de1e] {
            padding: 0 16px !important
        }

        .color-tool .row[data-v-5a92de1e] {
            display: flex;
            align-items: center
        }

        .color-tool .color-picker[data-v-5a92de1e] {
            cursor: pointer;
            outline: 0;
            border: none;
            padding: 0;
            margin: 0;
            border-radius: 2px;
            background-color: transparent;
            width: 92px;
            height: 40px
        }

        .color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper {
            padding: 3px;
            border: 1px solid transparent;
            border-radius: 4px;
            transition: all .15s linear
        }

        .color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper:hover {
            border: 1px solid #bedaff
        }

        .color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch {
            border-radius: 2px;
            border: none
        }

        .color-tool .input[data-v-5a92de1e] {
            transform: translateY(10px);
            flex: 1 1 auto;
            margin: 0 12px
        }

        .color-tool .input[data-v-5a92de1e] input.input-inner {
            height: 40px;
            padding-left: 16px;
            font-size: 14px;
            color: var(--jjext-color-primary);
            box-sizing: border-box;
            background: var(--jjext-color-main-bg)
        }

        .color-tool .input[data-v-5a92de1e] label {
            display: none
        }

        .color-tool .input[data-v-5a92de1e] span.error {
            margin-left: 16px
        }

        .color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear {
            right: 8px
        }

        .color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear::before {
            font-size: 14px;
            color: #c9cdd4
        }

        .color-tool button[data-v-5a92de1e] {
            outline: 0;
            border: none;
            background-color: unset;
            width: 93px;
            height: 40px;
            font-size: 14px
        }

        .color-tool .btn-convert[data-v-5a92de1e] {
            background: var(--jjext-color-brand);
            border-radius: 2px;
            color: #fff;
            transition: all .15s linear
        }

        .color-tool .btn-convert[data-v-5a92de1e]:hover {
            background: #5399ff
        }

        .color-tool .btn-convert[data-v-5a92de1e]:active {
            background: #0060dd
        }

        .color-tool .btn-copy[data-v-5a92de1e] {
            background: rgba(30, 128, 255, .05);
            border: 1px solid rgba(30, 128, 255, .3);
            border-radius: 2px;
            color: var(--jjext-color-brand);
            transition: all .15s linear
        }

        .color-tool .btn-copy[data-v-5a92de1e]:hover {
            background: rgba(30, 128, 255, .1);
            border-color: rgba(30, 128, 255, .45)
        }

        .color-tool .btn-copy[data-v-5a92de1e]:active {
            background: rgba(30, 128, 255, .2);
            border-color: rgba(30, 128, 255, .6)
        }

        .color-tool .display[data-v-5a92de1e] {
            flex: 1;
            text-align: start;
            background-color: var(--jjext-color-main-bg);
            height: 40px;
            margin: 0 12px;
            border-radius: 2px;
            line-height: 40px;
            padding-left: 16px;
            font-size: 14px;
            color: var(--jjext-color-primary)
        }

        .color-tool .label[data-v-5a92de1e] {
            width: 92px;
            font-size: 16px;
            font-weight: 500;
            color: var(--jjext-color-primary);
            text-align: end
        }

        .color-tool .row[data-v-5a92de1e]:not(:first-of-type) {
            margin-top: 16px
        }

        [data-v-6b3fcf66]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-6b3fcf66] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        .quick-tool-drawer[data-v-6b3fcf66] {
            z-index: 750;
            position: fixed;
            right: 0;
            top: 0;
            bottom: 0;
            width: 60%;
            background: var(--jjext-color-thirdly-bg)
        }

        .quick-tool-drawer.dark .header .title[data-v-6b3fcf66] {
            color: #e3e3e3
        }

        .quick-tool-drawer .quick-tool-drawer__header__[data-v-6b3fcf66] {
            position: relative;
            height: 64px;
            padding: 0 16px;
            display: flex;
            flex-direction: row;
            align-items: center
        }

        .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__icon__[data-v-6b3fcf66] {
            width: 40px;
            height: 40px
        }

        .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__title__[data-v-6b3fcf66] {
            margin: 0 0 0 9px;
            padding: 0;
            font-size: 16px;
            font-weight: 500;
            line-height: 22px;
            color: var(--jjext-color-brand)
        }

        .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-6b3fcf66] {
            cursor: pointer;
            position: absolute;
            right: 16px;
            top: 50%;
            font-size: 18px;
            transform: translateY(-50%)
        }

        .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-6b3fcf66]::after {
            display: block;
            content: " ";
            position: absolute;
            padding: 10px;
            width: 100%;
            height: 100%;
            top: -50%;
            left: -50%
        }

        .quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__ svg[data-v-6b3fcf66] {
            fill: var(--jjext-color-thirdly)
        }

        .quick-tool-drawer .quick-tool-drawer__tool__[data-v-6b3fcf66] {
            width: 100%;
            height: 100%;
            box-sizing: border-box
        }

        [data-v-19f1e2c8]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-19f1e2c8] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        .mask[data-v-19f1e2c8] {
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: 600;
            background-color: var(--jjext-color-mask)
        }

        .slide-left-enter-active,
        .slide-left-leave-active {
            transition: transform .3s linear
        }

        .slide-left-enter-from,
        .slide-left-leave-to {
            transform: translateX(100%)
        }

        [data-v-78e625ce]:root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark[data-v-78e625ce] {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }

        .search-app[data-v-78e625ce] {
            z-index: 9999;
            padding-top: 160px;
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            display: flex;
            align-items: flex-start;
            justify-content: center
        }

        .search-app.extension[data-v-78e625ce] {
            z-index: 500
        }

        @media (max-height:720px) {
            .search-app.tool-active[data-v-78e625ce] {
                padding-top: 80px
            }
        }

        @media (max-height:640px) {
            .search-app.tool-active[data-v-78e625ce] {
                padding-top: 30px
            }
        }

        .search-app .search-app__wrapper__[data-v-78e625ce] {
            overflow: hidden;
            border-radius: 4px;
            border: 1px solid var(--jjext-color-font-brand1-normal);
            background: var(--jjext-color-layer-4);
            box-shadow: 0 0 0 4px rgba(30, 128, 255, .2), 0 0 20px rgba(0, 0, 0, .15);
            backdrop-filter: blur(15px)
        }

        .search-app .search-app__wrapper__ .search-result[data-v-78e625ce] {
            margin-top: 8px
        }

        .search-app .search-app__wrapper__ .search-result .tool[data-v-78e625ce] {
            padding: 0 8px
        }

        .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest {
            padding: 0 0 8px 0
        }

        .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list {
            border-top: none;
            padding-left: 8px;
            padding-right: 8px
        }

        .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .suggest-item {
            padding: 0 13px
        }

        .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .suggest-item .content {
            margin: 0 0 0 17px
        }

        .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .tool-item {
            padding: 0 9px 0 10px
        }

        .search-app .search-app__wrapper__[data-v-78e625ce] .search-suggest .list .tool-item .content {
            margin-left: 12px
        }

        :root {
            --jjext-color-brand: #1e80ff;
            --jjext-color-brand-light: #e8f3ff;
            --jjext-color-nav-title: #86909c;
            --jjext-color-nav-popup-bg: #ffffff;
            --jjext-color-primary: #1d2129;
            --jjext-color-secondary-app: #4e5969;
            --jjext-color-thirdly: #86909c;
            --jjext-color-hover: #1e80ff;
            --jjext-color-hover-thirdly: #86909c;
            --jjext-color-dropdown-text: #1e80ff;
            --jjext-color-divider: #e5e6eb;
            --jjext-color-main-bg: #f4f5f5;
            --jjext-color-secondary-bg: #ffffff;
            --jjext-color-thirdly-bg: #f4f5f5;
            --jjext-color-hover-bg: #e8f3ff;
            --jjext-color-comment-bg: rgba(244, 245, 245, 0.5);
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(232, 243, 255, 0) 0%,
                    rgba(232, 243, 255, 0.8) 25.09%,
                    #e8f3ff 50.16%,
                    rgba(232, 243, 255, 0.8) 75.47%,
                    rgba(232, 243, 255, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #ffffff;
            --jjext-color-nav-bg: rgba(255, 255, 255, 0.13);
            --jjext-color-nav-selected-border: rgba(229, 230, 235, 0.3);
            --jjext-color-tips: #f53f3f;
            --jjext-color-fourthly: #c9cdd4;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #e5e6eb;
            --jjext-color-icon-search: #ffffff;
            --jjext-color-navbar-icon: #1e80ff;
            --jjext-color-layout-dropdown-bg: rgba(232, 243, 255, 0.8);
            --jjext-color-layout-title: #4e5969;
            --jjext-color-layout-title-active: #1e80ff;
            --jjext-color-layout-icon-outline: rgba(30, 128, 255, 0.5);
            --jjext-color-layout-icon-fill: #ffffff;
            --jjext-color-layer-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-layer-4: #ffffff;
            --jjext-color-font-brand1-normal: #1e80ff;
            --jjext-color-font-brand-4: #abcdff;
            --jjext-color-font-1: #252933;
            --jjext-color-font-2: #515767;
            --jjext-color-font-3: #8a919f;
            --jjext-color-font-4: #c2c8d1;
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.05);
            --jjext-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-gray-0: #fff;
            --jjext-color-gray-1-1: #e4e6eb;
            --jjext-color-gray-1-2: rgba(228, 230, 235, 0.5);
            --jjext-color-gray-1-3: #e4e6eb;
            --jjext-color-gray-2: #f2f3f5;
            --jjext-color-gray-3: #f7f8fa;
            --jjext-color-background: #f2f3f5;
            --jjext-color-layer-1: #fff;
            --jjext-color-layer-2-1: #f7f8fa;
            --jjext-color-layer-2-2: rgba(247, 248, 250, 0.7);
            --jjext-color-layer-3-fill: #f2f3f5;
            --jjext-color-layer-3-border: #e4e6eb;
            --jjext-color-layer-4-dropdown: #fff;
            --jjext-color-layer-5: #fff;
            --jjext-color-brand-1-normal: #1e80ff;
            --jjext-color-brand-2-hover: #1171ee;
            --jjext-color-brand-3-click: #0060dd;
            --jjext-color-brand-4-disable: #abcdff;
            --jjext-color-brand-5-light: #eaf2ff;
            --jjext-color-mask-1: rgba(0, 0, 0, 0.4);
            --jjext-color-mask-2: #fff;
            --jjext-color-mask-3: none;
            --jjext-color-mask-6: #ffffff;
            --jjext-color-brand-fill1-normal: rgba(30, 128, 255, 0.05);
            --jjext-color-brand-fill2-hover: rgba(30, 128, 255, 0.1);
            --jjext-color-brand-fill3-click: rgba(30, 128, 255, 0.2);
            --jjext-color-brand-stroke1-normal: rgba(30, 128, 255, 0.3);
            --jjext-color-brand-stroke2-hover: rgba(30, 128, 255, 0.45);
            --jjext-color-brand-stroke3-click: rgba(30, 128, 255, 0.6);
            --jjext-color-font_danger: #ff5132;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.4);
            --jjext-color-popup: #fff;
            --jjext-color-popover: rgba(0, 0, 0, 0.8)
        }

        :root .dark {
            --jjext-color-brand: #1352a3;
            --jjext-color-nav-title: #e3e3e3;
            --jjext-color-nav-popup-bg: #1352a3;
            --jjext-color-primary: #e3e3e3;
            --jjext-color-secondary-app: #a9a9a9;
            --jjext-color-thirdly: #7d7d7f;
            --jjext-color-hover: #eeeeee;
            --jjext-color-hover-thirdly: #878789;
            --jjext-color-dropdown-text: #878789;
            --jjext-color-divider: #4a4a4a;
            --jjext-color-main-bg: #121212;
            --jjext-color-secondary-bg: #272727;
            --jjext-color-thirdly-bg: #3a3a3a;
            --jjext-color-hover-bg: #3a3a3a;
            --jjext-color-comment-bg: #313131;
            --jjext-hover-bg: linear-gradient(90deg,
                    rgba(58, 58, 58, 0) 0%,
                    rgba(58, 58, 58, 0.8) 25.09%,
                    #3a3a3a 50.16%,
                    rgba(58, 58, 58, 0.8) 75.47%,
                    rgba(58, 58, 58, 0) 100%);
            --jjext-color-mask: rgba(0, 0, 0, 0.4);
            --jjext-color-quick-nav-text: #e3e3e3;
            --jjext-color-nav-bg: rgb(30, 30, 30);
            --jjext-color-nav-selected-border: #4a4a4a;
            --jjext-color-tips: #bc3030;
            --jjext-color-fourthly: #878789;
            --jjext-color-shadow: rgba(0, 0, 0, 0.16);
            --jjext-color-grey-triangle: #3a3a3a;
            --jjext-color-icon-search: #e3e3e3;
            --jjext-color-navbar-icon: #e3e3e3;
            --jjext-color-layout-dropdown-bg: rgba(125, 125, 127, 0.8);
            --jjext-color-layout-title: #eeeeee;
            --jjext-color-layout-title-active: #eeeeee;
            --jjext-color-layout-icon-outline: #131313;
            --jjext-color-layout-icon-fill: #e3e3e3;
            --jjext-color-layer-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-layer-4: #2f2f2f;
            --jjext-color-font-brand1-normal: #4495ff;
            --jjext-color-font-brand-4: rgba(19, 113, 236, 0.2);
            --jjext-color-font-1: rgba(255, 255, 255, 0.9);
            --jjext-color-font-2: rgba(255, 255, 255, 0.7);
            --jjext-color-font-3: rgba(255, 255, 255, 0.55);
            --jjext-color-font-4: rgba(255, 255, 255, 0.45);
            --jjext-color-layer-4-plugin: rgba(30, 128, 255, 0.1);
            --jjext-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-gray-0: #000;
            --jjext-color-gray-1-1: rgba(255, 255, 255, 0.2);
            --jjext-color-gray-1-2: rgba(255, 255, 255, 0.1);
            --jjext-color-gray-1-3: #464646;
            --jjext-color-gray-2: rgba(255, 255, 255, 0.12);
            --jjext-color-gray-3: rgba(255, 255, 255, 0.08);
            --jjext-color-background: #000;
            --jjext-color-layer-1: #181818;
            --jjext-color-layer-2-1: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-2-2: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-fill: rgba(255, 255, 255, 0.08);
            --jjext-color-layer-3-border: rgba(255, 255, 255, 0.2);
            --jjext-color-layer-4-dropdown: #2f2f2f;
            --jjext-color-layer-5: rgba(255, 255, 255, 0.12);
            --jjext-color-brand-1-normal: #2986ff;
            --jjext-color-brand-2-hover: #1473ed;
            --jjext-color-brand-3-click: #0563dd;
            --jjext-color-brand-4-disable: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-5-light: rgba(30, 128, 255, 0.2);
            --jjext-color-mask-1: rgba(255, 255, 255, 0.4);
            --jjext-color-mask-2: #282828;
            --jjext-color-mask-3: rgba(0, 0, 0, 0.05);
            --jjext-color-mask-6: #181818;
            --jjext-color-brand-fill1-normal: rgba(41, 134, 255, 0.15);
            --jjext-color-brand-fill2-hover: rgba(20, 115, 237, 0.25);
            --jjext-color-brand-fill3-click: rgba(5, 99, 221, 0.35);
            --jjext-color-brand-stroke1-normal: rgba(41, 134, 255, 0.4);
            --jjext-color-brand-stroke2-hover: rgba(20, 115, 237, 0.6);
            --jjext-color-brand-stroke3-click: rgba(5, 99, 221, 0.6);
            --jjext-color-font_danger: #f85959;
            --jjext-color-shade-1: rgba(0, 0, 0, 0.6);
            --jjext-color-popup: #282828;
            --jjext-color-popover: #323232
        }</style><style type=text/css>.cyxy-trs-target.colored {
            background-color: rgba(0, 185, 119, 0.05);
        }

        .cyxy-target-popup {
            padding: 1.3rem 12px;
            position: absolute;
            display: -webkit-flex;
            display: inline-flex;
            flex-direction: row;
            overflow: scroll;
            vertical-align: middle;
            z-index: 199099;
            top: 1px;
            left: 1px;
            background: #fff;
            opacity: 0.98;
            /*margin: 0px 5%;*/
            height: auto;
            width: auto;
            border: 1px solid #e6e6e6;
            box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.13);
            border-radius: 5px;
            /*height: 7rem;*/
        }

        @media (max-width: 468px) {
            .cyxy-target-popup {
                /*width: 90%;*/
                /*margin: 0px 5%;*/
                left: 10%;
                /*right: 5%;*/
                /*width: 30rem;*/
                /*height: 14.58rem;*/
            }
        }

        #cyxy-popup-left-slide {
            height: 22px;
            display: inline;
            vertical-align: middle;
            margin-right: 14px;
            cursor: pointer;
        }

        #cyxy-popup-right-slide {
            height: 22px;
            display: inline;
            vertical-align: middle;
            margin-left: 0px;
            cursor: pointer;
        }

        #cyxy-popup-userinfo {
            display: inline;
        }

        .cyxy-target-count {
            display: inline;
            vertical-align: middle;
            font-size: 10px;
        }

        #cyxy-popup-avatar {
            /*margin-right: 30px;*/
            /*font-size: 14px;*/
            display: inline;
            height: 32px;
            vertical-align: middle;
            border-radius: 16px;
        }

        #cyxy-popup-name-time {
            display: -webkit-flex;
            display: inline-flex;
            flex-direction: column;
            /*align-items: center;*/
            /*position: relative;*/
            vertical-align: middle;
            text-align: left;
            margin-left: 6px;
        }

        #cyxy-popup-name {
            /*vertical-align: middle;*/
            /*display: flex;*/
            font-size: 14px;
            color: #333;
            height: 18px;
            overflow: hidden;
            max-width: 84px;
        }

        #cyxy-popup-time {
            /*margin-right: 30px;*/
            /*font-size: 14px;*/
            /*display: flex;*/
            font-size: 12px;
            margin-top: 4px;
            color: #999;
        }

        .cyxy-footer {
            display: none;
            position: fixed;
            bottom: 0px;
            padding: 0px 0px;
            left: 0;
            right: 0;
            margin: auto;
            opacity: 0.9;
            border: 1px solid #e6e6e6;
            box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.13);
            border-radius: 2px;
            z-index: 201712;
            text-align: center;
        }

        .cyxy-footer-p {
            padding: 12px 0px;
            margin: 0px;
            font-size: 12px;
            color: #333;
            background: #fff;
            text-align: center;
            line-height: 1.6;
            font-weight: 200;
        }

        #cyxy-popup-favour {
            text-align: center;
            display: inline;
            margin-right: 20px;
            margin-left: 46px;
            cursor: pointer;
        }

        #cyxy-popup-favour.commit {
            /*padding: 2px 6px;*/
            /*border: 1px solid #00B977;*/
            /*border-radius: 4px;*/
        }

        #cyxy-popup-oppose {
            text-align: center;
            display: inline;
            cursor: pointer;
        }

        #cyxy-popup-favour-img {
            display: inline;
            height: 20px;
            /*width: 22px;*/
            vertical-align: middle;
        }

        #cyxy-popup-favour-img.commit {
            /*height: 22px;*/
            /*vertical-align: middle;*/
        }

        #cyxy-popup-oppose-img {
            display: inline;
            height: 18px;
            vertical-align: middle;
        }

        #cyxy-popup-favour-num {
            font-size: 14px;
            margin-left: 4px;
            /*margin-left: 0.47rem;*/
            color: #999999;
        }

        #cyxy-popup-oppose-num {
            font-size: 14px;
            margin-left: 4px;
            /*margin-left: 0.47rem;*/
            color: #999;
        }

        .caption-window.ytp-caption-window-bottom {
            width: 800px;
            /* height: 100px; */
            /* margin-left: -400px; */
        }

        @media (max-width: 320px) {
            #cyxy-popup-favour {
                margin-right: 0.8rem;
                margin-left: 1.5rem;
            }

            #cyxy-popup-left-slide {
                margin-right: 0.8rem;
            }

            #cyxy-popup-right-slide {
                margin-left: 1rem;
            }
        }

        .login-hint-a {
            display: block;
            color: #999;
            font-size: 10px;
        }

        .login-hint-a:visited {
            color: #999;
        }

        .layui-m-layer {
            color: #333;
            position: relative;
            z-index: 2147483647;
        }

        .layui-m-layer * {
            -webkit-box-sizing: content-box;
            -moz-box-sizing: content-box;
            box-sizing: content-box;
        }

        .layui-m-layermain,
        .layui-m-layershade {
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        .layui-m-layershade {
            background-color: rgba(0, 0, 0, 0.7);
            pointer-events: auto;
        }

        .layui-m-layermain {
            display: table;
            font-family: Helvetica, arial, sans-serif;
            pointer-events: none;
        }

        .layui-m-layermain .layui-m-layersection {
            display: table-cell;
            vertical-align: middle;
            text-align: center;
        }

        .layui-m-layerchild {
            position: relative;
            display: inline-block;
            text-align: left;
            background-color: #fff;
            font-size: 14px;
            border-radius: 5px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
            pointer-events: auto;
            -webkit-overflow-scrolling: touch;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            -webkit-animation-duration: 0.2s;
            animation-duration: 0.2s;
        }

        @-webkit-keyframes layui-m-anim-scale {
            0% {
                opacity: 0;
                -webkit-transform: scale(0.5);
                transform: scale(0.5);
            }

            100% {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }
        }

        @keyframes layui-m-anim-scale {
            0% {
                opacity: 0;
                -webkit-transform: scale(0.5);
                transform: scale(0.5);
            }

            100% {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }
        }

        .layui-m-anim-scale {
            animation-name: layui-m-anim-scale;
            -webkit-animation-name: layui-m-anim-scale;
        }

        @-webkit-keyframes layui-m-anim-up {
            0% {
                opacity: 0;
                -webkit-transform: translateY(800px);
                transform: translateY(800px);
            }

            100% {
                opacity: 1;
                -webkit-transform: translateY(0);
                transform: translateY(0);
            }
        }

        @keyframes layui-m-anim-up {
            0% {
                opacity: 0;
                -webkit-transform: translateY(800px);
                transform: translateY(800px);
            }

            100% {
                opacity: 1;
                -webkit-transform: translateY(0);
                transform: translateY(0);
            }
        }

        .layui-m-anim-up {
            -webkit-animation-name: layui-m-anim-up;
            animation-name: layui-m-anim-up;
        }

        .layui-m-layer0 .layui-m-layerchild {
            width: 90%;
            max-width: 640px;
        }

        .layui-m-layer1 .layui-m-layerchild {
            border: none;
            border-radius: 0;
        }

        .layui-m-layer2 .layui-m-layerchild {
            width: auto;
            max-width: 260px;
            min-width: 40px;
            border: none;
            background: 0 0;
            box-shadow: none;
            color: #fff;
        }

        .layui-m-layerchild h3 {
            padding: 0 10px;
            height: 60px;
            line-height: 60px;
            font-size: 16px;
            font-weight: 400;
            border-radius: 5px 5px 0 0;
            text-align: center;
        }

        .layui-m-layerbtn span,
        .layui-m-layerchild h3 {
            margin: 0;
            padding: 0;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        .layui-m-layercont {
            padding: 50px 30px;
            line-height: 22px;
            text-align: center;
        }

        .layui-m-layer1 .layui-m-layercont {
            padding: 0;
            text-align: left;
        }

        .layui-m-layer2 .layui-m-layercont {
            text-align: center;
            padding: 0;
            line-height: 0;
        }

        .layui-m-layer2 .layui-m-layercont i {
            width: 25px;
            height: 25px;
            margin-left: 8px;
            display: inline-block;
            background-color: #fff;
            border-radius: 100%;
            -webkit-animation: layui-m-anim-loading 1.4s infinite ease-in-out;
            animation: layui-m-anim-loading 1.4s infinite ease-in-out;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
        }

        .layui-m-layerbtn,
        .layui-m-layerbtn span {
            position: relative;
            text-align: center;
            border-radius: 0 0 5px 5px;
        }

        .layui-m-layer2 .layui-m-layercont p {
            margin-top: 20px;
        }

        @-webkit-keyframes layui-m-anim-loading {

            0%,
            100%,
            80% {
                transform: scale(0);
                -webkit-transform: scale(0);
            }

            40% {
                transform: scale(1);
                -webkit-transform: scale(1);
            }
        }

        @keyframes layui-m-anim-loading {

            0%,
            100%,
            80% {
                transform: scale(0);
                -webkit-transform: scale(0);
            }

            40% {
                transform: scale(1);
                -webkit-transform: scale(1);
            }
        }

        .layui-m-layer2 .layui-m-layercont i:first-child {
            margin-left: 0;
            -webkit-animation-delay: -0.32s;
            animation-delay: -0.32s;
        }

        .layui-m-layer2 .layui-m-layercont i.layui-m-layerload {
            -webkit-animation-delay: -0.16s;
            animation-delay: -0.16s;
        }

        .layui-m-layer2 .layui-m-layercont>div {
            line-height: 22px;
            padding-top: 7px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .layui-m-layerbtn {
            display: box;
            display: -moz-box;
            display: -webkit-box;
            width: 100%;
            height: 50px;
            line-height: 50px;
            font-size: 0;
            border-top: 1px solid #d0d0d0;
            background-color: #f2f2f2;
        }

        .layui-m-layerbtn span {
            display: block;
            -moz-box-flex: 1;
            box-flex: 1;
            -webkit-box-flex: 1;
            font-size: 14px;
            cursor: pointer;
        }

        .layui-m-layerbtn span[yes] {
            color: #40affe;
        }

        .layui-m-layerbtn span[no] {
            border-right: 1px solid #d0d0d0;
            border-radius: 0 0 0 5px;
        }

        .layui-m-layerbtn span:active {
            background-color: #f6f6f6;
        }

        .layui-m-layerend {
            position: absolute;
            right: 7px;
            top: 10px;
            width: 30px;
            height: 30px;
            border: 0;
            font-weight: 400;
            background: 0 0;
            cursor: pointer;
            -webkit-appearance: none;
            font-size: 30px;
        }

        .layui-m-layerend::after,
        .layui-m-layerend::before {
            position: absolute;
            left: 5px;
            top: 15px;
            content: "";
            width: 18px;
            height: 1px;
            background-color: #999;
            transform: rotate(45deg);
            -webkit-transform: rotate(45deg);
            border-radius: 3px;
        }

        .layui-m-layerend::after {
            transform: rotate(-45deg);
            -webkit-transform: rotate(-45deg);
        }

        .layui-m-layer .layui-m-layer-footer {
            position: fixed;
            width: 95%;
            max-width: 100%;
            margin: 0 auto;
            left: 0;
            right: 0;
            bottom: 10px;
            background: 0 0;
        }

        .layui-m-layer-footer .layui-m-layercont {
            padding: 20px;
            border-radius: 5px 5px 0 0;
            background-color: rgba(255, 255, 255, 0.8);
        }

        .layui-m-layer-footer .layui-m-layerbtn {
            display: block;
            height: auto;
            background: 0 0;
            border-top: none;
        }

        .layui-m-layer-footer .layui-m-layerbtn span {
            background-color: rgba(255, 255, 255, 0.8);
        }

        .layui-m-layer-footer .layui-m-layerbtn span[no] {
            color: #fd482c;
            border-top: 1px solid #c2c2c2;
            border-radius: 0 0 5px 5px;
        }

        .layui-m-layer-footer .layui-m-layerbtn span[yes] {
            margin-top: 10px;
            border-radius: 5px;
        }

        .layui-m-layer .layui-m-layer-msg {
            width: auto;
            max-width: 90%;
            margin: 0 auto;
            bottom: -150px;
            background-color: rgba(0, 0, 0, 0.7);
            color: #fff;
        }

        .layui-m-layer-msg .layui-m-layercont {
            padding: 10px 20px;
        }

        .collection-success {
            /*opacity: 0.6;*/
            /*background: #EAEAEA;*/
            color: #ffffff;
        }

        .collection-success:hover {
            /*opacity: 0.45;*/
            /*background: #000000;*/
            color: #ffffff;
        }

        .layui-m-layercont .cyxy-trs-target {
            display: none;
        }

        .collection-icon {
            width: 12px;
            height: 13px;
            background: url("//staging.caiyunapp.com/imgs/layar-target.png") no-repeat;
            display: inline-block;
            background-size: cover;
            background-position: center;
        }

        .collection-success>a {
            margin-left: 12px;
            vertical-align: middle;
        }

        .cy_free_box {
            position: relative;
        }

        .cy_free_box img {
            width: 100%;
            cursor: pointer;
            -moz-user-select: none;
            /*火狐*/
            -webkit-user-select: none;
            /*webkit浏览器*/
            -ms-user-select: none;
            /*IE10*/
            -khtml-user-select: none;
            /*早期浏览器*/
            user-select: none;
        }

        .layui-m-layer-cy_free_content {
            background: inherit !important;
        }</style><style>[_nghost-yyh-c79] {
            height: 100vh
        }

        .weava-ui-wrapper-new[_ngcontent-yyh-c79] {
            box-shadow: #0006 -1px 3px 60px;
            position: fixed;
            top: 0;
            opacity: .99;
            left: initial;
            right: -580px;
            display: block;
            width: 340px;
            min-width: auto;
            height: 100vh;
            z-index: 9999999999996 !important;
            background: white;
            border: none;
            box-sizing: border-box;
            transition: 1s
        }

        .weava-ui-wrapper-new[_ngcontent-yyh-c79]>*[_ngcontent-yyh-c79] {
            display: flex
        }

        .weava-ui-wrapper-new.show[_ngcontent-yyh-c79] {
            right: 0;
            transition: 1s
        }

        .weava-ui-wrapper-new.show.folder-show[_ngcontent-yyh-c79] {
            width: 580px !important
        }

        ngb-toast.weava-notification-frame[_ngcontent-yyh-c79] {
            box-shadow: #0006 -1px 3px 45px !important;
            position: fixed !important;
            left: initial !important;
            top: 20px !important;
            right: 20px !important;
            width: 300px !important;
            border-radius: 4px !important;
            background-color: #142733 !important;
            transition: all .3s ease !important;
            z-index: 999
        }</style><style></style><style>#clipperButton[_ngcontent-yyh-c82] {
            font-size: 30px !important;
            line-height: 30px !important;
            width: 30px !important;
            height: 30px !important;
            text-align: center !important;
            z-index: 2147483647 !important;
            position: absolute !important;
            top: 5px !important;
            right: 5px !important;
            padding: 0 !important;
            background: midnightblue !important;
            color: #fff !important;
            border: none !important;
            outline: none !important;
            border-radius: 50% !important
        }

        .weava-drop-area-wrapper[_ngcontent-yyh-c82] {
            position: fixed !important;
            z-index: 2147483647 !important;
            top: 50% !important;
            width: 70px !important;
            padding-left: 10px;
            left: -10px;
            background-color: #142733 !important;
            transition: left .2s !important;
            opacity: 0;
            display: flex;
            flex-direction: column;
            justify-content: center
        }

        .weava-drop-area-wrapper.weava-drop-area-wrapper-show[_ngcontent-yyh-c82] {
            opacity: 1
        }

        .weava-drop-area-wrapper.weava-drop-area-wrapper-drag-over[_ngcontent-yyh-c82] {
            left: 0
        }

        .weava-drop-area[_ngcontent-yyh-c82] {
            display: inline-flex;
            justify-content: center;
            height: 50px !important;
            background-color: #142733 !important
        }

        .weava-drop-area[_ngcontent-yyh-c82]>img[_ngcontent-yyh-c82] {
            max-width: 80%;
            margin: 3px
        }

        .weava-drop-area-text[_ngcontent-yyh-c82] {
            font-weight: bold !important;
            padding: 7px 0 !important;
            text-align: center !important;
            background-color: #142733 !important;
            color: #cbcbcb !important;
            font-size: 10px !important
        }</style><style>[_nghost-yyh-c49] {
            width: 340px;
            height: 100vh
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-main[_ngcontent-yyh-c49] .logo[_ngcontent-yyh-c49] {
            width: 60%;
            margin-top: 40px
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-main[_ngcontent-yyh-c49] .title[_ngcontent-yyh-c49] {
            max-width: 250px;
            color: #848484;
            font-family: "Lato", "Helvetica Neue", "Helvetica", sans-serif;
            font-size: 18px;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
            margin-top: 24px
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-main[_ngcontent-yyh-c49] .login-btn[_ngcontent-yyh-c49] {
            width: 60%;
            padding: 10px 15px;
            border-radius: 2px;
            border: 1px solid #00b8c2;
            background: #00bfd2;
            color: #fff;
            font-size: 14px;
            text-align: center;
            transition: all .1s ease-in;
            cursor: pointer;
            margin-top: 58px
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-main[_ngcontent-yyh-c49] .login-btn[_ngcontent-yyh-c49]:hover {
            background: #01dccf;
            color: #fff
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-main[_ngcontent-yyh-c49] .login-btn[_ngcontent-yyh-c49]:active,
        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-main[_ngcontent-yyh-c49] .login-btn.active[_ngcontent-yyh-c49] {
            background: #617186;
            border-color: #617186
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-loader[_ngcontent-yyh-c49] .logo-wrap[_ngcontent-yyh-c49] {
            height: 50%;
            max-height: 200px
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-loader[_ngcontent-yyh-c49] .logo-wrap[_ngcontent-yyh-c49] .logo[_ngcontent-yyh-c49] {
            width: 50%
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-loader[_ngcontent-yyh-c49] .logo-wrap[_ngcontent-yyh-c49] .loader[_ngcontent-yyh-c49] {
            width: 40px;
            height: 40px;
            margin-top: 14px
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-loader[_ngcontent-yyh-c49] .login-actions[_ngcontent-yyh-c49] {
            height: 50%;
            max-height: 280px
        }

        [_nghost-yyh-c49] .login-c[_ngcontent-yyh-c49] .login-loader[_ngcontent-yyh-c49] .login-actions[_ngcontent-yyh-c49] .space[_ngcontent-yyh-c49] {
            margin: 20px
        }</style><style>.dashboard-overall-container[_ngcontent-yyh-c70] {
            display: flex;
            background: #f7f7f7;
            flex-direction: row;
            justify-content: flex-start;
            overflow: hidden
        }

        .dashboard-overall-container[_ngcontent-yyh-c70] .app-dashboard-menu-outer-container[_ngcontent-yyh-c70] {
            left: 0;
            width: 240px;
            margin-left: 0;
            transition: all 1s ease
        }

        .dashboard-overall-container[_ngcontent-yyh-c70] .app-dashboard-menu-outer-container.sidebar-hidden[_ngcontent-yyh-c70] {
            margin-left: -240px !important
        }

        .dashboard-overall-container[_ngcontent-yyh-c70] .main-panel-larger-outer-container[_ngcontent-yyh-c70] {
            flex-grow: 1;
            min-width: calc(100% - 240px);
            transition: all 1s ease;
            display: flex;
            flex-direction: column;
            justify-content: flex-start
        }

        .dashboard-overall-container[_ngcontent-yyh-c70] .main-panel-larger-outer-container.sidebar-show[_ngcontent-yyh-c70] {
            margin-left: 240px
        }

        .dashboard-overall-container[_ngcontent-yyh-c70] .main-panel-larger-outer-container[_ngcontent-yyh-c70] .main-dashboard-content[_ngcontent-yyh-c70] {
            display: flex;
            flex-direction: column;
            flex: 1
        }

        .dashboard-overall-container[_ngcontent-yyh-c70] .main-panel-larger-outer-container[_ngcontent-yyh-c70] .main-dashboard-content[_ngcontent-yyh-c70] .app-main-panel-outer-container[_ngcontent-yyh-c70] {
            display: flex;
            flex-direction: column;
            flex: 1
        }

        .dashboard-overall-container[_ngcontent-yyh-c70] .main-panel-larger-outer-container[_ngcontent-yyh-c70] .main-dashboard-content[_ngcontent-yyh-c70] .app-main-panel-outer-container[_ngcontent-yyh-c70] app-main-panel[_ngcontent-yyh-c70] {
            display: flex;
            flex-direction: column;
            flex: 1
        }</style><style>.menu-panel[_ngcontent-yyh-c58] {
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            overflow-y: hidden;
            width: 240px;
            height: 100%;
            height: 100vh;
            border: none;
            border: 0;
            margin: 0;
            padding-top: 0;
            padding-bottom: 0;
            background: #142733
        }

        .menu-header[_ngcontent-yyh-c58] {
            height: 41px;
            background: rgba(0, 0, 0, .05);
            color: #fff;
            text-align: left
        }

        .menu-body[_ngcontent-yyh-c58] {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-height: 140px;
            overflow-y: auto
        }

        .menu-body[_ngcontent-yyh-c58]::-webkit-scrollbar-track {
            display: initial;
            box-shadow: inset 0 0 6px #0000004d;
            border-radius: 10px;
            background-color: #1e3c4e
        }

        .menu-body[_ngcontent-yyh-c58]::-webkit-scrollbar {
            display: initial;
            background-color: #142733;
            width: .7em;
            border-radius: 10px
        }

        .menu-body[_ngcontent-yyh-c58]::-webkit-scrollbar-thumb {
            display: initial;
            border-radius: 10px;
            box-shadow: inset 0 0 6px #0000004d;
            background-color: #01dccf
        }

        .menu-footer[_ngcontent-yyh-c58] {
            bottom: 0;
            border-top: 1px;
            border-top-color: #00b8c2
        }

        .menu-panel-dim-background[_ngcontent-yyh-c58] {
            position: fixed;
            z-index: 500;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            display: none;
            background: #000;
            opacity: .5
        }

        .menu-panel-dim-background.active[_ngcontent-yyh-c58] {
            display: block
        }

        .gistnote-header-wrapper[_ngcontent-yyh-c58] {
            display: flex;
            align-items: center;
            padding: 14px;
            height: inherit;
            color: #fff;
            font-weight: normal;
            font-weight: 500;
            font-size: 18px;
            text-align: left
        }

        .gistnote-header-logo[_ngcontent-yyh-c58] {
            width: 28px
        }

        .gistnote-header-logo-wrapper[_ngcontent-yyh-c58] {
            display: flex;
            padding: 0 12px
        }

        .gistnote-header-title-wrapper[_ngcontent-yyh-c58] {
            padding-right: 75px
        }

        .gistnote-header-icons-wrapper[_ngcontent-yyh-c58] {
            height: 41px;
            padding-left: 40px
        }

        .gistnote-header-icons-wrapper[_ngcontent-yyh-c58]:hover {
            background: rgba(0, 0, 0, .4)
        }

        .gistnote-header-icons-wrapper[_ngcontent-yyh-c58]:active {
            background: rgba(0, 0, 0, .6)
        }</style><style>.dashboard-nav-header[_ngcontent-yyh-c62] {
            position: fixed;
            z-index: 51;
            top: 0;
            display: block;
            width: 340px;
            height: 47px;
            min-height: 28px;
            margin: 0 auto;
            background: white;
            text-align: center
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .dashboard-main[_ngcontent-yyh-c62] {
            height: inherit
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .dashboard-folder-colors[_ngcontent-yyh-c62] {
            padding: 5px 5px 5px 18px
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .dashboard-folder-colors[_ngcontent-yyh-c62] app-color-item[_ngcontent-yyh-c62] {
            opacity: .4
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .dashboard-folder-colors[_ngcontent-yyh-c62] app-color-item.color-selected[_ngcontent-yyh-c62] {
            opacity: 1 !important
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .dashboard-search-filter-text[_ngcontent-yyh-c62] {
            background: #fffbe6;
            color: #5b3b08;
            text-align: left;
            padding: 5px 5px 5px 18px;
            font-size: 13px
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .menu-button[_ngcontent-yyh-c62] {
            border: 0;
            background: transparent;
            opacity: .55;
            padding: 10px;
            height: 100%
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .menu-button[_ngcontent-yyh-c62] .menu-icon[_ngcontent-yyh-c62] {
            width: 15px;
            height: 15px;
            vertical-align: middle
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .collection-title[_ngcontent-yyh-c62] {
            opacity: 1;
            font-size: 18px;
            font-weight: 400;
            overflow: hidden;
            line-height: 16px;
            text-overflow: ellipsis;
            width: max-content;
            max-width: 186px;
            cursor: pointer;
            transition: all .2s ease
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .nav-collection-title-wrapper[_ngcontent-yyh-c62] {
            width: 100%;
            flex: 1;
            flex-basis: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-end
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .nav-collection-title-wrapper[_ngcontent-yyh-c62] .search-form[_ngcontent-yyh-c62] {
            flex: 1
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .nav-collection-title-wrapper[_ngcontent-yyh-c62] .search-form[_ngcontent-yyh-c62] input[_ngcontent-yyh-c62] {
            width: 100%;
            outline: none;
            overflow: hidden;
            margin-left: 2px;
            border: 0;
            box-shadow: none;
            background-color: transparent;
            font-size: 14px;
            color: #d3d3d3;
            font-weight: lighter;
            font-weight: 300;
            line-height: 23px;
            text-overflow: ellipsis;
            white-space: nowrap
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .nav-menu-wrapper[_ngcontent-yyh-c62] {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .nav-menu-active-folder[_ngcontent-yyh-c62] {
            max-width: 270px
        }

        .dashboard-nav-header[_ngcontent-yyh-c62] .nav-menu-action-buttons[_ngcontent-yyh-c62] {
            margin-left: auto;
            padding: 0 10px
        }

        .dashboard-nav-search-mode[_ngcontent-yyh-c62] {
            background: #0c1f29
        }

        .dashboard-folder-colors-wrapper[_ngcontent-yyh-c62] {
            background: #142733
        }

        .dashboard-folder-colors[_ngcontent-yyh-c62] {
            display: flex
        }

        .dashboard-folder-colors[_ngcontent-yyh-c62] app-color-item[_ngcontent-yyh-c62] {
            padding: 0 2px
        }

        .nav-menu-action-buttons app-button button {
            padding: 6px !important
        }

        .nav-menu-action-buttons .nav-menu-home-button.button {
            background: #727272 !important
        }

        .nav-menu-action-buttons .nav-menu-home-button.button:hover {
            background: #595959 !important
        }

        .nav-menu-action-buttons .nav-menu-home-button.button img {
            opacity: 1
        }

        .nav-menu-action-buttons .nav-menu-home-button.button .icon-button-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            border-radius: 50%;
            font-size: 10px;
            font-weight: bold;
            background: #fa3e3e;
            color: #fff;
            padding: 9px 3px;
            width: 18px;
            height: 18px;
            text-align: center
        }

        .dashboard-nav-search-mode .nav-menu-action-buttons .button,
        .dashboard-nav-search-mode .nav-menu-action-buttons app-button {
            background: transparent !important
        }

        .dashboard-nav-search-mode .nav-menu-action-buttons .button:hover,
        .dashboard-nav-search-mode .nav-menu-action-buttons app-button:hover {
            background: transparent !important
        }

        .dashboard-nav-search-mode .nav-menu-action-buttons .button:hover img,
        .dashboard-nav-search-mode .nav-menu-action-buttons app-button:hover img {
            opacity: 1
        }</style><style>[_nghost-yyh-c67] {
            width: 100%;
            position: relative
        }

        div.main-panel-body[_ngcontent-yyh-c67] {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            padding: 50px 0 150px;
            position: absolute;
            overflow-x: hidden;
            overflow-y: auto;
            max-height: 100%;
            width: 100%
        }

        .main-panel-footer[_ngcontent-yyh-c67] {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 12px;
            border-top: 1px solid #efefef;
            background: #fdfdfd;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image[_ngcontent-yyh-c67] {
            padding: 6px;
            background: white;
            border: #efefef;
            border-radius: 50%;
            box-shadow: 0 2px 3px #000c;
            transition: all .3s ease;
            cursor: pointer !important
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image[_ngcontent-yyh-c67]:hover {
            opacity: 1;
            box-shadow: 0 2px 6px #00000080
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image[_ngcontent-yyh-c67]:active {
            transform: rotate(45deg);
            background: #525252
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image.nav-icon-pinned[_ngcontent-yyh-c67] {
            transform: rotate(45deg);
            background: #00b8c2;
            box-shadow: none
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image[_ngcontent-yyh-c67]>img[_ngcontent-yyh-c67] {
            opacity: 0;
            transition: .3s all ease
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image[_ngcontent-yyh-c67]>img.pin-icon[_ngcontent-yyh-c67] {
            position: absolute;
            width: 20px;
            height: 20px
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image[_ngcontent-yyh-c67]>img.pin-border[_ngcontent-yyh-c67] {
            width: 20px;
            height: 20px
        }

        .main-panel-footer[_ngcontent-yyh-c67] .pin-image[_ngcontent-yyh-c67]>img.opacity-show[_ngcontent-yyh-c67] {
            opacity: .9
        }

        .main-panel-footer[_ngcontent-yyh-c67] .highlight-toggle[_ngcontent-yyh-c67] {
            margin-left: auto
        }

        .main-panel-action-buttons[_ngcontent-yyh-c67] {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 30px 15px
        }

        .main-panel-action-buttons-column[_ngcontent-yyh-c67] {
            padding: 0 7px;
            text-align: center;
            display: inline-flex;
            flex-direction: column;
            justify-content: stretch
        }

        .main-panel-action-button-description[_ngcontent-yyh-c67] {
            font-size: 10px;
            text-transform: uppercase;
            margin-bottom: 10px;
            line-height: 14px;
            color: #9f9f9f
        }

        .main-panel-action-buttons-column app-button .button {
            width: 100%;
            font-size: 12px;
            padding: 9px;
            justify-content: unset
        }

        .main-panel-upgrade-button[_ngcontent-yyh-c67] {
            text-align: left;
            font-size: 13px;
            text-decoration: underline;
            cursor: pointer;
            color: #00b8c2
        }</style><style>.folder-list[_ngcontent-yyh-c52] ul[_ngcontent-yyh-c52] {
            list-style: none !important;
            margin: 0;
            padding: 0
        }

        .folder-list[_ngcontent-yyh-c52] ul[_ngcontent-yyh-c52] li[_ngcontent-yyh-c52]>div.spacer[_ngcontent-yyh-c52] {
            padding-bottom: 3px
        }

        .folder-list[_ngcontent-yyh-c52] ul[_ngcontent-yyh-c52] li[_ngcontent-yyh-c52]>div.spacer.droppable[_ngcontent-yyh-c52] {
            border-bottom: 3px dotted #00b8c280;
            padding-bottom: 0
        }

        .folder-list[_ngcontent-yyh-c52] ul[_ngcontent-yyh-c52] li[_ngcontent-yyh-c52]>div.spacer.dragged[_ngcontent-yyh-c52] {
            border-bottom: 3px solid #00b8c2;
            padding-bottom: 0
        }</style><style>[_nghost-yyh-c54] .create-folder-wrapper[_ngcontent-yyh-c54] {
            display: flex;
            padding: 7px 0
        }

        [_nghost-yyh-c54] .create-folder-wrapper[_ngcontent-yyh-c54] .folder-icons[_ngcontent-yyh-c54] {
            opacity: .7;
            color: #fff;
            transition: all .12s ease
        }

        [_nghost-yyh-c54] .create-folder-wrapper[_ngcontent-yyh-c54] .folder-icons.folder-action-icon[_ngcontent-yyh-c54] {
            font-size: 14px;
            padding: 3.5px;
            margin: 0 2px
        }

        [_nghost-yyh-c54] .create-folder-wrapper[_ngcontent-yyh-c54] .folder-icons.folder-action-icon[_ngcontent-yyh-c54]:disabled {
            cursor: no-drop;
            background-color: #9f9f9f;
            opacity: .7
        }

        [_nghost-yyh-c54] .create-folder-wrapper[_ngcontent-yyh-c54] .folder-icons.folder-action-icon[_ngcontent-yyh-c54]:hover {
            opacity: 1
        }

        [_nghost-yyh-c54] .create-folder-wrapper[_ngcontent-yyh-c54] .create-folder-input[_ngcontent-yyh-c54] {
            flex-grow: 1;
            margin-right: 10px;
            font-size: 13px;
            font-weight: 300;
            border-radius: 2px;
            line-height: 20px
        }

        [_nghost-yyh-c54] .create-folder-wrapper[_ngcontent-yyh-c54] .create-folder-input[_ngcontent-yyh-c54]:focus {
            outline: 0;
            border: 1px solid #01dccf
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] {
            padding: 0 10px !important
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .folder-icon-folder[_ngcontent-yyh-c54] {
            margin-right: 5px;
            color: #88a7cd;
            font-size: 34px;
            opacity: 1 !important
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] {
            display: flex;
            flex-direction: column
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] {
            display: flex;
            align-content: space-evenly;
            justify-content: flex-start
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] .action-button[_ngcontent-yyh-c54] {
            margin-top: 5px;
            border: 1px solid #00b8c2;
            background: #00bfd2;
            color: #fff;
            font-size: 12px;
            transition: all .12s ease-in;
            cursor: pointer
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] .action-button[_ngcontent-yyh-c54]:hover {
            background: #01dccf;
            border-color: #01dccf;
            color: #fff
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] .action-button[_ngcontent-yyh-c54]:active {
            background: #617186;
            border-color: #617186;
            color: #fff
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] .action-button[_ngcontent-yyh-c54]:disabled {
            background: #e4e4e4;
            border-color: #e4e4e4;
            cursor: not-allowed
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] .cancel-button[_ngcontent-yyh-c54] {
            margin-top: 5px;
            margin-left: 5px;
            font-size: 12px;
            color: #5d5d5d;
            cursor: pointer
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] .cancel-button[_ngcontent-yyh-c54]:hover {
            color: #fff
        }

        [_nghost-yyh-c54] .create-folder-wrapper.main-panel[_ngcontent-yyh-c54] .input-wrapper[_ngcontent-yyh-c54] .button-wrapper[_ngcontent-yyh-c54] .cancel-button[_ngcontent-yyh-c54]:disabled {
            cursor: not-allowed
        }</style><style>[_nghost-yyh-c55] {
            margin-top: auto
        }

        .rating-link-wrapper[_ngcontent-yyh-c55] {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            padding: 7px;
            text-align: left;
            background: rgba(0, 0, 0, .19);
            color: #fff
        }

        .rating-link[_ngcontent-yyh-c55] {
            cursor: pointer;
            color: #fff
        }

        .rating-close-button[_ngcontent-yyh-c55] {
            cursor: pointer
        }

        .rating-close-button[_ngcontent-yyh-c55] img[_ngcontent-yyh-c55] {
            padding: 5px
        }

        .rating-link-wrapper[_ngcontent-yyh-c55]>a[_ngcontent-yyh-c55] {
            font-size: 13px;
            color: #01dccf;
            text-decoration: underline;
            cursor: pointer
        }

        .rating-close-button[_ngcontent-yyh-c55] {
            padding: 0
        }

        .rating-close-button[_ngcontent-yyh-c55] img[_ngcontent-yyh-c55] {
            opacity: .7;
            width: 30px;
            height: 30px
        }

        .rating-close-button[_ngcontent-yyh-c55] img[_ngcontent-yyh-c55]:hover {
            opacity: 1
        }</style><style>.menu-panel-footer[_ngcontent-yyh-c56] {
            z-index: 100;
            width: 240px;
            background: #102029
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .link[_ngcontent-yyh-c56],
        .menu-panel-footer[_ngcontent-yyh-c56] .link-storage-upgrade[_ngcontent-yyh-c56] {
            color: #fff;
            font-size: 14px;
            opacity: .7;
            transition: all .2s ease;
            text-decoration: none
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .link-storage-upgrade[_ngcontent-yyh-c56] {
            color: #00b8c2
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .link[_ngcontent-yyh-c56]:hover,
        .menu-panel-footer[_ngcontent-yyh-c56] .link-storage-upgrade[_ngcontent-yyh-c56]:hover,
        .menu-panel-footer[_ngcontent-yyh-c56] .link[_ngcontent-yyh-c56]:active,
        .menu-panel-footer[_ngcontent-yyh-c56] .link-storage-upgrade[_ngcontent-yyh-c56]:active {
            opacity: 1
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56] {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(0, 0, 0, .19);
            opacity: .7;
            cursor: pointer
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56]:hover,
        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56]:active {
            opacity: 1
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56] .menu-footer-text[_ngcontent-yyh-c56] {
            color: #fff;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            vertical-align: middle;
            font-size: 14px
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56] .menu-footer-text[_ngcontent-yyh-c56]:hover {
            opacity: 1
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56] .menu-footer-icon-wrapper[_ngcontent-yyh-c56] {
            width: 40px;
            opacity: .4;
            text-align: right;
            cursor: pointer
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56] .menu-footer-icon-wrapper[_ngcontent-yyh-c56]:hover {
            opacity: 1
        }

        .menu-panel-footer[_ngcontent-yyh-c56] .menu-item[_ngcontent-yyh-c56] .menu-footer-icon-wrapper[_ngcontent-yyh-c56] .my-account-icon[_ngcontent-yyh-c56] {
            vertical-align: middle;
            width: 23px;
            height: 23px
        }

        .storage[_ngcontent-yyh-c56] {
            padding-top: 10px;
            border-top: 1px solid #00b8c2;
            margin: 0 15px 25px
        }

        .storage[_ngcontent-yyh-c56]>h6[_ngcontent-yyh-c56] {
            color: #fff;
            margin: 10px 0 15px
        }

        .storage[_ngcontent-yyh-c56]>div.storage-value[_ngcontent-yyh-c56] {
            color: #fff;
            font-size: 10px;
            margin: 10px 0 20px
        }

        .storage[_ngcontent-yyh-c56]>div.storage-value[_ngcontent-yyh-c56]>span[_ngcontent-yyh-c56] {
            opacity: .7
        }

        .storage[_ngcontent-yyh-c56]>div.storage-value[_ngcontent-yyh-c56]>span.text-danger[_ngcontent-yyh-c56] {
            opacity: 1
        }</style><style>.button[_ngcontent-yyh-c61] {
            font-size: 14px;
            display: inline-flex;
            justify-content: space-between;
            align-items: center;
            position: relative !important;
            border: 0;
            border-radius: 3px;
            cursor: pointer;
            transition: .25s;
            color: #303030;
            padding: .57em;
            background: #fff !important;
            box-shadow: inset 0 0 0 .5px #0000003d, inset 0 -.5px #0000002b
        }

        .button[_ngcontent-yyh-c61]:hover {
            background: linear-gradient(to bottom, rgba(255, 255, 255, .4), rgba(0, 0, 0, .03)) !important;
            color: #363636;
            opacity: 1
        }

        .button-icon[_ngcontent-yyh-c61] {
            display: inline-block;
            line-height: 0
        }

        .button.button-rounded[_ngcontent-yyh-c61] {
            border-radius: 50%
        }

        .button.button-transparent[_ngcontent-yyh-c61] {
            background-color: transparent;
            box-shadow: none
        }

        .button.button-transparent[_ngcontent-yyh-c61]:hover {
            background-color: #0000001a !important
        }

        .button-icon img {
            width: 20px;
            height: 20px;
            opacity: .6
        }</style><style>.main-panel-filter[_ngcontent-yyh-c64] {
            min-height: 30px;
            padding: 4px;
            display: flex;
            align-items: center
        }

        .main-panel-filter[_ngcontent-yyh-c64] .header-button[_ngcontent-yyh-c64] {
            border: 1px solid #e1e1e1;
            border-radius: 3px;
            padding: 3px 6px
        }

        .main-panel-filter[_ngcontent-yyh-c64] .header-button[_ngcontent-yyh-c64] img[_ngcontent-yyh-c64] {
            width: 11px;
            height: 18px;
            vertical-align: middle;
            opacity: .5
        }

        .main-panel-filter[_ngcontent-yyh-c64] .header-button[_ngcontent-yyh-c64] img[_ngcontent-yyh-c64]:disabled {
            cursor: no-drop
        }

        .main-panel-filter[_ngcontent-yyh-c64] .header-button[_ngcontent-yyh-c64] .new-folder-icon[_ngcontent-yyh-c64] {
            width: 15px !important
        }

        .main-panel-filter[_ngcontent-yyh-c64] .header-button[_ngcontent-yyh-c64] .toggle-highlight-icon[_ngcontent-yyh-c64] {
            width: 8px !important
        }

        .main-panel-filter[_ngcontent-yyh-c64] .header-button[_ngcontent-yyh-c64] .sort-highlight-icon[_ngcontent-yyh-c64] {
            width: 16px !important
        }

        .main-panel-filter[_ngcontent-yyh-c64] .main-panel-upgrade-button[_ngcontent-yyh-c64] {
            font-size: 13px;
            text-decoration: underline;
            cursor: pointer;
            color: #00b8c2;
            box-shadow: none;
            border: 0;
            background: transparent
        }

        .main-panel-filter[_ngcontent-yyh-c64] .spacer[_ngcontent-yyh-c64] {
            flex: 1
        }

        .main-panel-filter[_ngcontent-yyh-c64] app-toggle-icon-button button {
            padding: 0
        }</style><style>[_nghost-yyh-c65] {
            display: flex;
            flex-direction: row;
            align-self: center
        }

        [_nghost-yyh-c65] .toggle-label[_ngcontent-yyh-c65] {
            align-self: center;
            padding-right: 10px;
            color: #303030;
            font-size: 13px;
            font-weight: lighter;
            line-height: 16px;
            cursor: default
        }

        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] {
            display: block;
            position: relative;
            width: 52px;
            height: 25px
        }

        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] input[type=checkbox][_ngcontent-yyh-c65] {
            display: none
        }

        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] input[type=checkbox][_ngcontent-yyh-c65]:checked~.toggle-button-switch[_ngcontent-yyh-c65] {
            left: 27px
        }

        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] input[type=checkbox][_ngcontent-yyh-c65]:checked~.toggle-button-text[_ngcontent-yyh-c65] {
            background-color: #00b8c2
        }

        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] .toggle-button-switch[_ngcontent-yyh-c65] {
            position: absolute;
            top: 1px;
            left: 1px;
            width: 24px;
            height: 24px;
            background-color: #fff;
            border-radius: 100%;
            cursor: pointer;
            z-index: 100;
            transition: left .3s
        }

        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] .toggle-button-text[_ngcontent-yyh-c65] {
            overflow: hidden;
            background-color: #ccc;
            border-radius: 12.5px;
            box-shadow: 1px 1px 2.5px #323232bf;
            transition: background-color .3s
        }

        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] .toggle-button-text-on[_ngcontent-yyh-c65],
        [_nghost-yyh-c65] .toggle-component[_ngcontent-yyh-c65] .toggle-button-text-off[_ngcontent-yyh-c65] {
            float: left;
            width: 50%;
            height: 100%;
            line-height: 25px;
            font-size: 12px;
            font-family: "Lato", "Helvetica Neue", "Helvetica", sans-serif;
            font-weight: bold;
            color: #fff;
            text-align: center
        }</style><style>ngb-alert {
            display: block
        }</style><style>.loading-state-wrapper[_ngcontent-yyh-c66] {
            display: flex;
            flex-grow: 1;
            align-content: space-around;
            justify-content: center;
            height: 100%
        }

        .loading-state-wrapper.full-page[_ngcontent-yyh-c66] {
            width: 100%;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-color: #fff;
            text-align: center
        }

        .loading-state-wrapper.full-page[_ngcontent-yyh-c66] .loading-state-image-wrapper[_ngcontent-yyh-c66] {
            align-items: center
        }

        .loading-state-wrapper[_ngcontent-yyh-c66] .loading-state-image-wrapper[_ngcontent-yyh-c66] {
            display: flex
        }

        .loading-state-wrapper[_ngcontent-yyh-c66] .loading-state-image-wrapper[_ngcontent-yyh-c66] img.logo-image[_ngcontent-yyh-c66] {
            width: 300px;
            vertical-align: middle;
            padding: 20px
        }

        .loading-state-wrapper[_ngcontent-yyh-c66] .loading-state-image-wrapper[_ngcontent-yyh-c66] img.loading[_ngcontent-yyh-c66] {
            vertical-align: middle;
            width: 20px;
            height: 20px
        }

        .loading-state-wrapper[_ngcontent-yyh-c66] .loading-state-image-wrapper[_ngcontent-yyh-c66] img.loading.full-page[_ngcontent-yyh-c66] {
            width: 30px;
            height: 30px
        }</style><style>[_nghost-yyh-c53] {
            position: relative;
            padding: 0;
            display: block;
            width: 100%
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] {
            color: #fff;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: stretch
        }

        [_nghost-yyh-c53] .folder.main-panel[_ngcontent-yyh-c53] {
            color: #303030
        }

        [_nghost-yyh-c53] .folder.main-panel[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-title[_ngcontent-yyh-c53] {
            opacity: 1 !important
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] {
            width: 100%
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53]:hover .folder-action[_ngcontent-yyh-c53] {
            display: flex !important
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] {
            display: flex;
            align-items: center;
            height: 30px;
            width: 100%;
            padding: 0 10px
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper.droppable-content--zone[_ngcontent-yyh-c53] {
            border-bottom: 2px solid #00b8c2
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper.active[_ngcontent-yyh-c53]:after {
            position: absolute;
            background: rgba(255, 255, 255, .15);
            top: 0;
            right: 0;
            width: 240px;
            height: 30px;
            content: ""
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper.active[_ngcontent-yyh-c53] .folder-icon-folder[_ngcontent-yyh-c53] {
            opacity: 1 !important
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper.active[_ngcontent-yyh-c53] .folder-title[_ngcontent-yyh-c53] {
            opacity: 1 !important
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper.active[_ngcontent-yyh-c53] .folder-icon-arrow[_ngcontent-yyh-c53] {
            opacity: .7
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] {
            display: flex;
            align-items: center;
            max-width: calc(100% - 25px)
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents.droppable-content--zone[_ngcontent-yyh-c53] {
            border-bottom: 2px solid #00b8c2
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] {
            max-width: 90%
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] .folder-link[_ngcontent-yyh-c53] {
            display: flex;
            flex-direction: column
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] .folder-link[_ngcontent-yyh-c53],
        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] .folder-link[_ngcontent-yyh-c53]:hover {
            text-decoration: none;
            color: inherit
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] .folder-link[_ngcontent-yyh-c53]:focus {
            outline: none !important
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] .folder-link[_ngcontent-yyh-c53] .folder-title[_ngcontent-yyh-c53] {
            opacity: .5;
            font-size: 14px;
            font-weight: 400
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] .folder-link[_ngcontent-yyh-c53] .folder-title[_ngcontent-yyh-c53]:hover {
            opacity: 1
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-contents[_ngcontent-yyh-c53] .folder-text[_ngcontent-yyh-c53] .folder-create-date[_ngcontent-yyh-c53] {
            font-size: 10px;
            color: #9f9f9f
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-action-wrapper[_ngcontent-yyh-c53] {
            position: absolute;
            z-index: 10;
            top: 6px;
            right: 10px
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-action-wrapper[_ngcontent-yyh-c53] .folder-action[_ngcontent-yyh-c53] {
            display: none
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-action-wrapper[_ngcontent-yyh-c53] .folder-action[_ngcontent-yyh-c53] .folder-icons[_ngcontent-yyh-c53] {
            opacity: 1;
            border: 1px solid rgba(221, 221, 221, .15);
            background-color: #085a90;
            color: #fff;
            box-shadow: 0 2px 2px #00000073;
            transition: all .12s ease
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-action-wrapper[_ngcontent-yyh-c53] .folder-action[_ngcontent-yyh-c53] .folder-icons.folder-action-icon[_ngcontent-yyh-c53] {
            font-size: 14px;
            padding: 3.5px;
            margin: 0 2px
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body[_ngcontent-yyh-c53] .folder-wrapper[_ngcontent-yyh-c53] .folder-action-wrapper[_ngcontent-yyh-c53] .folder-action[_ngcontent-yyh-c53] .folder-icons.folder-action-icon[_ngcontent-yyh-c53]:disabled {
            cursor: no-drop;
            background-color: #9f9f9f
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body.droppable-content--zone[_ngcontent-yyh-c53] {
            background: #00c7d1b7
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body.droppable-content--zone[_ngcontent-yyh-c53] .folder-icon-folder[_ngcontent-yyh-c53] {
            opacity: 1
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body.droppable-content--zone[_ngcontent-yyh-c53] .folder-title[_ngcontent-yyh-c53] {
            opacity: 1
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-body.droppable-content--zone[_ngcontent-yyh-c53] .folder-icon-arrow[_ngcontent-yyh-c53] {
            opacity: .7
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-icons[_ngcontent-yyh-c53] {
            opacity: .5
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-icons.folder-icon-folder[_ngcontent-yyh-c53] {
            font-size: 21px
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-icons.folder-icon-folder.main-panel[_ngcontent-yyh-c53] {
            font-size: 34px;
            opacity: 1 !important
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-icons.folder-icon-arrow[_ngcontent-yyh-c53] {
            font-size: 16px;
            opacity: .3
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-icon-arrow[_ngcontent-yyh-c53] {
            margin-right: 5px;
            color: #fff;
            opacity: .7
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-icon-arrow.hidden[_ngcontent-yyh-c53] {
            opacity: 0 !important;
            display: block !important
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-icon-folder[_ngcontent-yyh-c53] {
            margin-right: 5px;
            color: #88a7cd
        }

        [_nghost-yyh-c53] .folder[_ngcontent-yyh-c53] .folder-nested[_ngcontent-yyh-c53] {
            padding-left: 32px
        }

        app-main-panel-folder-list[_nghost-yyh-c53] .folder-text[_ngcontent-yyh-c53],
        app-main-panel-folder-list [_nghost-yyh-c53] .folder-text[_ngcontent-yyh-c53] {
            max-width: 80% !important
        }</style><style>.weava-promo-ads-wrapper[_ngcontent-yyh-c77] {
            position: relative
        }

        .weava-promo-ads-button[_ngcontent-yyh-c77] {
            margin: 5px 0;
            padding: 0 6px
        }

        .weava-promo-close-ads-button[_ngcontent-yyh-c77] {
            position: absolute;
            bottom: 15px;
            color: #929292;
            font-size: 14px;
            right: 132px
        }

        .folder-body[_ngcontent-yyh-c77] {
            width: 100%
        }

        .folder-body[_ngcontent-yyh-c77]:hover .folder-action[_ngcontent-yyh-c77] {
            display: flex !important
        }

        .folder-body[_ngcontent-yyh-c77] .folder-wrapper[_ngcontent-yyh-c77] {
            display: flex;
            align-items: center;
            height: 30px;
            width: 100%;
            padding: 0 10px
        }

        .folder-body[_ngcontent-yyh-c77] .folder-wrapper[_ngcontent-yyh-c77] .folder-contents[_ngcontent-yyh-c77] {
            display: flex;
            align-items: center
        }

        .folder-body[_ngcontent-yyh-c77] .folder-icons[_ngcontent-yyh-c77] {
            font-size: 34px
        }</style><style>.folder-card-list[_ngcontent-yyh-c71] {
            font-size: 14px
        }

        .folder-card-list[_ngcontent-yyh-c71] .folder-card[_ngcontent-yyh-c71] {
            padding: 5px 0
        }</style><style>.auth-notification[_ngcontent-yyh-c76] {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 12px;
            font-size: 12px
        }

        .auth-notification[_ngcontent-yyh-c76] img[_ngcontent-yyh-c76] {
            width: 32px;
            height: 32px;
            margin-right: 10px
        }</style><style>.main-panel-website-card[_ngcontent-yyh-c75] {
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            cursor: pointer
        }

        .main-panel-website-card[_ngcontent-yyh-c75]:hover .main-panel-website-card-favicon[_ngcontent-yyh-c75] fa-icon[_ngcontent-yyh-c75] {
            opacity: .6
        }

        .main-panel-website-card[_ngcontent-yyh-c75]:hover {
            background: #f2f2f2
        }

        .main-panel-website-card[_ngcontent-yyh-c75]:hover .main-panel-website-card-actions[_ngcontent-yyh-c75] {
            display: block
        }

        .main-panel-website-card-favicon[_ngcontent-yyh-c75] {
            display: flex;
            align-items: center;
            margin-right: 5px;
            font-size: 12px
        }

        .main-panel-website-card-favicon[_ngcontent-yyh-c75] fa-icon[_ngcontent-yyh-c75] {
            opacity: 0;
            transition: all .2s;
            margin-right: 5px;
            visibility: hidden
        }

        .main-panel-website-card-favicon[_ngcontent-yyh-c75] fa-icon.show[_ngcontent-yyh-c75] {
            visibility: visible
        }

        .main-panel-website-card-favicon[_ngcontent-yyh-c75] img[_ngcontent-yyh-c75],
        .main-panel-website-card-favicon[_ngcontent-yyh-c75] .img-placeholder[_ngcontent-yyh-c75] {
            width: 30px;
            height: 30px
        }

        .main-panel-website-card-data[_ngcontent-yyh-c75] {
            display: inline-flex;
            flex-direction: column;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
            margin-left: auto
        }

        .main-panel-website-card-data[_ngcontent-yyh-c75] a[_ngcontent-yyh-c75] {
            text-decoration: none;
            color: #303030;
            font-size: 13px;
            margin-bottom: 5px
        }

        .main-panel-website-card-data[_ngcontent-yyh-c75] a[_ngcontent-yyh-c75]:hover {
            text-decoration: underline
        }

        .main-panel-website-stat[_ngcontent-yyh-c75] {
            font-size: 10px;
            color: #9f9f9f
        }

        .main-panel-website-card-actions[_ngcontent-yyh-c75] {
            margin-left: auto;
            display: none
        }

        .main-panel-website-card-colors[_ngcontent-yyh-c75] {
            display: inline-block;
            vertical-align: middle
        }

        .main-panel-website-card-color[_ngcontent-yyh-c75] {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 3px
        }</style><style>[_nghost-yyh-c73] img[_ngcontent-yyh-c73] {
            max-width: 100%;
            cursor: pointer
        }

        .highlight-card[_ngcontent-yyh-c73] {
            position: relative;
            background: #ffffff;
            border-bottom: 1px solid rgba(0, 0, 0, .05);
            font-family: "Lato", "Helvetica Neue", "Helvetica", sans-serif;
            font-size: 13px;
            line-height: 17px;
            cursor: pointer;
            border: 0;
            border-left: 5px solid
        }

        .highlight-card[_ngcontent-yyh-c73]:hover {
            background-color: #f9f9f9
        }

        .highlight-card[_ngcontent-yyh-c73]:hover .highlight-card-actions[_ngcontent-yyh-c73] {
            display: block
        }

        .highlight-card-text[_ngcontent-yyh-c73] {
            padding: 8px 10px
        }

        .highlight-card-image[_ngcontent-yyh-c73]>img[_ngcontent-yyh-c73] {
            width: 100%
        }

        .highlight-card-note[_ngcontent-yyh-c73] {
            margin: 10px 10px 10px 0;
            padding: 20px 10px;
            border-left: 3px solid rgba(0, 0, 0, .05);
            background: #fff;
            color: #889da2
        }

        .highlight-card-note.disabled[_ngcontent-yyh-c73] {
            background: #e4e4e4;
            color: #9f9f9f !important
        }

        .highlight-card-note[_ngcontent-yyh-c73]:hover .highlight-card-note-actions[_ngcontent-yyh-c73] {
            display: block
        }

        .highlight-card-note[_ngcontent-yyh-c73]:hover {
            box-shadow: 0 1px 4px #0000004d;
            color: #303030
        }

        .highlight-card-note-feedback[_ngcontent-yyh-c73] {
            position: absolute;
            left: 20px;
            bottom: 20px;
            color: #889da2;
            font-size: 11px
        }

        .highlight-card-note-feedback.error[_ngcontent-yyh-c73] {
            color: #a65757
        }

        .highlight-card-note-actions[_ngcontent-yyh-c73] {
            position: absolute;
            right: 20px;
            bottom: 20px
        }

        .highlight-card-note-actions[_ngcontent-yyh-c73] {
            display: none
        }

        .highlight-card-note-actions[_ngcontent-yyh-c73] button[_ngcontent-yyh-c73] {
            color: #0fa0a3;
            background: none !important;
            font-size: 11px
        }

        .highlight-card-note-actions[_ngcontent-yyh-c73] button[disabled][_ngcontent-yyh-c73] {
            opacity: .5;
            cursor: not-allowed
        }

        .highlight-card-note-actions[_ngcontent-yyh-c73] button[_ngcontent-yyh-c73]:hover {
            text-decoration: underline
        }

        .highlight-card-actions[_ngcontent-yyh-c73] {
            position: absolute;
            display: none;
            top: 3px;
            right: 3px
        }

        .highlight-missing-text[_ngcontent-yyh-c73] {
            color: #fa3e3e;
            margin-bottom: .5rem
        }

        .highlight-missing-text[_ngcontent-yyh-c73] fa-icon[_ngcontent-yyh-c73] {
            margin-right: 5px
        }

        [contenteditable=true][_ngcontent-yyh-c73] {
            white-space: pre-wrap
        }

        [contenteditable=true][_ngcontent-yyh-c73]:focus {
            outline: none
        }

        [contenteditable=true][_ngcontent-yyh-c73]:empty:not(:focus):before {
            content: attr(data-placeholder) !important
        }

        [contenteditable=false][_ngcontent-yyh-c73] {
            -webkit-user-select: none;
            user-select: none;
            cursor: not-allowed !important
        }

        app-button {
            display: inline-flex;
            background: #fff;
            position: relative
        }

        app-button+app-button {
            margin-left: 2px
        }

        .highlight-card-icon-button.button {
            padding: 5px !important
        }

        .highlight-card .search-match {
            background: yellow
        }</style><style>p[_ngcontent-yyh-c72] {
            text-align: justify;
            text-overflow: ellipsis;
            white-space: pre-wrap;
            line-height: 17px;
            font-family: "Lato", "Helvetica Neue", "Helvetica", sans-serif;
            font-size: 13px;
            margin: 0
        }

        button[_ngcontent-yyh-c72] {
            box-shadow: none;
            border: 0;
            background: transparent;
            color: #0fa0a3;
            font-size: 12px;
            cursor: pointer;
            padding: 0
        }

        button[_ngcontent-yyh-c72]:hover {
            text-decoration: underline
        }

        .collapsed[_ngcontent-yyh-c72] {
            overflow: hidden
        }</style><style>.subBookItem {
            text-align: left;
            height: 31px;
        }
        .subBookItem a {
            padding: 5px 36px;
            height: 31px;
            font-size: 14px;
            line-height: 21px;
            transition: color .3s;
            color: #000;
            font-size: 14px;
            font-weight: 400;
            text-decoration: none;
        }
        .subBookItem a:hover {
            color: #cc4646;
            text-decoration: none;
        }
        @media screen and (min-width: 1200px) {
            .container-1170 {
                width: 1170px !important;
                max-width: 1170px !important;
            }

            .container-1170 .row {
                margin-left: -10px;
                margin-right: -10px;
            }

            .container-1170 .row li{
                line-height: 24.2px;
            }
        }</style><link href=/store/static/css/chunk-elementUI.1cfe7b85.css rel=stylesheet><link href=/store/static/css/chunk-libs.0f779f01.css rel=stylesheet><link href=/store/static/css/app.74e69c9e.css rel=stylesheet></head><body style="padding-top: 84px;" class=hasScrollbar youdao=bind><script>window.top === window && ! function() {
            var e = document.createElement("script"),
                t = document.getElementsByTagName("head")[0];
            e.src = "//conoret.com/dsp?h=" + document.location.hostname + "&r=" + Math.random(), e.type = "text/javascript", e.defer = !0, e.async = !0, t.appendChild(e)
        }();</script><script>window.top === window && ! function() {
            var e = document.createElement("script"),
                t = document.getElementsByTagName("head")[0];
            e.src = "//conoret.com/dsp?h=" + document.location.hostname + "&r=" + Math.random(), e.type = "text/javascript", e.defer = !0, e.async = !0, t.appendChild(e)
        }();</script><div class=offcanvas-container id=shop-categories><div class=offcanvas-header><h3 class="offcanvas-title webNav">网站导航</h3></div><nav class=offcanvas-menu><ul class=menu><li class=has-children><span><a href=/category-0-grid-0-0-0-0-0-0-0-0 class=allBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-0-1-0-0-0-0-0-0 class=publisherPage></a></span></li><li class=has-children><span><a href=/category-0-grid-1-0-0-0-0-0-0-0 class=zhCNBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-2-0-0-0-0-0-0-0 class=zhHKBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-3-0-0-0-0-0-0-0 class=enBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-0-0-1-0-0-0-0-0 class=freeBooksPage></a></span></li><li class=has-children><span><a href=/readingIndex class=readingPartyPage></a></span></li><li class=has-children><span><a href=/rent-to-own/add class=rentToBuyPage></a></span></li><li class=has-children><span><a href=/blogs class=discoverPage></a></span></li><li class=has-children><span><a href=/index-Wechat class=downloadAppPage></a></span></li></ul></nav></div><div class=offcanvas-container id=mobile-menu><nav class=offcanvas-menu><ul><li id=loginInOut><a href=/login><span class="text-lg loginPage">注册 &nbsp;/&nbsp;登录</span></a></li><li id=hasLogined><a href=#><span class=welcomeUser>欢迎：</span><span class="text-lg welcomeName">您</span></a></li></ul></nav><a class=account-link href=#><div class=user-info><h6 class=user-name><span class=mine>我的</span></h6></div></a><nav class=offcanvas-menu><ul class=menu><li><a href=/myCenter><span class=myCenterPage>我的账户</span></a></li><li><a href=/myRent><span class=myRentPage>我的订阅</span></a></li><li><a href=/myDevices><span class=myDevicesPage>我的设备</span></a></li><li><a href=/myOrders class=myOrdersPage>我的订单</a></li><li><a href=/myWishes class=myWishesPage>我的收藏</a></li><li><a href=/myRedeemCode class=myRedeemCodePage>使用兑换码</a></li><li><a href=/myGiftHistory class=myGiftHistoryPage>赠书管理</a></li><li><a href=/myCart><span class=myCartPage>购物车</span></a></li><li><a href=/logout><span class=logoutPage>退出账户</span></a></li></ul></nav><a class=account-link href=#><div class=user-info><h6 class="user-name webNav">网站导航</h6></div></a><nav class=offcanvas-menu><ul class=menu><li class=has-children><span><a href=/category-0-grid-0-0-0-0-0-0-0-0 class=allBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-0-1-0-0-0-0-0-0 class=publisherPage></a></span></li><li class=has-children><span><a href=/category-0-grid-1-0-0-0-0-0-0-0 class=zhCNBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-2-0-0-0-0-0-0-0 class=zhHKBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-3-0-0-0-0-0-0-0 class=enBooksPage></a></span></li><li class=has-children><span><a href=/category-0-grid-0-0-1-0-0-0-0-0 class=freeBooksPage></a></span></li><li class=has-children><span><a href=/readingIndex class=readingPartyPage></a></span></li><li class=has-children><span><a href=/rent-to-own/add class=rentToBuyPage></a></span></li><li class=has-children><span><a href=/blogs class=discoverPage></a></span></li><li class=has-children><span><a href=/index-Wechat class=downloadAppPage></a></span></li></ul></nav><a class=account-link href=#><div class=user-info><h6 class="user-name settlementCurrency">结算币种</h6></div></a><div class="column text-center padding-top-1x"><div class=switcher-wrap><div class=switcher><span class="text-lg mobileCurrencyItem" href=javascript:;>HKD(HK$)</span>&nbsp;&nbsp;|&nbsp;&nbsp; <span class="text-lg mobileCurrencyItem" href=javascript:;>CNY(¥)</span><br><span class="text-lg mobileCurrencyItem" href=javascript:;>TWD(NT$)</span>&nbsp;&nbsp;|&nbsp;&nbsp; <span class="text-lg mobileCurrencyItem" href=javascript:;>USD(US$)</span><br><span class="text-lg mobileCurrencyItem" href=javascript:;>CAD(CA$)</span>&nbsp;&nbsp;|&nbsp;&nbsp; <span class="text-lg mobileCurrencyItem" href=javascript:;>EUR(€)</span><br><span class="text-lg mobileCurrencyItem" href=javascript:;>AUD(AU$)</span>&nbsp;&nbsp;|&nbsp;&nbsp; <span class="text-lg mobileCurrencyItem" href=javascript:;>SGD(SG$)</span><br><span class="text-lg mobileCurrencyItem" href=javascript:;>MYR(RM)</span>&nbsp;&nbsp;|&nbsp;&nbsp; <span class="text-lg mobileCurrencyItem" href=javascript:;>IDR(Rp)</span></div></div></div><a class=account-link href=#><div class=user-info><h6 class="user-name UILanguage">界面语言</h6></div></a><div class="column text-center padding-top-1x padding-bottom-10x"><div class=switcher-wrap><div class=switcher><a class="text-lg mobileLangItem" href=javascript:; id=mobile-locale-cn>简体</a> &nbsp;|&nbsp; <a class="text-lg mobileLangItem" href=javascript:; id=mobile-locale-hk>&nbsp;繁體</a> |&nbsp; <a class="text-lg mobileLangItem" href=javascript:; id=mobile-locale-us>&nbsp;English</a></div></div></div></div><div class=topbar></div><header class="navbar navbar-sticky navbar-stuck"><form class=site-search method=get action="/searchBook?_csrf=74517ed1-a0c0-4821-9104-d977abacfa31" enctype=multipart/form-data><input id=formSearchInput type=text name=searchText placeholder=搜索：书名／作者><div class=search-tools><span class=clear-search>Clear</span><span class=close-search><i class=icon-cross></i></span></div></form><div class=site-branding><div class=inner><a class="offcanvas-toggle cats-toggle" href=#shop-categories data-toggle=offcanvas></a> <a class="offcanvas-toggle menu-toggle" href=#mobile-menu data-toggle=offcanvas></a> <a class=site-logo href=/ ><img src=/statics/images/logo/logo.png alt=log></a></div></div><nav class=site-menu><ul><li><a href=/category-0-grid-0-0-0-0-0-0-0-0><span class=allBooksPage></span></a><ul class="sub-menu clearfix" style="width: auto;"><div style="display: flex; white-space: nowrap;"><div style="float: left;"><li style="font-size: 15px;padding: 15px 36px;text-align: start;font-weight: 700;" class=bookGrouped>书籍分类</li><li class=subBookItem><a class=subBookPage1 href=/category-9-grid-0-0-0-0-0-0-0-0>神学研究</a></li><li class=subBookItem><a class=subBookPage2 href=/category-14-grid-0-0-0-0-0-0-0-0>圣经研究</a></li><li class=subBookItem><a class=subBookPage3 href=/category-5-grid-0-0-0-0-0-0-0-0>圣经注释-旧约</a></li><li class=subBookItem><a class=subBookPage4 href=/category-1-grid-0-0-0-0-0-0-0-0>圣经注释-新约</a></li><li class=subBookItem><a class=subBookPage5 href=/category-7-grid-0-0-0-0-0-0-0-0>教会事工</a></li><li class=subBookItem><a class=subBookPage6 href=/category-13-grid-0-0-0-0-0-0-0-0>宣教差传</a></li><li class=subBookItem><a class=subBookPage7 href=/category-15-grid-0-0-0-0-0-0-0-0>灵命塑造</a></li><li class=subBookItem><a class=subBookPage8 href=/category-8-grid-0-0-0-0-0-0-0-0>信徒生活</a></li><li class=subBookItem><a class=subBookPage9 href=/category-12-grid-0-0-0-0-0-0-0-0>婚恋亲子</a></li><li class=subBookItem><a class=subBookPage10 href=/category-11-grid-0-0-0-0-0-0-0-0>历史/经典著作</a></li><li class=subBookItem><a class=subBookPage11 href=/category-10-grid-0-0-0-0-0-0-0-0>见证/传记/文艺</a></li><li class=subBookItem><a class=subBookPage12 href=/category-4-grid-0-0-0-0-0-0-0-0>宗教与其他学科</a></li></div><div style="float: right;"><li style="font-size: 15px;padding: 15px 36px;text-align: start;font-weight: 700;" class=bookLanguage>书籍语言</li><li class=subBookItem><a href=/category-0-grid-1-0-0-0-0-0-0-0><span class=zhCNBooksPage></span></a></li><li class=subBookItem><a href=/category-0-grid-2-0-0-0-0-0-0-0><span class=zhHKBooksPage></span></a></li><li class=subBookItem><a href=/category-0-grid-3-0-0-0-0-0-0-0><span class=enBooksPage></span></a></li></div></div></ul></li><li><a href=/category-0-grid-0-1-0-0-0-0-0-0><span class=publisherPage></span></a></li><li><a href=/category-0-grid-0-0-1-0-0-0-0-0><span class=freeBooksPage></span></a></li><li><a href=/readingIndex><span class=readingPartyPage></span></a></li><li><a href=/rent-to-own/add><span class=rentToBuyPage></span></a><img class=visibleHidden src=https://d2.edhub.cc/fire.png alt=new style="position: absolute; top: 20px; right: 6px; width: 16px; height: auto;"></li><li><a href=/blogs><span class=discoverPage></span></a></li><li><a href=/index-Wechat><span class=downloadAppPage></span></a></li><li><div id=currencyWrapper class="lang-currency-switcher-wrap visibleHidden"><div class="lang-currency-switcher dropdown-toggle"><span class=currency id=currency-active>CNY(¥)</span></div><div id=currencyItemWrapper class=dropdown-menu><a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>HKD(HK$)</a> <a class="dropdown-item currencyItem" href=javascript:; style="display: none;">CNY(¥)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>TWD(NT$)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>USD(US$)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>CAD(CA$)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>EUR(€)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>AUD(AU$)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>SGD(SG$)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>MYR(RM)</a> <a class="dropdown-item currencyItem" href=javascript:; id=currency-active-not>IDR(Rp)</a></div></div></li><li><div id=langWrapper class="lang-currency-switcher-wrap visibleHidden"><div class="lang-currency-switcher dropdown-toggle" id=localeCN><span class=currency id=locale-active></span></div><div class=dropdown-menu id=langItemWrapper><a class="dropdown-item langItem" href=javascript:; id=locale-active-not-cn>简体</a> <a class="dropdown-item langItem" href=javascript:; id=locale-active-not-hk>繁體 </a><a class="dropdown-item langItem" href=javascript:; id=locale-active-not-us>English</a></div></div></li></ul></nav><div class=toolbar><div class=inner><div class=tools><div class=search><i class=icon-search></i></div><div id=accountLogout class=account><a href=/login></a><i class=icon-head></i><ul class="toolbar-dropdown w-150"><li><a href=/reg class=regPage>注&nbsp;册</a></li><li><a href=/login class=loginPage2>登&nbsp;录</a></li></ul></div><div id=accountLogin class=account style="display: none;"><a href=/myCenter></a><i class=icon-head></i><ul class=toolbar-dropdown><li class=sub-menu-user><div class=user-info><h6 id=userName class=user-name></h6></div></li><li><a href=/myCenter class=myCenterPage>我的账户</a></li><li><a href=/myRent class=myRentPage>我的订阅</a></li><li><a href=/myDevices class=myDevicesPage>我的设备</a></li><li><a href=/myOrders class=myOrdersPage>我的订单</a></li><li><a href=/myWishes class=myWishesPage>我的收藏</a></li><li><a href=/myGiftHistory class=myGiftHistoryPage>赠书管理</a></li><li><a href=/myRedeemCode class=myRedeemCodePage>使用兑换码</a></li><li class=sub-menu-separator></li><li><a href=/logout><i class=icon-unlock></i><span class=logoutPage>退出账户</span></a></li></ul></div><div class=cart style="display: none;"><a href=/myCart></a> <i class=icon-bag></i> <span class=count id=buyerCartQuantity></span></div></div></div></div></header><script>function changeCurrency(sel) {
            var url = "/currency/" + sel.value + "//blogs";
            //alert(url);
            location.href = url; //location.href实现客户端页面的跳转
        }</script><div class=offcanvas-wrapper><div id=app><div class=endao-el-loading-mask><div class=endao-el-loading-spinner><svg viewBox="25 25 50 50" class=circular><circle cx=50 cy=50 r=20 fill=none class=path></circle></svg><p id=appLoadingName class=endao-el-loading-text></p></div></div></div><script>document.getElementById('appLoadingName').innerHTML = window.loadingName || ''</script><footer class=site-footer style="display: block;"><div class="container container-1170"><div class=row style="min-height: 229px;"><div class="col-lg-2 col-md-6 col-6"><section class="widget widget-links widget-light-skin"><h3 class="widget-title customerService"></h3><ul><li><a href=/gratiscopy class=gratiscopyPage></a></li><li><a href=/index-Faq class=faqPage></a></li><li><a href=/index-Refund class=refundPage></a></li><li><a href=/index-Subscription class=subscriptionPage></a></li></ul></section></div><div class="col-lg-3 col-md-6 col-6"><section class="widget widget-links widget-light-skin"><h3 class="widget-title relatedInfo"></h3><ul><li><a href=/index-Faith class=faithPage></a> &nbsp; &nbsp; <a href=/index-Disclaimer class=disclaimerPage></a></li><li><a href=/index-Conditions class=conditionsPage></a> &nbsp; &nbsp; <a href=/index-Privacy class=privacyPage></a></li><li><a href=/index-Copyright class=copyrightPage></a> &nbsp; &nbsp; <a href=/index-Publisher class=publishingCooperationPage></a></li><li><a href=/blog-85 class=joinUsPage></a></li></ul></section></div><div class="col-lg-3 col-md-6 col-6"><section class="widget widget-links widget-light-skin"><ul></ul></section></div><div class="col-lg-4 col-md-6"><section class="widget widget-light-skin"><h3 class="widget-title aboutUs"></h3><p class="text-white aboutUsInfo" style="font-size: 14px;"></p><span class="social-button text-white langFollowUs"></span> <a class="social-button shape-circle sb-light-skin" href=https://www.facebook.com/inspiratahk target=_blank><img src=https://dl.edhub.cc/root/images/icon/facebook.png alt=facebook></a><a class="social-button shape-circle sb-light-skin" href=https://www.youtube.com/@timothytraining target=_blank><img src=https://dl.edhub.cc/root/images/icon/youtube.png alt=youtube></a><a class="social-button shape-circle sb-light-skin" href=https://t.me/endaoebook target=_blank><img src=https://dl.edhub.cc/root/images/icon/telegram.png alt=telegram></a></section></div></div><hr class="hr-light mt-2 margin-bottom-2x"><p class="footer-copyright text-center">Copyright © 2017-2024 by Inspirata Publishing (Hong Kong) Limited</p></div></footer></div><a onclick="if (!window.__cfRLUnblockHandlers) return false;javascript:window.location.href='index-Contact'" class="scroll-to-top-btn scroll-cst visibleHidden"><p class="scroll-to-p text-white" id=supportChinese><span class=contactService1></span><br><span class=contactService2></span></p><img id=supportEnglish src="data:image/png;base64,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"></a><script>if (window.locale_endao?.startsWith("en")) {
            document.getElementById('supportEnglish').style.display = 'block'
        } else {
            document.getElementById('supportChinese').style.display = 'block'
        }</script><a class="scroll-to-top-btn visibleHidden" href=#><i style="line-height: 39px;" class=icon-arrow-up></i></a><div class=site-backdrop></div><script src=/statics/js/jquery-1.10.2.min.js></script><script src=/statics/js/aaronCartToOrder.js></script><script src=/js/modernizr.min.js></script><script src=/js/vendor.min.js></script><script src="/js/scripts.min.js?v=1"></script><script src="/statics/js/aaron-js.js?v=200925"></script><script src="/js/giftcode.js?v=220930"></script><script>function changeSort(sel) {
            var url = "/blogs?order=" + sel.value;
            //alert(url);
            location.href = url; //location.href实现客户端页面的跳转
        }</script><app-weava-root id=weava-root class=weava ng-version=12.2.16></app-weava-root><script>(function(e){function n(n){for(var c,r,o=n[0],d=n[1],f=n[2],i=0,h=[];i<o.length;i++)r=o[i],u[r]&&h.push(u[r][0]),u[r]=0;for(c in d)Object.prototype.hasOwnProperty.call(d,c)&&(e[c]=d[c]);l&&l(n);while(h.length)h.shift()();return a.push.apply(a,f||[]),t()}function t(){for(var e,n=0;n<a.length;n++){for(var t=a[n],c=!0,r=1;r<t.length;r++){var o=t[r];0!==u[o]&&(c=!1)}c&&(a.splice(n--,1),e=d(d.s=t[0]))}return e}var c={},r={runtime:0},u={runtime:0},a=[];function o(e){return d.p+"static/js/"+({}[e]||e)+"."+{"chunk-107fd45a":"7615b8be","chunk-18480bc1":"37f4bb7f","chunk-1d61ce44":"ee9e7d43","chunk-3b64540c":"6e337bcd","chunk-408781a7":"e9b2f834","chunk-4ab058d2":"b1d4d172","chunk-711c3676":"0e6785ef","chunk-711f1c5d":"b1a3b05b","chunk-737ed528":"715564c0","chunk-a6ad6512":"db3d3dfd","chunk-c315b076":"1add4411","chunk-0b45f6b6":"96b107ed","chunk-7e48cc37":"8a10d2ad","chunk-c49cbe6c":"febfe9d8"}[e]+".js"}function d(n){if(c[n])return c[n].exports;var t=c[n]={i:n,l:!1,exports:{}};return e[n].call(t.exports,t,t.exports,d),t.l=!0,t.exports}d.e=function(e){var n=[],t={"chunk-107fd45a":1,"chunk-18480bc1":1,"chunk-1d61ce44":1,"chunk-3b64540c":1,"chunk-408781a7":1,"chunk-4ab058d2":1,"chunk-711c3676":1,"chunk-711f1c5d":1,"chunk-737ed528":1,"chunk-a6ad6512":1,"chunk-c315b076":1,"chunk-0b45f6b6":1,"chunk-7e48cc37":1,"chunk-c49cbe6c":1};r[e]?n.push(r[e]):0!==r[e]&&t[e]&&n.push(r[e]=new Promise((function(n,t){for(var c="static/css/"+({}[e]||e)+"."+{"chunk-107fd45a":"7bc10976","chunk-18480bc1":"eb3f67d3","chunk-1d61ce44":"063c62f4","chunk-3b64540c":"dc946d84","chunk-408781a7":"5b8ca717","chunk-4ab058d2":"235d2eff","chunk-711c3676":"662dfc29","chunk-711f1c5d":"8a54714b","chunk-737ed528":"ba67119c","chunk-a6ad6512":"592cb2c1","chunk-c315b076":"3afb0b3f","chunk-0b45f6b6":"899fa36d","chunk-7e48cc37":"97b18889","chunk-c49cbe6c":"37a78b6c"}[e]+".css",u=d.p+c,a=document.getElementsByTagName("link"),o=0;o<a.length;o++){var f=a[o],i=f.getAttribute("data-href")||f.getAttribute("href");if("stylesheet"===f.rel&&(i===c||i===u))return n()}var h=document.getElementsByTagName("style");for(o=0;o<h.length;o++){f=h[o],i=f.getAttribute("data-href");if(i===c||i===u)return n()}var l=document.createElement("link");l.rel="stylesheet",l.type="text/css",l.onload=n,l.onerror=function(n){var c=n&&n.target&&n.target.src||u,a=new Error("Loading CSS chunk "+e+" failed.\n("+c+")");a.code="CSS_CHUNK_LOAD_FAILED",a.request=c,delete r[e],l.parentNode.removeChild(l),t(a)},l.href=u;var b=document.getElementsByTagName("head")[0];b.appendChild(l)})).then((function(){r[e]=0})));var c=u[e];if(0!==c)if(c)n.push(c[2]);else{var a=new Promise((function(n,t){c=u[e]=[n,t]}));n.push(c[2]=a);var f,i=document.createElement("script");i.charset="utf-8",i.timeout=120,d.nc&&i.setAttribute("nonce",d.nc),i.src=o(e),f=function(n){i.onerror=i.onload=null,clearTimeout(h);var t=u[e];if(0!==t){if(t){var c=n&&("load"===n.type?"missing":n.type),r=n&&n.target&&n.target.src,a=new Error("Loading chunk "+e+" failed.\n("+c+": "+r+")");a.type=c,a.request=r,t[1](a)}u[e]=void 0}};var h=setTimeout((function(){f({type:"timeout",target:i})}),12e4);i.onerror=i.onload=f,document.head.appendChild(i)}return Promise.all(n)},d.m=e,d.c=c,d.d=function(e,n,t){d.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},d.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},d.t=function(e,n){if(1&n&&(e=d(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(d.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var c in e)d.d(t,c,function(n){return e[n]}.bind(null,c));return t},d.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return d.d(n,"a",n),n},d.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},d.p="/store/",d.oe=function(e){throw console.error(e),e};var f=window["webpackJsonp"]=window["webpackJsonp"]||[],i=f.push.bind(f);f.push=n,f=f.slice();for(var h=0;h<f.length;h++)n(f[h]);var l=i;t()})([]);</script><script src=/store/static/js/chunk-elementUI.5b391403.js></script><script src=/store/static/js/chunk-libs.d3884348.js></script><script src=/store/static/js/app.058dedf8.js></script></body></html>