$('.btn-counter').click(function (e) {
    e.preventDefault();
    var inc = parseInt($(this).attr('data-inc'));
    var input = $("#count");
    var giftBookId = $("#giftBookId").val();
    var currentVal = parseInt(input.text()) + inc;
    $('.btn-counter').removeAttr('disabled');
    if (currentVal <= 1) {
        currentVal = 1;
        $(this).attr('disabled', true);
    } else if (currentVal >= 10) {
        currentVal = 10;
        $(this).attr('disabled', true);
    } else {
    }
    input.text(currentVal);
    $('#btn-buy').attr('href', 'orderConfirmGift-' + giftBookId + '-' + currentVal + '');
});


var text_max = 100;
$('#count_message').html('0 / ' + text_max);

$('#say-something').on("input", function () {
    var text_length = $('#say-something').val().length;
    var text_remaining = text_max - text_length;
    $('#count_message').html(text_length + ' / ' + text_max);
});

var currentRow;
$('#modal-sendcode').on('show.bs.modal', function (e) {
    var row = $(e.relatedTarget).closest('tr');
    currentRow = row;
    var name = $('td', row).first().text();
    //console.log(name);
    var code = $('td', row).eq(1).text();
    $('#giftcode').val(code);
    $('#bookname').text(name);
    $('#modal-sendcode #email').val('');
    $('#say-something').val('');
    $('#check-email').prop('checked', false);
    $('#btn-send').attr('disabled', true);
});

/*
$('#modal-sendcode').on('shown.bs.modal', function (e) {
  var modalDialog = $(this).find('.modal-dialog');
  var pos = Math.max(0, ($(window).height() - modalDialog.height()) / 2);
  console.log(pos);
  console.log(modalDialog.height());
  modalDialog.css("top", pos);
});
*/

function updateValideState() {
    var valid = $('#check-email').prop('checked');
    var email = $('#modal-sendcode #email').val();
    if (email.length < 5 || email.indexOf('@') <= 0) {
        valid = false;
    }
    $('#btn-send').attr('disabled', !valid);
}

$('#modal-sendcode #email').on("input", function () {
    updateValideState();
});

$('#check-email').change(function () {
    updateValideState();
});

$('#btn-send').on('click',function(e){
    var url = "sendGiftEmail";
    var email = $('#email').val();
    var giftcode = $('#giftcode').val();
    var sendText = $('#say-something').val();
    //console.log(giftcode);
    //console.log(sendText);
    //console.log(email);
    $.jpost(url, {
        email:email,
        giftcode:giftcode,
        sendText:sendText
        //_token:csrf_token
    }).then(res => {
        //console.log(res);
        //alert(res.successMessage);
        if(!res.success){
            if(res.errorMessages[0] == "请登录"){
                window.location.href = "https://ebook.endao.co/login";
            }else {
                alert(res.errorMessages[0]);
            }
        }else {
            $('.sendto',currentRow).text(email);
            $('#modal-sendcode').modal('hide');
        }
    });
});