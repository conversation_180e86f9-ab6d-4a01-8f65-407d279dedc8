(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1793018c"],{"0652":function(t,e,a){"use strict";(function(t){a("ac6a");var r=a("75fc"),n=(a("28a5"),a("5ac9"));e["a"]={data:function(){return{submitOrderDisabled:!1,payData:{totalHK:0,discountHK:0,willPayTotalHK:0},setId:0,allBooksList:[]}},computed:{totalCurrency:function(){return Math.round(this.payData.willPayTotalHK*this.$store.state.app.exchangeRate*100)/100},formatAuthor:function(){return function(t){return t.split("#")}}},created:function(){t("footer").css("display","block"),this.setId=this.$route.params.item.setId,this.getAllSetBooks()},mounted:function(){},methods:{getAllSetBooks:function(){var t=this;Object(n["a"])({setId:this.setId}).then(function(e){if(e.success){var a=e.result.notBuyList.map(function(t){return t.isBuy=0,t}),n=e.result.buyList.map(function(t){return t.isBuy=1,t});t.allBooksList=[].concat(Object(r["a"])(a),Object(r["a"])(n)),e.result.notBuyList.forEach(function(e){t.payData.totalHK+=e.price,t.payData.discountHK+=e.price-e.priceDiscount,t.payData.willPayTotalHK+=e.priceDiscount})}})},submitOrder:function(){var t=this;this.submitOrderDisabled=!0,Object(n["e"])({setId:this.setId}).then(function(e){t.submitOrderDisabled=!1,e.success&&(window.location.href="/orderDetail-".concat(e.result.orderId))})},goBack:function(){this.$router.go(-1)}}}}).call(this,a("1157"))},"1af6":function(t,e,a){var r=a("63b6");r(r.S,"Array",{isArray:a("9003")})},"20fd":function(t,e,a){"use strict";var r=a("d9f6"),n=a("aebd");t.exports=function(t,e,a){e in t?r.f(t,e,n(0,a)):t[e]=a}},3702:function(t,e,a){var r=a("481b"),n=a("5168")("iterator"),o=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||o[n]===t)}},"40c3":function(t,e,a){var r=a("6b4c"),n=a("5168")("toStringTag"),o="Arguments"==r(function(){return arguments}()),s=function(t,e){try{return t[e]}catch(a){}};t.exports=function(t){var e,a,c;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(a=s(e=Object(t),n))?a:o?r(e):"Object"==(c=r(e))&&"function"==typeof e.callee?"Arguments":c}},"4ee1":function(t,e,a){var r=a("5168")("iterator"),n=!1;try{var o=[7][r]();o["return"]=function(){n=!0},Array.from(o,function(){throw 2})}catch(s){}t.exports=function(t,e){if(!e&&!n)return!1;var a=!1;try{var o=[7],c=o[r]();c.next=function(){return{done:a=!0}},o[r]=function(){return c},t(o)}catch(s){}return a}},"549b":function(t,e,a){"use strict";var r=a("d864"),n=a("63b6"),o=a("241e"),s=a("b0dc"),c=a("3702"),i=a("b447"),u=a("20fd"),d=a("7cd6");n(n.S+n.F*!a("4ee1")(function(t){Array.from(t)}),"Array",{from:function(t){var e,a,n,l,f=o(t),m="function"==typeof this?this:Array,p=arguments.length,b=p>1?arguments[1]:void 0,v=void 0!==b,h=0,y=d(f);if(v&&(b=r(b,p>2?arguments[2]:void 0,2)),void 0==y||m==Array&&c(y))for(e=i(f.length),a=new m(e);e>h;h++)u(a,h,v?b(f[h],h):f[h]);else for(l=y.call(f),a=new m;!(n=l.next()).done;h++)u(a,h,v?s(l,b,[n.value,h],!0):n.value);return a.length=h,a}})},"54a1":function(t,e,a){a("6c1c"),a("1654"),t.exports=a("95d5")},"5ac9":function(t,e,a){"use strict";a.d(e,"j",function(){return o}),a.d(e,"i",function(){return s}),a.d(e,"b",function(){return c}),a.d(e,"f",function(){return i}),a.d(e,"d",function(){return u}),a.d(e,"h",function(){return d}),a.d(e,"g",function(){return l}),a.d(e,"a",function(){return f}),a.d(e,"e",function(){return m}),a.d(e,"c",function(){return p}),a.d(e,"l",function(){return b}),a.d(e,"k",function(){return v});var r=a("b775"),n=a("4360");function o(t){return Object(r["a"])({url:"/bookSet/bookSetById",method:"POST",data:t,headers:{currency:n["a"].state.app.currency}})}function s(t){return Object(r["a"])({url:"/bookSet/bookListBySet",method:"POST",data:t,headers:{currency:n["a"].state.app.currency}})}function c(t){return Object(r["a"])({url:"/bookSet/buyBookSetToBuy",method:"POST",data:t})}function i(t){return Object(r["a"])({url:"/bookSet/buyBookSet",method:"POST",data:t})}function u(t){return Object(r["a"])({url:"/shop/cartAddWithSetId",method:"POST",data:t})}function d(t){return Object(r["a"])({url:"/bookSet/bookMenuById",method:"POST",data:t,headers:{currency:n["a"].state.app.currency}})}function l(t){return Object(r["a"])({url:"/bookSet/bookListByMenu",method:"POST",data:t})}function f(t){return Object(r["a"])({url:"/bookSet/buyBookMenuToBuy",method:"POST",data:t})}function m(t){return Object(r["a"])({url:"/bookSet/buyBookMenu",method:"POST",data:t})}function p(t){return Object(r["a"])({url:"/shop/cartAddWithListId",method:"POST",data:t})}function b(t){return Object(r["a"])({url:"/shop/favoriteAdd",method:"POST",data:t})}function v(t){return Object(r["a"])({url:"/shop/cartAdd",method:"POST",data:t})}},"75fc":function(t,e,a){"use strict";var r=a("a745"),n=a.n(r);function o(t){if(n()(t)){for(var e=0,a=new Array(t.length);e<t.length;e++)a[e]=t[e];return a}}var s=a("774e"),c=a.n(s),i=a("c8bb"),u=a.n(i);function d(t){if(u()(Object(t))||"[object Arguments]"===Object.prototype.toString.call(t))return c()(t)}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function f(t){return o(t)||d(t)||l()}a.d(e,"a",function(){return f})},"774e":function(t,e,a){t.exports=a("d2d5")},"7cd6":function(t,e,a){var r=a("40c3"),n=a("5168")("iterator"),o=a("481b");t.exports=a("584a").getIteratorMethod=function(t){if(void 0!=t)return t[n]||t["@@iterator"]||o[r(t)]}},"7fe2":function(t,e,a){},"95d5":function(t,e,a){var r=a("40c3"),n=a("5168")("iterator"),o=a("481b");t.exports=a("584a").isIterable=function(t){var e=Object(t);return void 0!==e[n]||"@@iterator"in e||o.hasOwnProperty(r(e))}},a6c5:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container padding-bottom-3x mb-1 padding-top-2x setBooks-container"},[a("div",{staticClass:"table-responsive shopping-cart"},[a("table",{staticClass:"table"},[a("thead",[a("tr",[a("th",{staticStyle:{width:"55.7%"}},[t._v(t._s(t.$t("message.common.bookName"))+" ")]),t._v(" "),a("th",{staticClass:"text-center",staticStyle:{width:"24.5%"}},[t._v(t._s(t.$t("message.common.price"))+" ")]),t._v(" "),a("th",{staticClass:"text-center",staticStyle:{width:"6.9%"}},[t._v(t._s(t.$t("message.common.number"))+" ")]),t._v(" "),a("th",{staticClass:"text-center",staticStyle:{width:"12.9%"}},[t._v(t._s(t.$t("message.common.subTotal")))])])]),t._v(" "),a("tbody",t._l(t.allBooksList,function(e){return a("tr",{key:e.bookId},[a("td",[a("div",{staticClass:"product-item"},[a("a",{staticClass:"product-thumb",attrs:{href:"javascript:;"}},[a("img",{attrs:{src:""+e.imgUrl,alt:"Product"}})]),t._v(" "),a("div",{staticClass:"product-info"},[a("h4",{staticClass:"product-title",staticStyle:{display:"flex","align-items":"baseline",color:"#7a7a7a"}},[e.isBuy?a("span",{staticStyle:{"font-size":"14px"}},[t._v(t._s(t.$t("message.common.purchased"))+"  ")]):t._e(),t._v(" "),a("a",{class:{isBuy:!!e.isBuy},attrs:{href:"/book-"+e.bookId}},[t._v(t._s(e.name))])]),t._v(" "),a("span",[a("em",{class:{author:!0,isBuy:!!e.isBuy}},[t._v(t._s(t.$t("message.common.author"))+" :")]),t._v(" "),t._l(t.formatAuthor(e.author),function(r,n){return a("a",{key:n,class:{"navi-link":!0,isBuy:!!e.isBuy},attrs:{href:"/searchBook?searchText="+r}},[t._v(t._s(r))])})],2)])])]),t._v(" "),a("td",{staticClass:"text-center text-lg text-medium tdItem"},[e.isBuy?t._e():a("span",[a("del",[t._v("HK$"+t._s(t._f("twoDecimal")(e.price)))]),t._v(" HK$"+t._s(t._f("twoDecimal")(e.priceDiscount))+" ")])]),t._v(" "),a("td",{staticClass:"text-center text-lg text-medium tdItem"},[e.isBuy?t._e():a("span",[t._v("1")])]),t._v(" "),a("td",{staticClass:"text-center text-lg text-medium tdItem"},[e.isBuy?t._e():a("span",[t._v(" HK$"+t._s(t._f("twoDecimal")(e.priceDiscount))+" ")])])])}),0)])]),t._v(" "),a("div",{staticClass:"shopping-cart-footer"},[a("div",{staticClass:"col-xl-11"},[a("span",{staticClass:"text-medium text-danger",attrs:{id:"warning"}},[t._v(t._s(t.$t("message.common.intro")))])]),t._v(" "),a("div",{staticClass:"column text-lg-right"},[a("h4",{staticClass:"product-price"},[t._v("\n        "+t._s(t.$t("message.common.total"))+" :   "),a("span",{staticClass:"text-medium"},[t._v("HK$"+t._s(t._f("twoDecimal")(t.payData.totalHK)))])]),t._v(" "),a("h4",{staticClass:"product-price"},[t._v("\n        "+t._s(t.$t("message.common.discountAmount"))+" :  "),a("span",{staticClass:"text-medium",staticStyle:{"margin-left":"-3px",display:"inline-block"}},[t._v("-HK$"+t._s(t._f("twoDecimal")(t.payData.discountHK)))])]),t._v(" "),a("h4",{staticClass:"product-price"},[t._v("\n        "+t._s(t.$t("message.common.payAble"))+" :   "),a("span",{staticClass:"text-medium"},[t._v("HK$"+t._s(t._f("twoDecimal")(t.payData.willPayTotalHK)))])]),t._v(" "),a("h4",{staticClass:"product-price"},[a("span",{staticClass:"text-medium"},[t._v("("+t._s(t.$store.state.app.currencySign)+t._s(t.totalCurrency)+")")])])])]),t._v(" "),a("div",{staticClass:"shopping-cart-footer"},[a("div",{staticClass:"column"},[a("el-button",{staticStyle:{"text-transform":"uppercase"},attrs:{round:""},on:{click:t.goBack}},[a("i",{staticClass:"el-icon-back",staticStyle:{"font-size":"12px"}}),t._v(" "+t._s(t.$t("message.common.goBack")))]),t._v(" "),a("el-button",{staticStyle:{"text-transform":"uppercase"},attrs:{type:"danger",round:"",disabled:t.submitOrderDisabled},on:{click:t.submitOrder}},[t._v(t._s(t.$t("message.common.submitOrder")))])],1)])])},n=[],o=a("0652"),s=o["a"],c=(a("f8de"),a("2877")),i=Object(c["a"])(s,r,n,!1,null,"34cd7c7c",null);e["default"]=i.exports},a745:function(t,e,a){t.exports=a("f410")},b0dc:function(t,e,a){var r=a("e4ae");t.exports=function(t,e,a,n){try{return n?e(r(a)[0],a[1]):e(a)}catch(s){var o=t["return"];throw void 0!==o&&r(o.call(t)),s}}},c8bb:function(t,e,a){t.exports=a("54a1")},d2d5:function(t,e,a){a("1654"),a("549b"),t.exports=a("584a").Array.from},f410:function(t,e,a){a("1af6"),t.exports=a("584a").Array.isArray},f8de:function(t,e,a){"use strict";var r=a("7fe2"),n=a.n(r);n.a}}]);