<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title>Print Preview</title>
    <style type="text/css">
        body {
          font-family:Tahoma;
        }

        img {
          border:0;
        }
        * {
            font-family:"Microsoft YaHei",微软雅黑,"Microsoft JhengHei",华文细黑,STHeiti,MingLiu;
        }
        #page {
          width:800px;
          margin:0 auto;
          padding:15px;
        }
        #address {

        }
        #logo {
          margin:0;
        }
        #info {
            margin-top: 30px;
        }
        #profile {
            height: 480px;
            border:0px solid gray;
            font-size: 20px;

            -webkit-transform: rotate(90deg);
            -moz-transform: rotate(90deg);
        }
        #notes {
            height: 480px;
            -webkit-transform: rotate(-90deg);
            -moz-transform: rotate(-90deg);
        }
        table {
          width:100%;
        }

        td {
          padding:5px;
        }

        tr.odd {
          background:#e1ffe1;
        }
        ul li {
            font-size: 16px;
            line-height: 20px;
        }
    </style>
    <script src="https://cdn.staticfile.org/jquery/2.1.0/jquery.min.js"></script>
    <script type="application/javascript">
        $(document).keydown(function(e){
            if (e.keyCode == 37) {
               window.location.href="{{ previous_link }}";
               return false;
            }
            if (e.keyCode == 39) {
               window.location.href="{{ next_link }}";
               return false;
            }
            {% if is_print %}
            if (e.keyCode == 13) {
               window.location.href="{{ next_link }}";
               return false;
            }
            {% endif %}
        });
        {% if is_print %}
        $( document ).ready(function() {
            print();
        });
        {% endif %}
    </script>
</head>
<body>
<div id="page" th:style="${loopStatus.last == false}?'page-break-after:always'" th:each="student,loopStatus : ${list}">

    <div id="address">
    </div><!--end address-->

    <div id="content">
        <p>
        <p style="color:grey;font-size: 9px;font-style: italic;text-align: center">
            - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -- - - - - - - - - - - -剪裁线- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        </p>
    </div><!--end content-->

    <div id="info">
        <div style="border:1px solid gray;float:left;width: 49%;line-height:36px">
            <div id="profile">
    <br/>
    <br/>
                <p style="text-align: center;font-size: 22px"><strong>学员信息</strong></p>
                <a style="color: black;text-decoration: none;" href="#">
                姓名：<span style="font-size: 42px;font-weight: bold" th:text="${student.lastName+student.firstName}">姓名</span></a><br/>
                班级：<span th:text="${student.stuClass}">班级</span><br/>
                住房：<span th:text="${student.stuHousing}">住房</span><br/>
                小组编号：<span th:text="${student.stuTeamName}">小组编号</span><br/>
                小组讨论房间：<span th:text="${student.discussionRoom}">小组讨论房间</span><br/>
                报到码：<span th:text="${student.enrollCode}">报到码</span><br/>
            </div>
        </div>
        <div style="border:1px solid gray;float: right;width: 49%">
            <div id="notes">
    		<br/>
		<br/>
    		<br/>
                <p style="text-align: center;"><strong>营期注意事项</strong></p>
                <ul>
                    <li>营会期间，请佩戴好各自的名牌。</li>
                    <li>在宿舍楼请保持安静，集体生活注意说话音量与关门音量。</li>
                    <li>请同学务必保管好自己的房匙/房卡。如遗失房匙/房卡，岭南会罚款200元，且需要三个工作日才能补发。</li>
                    <li>在教室上课，请关闭手机声音。因版权限制，禁止私自拍照录音。</li>
		                <li>营期内紧急联系电话:+852-91277881。</li>
                </ul>
            </div>
        </div>
    </div>
</div><!--end page-->


</body>
</html>
