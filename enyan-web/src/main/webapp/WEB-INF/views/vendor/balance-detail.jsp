<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->

    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->
            <form:form action="balances?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
            <div class="form-group" >
                <spring:message code="search.by.range"/>：
                <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="请选择时间范围"/>
                <script>
                    laydate.render({
                        elem: '#rangeDate'
                        ,type: 'month'
                        ,format: 'yyyyMM'
                        ,range: true
                    });
                </script>
                <button type="submit" class="btn btn-primary m-bot15" onclick="disabled=true;this.form.submit();" style="margin-top: 1.5rem"><spring:message code="search.select"/> </button>
            </div>
            </form:form>

        </div>
        <!-- header section end-->
        <sec:authentication var="user" property="principal" />
        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="sell.balance"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active"> <spring:message code="sell.balance"/></li>
            </ul>
        </div>
        <!-- page heading end-->
<form:form action="balanceConfirm?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="sell.count"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <table class="table  table-hover general-table" >
                                <thead>
                                <tr>
                                    <th>
                                        <input class="custom-control-input" type="checkbox" id="checkAllBox" name="checkAllBox" onchange="changeAllCheckState()"><span class="custom-control-indicator"></span>
                                    </th>
                                    <th><spring:message code="label.month"/> </th>
                                    <th><spring:message code="order.sales"/> </th>
                                    <th><spring:message code="income.sell"/></th>
                                    <th><spring:message code="income.vendor"/> </th>
                                    <th><spring:message code="operation.label"/> </th>
                                    <th><spring:message code="label.status"/> </th>
                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                <tr>
                                    <td>
                                        <c:if test="${list.isCounted == -1}">
                                            <input class="custom-control-input" type="checkbox" name="productId" value="${list.balanceId}" onchange="changeCheckState()">
                                        </c:if>
                                    </td>
                                    <td class="hidden-phone">${list.purchasedMonth}</td>
                                    <td>
                                            ${list.quantity}
                                    </td>
                                    <td>
                                        HK$${list.incomeTotal}
                                    </td>
                                    <td>
                                        HK$${list.incomeVendor}
                                    </td>
                                    <td>
                                        <a href="orderDetailsBook-${list.purchasedMonth}"> <spring:message code="label.sell.look"/></a>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${list.isCounted == 0}">
                                                <span class="label label-info label-mini"><spring:message code="label.counted.0"/></span>
                                            </c:when>
                                            <c:when test="${list.isCounted == -1}">
                                                <span class="label label-danger label-mini"><spring:message code="label.counted.-1"/></span>
                                            </c:when>
                                            <c:when test="${list.isCounted == -2}">
                                                <span class="label label-danger label-mini"><spring:message code="label.counted.-2"/></span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="label label-success label-mini"><spring:message code="label.counted.1"/></span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                                </c:forEach>
                                <c:if test="${user.authorities[0].authority == 'ROLE_VENDOR'}">
                                    <tr>
                                        <td colspan="7" align="right">
                                            <button class="btn btn-primary" type="submit" onclick="return cartValidate()"><spring:message code="account.settle"/> </button>
                                        </td>
                                    </tr>
                                </c:if>
                                </tbody>
                            </table>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->
</form:form>

        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>
<script src="<c:url value='/statics/js/aaronBalance.js' />"></script>
<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#sellBalanceUI").addClass("active");
    })

    function cartValidate() {
        var checked = false;
        for (var i=0; i<checkMyBox.length; i++){
            if (checkMyBox[i].checked){
                checked = true;
            }
        }
        if (checked){
            return true;
        }
        alert("<spring:message code='error.balance.select'/>");
        return false;
    }
</script>

</body>
</html>
