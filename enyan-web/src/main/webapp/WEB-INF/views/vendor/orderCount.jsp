<%@ page import="com.aaron.spring.common.Constant" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="orderCounts?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->


        </div>
        <!-- header section end-->
        <sec:authentication var="user" property="principal" />
        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="sell.count"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active"> <spring:message code="sell.count"/></li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                            <spring:message code="sell.count"/>
                            <span class="tools pull-right">
                         </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <spring:message code="search.by.range"/>：
                            <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}"/>
                            <script>
                                laydate.render({
                                    elem: '#rangeDate'
                                    ,format: 'yyyyMMdd'
                                    ,range: true
                                    ,lang:'<spring:message code="search.time.lang"/>'
                                });
                            </script>
                            <form:select path="searchType"
                                         cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                         cssClass="form-control m-bot15">
                                <form:option value="0" ><spring:message code="search.by.day"/></form:option>
                                <form:option value="1" ><spring:message code="search.by.week"/></form:option>
                                <form:option value="2" ><spring:message code="search.by.month"/></form:option>
                                <form:option value="3" ><spring:message code="search.by.year"/></form:option>
                            </form:select>
                            <form:select path="searchSelect" items="${publisherList}" itemLabel="name" itemValue="value"
                                         cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                         cssClass="form-control m-bot15"/>

                            <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                            <!--pagination start-->
                            <div class="">
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->
        <jsp:include page="../admin/footer_show.jsp"></jsp:include>
    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#orderCountUI").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
