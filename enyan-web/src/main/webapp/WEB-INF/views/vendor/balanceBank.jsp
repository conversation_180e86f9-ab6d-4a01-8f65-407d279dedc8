<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->


            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="sell.balance.confirm"/>-<spring:message code="my.balance.account"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active">
                    <spring:message code="my.balance.account"/>
                </li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="sell.balance.confirm"/>-<spring:message code="my.balance.account"/>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                            ${msg}
                        </div>
                        <div class="panel-body">
                            <form role="form" action="updatePwdAction" method="post" class="form-horizontal adminex-form">
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="bankName"><spring:message code="publisher.bank.name"/> </label>
                                    <div class="col-sm-10">
                                        <input class="form-control" type="text" name="bankName" class="form-control" id="bankName" value="${balanceDetail.publisher.bankName}" maxlength="50" size="70"  disabled/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="bankAddress"><spring:message code="publisher.bank.address"/> </label>
                                    <div class="col-sm-10">
                                        <input class="form-control" type="text" name="bankAddress" class="form-control" id="bankAddress" value="${balanceDetail.publisher.bankAddress}" maxlength="50" size="70"  disabled/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="bankCode"><spring:message code="publisher.bank.code"/> </label>
                                    <div class="col-sm-10">
                                        <input class="form-control" type="text" name="bankCode" class="form-control" id="bankCode" value="${balanceDetail.publisher.bankCode}" maxlength="50" size="70" disabled/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="bankTitle"><spring:message code="publisher.bank.title"/> </label>
                                    <div class="col-sm-10">
                                        <input class="form-control" type="text" name="bankTitle" class="form-control" id="bankTitle" value="${balanceDetail.publisher.bankTitle}" maxlength="50" size="70" disabled/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="bankNum"><spring:message code="publisher.bank.num"/> </label>
                                    <div class="col-sm-10">
                                        <input class="form-control" type="text" name="bankNum" class="form-control" id="bankNum" value="${balanceDetail.publisher.bankNum}" maxlength="50" size="70" disabled/>
                                    </div>
                                </div>
                                <div class="pull-right">
                                    <a type="submit" class="btn btn-danger" onclick="javascript:history.go(-1)"><spring:message code="button.cancel"/> </a>
                                    <a type="submit" class="btn btn-primary" href="balanceConfirmCreate"><spring:message code="button.confirm"/> </a>
                                </div>

                            </form>

                        </div>
                    </section>
                </div>

            </div>


        </div>
        <!--body wrapper end-->


        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#sellBalanceUI").addClass("active");
    })
</script>

</body>
</html>
