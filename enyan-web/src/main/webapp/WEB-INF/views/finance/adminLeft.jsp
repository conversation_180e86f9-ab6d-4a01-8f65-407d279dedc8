<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<li class="menu-list" id="menu_balance"><a href="#"><i class="fa fa-list-alt"></i> <span><spring:message code="menu.balance"/></span></a>
    <ul class="sub-menu-list">
        <li id="orderList"><a href="<c:url value="/order/orders" />"><spring:message code="order.list"/></a></li>
        <li id="orderDetailsUIOrder"><a href="<c:url value="/vendor/orderDetailsUIOrder" />"><spring:message code="sell.info"/></a></li>
        <li id="orderDetailsUIBook"><a href="<c:url value="/vendor/orderDetailsUIBook" />"><spring:message code="sell.analyze"/></a></li>
        <li id="orderCountUI"><a href="<c:url value="/vendor/orderCountUI" />"><spring:message code="sell.statistics"/></a></li>
        <li id="refundList"><a href="<c:url value="/refund/refundUI" />"><spring:message code="refund.list"/></a></li>
        <li id="sellBalanceUI"><a href="<c:url value="/vendor/balanceUI" />"><spring:message code="sell.balance"/></a></li>
        <li id="balanceHistoryUI"><a href="<c:url value="/vendor/balanceHistory" />"><spring:message code="balance.history"/></a></li>
        <li id="rentList"><a href="<c:url value="/rentAdmin/list" />"><spring:message code="rent.list"/></a></li>
        <li id="rentDetails"><a href="<c:url value="/vendor/rentDetails" />"><spring:message code="rent.detail"/></a></li>
    </ul>
</li>
<li class="menu-list" id="menu_stat"><a href="#"><i class="fa fa-list-alt"></i> <span>数据统计</span></a>
    <ul class="sub-menu-list">
        <li id="dataStatList"><a href="<c:url value="/dataStat/ui" />">下单/注册</a></li>
        <li id="dataRentStatList"><a href="<c:url value="/dataRentStat/ui" />">先租后买统计</a></li>
    </ul>
</li>

<li class="menu-list" id="menu_info"><a href="#"><i class="fa fa-user"></i> <span><spring:message code="menu.info"/></span></a>
    <ul class="sub-menu-list">
        <li id="passwd"><a href="<c:url value="/user/updatePwd" />"><spring:message code="passwd.update"/></a></li>

    </ul>
</li>
<li class="menu-list" id="menu_lang"><a href="#"><i class="fa fa-user"></i> <span>语言/language</span></a>
    <ul class="sub-menu-list">
        <c:choose>
            <c:when test="${pageContext.response.locale == 'zh_CN'}">
                <%--                    <li><i class="fa fa-stack-exchange"></i> <span>简体 </span></li>--%>
                <li><a href="<c:url value="/admin" />?locale=zh_HK"><i class="fa fa-stack-exchange"></i> <span>切換繁體 </span></a></li>
                <li><a href="<c:url value="/admin" />?locale=en_US"><i class="fa fa-stack-exchange"></i> <span>English </span></a></li>
            </c:when>
            <c:when test="${pageContext.response.locale == 'zh_HK'}">
                <li><a href="<c:url value="/admin" />?locale=zh_CN"><i class="fa fa-stack-exchange"></i> <span>切换简体 </span></a></li>
                <%--                    <li><i class="fa fa-stack-exchange"></i> <span>繁體 </span></li>--%>
                <li><a href="<c:url value="/admin" />?locale=en_US"><i class="fa fa-stack-exchange"></i> <span>English </span></a></li>
            </c:when>
            <c:otherwise>
                <li><a href="<c:url value="/admin" />?locale=zh_CN"><i class="fa fa-stack-exchange"></i> <span>切换简体 </span></a></li>
                <li><a href="<c:url value="/admin" />?locale=zh_HK"><i class="fa fa-stack-exchange"></i> <span>切換繁體 </span></a></li>
                <%--                    <li><i class="fa fa-stack-exchange"></i> <span>English </span></li>--%>
            </c:otherwise>
        </c:choose>
    </ul>
</li>
