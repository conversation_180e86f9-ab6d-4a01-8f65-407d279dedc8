<%@ page import="java.util.Enumeration" %>
<%@ page import="org.springframework.security.web.csrf.DefaultCsrfToken" %><%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>404</title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书">
    <meta name="keywords" content="恩道电子书">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Favicon Icons-->
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">
    <!-- Main Template Styles-->
    <link rel="stylesheet" media="screen" href="<c:url value='/css/vendor.min.css' />">
    <link id="mainStyles" rel="stylesheet" media="screen" href="<c:url value='/css/styles.css' />">
    <!-- Modernizr-->
    <script src="<c:url value='/js/modernizr.min.js' />"></script>
    <jsp:include page="shop/track-info.jsp"/>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="shop/header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->


    <!-- Page Content-->

    <div class="container padding-top-3x padding-bottom-3x mb-1"><img class="d-block m-auto" src="<c:url value='/statics/images/404.png'/>" style="width: 100%; max-width: 350px;" alt="404">
        <div class="padding-top-2x mt-2 text-center">
            <h6 class="text-muted"><spring:message code="error.404"/> </h6>
        </div>
        <div class="text-center">
            <div class="column"><a class="btn btn-primary" href="/"><spring:message code="back.index"/> </a>&nbsp;&nbsp;<a class="btn btn-primary" href="javascript:history.go(-1)"><spring:message code="back.page"/> </a></div>
        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="shop/footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<jsp:include page="shop/footer-js.jsp"/>
</body>
</html>