<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>
    <!--tags input-->
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/jquery-tags-input/jquery.tagsinput.css' />" />

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <link href="<c:url value='/statics/js/pureScript/style.css' />" rel="stylesheet">
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="setCategory?${_csrf.parameterName}=${_csrf.token}" modelAttribute="enyanCategory" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <c:choose>
                        <c:when test="${enyanCategory.categoryId==null}">
                            <spring:message code="category.add"/>
                        </c:when>
                        <c:otherwise>
                            <spring:message code="category.edit"/>
                        </c:otherwise>
                    </c:choose>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active">
                        <c:choose>
                            <c:when test="${enyanCategory.categoryId==null}">
                                <spring:message code="category.add"/>
                            </c:when>
                            <c:otherwise>
                                <spring:message code="category.edit"/>
                            </c:otherwise>
                        </c:choose>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <c:choose>
                                    <c:when test="${enyanCategory.categoryId==null}">
                                        <spring:message code="category.add"/>
                                    </c:when>
                                    <c:otherwise>
                                        <spring:message code="category.edit"/>
                                    </c:otherwise>
                                </c:choose>
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="categoryName">分类简体名称</label>
                                        <div class="col-sm-10">
                                            <form:input path="categoryName" cssClass="form-control" id="categoryName" maxlength="40" disabled="true"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="categoryName">设置推荐的书籍ID</label>
                                        <div class="col-sm-10">
                                            <form:input path="bookRecommended" cssClass="form-control tags" id="tags_1" maxlength="10"/>
                                        </div>
                                    </div>

<%--                                    <div class="form-group">--%>
<%--                                        <label class="col-sm-2 col-sm-2 control-label" for="categoryOrder">设置推荐的书籍</label>--%>
<%--                                        <div class="col-sm-10 directorist-select directorist-select-multi" id="multiSelect" data-isSearch="true" data-multiSelect='[]' data-max="10">--%>


<%--                                            <form:select path="bookIDs" items="${bookIDsList}" itemLabel="name" itemValue="value" multiple="single"/>--%>
<%--                --%>
<%--                                        </div>--%>
<%--                                    </div>--%>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <form:hidden path="categoryId"/>
                                        <form:hidden path="categoryName"/>
                                        <form:hidden path="bookRecommendedOld"/>
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                    </div>
                                </form>

                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>
<jsp:include page="footer_js.jsp"></jsp:include>
<script src="<c:url value='/statics/js/pureScript/script.js' />"></script>
<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#categoryList").addClass("active");

        // pureScriptSelect('#multiSelect');
    })
</script>
<!--tags input-->
<script src="<c:url value='/statics/js/jquery-tags-input/jquery.tagsinput.js' />"></script>
<script src="<c:url value='/statics/js/tagsinput-init.js' />"></script>
</body>
</html>
