<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->

    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="save?${_csrf.parameterName}=${_csrf.token}" modelAttribute="record" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <c:choose>
                        <c:when test="${record.episodeId==null}">
                            新增播客单集
                        </c:when>
                        <c:otherwise>
                            编辑播客单集
                        </c:otherwise>
                    </c:choose>
                </h3>
                <ul class="breadcrumb">
                    <li><a href=""><spring:message code="home"/></a></li>
                    <li><a href="#">播客管理</a></li>
                    <li><a href="../list">栏目管理</a></li>
                    <li><a href="list">单集管理</a></li>
                    <li class="active">
                        <c:choose>
                            <c:when test="${record.episodeId==null}">
                                新增单集
                            </c:when>
                            <c:otherwise>
                                编辑单集
                            </c:otherwise>
                        </c:choose>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">
                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <c:choose>
                                    <c:when test="${record.episodeId==null}">
                                        新增播客单集
                                    </c:when>
                                    <c:otherwise>
                                        编辑播客单集
                                    </c:otherwise>
                                </c:choose>
                            </header>
                            <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> 
                                <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="podcastId">所属播客 *</label>
                                        <div class="col-sm-10">
                                            <form:select path="podcastId" cssClass="form-control" id="podcastId">
                                                <form:option value="">请选择播客</form:option>
                                                <c:forEach var="podcast" items="${podcastList}">
                                                    <form:option value="${podcast.podcastId}">${podcast.title}</form:option>
                                                </c:forEach>
                                            </form:select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="title">单集标题 *</label>
                                        <div class="col-sm-10">
                                            <form:input path="title" cssClass="form-control" id="title" maxlength="200" placeholder="请输入单集标题"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="description">单集描述</label>
                                        <div class="col-sm-10">
                                            <form:textarea path="description" cssClass="form-control" id="description" rows="4" placeholder="请输入单集描述"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="audioFileUrl">音频文件URL</label>
                                        <div class="col-sm-10">
                                            <form:input path="audioFileUrl" cssClass="form-control" id="audioFileUrl" maxlength="500" placeholder="请输入音频文件URL"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="episodeNumber">集数</label>
                                        <div class="col-sm-10">
                                            <form:input path="episodeNumber" cssClass="form-control" id="episodeNumber" type="number" placeholder="请输入集数"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="durationSeconds">时长(秒)</label>
                                        <div class="col-sm-10">
                                            <form:input path="durationSeconds" cssClass="form-control" id="durationSeconds" type="number" placeholder="请输入时长(秒)"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="isPublished">发布状态</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="isPublished" value="1" id="isPublished"/>已发布
                                            <form:radiobutton path="isPublished" value="0"/>未发布
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="publicationDate">发布日期</label>
                                        <div class="col-sm-10">
                                            <form:input path="publicationDate" cssClass="form-control" id="publicationDate" placeholder="请选择发布日期"/>
                                            <script>
                                                laydate.render({
                                                    elem: '#publicationDate',
                                                    format: 'yyyy-MM-dd',
                                                    type: 'date'
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <form:hidden path="episodeId"/>
                                        <form:hidden path="topicId"/>
                                        <form:hidden path="episodeCount"/>
                                        <form:hidden path="listenCount"/>
                                        <form:hidden path="likeCount"/>
                                        <form:hidden path="isDeleted"/>
                                        <form:hidden path="createdAt"/>
                                        <form:hidden path="coverImageUrl"/>
                                        <form:hidden path="coverImageUrl2"/>
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                        <a href="list<c:if test='${not empty record.podcastId}'>?podcastId=${record.podcastId}</c:if>" class="btn btn-default">返  回</a>
                                    </div>
                                </form>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <!--body wrapper end-->

            <jsp:include page="../footer_show.jsp"></jsp:include>

        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        $("#menu_web").addClass("nav-active");
        $("#podcastList").addClass("active");
    });
</script>

</body>
</html>
