<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">
<sec:authentication var="user" property="principal" />
<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->

                <div class="form-group">
                    <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                    <spring:message code="search.by.range"/>：
                    <form:input path="rangeDate" cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                cssClass="form-control m-bot15" size="20" placeholder="${placeHolderTimeRange}"/>
                    <c:choose>
                        <c:when test="${dto.searchType == 0}">
                            <script>
                                laydate.render({
                                    elem: '#rangeDate'
                                    ,format: 'yyyyMMdd'
                                    ,range: true
                                });
                            </script>
                        </c:when>
                        <c:when test="${dto.searchType == 2}">
                            <script>
                                laydate.render({
                                    elem: '#rangeDate'
                                    ,type: 'month'
                                    ,format: 'yyyyMM'
                                    ,range: true
                                });
                            </script>
                        </c:when>
                        <c:when test="${dto.searchType == 3}">
                            <script>
                                laydate.render({
                                    elem: '#rangeDate'
                                    ,type: 'year'
                                    ,range: true
                                });
                            </script>
                        </c:when>
                    </c:choose>

                    <form:hidden path="searchType" />

                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                </div>

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                先租后买统计
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#">数据统计</a>
                </li>
                <li class="active">先租后买统计</li>
            </ul>

        </div>
        <!-- page heading end-->
        <sec:authentication var="user" property="principal" />
        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="sell.count"/>
                            <span class="tools pull-right">
                            </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <table class="table  table-hover general-table" >
                                <thead>
                                <tr>
                                    <th>时间</th>
                                    <th colspan="18" class="text-center" style="background-color: mediumseagreen">简体</th>
                                    <th colspan="18" class="text-center" style="background-color: deepskyblue">繁体</th>
                                </tr>
                                <tr>
                                    <th>时间</th>
                                    <th>全套历史订阅量</th>
                                    <th>旧约历史订阅量</th>
                                    <th>新约历史订阅量</th>
                                    <th>全套新增订阅</th>
                                    <th>旧约新增订阅</th>
                                    <th>新约新增订阅</th>
                                    <th>全套订阅中</th>
                                    <th>旧约订阅中</th>
                                    <th>新约订阅中</th>
                                    <th>全套新增退订</th>
                                    <th>旧约新增退订</th>
                                    <th>新约新增退订</th>
                                    <th>全套平均订阅时长(月)</th>
                                    <th>旧约平均订阅时长(月)</th>
                                    <th>新约平均订阅时长(月)</th>
                                    <th>全套新增退订购买</th>
                                    <th>旧约新增退订购买</th>
                                    <th>新约新增退订购买</th>

                                    <th>全套历史订阅量</th>
                                    <th>旧约历史订阅量</th>
                                    <th>新约历史订阅量</th>
                                    <th>全套新增订阅</th>
                                    <th>旧约新增订阅</th>
                                    <th>新约新增订阅</th>
                                    <th>全套订阅中</th>
                                    <th>旧约订阅中</th>
                                    <th>新约订阅中</th>
                                    <th>全套新增退订</th>
                                    <th>旧约新增退订</th>
                                    <th>新约新增退订</th>
                                    <th>全套平均订阅时长(月)</th>
                                    <th>旧约平均订阅时长(月)</th>
                                    <th>新约平均订阅时长(月)</th>
                                    <th>全套新增退订购买</th>
                                    <th>旧约新增退订购买</th>
                                    <th>新约新增退订购买</th>
                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                    <tr>
                                        <td>
                                                ${list.dateString}
                                        </td>
                                        <td>
                                                ${list.rentScAllSum}
                                        </td>
                                        <td>
                                                ${list.rentScOtSum}
                                        </td>
                                        <td>
                                                ${list.rentScNtSum}
                                        </td>
                                        <td>
                                                ${list.rentScAllNew}
                                        </td>
                                        <td>
                                                ${list.rentScOtNew}
                                        </td>
                                        <td>
                                                ${list.rentScNtNew}
                                        </td>
                                        <td>
                                                ${list.rentScAllActive}
                                        </td>
                                        <td>
                                                ${list.rentScOtActive}
                                        </td>
                                        <td>
                                                ${list.rentScNtActive}
                                        </td>
                                        <td>
                                                ${list.rentScAllLeave}
                                        </td>
                                        <td>
                                                ${list.rentScOtLeave}
                                        </td>
                                        <td>
                                                ${list.rentScNtLeave}
                                        </td>
                                        <td>
                                                ${list.rentScAllTime}
                                        </td>
                                        <td>
                                                ${list.rentScOtTime}
                                        </td>
                                        <td>
                                                ${list.rentScNtTime}
                                        </td>
                                        <td>
                                                ${list.rentScAllBuy}
                                        </td>
                                        <td>
                                                ${list.rentScOtBuy}
                                        </td>
                                        <td>
                                                ${list.rentScNtBuy}
                                        </td>

                                        <td>
                                                ${list.rentTcAllSum}
                                        </td>
                                        <td>
                                                ${list.rentTcOtSum}
                                        </td>
                                        <td>
                                                ${list.rentTcNtSum}
                                        </td>
                                        <td>
                                                ${list.rentTcAllNew}
                                        </td>
                                        <td>
                                                ${list.rentTcOtNew}
                                        </td>
                                        <td>
                                                ${list.rentTcNtNew}
                                        </td>
                                        <td>
                                                ${list.rentTcAllActive}
                                        </td>
                                        <td>
                                                ${list.rentTcOtActive}
                                        </td>
                                        <td>
                                                ${list.rentTcNtActive}
                                        </td>
                                        <td>
                                                ${list.rentTcAllLeave}
                                        </td>
                                        <td>
                                                ${list.rentTcOtLeave}
                                        </td>
                                        <td>
                                                ${list.rentTcNtLeave}
                                        </td>
                                        <td>
                                                ${list.rentTcAllTime}
                                        </td>
                                        <td>
                                                ${list.rentTcOtTime}
                                        </td>
                                        <td>
                                                ${list.rentTcNtTime}
                                        </td>
                                        <td>
                                                ${list.rentTcAllBuy}
                                        </td>
                                        <td>
                                                ${list.rentTcOtBuy}
                                        </td>
                                        <td>
                                                ${list.rentTcNtBuy}
                                        </td>
                                    </tr>
                                </c:forEach>

                                <%--<tr>
                                    <td class="hidden-phone" colspan="2" align="right">
                                            小计：
                                    </td>
                                    <td>
                                            ${orderDetailBottom.quantity}
                                    </td>
                                    <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN'}">
                                    <td>
                                        HK$${orderDetailBottom.incomeTotal}
                                    </td>
                                    </c:if>
                                    <td>
                                        HK$${orderDetailBottom.incomeVendor}
                                    </td>
                                </tr>--%>
                                </tbody>
                            </table>
                            <c:if test="${empty list}">
                                <div class="">
                                    <spring:message code="data.empty"/>
                                </div>
                            </c:if>
                            <!--pagination start-->
                            <div class="">
<%--                                ${pageLand}--%>
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>


        </div>
        <!--body wrapper end-->


        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>


<script>
    $(document).ready(function(){
        //do something
        $("#menu_stat").addClass("nav-active");
        $("#dataRentStatList").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

<!-- jQuery Flot Chart-->
<script type="text/javascript" src="<c:url value='/statics/js/flot-chart/jquery.flot.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/flot-chart/jquery.flot.tooltip.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/flot-chart/jquery.flot.resize.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/flot-chart/jquery.flot.pie.resize.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/flot-chart/jquery.flot.selection.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/flot-chart/jquery.flot.stack.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/flot-chart/jquery.flot.time.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/flot.chart.init.js' />"></script>

</body>
</html>
