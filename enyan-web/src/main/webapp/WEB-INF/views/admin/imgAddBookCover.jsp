<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/jquery-multi-select/css/multi-select.css' />" />

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-daterangepicker/daterangepicker-bs3.css' />" />
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-datepicker/css/datepicker-custom.css' />" />
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="saveImg?${_csrf.parameterName}=${_csrf.token}" modelAttribute="enyanImg" method="post" enctype="multipart/form-data"  role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    封面添加
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active"> 封面添加</li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                封面添加
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="imgDescription">资源名称</label>
                                        <div class="col-sm-10">
                                            <form:input path="imgDescription" cssClass="form-control" id="imgDescription" maxlength="40"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="uploadPic">上传资源</label>
                                        <div class="col-sm-10">
                                        <input type="file" name="uploadPic"  size="40" id="uploadPic">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="uploadPic">资源类型</label>
                                        <div class="col-sm-10">
                                            <form:select path="imageType" cssClass="form-control m-bot15">
                                                <form:option value="0"  label="封面图"/>
                                            </form:select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" >关联书籍</label>
                                        <div class="col-sm-10 input-group input-large">
                                            <form:select path="bookIDs" items="${bookIDsList}" itemLabel="name" itemValue="value" cssClass="multi-select" multiple="true" id="my_multi_select3" />
                                            <%--<form:hidden path="bookIDsOld"/>--%><%----%>
                                        </div>
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">添  加</button>
                                    </div>
                                </form>
                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#imgList").addClass("active");
    })
</script>
<script type="text/javascript" src="<c:url value='/statics/js/ckeditor/ckeditor.js' />"></script>
<!--multi-select-->
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.multi-select.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.quicksearch.js' />"></script>
<script src="<c:url value='/statics/js/multi-select-init.js' />"></script>
</body>
</html>
