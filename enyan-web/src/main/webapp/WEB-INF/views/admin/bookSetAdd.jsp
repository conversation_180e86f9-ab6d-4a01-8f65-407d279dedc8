<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-daterangepicker/daterangepicker-bs3.css' />" />
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-datepicker/css/datepicker-custom.css' />" />
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->

    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="saveSet?${_csrf.parameterName}=${_csrf.token}" modelAttribute="enyanBookSet" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <c:choose>
                        <c:when test="${record.setId==null}">
                            <spring:message code="bookSet.add"/>
                        </c:when>
                        <c:otherwise>
                            <spring:message code="bookSet.edit"/>
                        </c:otherwise>
                    </c:choose>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active">
                        <c:choose>
                            <c:when test="${record.setId==null}">
                                <spring:message code="bookSet.add"/>
                            </c:when>
                            <c:otherwise>
                                <spring:message code="bookSet.edit"/>
                            </c:otherwise>
                        </c:choose>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <c:choose>
                                    <c:when test="${record.setId==null}">
                                        <spring:message code="bookSet.add"/>
                                    </c:when>
                                    <c:otherwise>
                                        <spring:message code="bookSet.edit"/>
                                    </c:otherwise>
                                </c:choose>
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">书系名称</label>
                                        <div class="col-sm-10">
                                            <form:input path="setName" cssClass="form-control" id="setName" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">书系名称繁体</label>
                                        <div class="col-sm-10">
                                            <form:input path="setNameTc" cssClass="form-control" id="setNameTc" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">书系名称英文</label>
                                        <div class="col-sm-10">
                                            <form:input path="setNameEn" cssClass="form-control" id="setNameEn" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="bannerUrl">Banner Url(可选)</label>
                                        <div class="col-sm-10">
                                            <form:input path="bannerUrl" cssClass="form-control" id="bookCover" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="bannerUrl">封面 Url(可选)</label>
                                        <div class="col-sm-10">
                                            <form:input path="bookCover" cssClass="form-control" id="bookCover" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">首页排序优先级（0～100，不可为空，默认为0）</label>
                                        <div class="col-sm-10">
                                            <form:input path="showOrder" cssClass="form-control" id="discountValue" maxlength="4"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="bibleVersion">关于首页</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="isIndex" value="0" checked="checked"/>非首页
<%--                                            <form:radiobutton path="isIndex" value="1" id="bibleVersion"/>在首页列表--%>
                                            <form:radiobutton path="isIndex" value="2" id="bibleVersion"/>在首页套系推荐
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">折扣值（0～100，不可为空，默认为100）</label>
                                        <div class="col-sm-10">
                                            <form:input path="discountValue" cssClass="form-control" id="discountValue" maxlength="4"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">价格（可以为空,只在折扣值为100时生效）</label>
                                        <div class="col-sm-10">
                                            <form:input path="price" cssClass="form-control" id="price" maxlength="10"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">折扣后价格（可以为空,只在折扣值为100时生效）</label>
                                        <div class="col-sm-10">
                                            <form:input path="priceDiscount" cssClass="form-control" id="priceDiscount" maxlength="10"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setName">是否全套购买</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="canAllBuy" value="0" />否
                                            <form:radiobutton path="canAllBuy" value="1" id="bibleVersion"/>可以
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setAbstract">书系介绍(可选)</label>
                                        <div class="col-sm-10">
                                            <form:textarea path="setAbstract" cssClass="form-control ckeditor" id="setAbstract" rows="3" maxlength="1000"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="setAbstract">书系版本<a href="https://www.json.cn/" target="_blank">校验</a></label>
                                        <div class="col-sm-10">
                                            {"values":[{"name":"英文版","other":"English Version","value":"1"}]}
                                            <form:textarea path="bookWebVersions" cssClass="form-control" id="bookWebVersions"  rows="6"/>
                                        </div>
                                    </div>
                                    <%--
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="isValid">状态</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="isValid" value="1" id="isValid"/>有效
                                            <form:radiobutton path="isValid" value="0"/>无效
                                        </div>
                                    </div>
                                    --%>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <form:hidden path="setId"/>
                                        <form:hidden path="isValid" value="1"/>
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                    </div>
                                </form>

                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#bookSetList").addClass("active");
    })
</script>
<script type="text/javascript" src="<c:url value='/statics/js/ckeditor/ckeditor.js' />"></script>
</body>
</html>
