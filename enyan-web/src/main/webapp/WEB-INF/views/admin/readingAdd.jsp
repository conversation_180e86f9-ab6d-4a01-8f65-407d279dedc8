<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-daterangepicker/daterangepicker-bs3.css' />" />
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-datepicker/css/datepicker-custom.css' />" />
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="save?${_csrf.parameterName}=${_csrf.token}" modelAttribute="record" method="post" enctype="multipart/form-data"  role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <c:choose>
                        <c:when test="${record.dataId==null}">
                            <spring:message code="reading.add"/>
                        </c:when>
                        <c:otherwise>
                            <spring:message code="reading.edit"/>
                        </c:otherwise>
                    </c:choose>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active">
                        <c:choose>
                            <c:when test="${record.dataId==null}">
                                <spring:message code="reading.add"/>
                            </c:when>
                            <c:otherwise>
                                <spring:message code="reading.edit"/>
                            </c:otherwise>
                        </c:choose>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <c:choose>
                                    <c:when test="${record.dataId==null}">
                                        <spring:message code="reading.add"/>
                                    </c:when>
                                    <c:otherwise>
                                        <spring:message code="reading.edit"/>
                                    </c:otherwise>
                                </c:choose>
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataName">读书会名称</label>
                                        <div class="col-sm-10">
                                            <form:input path="dataName" cssClass="form-control" id="dataName" maxlength="40"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataImgUrl">图片的url</label>
                                        <div class="col-sm-10">
                                            <form:input path="dataImgUrl" cssClass="form-control" id="dataImgUrl" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataToUrl">转向的url</label>
                                        <div class="col-sm-10">
                                            <form:input path="dataToUrl" cssClass="form-control" id="dataToUrl" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataBuyUrl">购买书籍的url</label>
                                        <div class="col-sm-10">
                                            <form:input path="dataBuyUrl" cssClass="form-control" id="dataBuyUrl" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataPriority">推荐次序</label>
                                        <div class="col-sm-10">
                                            <form:input path="dataPriority" cssClass="form-control" id="dataPriority" maxlength="4"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataStatus">活动状态（手动启用，自动关闭）</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="dataStatus" value="0" /><spring:message code="reading.status.0"/>
                                            <form:radiobutton path="dataStatus" value="1" id="dataStatus"/><spring:message code="reading.status.1"/>
                                            <form:radiobutton path="dataStatus" value="2" id="dataStatus"/><spring:message code="reading.status.2"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataReadShow">起止日期</label>
                                        <div class="col-sm-10 input-group input-large">
                                            <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="请选择时间范围"/>
                                            <script>
                                                laydate.render({
                                                    elem: '#rangeDate'
                                                    ,format: 'yyyy/MM/dd HH:mm:ss'
                                                    ,type: 'datetime'
                                                    ,range: true
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="dataReadShow">读书会首页显示</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="dataReadShow" value="0" />否
                                            <form:radiobutton path="dataReadShow" value="1" id="dataReadShow"/>是
                                        </div>
                                    </div>

                                    <div class="col-lg-offset-2 col-lg-10">
                                        <form:hidden path="dataId"/>
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                    </div>
                                </form>
                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#readingList").addClass("active");
    })
</script>
<script type="text/javascript" src="<c:url value='/statics/js/ckeditor/ckeditor.js' />"></script>
</body>
</html>
