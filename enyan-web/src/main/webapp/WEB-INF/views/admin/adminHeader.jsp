<%@ page language="java" contentType="text/html; charset=utf-8"%>
<%@taglib uri="http://www.springframework.org/tags/form" prefix="form" %> 
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Header</title>
<script type="text/javascript" src="js/flash.js"></script>
<script type="text/javascript">
function correctPNG() 
   {
   for(var i=0; i<document.images.length; i++)
      {
      var img = document.images[i]
      var imgName = img.src.toUpperCase()
      if (imgName.substring(imgName.length-3, imgName.length) == "PNG")
         {
         var imgID = (img.id) ? "id='" + img.id + "' " : ""
         var imgClass = (img.className) ? "class='" + img.className + "' " : ""
         var imgTitle = (img.title) ? "title='" + img.title + "' " : "title='" + img.alt + "' "
         var imgStyle = "display:inline-block;" + img.style.cssText 
         if (img.align == "left") imgStyle = "float:left;" + imgStyle
         if (img.align == "right") imgStyle = "float:right;" + imgStyle
         if (img.parentElement.href) imgStyle = "cursor:hand;" + imgStyle        
         var strNewHTML = "<span "+ imgID + imgClass + imgTitle + " style=\"" + "width:" + img.width + "px; height:" + img.height + "px;" + imgStyle + ";" + "filter:progid:DXImageTransform.Microsoft.AlphaImageLoader" + "(src='" + img.src + "', sizingMethod='scale');\"></span>" 
   img.outerHTML = strNewHTML
         i = i-1
         }
      }
   }
window.attachEvent("onload", correctPNG);
</script>

<style type="text/css">
*{margin:0; padding:0}
body{background:#1f8be5 url(images/headerBg.jpg) no-repeat; font-size:14px}
img{border:none}
#logo{width:340px; height:80px}
.top{background:url(images/headerR.jpg) no-repeat right top}
#sub{list-style-type:none; height:60px; margin-left:30px;  white-space:nowrap; overflow:hidden}
#sub li{float:left; width:70px; height:52px; margin:5px 4px; text-align:center; font-weight:bold}
#sub li a{display:block; width:70px; height:52px; text-decoration:none; color:#fff}
#sub li a:hover{display:block; width:70px; height:52px; border:1px solid #39f; color:#f60}
#lable{float:right; width:560px; height:28px; line-height:28px; font-size:12px;}
html>body #lable{background-repeat:repeat;background-image:url(images/topMenuBg.png);}
* #lable{filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled=true, sizingMethod=scale, src='images/topMenuBg.png')}
#lable span{float:right; margin-right:25px}
#lable span a{position:relative; text-decoration:none;}
#lable span a:hover{color:#f60}
</style>

</head>

<body oncontextmenu="return false;">

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td width="340"><div id="logo"><script type="text/javascript">LoadFlash("flash/logo.swf",340,80,"logo")</script></div></td>
    <td>
	<table width="100%" border="0" cellpadding="0" cellspacing="0" class="top">
      <tr>
        <td height="62">
<ul id="sub">
  <li><a href="${advSetFlashAdminUrl}" target="dataMain"><img src="images/fun01.png" alt='<s:text name="left.flash"/>' /><br /><s:text name="left.flash"/></a></li>
  <li><a href="${advSetIndexAdminUrl}" target="dataMain"><img src="images/fun02.png" alt='<s:text name="left.index"/>' /><br /><s:text name="left.index"/></a></li>
  <li><a href="${advSetSogouAdminUrl}" target="dataMain"><img src="images/fun03.png" alt='<s:text name="left.sogou"/>' /><br /><s:text name="left.sogou"/></a></li>
  <li><a href="${inforAdminUrl}" target="dataMain"><img src="images/fun05.png" alt="个人信息" /><br />个人信息</a></li>
  <!--<li><a href="${notInboxUrl}" target="dataMain"><img src="images/fun04.png" alt="收件箱" /><br /><s:text name="left.note.inbox"/></a></li>
  <li><a href="${notAddUrl}" target="dataMain"><img src="images/fun06.png" alt="发通知" /><br /><s:text name="left.note.send"/></a></li>
  <li><a href="${alarmLogAdminUrl}" target="dataMain"><img src="images/fun07.png" alt="报警日志" /><br /><s:text name="left.log.alarm"/></a></li>
  <li><a href="alert/offline-warning.html" target="dataMain"><img src="images/fun08.png" alt="断线报警" /><br />断线报警</a></li>-->
</ul>
</td>
      </tr>
      <tr>
        <td headers="28"><div id="lable">
   <span><a href="${inforAdminUrl}" target="dataMain"><s:text name="left.infor"/></a> - <a href="" target="_blank">使用帮助</a> - <a href="${adminLogoutUrl}" target="_top"><s:text name='left.exit'/></a></span>&nbsp;&nbsp;欢迎您，<s:property value="#session.user_session.account"/>&nbsp;  今天是<script type="text/javascript">date_show()</script>

</div></td>
      </tr>
    </table></td>
  </tr>
</table>
</body>
</html>
