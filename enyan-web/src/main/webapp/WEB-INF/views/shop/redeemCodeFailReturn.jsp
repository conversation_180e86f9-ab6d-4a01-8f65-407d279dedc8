<%--
  User: Aaron
  Date: 2017/12/14
  Time: 上午8:58
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="fail.redeemCode"/> </title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->


    <c:choose>
        <c:when test="${pageContext.response.locale == 'zh_CN'}">
            <div class="container padding-bottom-3x mb-2 padding-top-2x">
                <div class="card text-center">
                    <div class="card-block padding-top-2x">
                        <h3 class="card-title"><spring:message code="fail.redeemCode"/>！</h3>
                        <p class="card-text">您已拥有该兑换码中的书籍，请勿重复兑换。</p>
                        <p class="card-text">该兑换码可转赠他人使用。</p>
                        <p>&nbsp;</p>
                    </div>
                </div>
            </div>
        </c:when>
        <c:when test="${pageContext.response.locale == 'en_US'}">
            <div class="container padding-bottom-3x mb-2 padding-top-2x">
                <div class="card text-center">
                    <div class="card-block padding-top-2x">
                        <h3 class="card-title"><spring:message code="fail.redeemCode"/>！</h3>
                        <p class="card-text">You already own the book(s) in this redeem code. Please avoid redeeming it again.</p>
                        <p class="card-text">The code can be gifted to others.</p>
                        <p>&nbsp;</p>
                    </div>
                </div>
            </div>
        </c:when>
        <c:otherwise>
            <div class="container padding-bottom-3x mb-2 padding-top-2x">
                <div class="card text-center">
                    <div class="card-block padding-top-2x">
                        <h3 class="card-title"><spring:message code="fail.redeemCode"/>！</h3>
                        <p class="card-text">您已擁有該兌換碼中的書籍，請勿重復兌換。</p>
                        <p class="card-text">該兌換碼可轉贈他人使用。</p>
                        <p>&nbsp;</p>
                    </div>
                </div>
            </div>
        </c:otherwise>
    </c:choose>

    <!-- Page pay -->


    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
</body>
</html>
