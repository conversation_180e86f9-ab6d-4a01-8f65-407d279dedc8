<%--
  User: Aaron
  Date: 2017/12/14
  Time: 上午8:58
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="book.download"/> </title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <!-- Modernizr-->
    <jsp:include page="track-info.jsp"/>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1><spring:message code="book.download"/></h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="/index"><spring:message code="shop.index"/> </a></li>
                    <li class="separator">&nbsp;</li>
                    <li><a href="myCenter"><spring:message code="info.own"/> </a></li>
                    <li class="separator">&nbsp;</li>
                    <li><a href="myOrders"><spring:message code="my.order"/> </a></li>
                    <li class="separator">&nbsp;</li>
                    <li><spring:message code="book.download"/></li>
                </ul>
            </div>
        </div>
    </div>
    <!-- Page pay -->
    <c:choose>
        <c:when test="${pageContext.response.locale == 'zh_CN'}">
            <div class="container padding-bottom-3x mb-2">
                <div class="card text-center">
                    <div class="card-block padding-top-2x">
                        <%--<h3 class="card-title">支付成功！请下载您所购买的电子书。</h3>--%>
                        <p class="card-text">本书当前仅支持在恩道电子书App阅读</p>
                        <p class="card-text">请点击<span class="text-medium">"App下载"</span>了解详情</p>
                        <p class="card-text">已安装的用户请直接进入App阅读本书</p>
                        <p class="card-text">预售图书需等待正式上架后方可阅读</p>
                        <div class="padding-top-1x padding-bottom-1x">
                            <a class="btn btn-outline-primary" href="index-Reader">App下载</a>
                        </div>
                    </div>
                </div>
            </div>
        </c:when>
        <c:when test="${pageContext.response.locale == 'en_US'}">
            <div class="container padding-bottom-3x mb-2">
                <div class="card text-center">
                    <div class="card-block padding-top-2x">
                            <%--<h3 class="card-title">支付成功！请下载您所购买的电子书。</h3>--%>
                        <p class="card-text">Purchased e-books can only be read in Inspirata eBooks App,</p>
                        <p class="card-text">please <span class="text-medium">download the App</span> to your device and sign in to read the book.</p>
                        <p class="card-text">Already downloaded? Open the App to read the e-book.</p>
                        <p class="card-text">Pre-order books can only be read after it's released.</p>
                        <div class="padding-top-1x padding-bottom-1x">
                            <a class="btn btn-outline-primary" href="index-Reader">Download</a>
                        </div>
                    </div>
                </div>
            </div>
        </c:when>
        <c:otherwise>
            <div class="container padding-bottom-3x mb-2">
                <div class="card text-center">
                    <div class="card-block padding-top-2x">
                            <%--<h3 class="card-title">支付成功！請下載您所購買的電子書。</h3>--%>
                        <p class="card-text">本書當前僅支持在恩道電子書App閱讀</p>
                        <p class="card-text">請點擊<span class="text-medium">"App下載"</span>了解詳情</p>
                        <p class="card-text">已安裝的用戶請直接進入App閱讀本書</p>
                        <p class="card-text">預售圖書需等待正式上架後方可閱讀</p>
                        <div class="padding-top-1x padding-bottom-1x">
                            <a class="btn btn-outline-primary" href="index-Reader">App下載</a>
                        </div>
                    </div>
                </div>
            </div>
        </c:otherwise>
    </c:choose>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
</body>
</html>
