<%--
  User: Aaron
  Date: 2017/12/8
  Time: 下午10:28
--%>
<%@ page import="java.util.Locale" %>
<%@ page import="com.aaron.spring.common.WebUtil" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%
    String baseServerBuyPath = WebUtil.getBaseBuyPath();
    request.setAttribute("baseServerBuyPath",baseServerBuyPath);
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>${enyanBook.bookTitle}-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='shop.title'/>在线销售正版《${enyanBook.bookTitle}》。最新《${enyanBook.bookTitle}》简介、书评、试读、价格、图片等相关信息，尽在${baseServerBuyPath}，网购《${enyanBook.bookTitle}》，就上<spring:message code='shop.title'/>。">
    <meta name="keywords" content="${enyanBook.bookKeywords}">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="${_csrf.token}">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
    <style>
        .comment-text {
            color: #1b1b1b;
        }

        .comment-form .form-group label {
            padding-left: 0px;
        }

        .comment-form .form-control-rounded {
            border-radius: 0px;
        }

        .comment-form .form-group {
            margin-bottom: 8px !important;
            font-size: 12px;
        }

        .comment-form .form-control-sm:not(textarea) {
            height: 28px;
        }

        .comment-form .text-sm {
            color: #7a7a7a;
            font-size: 13px;
        }

        .comment-footer-info {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }

        .comment-form .btn {
            margin: 0;
        }

        .comment-meta {
            font-size: 12px;
        }

        .comment-meta img {
            padding: 0 3px 2px 0;
        }

        .info-sm img {
            height: 16px;
        }

        .font-stars {
            font-family: 'Source Han Serif SC';
            font-style: normal;
            font-weight: 600;
            font-size: 32px;
            line-height: 46px;
            color: #EB4742;
        }

        form .textarea-counter {
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            margin: 0;
            padding: 12px 28px;
            font-size: 14px;
            color: #B2B2B2 !important;
        }

        .form-group textarea::placeholder {
            color: #b2b2b2;
        }

        .form-group input::placeholder {
            color: #b2b2b2;
        }

        .text-muted-number {
            color: #cfcfcf !important;
        }

        .modal-body-d {
            padding: 26px 32px 0 32px;
        }

        .rating-stars .star-number {
            color: #EB4742;
            font-family: 'Source Han Serif SC';
            font-style: normal;
            font-weight: 600;
            font-size: 32px;
            line-height: 46px;
        }

        .btn-sm {
            height: 26px;
            padding: 0 12px;
            border-radius: 12px;
            font-size: 12px;
            line-height: 24px;
        }
    </style>
    <style>
        #starMark {
            display: flex;
        }
        #starMark p {
            position: relative;
            margin: 0;
        }
        #starMark p:first-child {
            margin-right: 3px;
            letter-spacing: 1px;
        }
        #starMark p svg {
            cursor: pointer;
            margin-right: 2px;
        }
        #starMark p:last-child svg {
            margin-right: 0;
        }
        #starMark p:hover span {
            visibility: visible;
        }
        #starMark p span {
            position: absolute;
            top: -18px;
            left: -3px;
            font-size: 12px;
            width: 28px;
            cursor: pointer;
            visibility: hidden;
        }
        #comments-list li {
            list-style: none;
        }
        #comments-list img {
            cursor: pointer;
        }
        #comments-list > li {
            border-bottom: 1px solid #F7F7F7;
        }
        #comments-list > li:last-child {
            border: none;
        }
        #comments-list, #comments-list ul {
            padding-left: 0;
        }
        #comments-list .comment-title svg {
            position: relative;
            top: 5px;
        }
        #comments-list .nickName {
            position: relative;
            top: 2px;
            font-size: 15px;
        }
        #comments-list .parentCommentText {
            font-size: 15px;
            line-height: 24px;
        }
        #comment-form #commentTitle::placeholder {
            font-size: 15px;
            line-height: 22px;
            color: #B2B2B2;
        }
        #comment-form #commentContents {
            padding-top: 5px;
        }
        #comment-form #commentContents::placeholder {
            font-size: 15px;
            line-height: 22px;
            color: #B2B2B2;
        }
        #comment-form .infoIcon, #comments-list .infoIcon  {
            position: relative;
            bottom: 0;
        }
        #comment-form #submitComment, #comments-list .sub-comment-form {
            min-width: 64px;
            height: 30px;
            border-radius: 50px;
        }
        #comments-list .textarea-counter {
            padding-bottom: 9px;
        }
        #comments-list .commentTitleContent {
            position: relative;
            top: 2px;
            left: 2px;
            font-size: 15px;
            font-weight: 600;
        }
        [class*="subComments-"] ul > li hr {
            margin-top: 18px;
        }
        [class*="subComments-"] ul > li:first-of-type {
            padding-top: 2px;
        }
        [class*="subComments-"] ul > li:last-of-type hr {
            display: none;
        }
        [class*="subComments-"] ul > li:last-of-type .comment {
            margin-bottom: 0;
        }
        .viewMore, .viewMoreSub {
            display: none;
            margin: 0 auto;
            padding: 0 20px;
            width: 96px;
            height: 30px;
            line-height: 30px;
            border-radius: 50px;
            background-color: #f7f7f7;
            text-align: center;
            cursor: pointer;
        }
        .viewMoreSub {
            display: inline-block;
            width: auto;
        }
        [class*="sub-arrow-down-"] {
            width: 100%;
            text-align: center;
            cursor: pointer;
        }
        [class*="sub-arrow-down-"] img{
            position: relative;
            top: -1px;
        }
        .noComment {
            display: none;
            width: 100%;
            text-align: center;
        }
        .text7A {
            color: #7a7a7a !important;
        }
        .parentCommentText, .commentText {
            white-space: pre-line;
        }
        .turnReviews {
            cursor: pointer;
        }
        .fz12 {
            font-size: 12px !important;
        }
        .imgInfo {
            height: 30px;
            line-height: 30px;
        }
    </style>
    <style>
        #mayBeInterested {
            margin-top: 20px;
            font-size: 16px;
            color: #1b1b1b;
            letter-spacing: 0px;
            line-height: 24px;
            font-weight: 500;
        }

        #booksetList-wrapper {
            display: flex;
            justify-content: start;
            flex-wrap: nowrap;
            width: 100%;
        }

        @media screen and (min-width: 1200px) {
            #booksetList-wrapper {
                justify-content: space-between;
            }

            #booksetList-wrapper.less-booksetList {
                justify-content: start !important;
            }

            #booksetList-wrapper li {
                margin-right: 14px;
            }
        }

        @media screen and (max-width: 576px) {
            #booksetList-wrapper li {
                margin-right: 14px;
            }
        }

        #booksetList-wrapper li {
            margin-right: 30px;
            padding-top: 12px;
            width: 92px;
            list-style: none;
        }

        #booksetList-wrapper .last-booksetItem {
            margin-right: 0;
        }

        #booksetList-wrapper .fake-li-item {
            padding: 0;
            width: 0;
            height: 0;
        }

        #booksetList-wrapper li img {
            margin: 0 auto;
            width: 92px;
            height: 134px;
            border-radius: 7px;
            box-shadow: 3px 6px 6px 0 rgb(47 56 68 / 12%);
            cursor: pointer;
        }

        #booksetList-wrapper li .booksetItem-container {
            width: 92px;
        }

        @media screen and (max-width: 1199px) {
            #booksetList-wrapper {
                justify-content: start;
                flex-wrap: nowrap;
                width: 100%;
                overflow-x: auto;
            }

            #booksetList-wrapper li.fake-li-item {
                display: none;
            }
        }

        @media screen and (max-width: 402px) {
            .product-tabs .nav-tabs .nav-link {
                padding: 10px 13px;
            }
        }

        @media screen and (max-width: 384px) {
            .product-tabs .nav-tabs .nav-link {
                padding: 10px 11px;
            }
        }

        @media screen and (max-width: 368px) {
            .product-tabs .nav-tabs .nav-link {
                padding: 10px 10px;
            }
        }

        @media screen and (max-width: 360px) {
            .product-tabs .nav-tabs .nav-link {
                padding: 10px 8px;
            }
        }

        #recommendList-wrapper {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            width: 100%;
        }

        @media screen and (max-width: 840px) {
            #recommendList-wrapper {
                justify-content: space-around;
            }
        }

        #recommendList-wrapper li {
            margin-right: 5px;
            padding-top: 12px;
            width: 124px;
            list-style: none;
        }

        #recommendList-wrapper li .recommend-container {
            width: 124px;
        }

        @media screen and (max-width: 840px) {
            #recommendList-wrapper li {
                margin-right: 0;
                flex: 33.33%;
            }

            #recommendList-wrapper li .recommend-container {
                margin: 0 auto;
            }
        }

        @media screen and (max-width: 420px) {
            #recommendList-wrapper li {
                flex: 50%;
            }
        }

        #recommendList-wrapper li:nth-child(6) {
            margin-right: 0;
        }

        #recommendList-wrapper li img {
            margin: 0 auto;
            width: 124px;
            height: 185px;
            border-radius: 7px;
            box-shadow: 3px 6px 6px 0 rgb(47 56 68 / 12%);
            cursor: pointer;
        }

        #recommendList-wrapper li .recommendBookName, #booksetList-wrapper li .booksetItemBookName {
            margin-top: 10px;
            margin-bottom: 15px;
            font-size: 14px;
            letter-spacing: 0px;
            line-height: 22px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            max-height: 44px;
        }

        #booksetList-wrapper li .booksetItemBookName {
            font-size: 12px;
            line-height: 18px;
        }

        #recommendList-wrapper li .recommendBookName a, #booksetList-wrapper li .booksetItemBookName a{
            transition: color .3s;
            color: #1b1b1b;
            text-decoration: none;
        }

        #recommendList-wrapper li .recommendBookName a:hover, #booksetList-wrapper li .booksetItemBookName a:hover{
            color: #cc4646;
        }

        #booksetName {
            color: #cc4646;
            font-size: 14px;
            text-decoration: underline;
        }

        #bookSet {
            font-size: 14px;
        }
    </style>
</head>
<!-- Body-->
<body>
<!-- Delete Modal-->
<div class="modal fade" id="deleteCommentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body modal-content-d">
                <p id="deleteReview">确定删除该评论？</p>
            </div>
            <div class="modal-footer">
                <button id="cancelReviewBtn" class="btn btn-outline-secondary btn-sm" type="button" data-dismiss="modal">取消</button>
                <button id="deleteReviewBtn" class="btn btn-primary btn-sm delCommentBtn" type="button">删除</button>
            </div>
        </div>
    </div>
</div>
<!-- 弹出窗 -->
<div class="modal fade" id="modal-give" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5 class="modal-title  w-100"><spring:message code="gift.title"/></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body padding-top-2x">
                <div>
                    <span><spring:message code="gift.explain.choose"/></span>
                    <div style="display: inline-block;margin-left:1em;">
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-counter" data-inc="-1" disabled="true">-</button>
                        <span id="count" class="align-middle" style="padding-right: 18px;">1</span>
                        <input type="hidden" id="giftBookId" name="giftBookId" value="${enyanBook.bookId}">
                        <button type="button" class="btn btn-outline-secondary btn-sm btn-counter" data-inc="1">+</button>
                    </div>
                    <p><spring:message code="label.explain"/>：<br/>
                        <spring:message code="gift.explain.1"/>
                        <spring:message code="gift.explain.2"/>
                        <spring:message code="gift.explain.3"/>
                        <spring:message code="gift.explain.4"/>
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline-secondary btn-sm" type="button" data-dismiss="modal"><spring:message code="alert.cancel"/> </button>
                <a class="btn btn-primary btn-sm" href="orderConfirmGift-${enyanBook.bookId}-1" id="btn-buy"><spring:message code="alert.continue"/> </a>
            </div>
        </div>
    </div>
</div>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-1x padding-top-2x">
        <div class="row">
            <!-- Poduct -->
            <div class="col-md-4 offset-lg-1">
                <div class="product-gallery">
                    <!-- 产品图片 -->
                    <div class="product-carousel owl-carousel"><a class="product-thumb" href="#">
                        <div class="product-single text-center" data-hash="one"><div class="product-badge text-danger text-left">
                            <c:choose>
                                <c:when test="${enyanBook.discountIsValid == 1}">
                                    <spring:message code="discount.icon"/><br>
                                </c:when>
                                <c:otherwise>
                                    <c:if test="${enyanBook.discountSingleIsValid == 1}">
                                        <at:bookInfo infoType="discountSingle" enyanBook="${enyanBook}"/><br>
                                    </c:if>
                                </c:otherwise>
                            </c:choose>
                            <c:if test="${enyanBook.salesModel == 1}">
                                <div class="text-right"><spring:message code="book.presale"/></div>
                            </c:if>

                        </div><img src="${enyanBook.bookCover}" alt="Product"></div></a>
                    </div>
                    <%--<c:if test="${not empty enyanBook.bookSample}">
                    <!-- 增加试读 -->
                        <div class="text-center mt-3">
                            <a class="text-decoration-none" href="${enyanBook.bookSample}" target="_blank"><i class="icon-paper"></i><small>&nbsp;<spring:message code="book.part"/></small></a>
                        </div>
                    </c:if>--%>
                    <c:choose>
                        <c:when test="${enyanBook.starCount gt 0}">
                            <fmt:parseNumber value="${enyanBook.star}" var="bookStar" type="number"/>
                            <div class="rating-stars d-flex mt-3 align-items-center justify-content-center turnReviews">
                                <div class="column star-number align-items-start mr-2"><span>${enyanBook.star}</span></div>
                                <div class="column">
                                    <at:bookInfo infoType="star" enyanBook="${enyanBook}"></at:bookInfo>
                                    <span class="text-muted text-sm ml-1" style="position: relative;bottom: 4px;"><spring:message code="comment.star.count" arguments="${enyanBook.starCount}" htmlEscape="false"/></span>
                                </div>
                            </div>
                        </c:when>
                        <c:otherwise>

                        </c:otherwise>
                    </c:choose>
                </div>
                <c:if test="${enyanBook.starCount le 0}">
                    <div class="text-center mt-2">
                        <p class="text-muted fz12" id="noReviewsForNow" style="margin-bottom: 10px;"><spring:message code="comment.none"/></p>
                        <p class="comment-text turnReviews fz12">
                            <a class="navi-link" href="javascript:;"><span id="toFirstReview" class="u-line"><spring:message code="comment.go"/></span>&nbsp;></a>
                        </p>
                    </div>
                </c:if>
            </div>
            <!-- Product Info -->
            <div class="col-md-8 col-lg-6 col-xl-6">
                <div class="padding-top-2x mt-2 hidden-md-up"></div>
                <h3 class="text-normal">
                    <c:if test="${enyanBook.salesModel == 1}">
                        <spring:message code="book.title.presale"/>
                    </c:if>
                    ${enyanBook.bookTitle}
                </h3>
                <c:if test="${not empty enyanBook.bookAbstract}">
                    <p class="text-muted">${enyanBook.bookAbstract}</p>
                </c:if>

                <div class="mb-3">
                    <div class="h4 text-left">
                    <at:bookPrice enyanBook="${enyanBook}" currency="${currency}" bookInfo="true"/>
                  </div>
                    <div class="d-inline text-muted"><spring:message code="book.currency.label"/></div>
                </div>

                <c:if test="${enyanBook.discountIsValid == 1}">
                    <div class="d-inline">
                        <div class="text-decoration-none">
                            <span class="red-bg"><spring:message code="discount.icon"/></span>
                            <a class="navi-link text-danger" href="category-0-grid-0-0-0-0-1-0-0-0"><at:bookInfo infoType="discount" enyanBook="${enyanBook}"/></a>
                        </div>
                    </div>
                </c:if>
                <div class="pt-1">
                    <p class="d-inline"><spring:message code="book.author"/> :</p>
                    <div class="d-inline text-muted">
                        <c:if test="${not empty enyanBook.author}">
                            <c:set var="authors" value="${fn:split(enyanBook.author, '#')}" />
                            <c:forEach var="author" items="${authors}">
                                <a class="navi-link" href="searchAuthor?searchText=${author}">${author}</a>
                            </c:forEach>
                        </c:if>
                    </div>
                </div>

                <c:if test="${not empty enyanBook.translator}">
                    <div class="pt-1">
                        <p class="d-inline"><spring:message code="book.translator"/> :</p>
                        <div class="d-inline"><a class="navi-link" href="#">${enyanBook.translator}</a></div>
                    </div>
                </c:if>
                <%--
                <c:if test="${enyanBook.setId gt 0}">
                    <div class="pt-1">
                        <p class="d-inline"><spring:message code="book.set"/> :</p>
                        <div class="d-inline"><a class="navi-link" href="/store/bookset-${enyanBook.setId}#?locale=${pageContext.response.locale}"><span class="u-line text-primary"> ${enyanBook.setName}</span></a></div>
                    </div>
                </c:if>--%>
                <c:if test="${enyanBook.showPublisher == 1}">
                    <div class="pt-1">
                        <p class="d-inline"><spring:message code="book.publisher"/> :</p>
                        <div class="d-inline text-muted"><a class="navi-link" href="category-0-grid-0-${enyanBook.publisherId}-0-0-0-0-0-0"><at:publisherName publisherId="${enyanBook.publisherId}"/></a></div>
                    </div>
                </c:if>
                <c:choose>
                    <c:when test="${not empty enyanBook.bookWebInfo.versions}">
                        <div class="pt-1">
                            <p class="d-inline"><spring:message code="book.version"/> :</p>
                            <div class="d-inline">
                                <c:choose>
                                    <c:when test="${pageContext.response.locale == 'en_US'}">
                                        <c:forEach var="obj" items="${enyanBook.bookWebInfo.versions}">
                                            <a class="navi-link mr-2" href="book-${obj.value}#" target="_blank"><span class="u-line text-primary">${obj.other}</span></a>
                                        </c:forEach>
                                    </c:when>
                                    <c:otherwise>
                                        <c:forEach var="obj" items="${enyanBook.bookWebInfo.versions}">
                                            <a class="navi-link mr-2" href="book-${obj.value}#" target="_blank"><span class="u-line text-primary">${obj.name}</span></a>
                                        </c:forEach>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </div>
                    </c:when>
                    <c:otherwise>
                        <div class="pt-1 text-muted">
                            <p class="d-inline"><spring:message code="book.version"/> :</p>
                            <div class="d-inline">
                                <spring:message code="book.supply.none"/>
                            </div>
                        </div>
                    </c:otherwise>
                </c:choose>

        <c:choose>
            <c:when test="${not empty enyanBook.bookWebInfo.salePapers}">
                <div class="mt-3">
                    <a class="text-medium text-decoration-none" href="${enyanBook.bookWebInfo.salePapers[0].value}" target="_blank"><spring:message code="buy.paperbook"/> &nbsp;&gt;&gt;</a>
                </div>
            </c:when>
            <c:otherwise>
                <div class="pt-1 text-muted">
                    <p class="d-inline"><spring:message code="book.version.paper"/> :</p>
                    <div class="d-inline">
                        <spring:message code="book.supply.none"/>
                    </div>
                </div>
            </c:otherwise>
        </c:choose>
        <c:choose>
            <c:when test="${isBought}">
                <div class="sp-buttons mt-3 mb-4">
                    <a class="btn btn-primary mt-2 disabled" href="#"><spring:message code="buy.done"/></a>
                    <button class="btn btn-outline-primary btn-round mt-2 disabled" data-toast data-toggle="tooltip" title="<spring:message code='label.cart'/>" >
                        <spring:message code="cart.add"/>
                    </button>
                    <a class="btn btn-outline-primary mt-2"  data-toggle="modal" data-target="#modal-give"><spring:message code="gift.buy"/></a>
                </div>
            </c:when>
            <c:otherwise>
                <sec:authorize access="isAuthenticated() or isRememberMe()">
                    <div class="sp-buttons mt-4 mb-4 padding-bottom-1x">
                        <a class="btn btn-primary btn-md" href="${baseServerBuyPath}orderConfirmBook-${enyanBook.bookId}">
                            <c:choose>
                                <c:when test="${enyanBook.salesModel == 1}">
                                    <spring:message code="buy.presale"/>
                                </c:when>
                                <c:otherwise>
                                    <spring:message code="buy.now"/>
                                </c:otherwise>
                            </c:choose>
                        </a>
                        <c:if test="${enyanBook.bookType != 1}">
                            <button class="btn btn-outline-primary btn-md btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                    data-toast-title="${enyanBook.bookTitle}" data-toast-message="<spring:message code='success.cart.add'/>"
                                    data-toggle="tooltip"  onclick="addToCart(${enyanBook.bookId})">
                                <spring:message code="cart.add"/>
                            </button>
                        </c:if>
                        <a class="btn btn-outline-primary btn-md"  data-toggle="modal" data-target="#modal-give"><spring:message code="gift.buy"/></a>
                        <button class="btn btn-outline-secondary btn-md btn-round" data-toast data-toast-type="danger" data-toast-position="topRight"
                                data-toast-title="${enyanBook.bookTitle}" data-toast-message="<spring:message code='success.wishlist.add'/>"
                                data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="addToWish(${enyanBook.bookId})"><i class="icon-heart"></i></button>
                    </div>
                </sec:authorize>
                <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                    <div class="sp-buttons mt-4 mb-4 padding-bottom-1x">
                        <a class="btn btn-primary btn-md" href="${baseServerBuyPath}orderConfirmBook-${enyanBook.bookId}">
                            <c:choose>
                                <c:when test="${enyanBook.salesModel == 1}">
                                    <spring:message code="buy.presale"/>
                                </c:when>
                                <c:otherwise>
                                    <spring:message code="buy.now"/>
                                </c:otherwise>
                            </c:choose>
                        </a>

                        <button class="btn btn-outline-primary btn-md btn-round" data-toggle="tooltip" title="<spring:message code='label.cart'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                            <spring:message code="cart.add"/>
                        </button>
                        <a class="btn btn-outline-primary btn-md"  data-toggle="modal" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><spring:message code="gift.buy"/></a>
                        <button class="btn btn-outline-secondary btn-md btn-round" data-toggle="tooltip" title="<spring:message code='label.wish'/>" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'">
                            <i class="icon-heart"></i>
                        </button>
                    </div>
                </sec:authorize>
            </c:otherwise>
        </c:choose>

                <div class="sp-buttons">
                    <a class="text-medium text-decoration-none" href="index-Reader" target="_blank"><spring:message code="how.read"/> </a>
                </div>


                    <!-- <div class="sp-buttons mt-2 mb-2">
                      <button class="btn btn-outline-secondary btn-sm btn-wishlist" data-toggle="modal" title="<spring:message code='label.wish'/>"><i class="icon-heart"></i></button>
                      <a class="btn btn-outline-primary btn-sm" href="cart.html">加入<spring:message code='label.cart'/></a>
                    </div> -->
                </div>
            </div>
        </div>
    <!-- 书系列表 -->
    <div class="container">
        <div class="row">
            <div class="col-lg-10 offset-lg-1">
                <div class="bookset-details">
                    <p class="mb-2"><span id="bookSet"></span><a href="javascript:;" id="booksetName" style="visibility: hidden;"></a></p>
                    <div id="booksetList-wrapper"></div>
                </div>
            </div>
        </div>
    </div>
    <c:if test="${enyanBook.isCBCS}">
        <!-- Samll Banner -->
        <c:choose>
            <c:when test="${enyanBook.isInCn == 1}">
                <div class="container padding-top-1x">
                    <div class="row">
                        <div class="col-lg-5 col-sm-6 offset-lg-1">
                            <figure class="figure mb-3"><a href="/rent-to-own/"><img src="https://d2.edhub.cc/root/images/banners/product-01.png" alt="Image"></a></figure>
                        </div>
                        <div class="col-lg-5 col-sm-6">
                            <figure class="figure mb-3"><a href="https://cbcs.endao.vip/" target="_blank"><img src="https://d2.edhub.cc/root/images/banners/product-02.png" alt="Image"></a></figure>
                        </div>
                    </div>
                </div>
            </c:when>
            <c:when test="${enyanBook.isInCn == 2}">
                <div class="container padding-top-1x">
                    <div class="row">
                        <div class="col-lg-5 col-sm-6 offset-lg-1">
                            <figure class="figure mb-3" style="padding-left: 15px;"><a href="/rent-to-own/"><img src="https://d2.edhub.cc/root/images/banners/product-01-1.png" alt="Image"></a></figure>
                        </div>
                        <div class="col-lg-5 col-sm-6">
                            <figure class="figure mb-3" style="padding-left: 15px;"><a href="https://cbcs.endao.vip/" target="_blank"><img src="https://d2.edhub.cc/root/images/banners/product-02-1.png" alt="Image"></a></figure>
                        </div>
                    </div>
                </div>
            </c:when>
        </c:choose>
    </c:if>
        <!-- Product Tabs-->
    <div class="container product-tabs">
        <div class="row">
            <div class="col-lg-10 offset-lg-1">
            <ul class="nav nav-tabs" role="tablist" id="navTabContainer">
                    <li class="nav-item"><a class="nav-link active" href="#description" data-toggle="tab" role="tab"><spring:message code="book.description"/> </a></li>
                    <li class="nav-item"><a class="nav-link" href="#tableofcontent" data-toggle="tab" role="tab"><spring:message code="book.catalogue"/> </a></li>
                    <li class="nav-item"><a class="nav-link" href="#info" data-toggle="tab" role="tab"><spring:message code="book.info"/> </a></li>
                <li class="nav-item"><a class="nav-link" href="#reviews" data-toggle="tab" role="tab"><span id="langReview"><spring:message code='comment.tab.name'/></span><span id="allCommentsCount" class="text-muted text-sm text7A"></span></a></li>
                </ul>
                <div class="tab-content">
                    <!-- 图书简介-->
                    <div class="tab-pane fade show active" id="description" role="tabpanel">
                        ${enyanBook.bookDescription}
                    </div>
                    <!-- 图书目录 -->
                    <div class="tab-pane fade" id="tableofcontent" role="tabpanel">
                        ${enyanBook.bookCatalogue}
                    </div>
                    <!-- 基本信息 -->
                    <div class="tab-pane fade" id="info" role="tabpanel">

<%--                        <p class="mb-title"><strong>ISBN：</strong>${enyanBook.bookIsbn}</p>--%>
                        <c:if test="${not empty enyanBook.bookEsin}">
                            <p class="mb-title"><strong><spring:message code="book.esin"/>：</strong>${enyanBook.bookEsin}</p>
                        </c:if>
                        <%--<c:if test="${not empty enyanBook.bookPubCode}">
                            <p class="mb-title"><strong><spring:message code="book.pub.code"/>：</strong>${enyanBook.bookPubCode}</p>
                        </c:if>--%>
                        <c:if test="${not empty enyanBook.publishedAt}">
                            <p class="mb-title"><strong><spring:message code="book.publish.at"/>：</strong>${enyanBook.publishedAt}</p>
                        </c:if>
                        <p class="mb-title"><strong><spring:message code="book.format"/>：</strong><c:choose><c:when test="${enyanBook.ebookFormat == 3}"><spring:message code="book.format.3"/></c:when><c:when test="${enyanBook.ebookFormat == 2}"><spring:message code="book.format.2"/></c:when><c:otherwise><spring:message code="book.format.1"/></c:otherwise></c:choose>
                        </p>
                        <p class="mb-title"><strong><spring:message code="book.lang"/>：</strong><c:choose><c:when test="${enyanBook.isInCn==1}"><spring:message code="book.sc"/></c:when><c:when test="${enyanBook.isInCn==2}"><spring:message code="book.tc"/></c:when><c:otherwise><spring:message code="book.eng"/></c:otherwise></c:choose>
                        </p>
                        <c:if test="${not empty enyanBook.wordCount}">
                            <p class="mb-title"><strong><spring:message code="book.word.count"/>：</strong>${enyanBook.wordCountShow}<%--<spring:message code="book.word.show" arguments="${enyanBook.wordCount}"/>--%></p>
                        </c:if>
                        <c:if test="${not empty enyanBook.productWeb}">
                            <p class="mb-title"><strong><spring:message code="book.product.web"/>：</strong><a class="text-medium text-decoration-none" href="${enyanBook.productWeb}" target="_blank">${enyanBook.productWeb}</a> </p>
                        </c:if>
                        <p class="mb-title"><strong><spring:message code="book.tts"/>：</strong><c:choose><c:when test="${enyanBook.canTts==1}"><spring:message code="action.enable"/></c:when><c:otherwise><spring:message code="action.enable.not"/></c:otherwise></c:choose>
                        </p>
                        <p class="mb-title"><strong><spring:message code="book.pagination"/>：</strong><c:choose><c:when test="${enyanBook.hasBookPagination==1}"><spring:message code="action.enable"/></c:when><c:otherwise><spring:message code="action.enable.not"/></c:otherwise></c:choose>
                        </p>
                    </div>
                    <!-- 评论 -->
                    <div class="tab-pane fade" id="reviews" role="tabpanel">
                        <!-- 登录/注册 -->
                        <div class="padding-top-1x padding-bottom-1x bg-secondary hasNotLogined" style="display: none">
                            <div class="text-center">
                                <p class="comment-text">
                                    <a class="text-decoration-none" id="langLogin" href="/login">登录</a>/<a class="text-decoration-none" id="langReg" href="/reg">注册</a>&nbsp;<span id="langAfter">后可评论</span></p>
                            </div>
                        </div>

                        <!-- 发表 -->
                        <div class="comment hasLogined" style="display: none">
                            <div class="comment-author-ava"><img src="img/male.svg" alt="gender" id="loginSexImg"></div>
                            <div class="comment-header d-flex flex-wrap justify-content-between ml-2 mr-2" style="margin-bottom: -4px;">
                                <label for="review_name" id="cur_username"></label>
                                <div id="starMark" class="mb-2">
                                    <p id="langClickStar">点击星形评分：</p>
                                    <p>
                                        <svg t="1685068467614" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1072" width="20" height="20">
                                            <path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1073"></path>
                                        </svg><span id="langStar1">差劲</span>
                                    </p>
                                    <p>
                                        <svg t="1685068467614" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1072" width="20" height="20">
                                            <path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1073"></path>
                                        </svg><span id="langStar2" style="width: max-content">不好</span>
                                    </p>
                                    <p>
                                        <svg t="1685068467614" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1072" width="20" height="20">
                                            <path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1073"></path>
                                        </svg><span id="langStar3">还行</span>
                                    </p>
                                    <p>
                                        <svg t="1685068467614" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1072" width="20" height="20">
                                            <path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1073"></path>
                                        </svg><span id="langStar4">推荐</span>
                                    </p>
                                    <p>
                                        <svg t="1685068467614" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1072" width="20" height="20">
                                            <path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1073"></path>
                                        </svg><span id="langStar5">力荐</span>
                                    </p>
                                </div>
                            </div>
                            <form id="comment-form" class="row" method="post" style="margin-bottom: 25px;">
                                <div class="col-12">
                                    <div class="form-group">
                                        <input class="form-control form-control-square form-control-sm" id="commentTitle" maxlength="50" placeholder="标题（选填）">
                                    </div>
                                </div>
                                <div class="col-12" style="margin-bottom: -6px;">
                                    <div class="form-group">
                                        <textarea class="form-control form-control-square form-control-sm" id="commentContents" rows="3" placeholder="请写下您对本书的评论..." maxlength="2000" required></textarea>
                                    </div>
                                </div>
                                <div class="col-6 text-left">
                                    <div class="comment-footer-info">
                                        <div class="comment-meta text-left text-sm"><span id="remainInput">还可输入</span><span id="text-count">2000</span><span id="word"> 字</span></div>
                                    </div>
                                </div>
                                <div class="col-6 text-right d-flex justify-content-end align-items-center">
                      <span class="comment-meta text-muted info-sm text7A imgInfo">
                        <img src="./img/info.svg" class="infoIcon"><span id="langToAll">评论对所有人可见</span>
                      </span>
                                    <button id="submitComment" class="btn btn-sm btn-primary" type="submit">发表</button>
                                </div>
                            </form>
                        </div>
                        <ul id="comments-list"></ul>
                        <div class="padding-top-1x padding-bottom-1x noComment">
                            <p class="comment-text text-center" id="langOthers">谈谈本书，使他人受益</p>
                        </div>
                        <div class="viewMore">
                            <p class="comment-text text-center"><span id="viewMore1">查看更多</span></p>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
    </div>
    <!-- 书籍推荐 -->
    <div class="container">
    <div class="row mb-3">
        <div class="col-lg-10 offset-lg-1">
            <div class="book-recommend">
                <p id="mayBeInterested" style="display: none;">您可能感兴趣</p>
                <div id="recommendList-wrapper"></div>
            </div>
        </div>
        </div>
    </div>

        <!-- 新书上架 -->
    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
    <jsp:include page="ad-info.jsp"/>
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script src="<c:url value='/js/bookComments.js?v=240805' />"></script>
<script src="<c:url value='/js/bookRecommend.js?v=241012' />"></script>
<script>
    $(document).ready(
        loadAllComments('',${enyanBook.bookId}, ${isLogin}, ${sex}, '${pageContext.response.locale}'),
        loadRecommendBooks('',${enyanBook.bookId}),
        loadBooksetDetailsList('', ${enyanBook.setId}, ${enyanBook.bookId})
    )
</script>
</body>
</html>
