<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="shop.nav.reading"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="恩道电子书，2025下半年读书会书目">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
    <style>
        @media (min-width: 576px){
            .card-deck .card {
                margin-right: 0px;
                margin-left: 0px;
            }
        }
        @media (max-width: 768px){
            .product-card.product-list .product-thumb {
                width:120px;
                padding:0px 0px;
            }
        }
        @media (max-width: 576px){
            .product-card.product-list .product-thumb {
                width:100%;
            }
        }
        .owl-carousel .owl-dots {
            margin-top: -50px;
        }
        .txt-1 {
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }
        .txt-2 {
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .txt-3 {
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        .txt-4 {
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
        }

    </style>
</head>
<!-- Body-->
<body>

<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">

    <section class="container">
        <div class="owl-carousel" data-owl-carousel="{ &quot;dots&quot;: true, &quot;loop&quot;: true, &quot;autoplay&quot;: true, &quot;data-wrap&quot;: true, &quot;autoplayTimeout&quot;: 4000 }">
            <c:forEach var="list" items="${config.readBanners}">
                <figure><a class="post-thumb" href="${list.toUrl}"><img src="${list.imgUrl}" alt="banner"></a></figure>
            </c:forEach>
        </div>
    </section>

    <!-- 2025上半年读书会书目 开始 -->
    <section class="container padding-top-2x padding-bottom-3x">
        <h3 class="text-center padding-top-1x padding-bottom-1x">2025年上半年读书会书目<br></h3>
        <div class="row" id="contentContainer">
            <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule1">
                <div class="product-card product-list card"><a class="product-thumb" href="/blog-132#">
                    <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content1" style="display: none;"><img src="https://dl.edhub.cc/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://dl.edhub.cc/root/tmp/book_image/detail/1629448805594.png" alt="Product"></a>
                    <div class="product-info" style="vertical-align:top;">
                        <h3 class="product-title"><a href="/blog-132#">人灵魂中上帝的生命</a></h3><br>
                        <p>
                            <span class="txt-1">亨利·斯库格尔（Henry Scougal）</span>
                            <span class="d-inline">橡树文字工作室</span>
                        </p>
                        <p class="txt-4">什么是真正的基督信仰？亨利·斯库格尔（Henry Scougal）在书中这样写道：“真正的基督信仰是人的灵魂与上帝的联合，是真实地有份于上帝的性情，是灵命穿上上帝的形象；或如使徒保罗所言，是‘基督成形’在我们心里。” 愿在一年伊始，通过一同阅读这本情词恳切、鞭辟入里的小书，使我们更加渴慕上帝，在成圣的道路上越跑越坚定、越来越喜乐。</p>
                        <sec:authorize access="isAuthenticated() or isRememberMe()">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="人灵魂中上帝的生命" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏" onclick="addToWish(275)"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="人灵魂中上帝的生命" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车" onclick="addToCart(275)"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                        <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="收藏" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="加入购物车" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule2">
                <div class="product-card product-list card"><a class="product-thumb" href="/blog-133#">
                    <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content2" style="display: none;"><img src="https://dl.edhub.cc/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://dl.edhub.cc/root/images/book_img/2405/IP163_web.jpg" alt="Product"></a>
                    <div class="product-info" style="vertical-align:top;">
                        <h3 class="product-title"><a href="/blog-133#">学习循环(简)</a></h3><br>
                        <p>
                            <span class="txt-1">杜安·埃尔默（Duane H. Elmer） 穆莉尔·埃尔默（Muriel I. Elmer）</span>
                            <span class="d-inline">恩道出版社</span>
                        </p>
                        <p class="txt-4">《学习循环》是为教育工作者而写，但又不局限于这一个群体。事实上所有想要促进更好的认知理解、行为改变和基督徒生命成长的人都可以来阅读。在这本书中，资深教育家杜安‧埃尔默和穆莉尔‧埃尔默夫妇提出了学习循环的模型。</p>
                        <sec:authorize access="isAuthenticated() or isRememberMe()">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="学习循环(简)" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏" onclick="addToWish(1854)"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="学习循环(简)" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车" onclick="addToCart(1854)"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                        <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="收藏" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="加入购物车" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule3">
                <div class="product-card product-list card"><a class="product-thumb" href="/blog-134#">
                    <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content3" style="display: none;"><img src="https://dl.edhub.cc/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://dl.edhub.cc/root/images/book_img/2411/IP197_web.jpg" alt="Product"></a>
                    <div class="product-info" style="vertical-align:top;">
                        <h3 class="product-title"><a href="/blog-134#">复活的救主(简)</a></h3><br>
                        <p>
                            <span class="txt-1">库马赫（F. W. Krummacher）</span>
                            <span class="d-inline">恩道出版社</span>
                        </p>
                        <p class="txt-4">使徒保罗大声宣告说：「若基督没有复活……你们所信的也是枉然。」（林前15:14）在论及基督复活后四十日的作品中，《复活的救主》是极为罕见的。本书探讨的是我们在这地上所能默想的最崇高和最令人愉悦之主题。不论这见证是以何等微弱的形式呈现出来，唯愿主的灵感动许多的心灵，确认这见证的真实！复活节将至，恩道邀请您共同阅读此书，一起默想主的复活，使信心日益坚固。</p>
                        <sec:authorize access="isAuthenticated() or isRememberMe()">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="复活的救主(简)" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏" onclick="addToWish(2097)"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="复活的救主(简)" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车" onclick="addToCart(2097)"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                        <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="收藏" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="加入购物车" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6" style="border: 0;" id="schedule4">
                <div class="product-card product-list card"><a class="product-thumb" href="/blog-135#">
                    <div class="product-badge text-danger text-left"></div><div class="product-badge text-danger text-left" id="content4" style="display: none;"><img src="https://dl.edhub.cc/fire.png" alt="new" style="width: 24px; height: auto; margin-bottom: 6px;;"><span style="letter-spacing: 0px;">全球共读报名中</span></div><img src="https://dl.edhub.cc/root/images/book_img/2405/IP181_web.jpg" alt="Product"></a>
                    <div class="product-info" style="vertical-align:top;">
                        <h3 class="product-title"><a href="/blog-135#">保罗和他转变生命的神学(简)</a></h3><br>
                        <p>
                            <span class="txt-1">罗杰·莫朗（Roger Mohrlang）</span>
                            <span class="d-inline">恩道出版社</span>
                        </p>
                        <p class="txt-4">凡是基督徒，没有不知道保罗的。但你是否真的了解保罗？本书简要介绍了保罗的生活、思想和他受托向全世界宣扬的奇妙福音。作者莫朗博士基于多年的教学和研究成果，言简意深地勾勒出保罗竭诚尽忠的一生，缕述他丰富精深的神学思想，充分展现了福音转变生命的力量。本书在学术上富有启发，又为个人生命带来反思和挑战，切盼与您一起共读此书，更深认识保罗，亲历他心中的火焰，生命得以更新和坚固。</p>
                        <sec:authorize access="isAuthenticated() or isRememberMe()">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="保罗和他转变生命的神学(简)" data-toast-message="已成功加入收藏" data-toggle="tooltip" title="收藏" onclick="addToWish(1857)"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title="保罗和他转变生命的神学(简)" data-toast-message="成功添加到购物车" data-toggle="tooltip" title="加入购物车" onclick="addToCart(1857)"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                        <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="收藏" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-heart"></i></button>
                                <button class="btn btn-outline-primary btn-sm btn-round" data-toast data-toast-type="danger" data-toast-position="topRight" data-toast-title=" " data-toast-message="请登录" data-toggle="tooltip" title="加入购物车" onclick="javascript:window.location.href='<at:web type="login" value="/login?errorCode=S10"/>'"><i class="icon-bag"></i></button>
                            </div>
                        </sec:authorize>
                    </div>
                </div>
            </div>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const currentDate = new Date();
                const contentContainer = document.getElementById('contentContainer');

                // 定义多个日期范围和对应的内容
                const dateRanges = [
                    {
                        startDate: new Date('2024-12-23'),
                        endDate: new Date('2025-01-05'),
                        contentId: 'content1',
                        scheduleId: 'schedule1'
                    },
                    {
                        startDate: new Date('2025-01-27'),
                        endDate: new Date('2025-02-16'),
                        contentId: 'content2',
                        scheduleId: 'schedule2'
                    },
                    {
                        startDate: new Date('2025-03-17'),
                        endDate: new Date('2025-03-30'),
                        contentId: 'content3',
                        scheduleId: 'schedule3'
                    },
                    {
                        startDate: new Date('2025-05-12'),
                        endDate: new Date('2025-05-25'),
                        contentId: 'content4',
                        scheduleId: 'schedule4'
                    }
                ];
                dateRanges.forEach(function(range) {
                    const contentDiv = document.getElementById(range.contentId);
                    const colDiv = document.getElementById(range.scheduleId);
                    if (currentDate >= range.startDate && currentDate <= range.endDate) {
                        contentDiv.style.display = 'block';
                        // 将显示的 div 移动到容器的开始位置
                        contentContainer.prepend(colDiv);
                    }
                });
            });

        </script>
    </section>
    <!-- Site Footer-->
    <footer class="site-footer-1">
        <div class="container">
            <div class="row padding-bottom-3x">
                <div class="col-lg-10 col-md-10" style="margin-left: 20px;">
                    <span class="d-inline"><a class="navi-link" href="blog-122#"> 往期读书会 &nbsp; | &nbsp;</a></span>
                    <!-- <span class="d-inline"><a class="navi-link" href="#"> 读书点滴  &nbsp; | &nbsp;</a></span> -->
                    <span class="d-inline"><a class="navi-link" href="blog-103#"> 关于“读书伙伴”  &nbsp; | &nbsp;</a></span>
                    <span class="d-inline"><a class="navi-link" href="index-Reader"> 电子书App </a></span>
                </div>
            </div>
            <!-- Copyright-->
            <!-- <p class="footer-copyright">Copyright ©恩道电子书 All Rights Reserved</p> -->
        </div>
    </footer>

    <!-- 2024上半年读书会书目 结束 -->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script>
    $(document).ready(function(){
        //do something
        //$("#readingListIndex").addClass("active");
    })
</script>
</body>
</html>