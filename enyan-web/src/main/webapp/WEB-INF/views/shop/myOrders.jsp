<%--
  User: Aaron
  Date: 2017/12/12
  Time: 下午 19：40
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="aaron" uri="AaronTagLib" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="my.order"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="footer-js.jsp"/>
    <script src="<c:url value='/statics/js/moment.js' />"></script>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<c:forEach var="order" items="${orders}">

    <c:choose>
        <c:when test="${order.isValid == 1}">
            <c:choose>
                <c:when test="${order.isPaid == 1}">

                </c:when>
                <c:otherwise>
                    <div class="modal fade" id="modalCancel${order.orderId}" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-body padding-top-2x">
                                    <p><spring:message code="alert.orderCancel"/> </p>
                                </div>
                                <div class="modal-footer">
                                    <button class="btn btn-outline-secondary btn-sm" type="button" data-dismiss="modal"><spring:message code="alert.close"/> </button>
                                    <a href="cancelOrder-${order.orderId}" class="btn btn-primary btn-sm"><spring:message code="alert.cancel"/> </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:otherwise>
            </c:choose>

        </c:when>
        <c:otherwise>
            <div class="modal fade" id="modalDel${order.orderId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body padding-top-2x">
                            <p><spring:message code="alert.orderDel"/> </p>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-outline-secondary btn-sm" type="button" data-dismiss="modal"><spring:message code="alert.cancel"/> </button>
                            <a href="delOrder-${order.orderId}" class="btn btn-primary btn-sm"><spring:message code="alert.delete"/> </a>
                        </div>
                    </div>
                </div>
            </div>
        </c:otherwise>
    </c:choose>
</c:forEach>
<!--header start-->
<jsp:include page="header.jsp"/>
<script src="<c:url value='/statics/js/jquery.countdown.js' />"></script>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2 padding-top-2x">
        <div class="row">
            <div class="col-lg-4">
                <!-- 我的账户侧边栏 -->
                <nav class="list-group navbar-back">
                    <a class="list-group-item" href="#"><h4><spring:message code="info.own"/> </h4></a>
                    <a class="list-group-item " href="myCenter"><i class="icon-head"></i><spring:message code="my.account"/> </a>
                    <a class="list-group-item " href="myRent"><img src="<c:url value='/statics/images/icon/rent.svg'/>" alt="先租后买"/> <spring:message code="my.rent"/></a>
                    <a class="list-group-item with-badge" href="myDevices"><i class="icon-air-play"></i><spring:message code="my.device"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item active with-badge" href="#"><i class="icon-tag"></i><spring:message code="my.order"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myWishes"><i class="icon-heart"></i><spring:message code="my.wish"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myGiftHistory"><i class="icon-file"></i><spring:message code="my.gift.history"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myRedeemCode"><i class="icon-book"></i><spring:message code="my.redeemCode"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myCart"><i class="icon-bag"></i><spring:message code="cart.label"/> <span class="badge badge-primary badge-pill"></span></a>
                </nav>
            </div>
            <div class="col-lg-8">
                <div class="padding-top-2x mt-2 hidden-lg-up"></div>
            <c:forEach var="order" items="${orders}">
                <c:set value="0" var="productCount" scope="page"/>
                <!-- 订单 start -->
                <div class="table-responsive">
                    <table class="table margin-bottom-none">
                        <thead>
                        <tr>
                            <th><spring:message code="order.date"/> ：<span class="text-muted">
                                <script>document.write(moment(${order.purchasedAt.time}).format("YYYY-MM-DD HH:mm:ss"))</script>
                            </span></th>
                            <th><spring:message code="order.num"/> ：<span class="text-muted">${order.orderNum}</span></th>
                        </tr>
                        </thead>
                        <tbody>
                <c:forEach var="discountList" items="${order.orderDetailInfo.cartDiscountInfoList}" varStatus="status">
                        <c:forEach var="productInfo" items="${discountList.productInfoList}">
                            <c:set value="${productCount + 1}" var="productCount" scope="page"/>
                            <tr>
                                <td>
                                    <a class="text-medium navi-link" href="/book-${productInfo.code}">
                                        <c:if test="${productInfo.salesModel == 1}">
                                            <spring:message code="book.title.presale"/>
                                        </c:if>
                                            ${productInfo.name}
                                    </a>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${order.isPaid == 1}">
                                            <c:choose>
                                                <c:when test="${order.orderType == 1}">
                                                    <a class="text-medium text-decoration-none" href="myGiftHistory"><spring:message code="gift.look"/> </a>
                                                </c:when>
                                                <c:otherwise>
                                                    <a class="text-medium text-decoration-none" href="preDownload-${order.orderNum}-${productInfo.code}"><spring:message code="book.read"/> </a>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:when>
                                        <c:otherwise>
                                            <c:choose>
                                                <c:when test="${productInfo.discountAnyIsValid}">
                                                    <del class="text-danger">HK$${productInfo.priceHkd}</del>&nbsp;HK$${productInfo.priceHKDDiscount}
                                                </c:when>
                                                <c:otherwise>
                                                    HK$${productInfo.priceHkd}
                                                </c:otherwise>
                                            </c:choose>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                            </tr>
                        </c:forEach>
                </c:forEach>
                        </tbody>
                    </table>
                </div>
                <hr class="mb-30">
                <div class="text-right">
                    <c:if test="${order.isPaid != 1}">　
                        <c:if test="${order.orderDetailInfo.amountDiscount > 0}">
                            <h6 class="product-price">
                                <%--N件折（${order.orderDetailInfo.discountTitle}）--%><spring:message code="account.discount"/>：<span> -HK$${order.orderDetailInfo.amountDiscount}</span>
                            </h6>
                        </c:if>
                        <c:if test="${order.orderDetailInfo.amountCoupon > 0}">
                            <h6 class="product-price">
                                <spring:message code="coupon.value.label"/>：<span> -HK$<fmt:formatNumber type="number" value="${order.orderDetailInfo.amountCoupon}" pattern="0.00" maxFractionDigits="2"/></span>
                            </h6>
                        </c:if>

                    </c:if>
                    <h6 class="product-price">
                        <c:choose>
                            <c:when test="${order.orderType == 1}">
                                <spring:message code="redeem.total" arguments="${order.orderDetailInfo.productInfoList[0].quantity}"/> ：
                                HK$${order.orderDetailInfo.amountHkd}
                            </c:when>
                            <c:when test="${order.orderType == 2}"><%--兑换码兑换--%>
                                <spring:message code="order.total" arguments="${productCount}"/> ：
                                HK$0
                            </c:when>
                            <c:otherwise>
                                <spring:message code="order.total" arguments="${productCount}"/> ：
                                HK$${order.orderDetailInfo.amountHkd}
                            </c:otherwise>
                        </c:choose>

                    </h6>
                    <c:if test="${order.isValid == 1 and order.isPaid != 1}">
                        <%--<div class="column text-lg">
                            (CAD$${order.orderDetailInfo.amountHkd})
                        </div>--%>
                        <aaron:bookPrice order="true" priceToDo="${order.orderDetailInfo.amountHkd}"/>
                    </c:if>
                </div>
                <hr class="mb-4">
                <div class="text-right">
                    <c:choose>
                        <c:when test="${order.isValid == 1}">
                            <c:choose>
                                <c:when test="${order.isPaid == 1}">
                                    <div class="column">
<%--                                        <a class="btn btn-outline-secondary" href="showInvoice-${order.orderId}"><spring:message code="order.invoice"/></a>--%>
                                        <a class="btn btn-outline-secondary" href="downloadInvoice-${order.orderId}"><spring:message code="order.invoice"/></a>
                                        <a class="btn btn-outline-secondary" href="orderDetail-${order.orderId}"><spring:message code="order.look"/></a>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="column">
                                        <a class="btn btn-outline-secondary" href="orderDetail-${order.orderId}"><spring:message code="order.look"/></a>
                                        <button class="btn btn-outline-secondary" type="button" data-toggle="modal" data-target="#modalCancel${order.orderId}"><spring:message code="order.cancel"/> </button>
                                        <a class="btn btn-primary" href="orderDetail-${order.orderId}" target="_blank"><spring:message code="account.pay"/> </a>
                                    </div>
                                    <div class="column text-lg text-right">
                                        <!-- 订单剩余时间 -->
                                        <h6 class="padding-bottom-1x"><span class="text-muted"><spring:message code="pay.left.1"/>: <span class="text-muted" id="clock${order.orderId}"></span></span></h6>
                                        <script type="text/javascript">
                                            <%-- // 15 days from now!
                                             //var date = new Date(new Date().valueOf() + 15 * 24 * 60 * 60 * 1000);

                                             $('#clock${order.orderId}').countdown("<fmt:formatDate value='${order.expiredAt}' pattern='yyyy/MM/dd HH:mm:ss'/>", function(event) {
                                                 $(this).text(event.strftime('%H<spring:message code="time.hour"/>%M<spring:message code="time.min"/>'));
                                             });--%>
                                            <%--var offset = new Date().getTimezoneOffset();
                                            var date = new Date('<fmt:formatDate value='${orderMain.expiredAt}' pattern='yyyy/MM/dd HH:mm:ss' timeZone="GMT"/>');
                                            var now_utc =  Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
                                                date.getUTCHours(), date.getUTCMinutes() - offset, date.getUTCSeconds());
                                            var toDate = new Date(date.toUTCString().substr(0, 25))  ;
                                            alert(now_utc);--%>
                                            var newDate = new Date(${order.expiredAt.time});

                                            $('#clock${order.orderId}').countdown(newDate)
                                                .on('update.countdown', function(event) {
                                                    $(this).text(event.strftime(' %H : %M : %S'));
                                                })
                                                .on('finish.countdown', function(event) {
                                                    //$(this).html('This offer has expired!').parent().addClass('disabled');
                                                    window.location.href="";
                                                });
                                        </script>
                                    </div>
                                </c:otherwise>
                            </c:choose>

                        </c:when>
                        <c:otherwise>
                            <div class="column">
                                <button class="btn btn-outline-secondary" type="button" data-toggle="modal" data-target="#modalDel${order.orderId}"><spring:message code="order.del"/> </button>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
                <div class="padding-top-2x"></div>
                <!-- 订单 end -->
            </c:forEach>

                <div class="pt-2">
                    <!-- 页码 -->
                    <nav class="pagination">
                        ${pageLand}
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>

<script>
    function checkDel() {
        var msg = "您确认要取消该订单吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>
</body>
</html>
