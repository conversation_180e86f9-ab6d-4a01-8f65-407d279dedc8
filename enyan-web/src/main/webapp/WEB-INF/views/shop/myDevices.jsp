<%--
  User: Aaron
  Date: 2017/12/12
  Time: 下午 19：40
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="my.device"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<c:forEach var="list" items="${deviceLimitList}" varStatus="status">
    <div class="modal fade" id="modalDel${status.index}" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body padding-top-2x">
                    <p><spring:message code="alert.devicelimit.del"/> </p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline-secondary btn-sm" type="button" data-dismiss="modal"><spring:message code="alert.cancel"/> </button>
                    <a class="btn btn-primary btn-sm" href="device-del-${status.index}"><spring:message code="alert.confirm"/> </a>
                </div>
            </div>
        </div>
    </div>
</c:forEach>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2 padding-top-2x">
        <div class="row">
            <div class="col-lg-4">
                <!-- 我的账户侧边栏 -->
                <nav class="list-group navbar-back">
                    <a class="list-group-item" href="#"><h4><spring:message code="info.own"/></h4></a>
                    <a class="list-group-item with-badge" href="myCenter"><i class="icon-head"></i><spring:message code="my.account"/> </a>
                    <a class="list-group-item " href="myRent"><img src="<c:url value='/statics/images/icon/rent.svg'/>" alt="先租后买"/> <spring:message code="my.rent"/></a>
                    <a class="list-group-item active with-badge" href="#"><i class="icon-air-play"></i><spring:message code="my.device"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myOrders"><i class="icon-tag"></i><spring:message code="my.order"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myWishes"><i class="icon-heart"></i><spring:message code="my.wish"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myGiftHistory"><i class="icon-file"></i><spring:message code="my.gift.history"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myRedeemCode"><i class="icon-book"></i><spring:message code="my.redeemCode"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myCart"><i class="icon-bag"></i><spring:message code="cart.label"/> <span class="badge badge-primary badge-pill"></span></a>
                </nav>
            </div>
            <div class="col-lg-8">

                <div class="padding-top-2x mt-2 hidden-lg-up"></div>
                <!-- Wishlist Table-->
                <div class="table-responsive wishlist-table margin-bottom-none">
                    <table class="table">
                        <tbody>
                        <c:forEach var="list" items="${deviceLimitList}" varStatus="status">
                            <tr>
                                <td>
                                    <div class="product-item">
                                        <a class="product-thumb" href="#">
                                            <img src="<c:url value='/statics/images/icon/deviceIcon.png' />" alt="Product" >
                                        </a>
                                        <div class="product-info">
                                            <h4 class="product-title">
                                                <spring:message code="device.description" arguments="${list.deviceName};${list.deviceType}" htmlEscape="false" argumentSeparator=";"/>
                                            </h4>
                                            <div class="text-lg text-medium text-muted">
                                                <br/>
                                                <a class="btn btn-outline-primary btn-sm" data-toggle="modal" data-target="#modalDel${status.index}"><spring:message code='device.release'/></a>

                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center"><%--<a class="remove-from-cart" href="wish-del-${list.bookId}" data-toggle="tooltip" title="<spring:message code='wish.cancel'/>"><i class="icon-cross"></i></a>--%></td>
                            </tr>

                        </c:forEach>
                        </tbody>
                    </table>
                </div>
                <div class="pt-2">
                    <!-- 页码 -->
                    <nav class="pagination">
                        ${pageLand}
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>

<script>
    function checkDel() {
        var msg = "您真的确定要删除收藏吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>
</body>
</html>
