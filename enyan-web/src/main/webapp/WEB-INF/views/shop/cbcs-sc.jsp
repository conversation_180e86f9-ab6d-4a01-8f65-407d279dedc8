<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!-- Page Title-->


<!-- Page Content-->
<section class="hero-slider" style="background-image: url(https://inspiratas3.blob.core.windows.net/cbcs/ebook-cbcs/cbcs-bg.jpg);">
    <div class="item">
        <div class="container padding-top-3x">
            <div class="row justify-content-center align-items-center">
                <div class="col-lg-7 col-md-7 padding-bottom-3x text-md-left text-center">
                    <div class="from-bottom">
                        <div class="h2 text-body text-normal mb-2 pt-2">购买全套房角石<span class="text-h">圣经注释</span>电子书</div>
                        <div class="h2 text-body text-normal mb-4 pb-2">立享<span class="text-bold" style="color: #F23737; font-size: 3.2rem; padding: 0 10px;">55</span>折</div>
                        <div class="title-sm text-body text-normal pt-4 pb-4" style="font-size: 1.2rem; color: #000000 !important;">（系统将自动过滤您已购买的电子书，并扣除相应书价）</div>
                    </div>
                    <sec:authorize access="isAuthenticated() or isRememberMe()">
                        <a class="btn btn-primary scale-up delay-1" href="/rent-to-own/allBooksOrder?langType=1" >购买简体版</a>
                        <a class="btn btn-primary scale-up delay-1" href="/rent-to-own/allBooksOrder?langType=2" >购买繁体版</a>
                        <a class="btn btn-primary scale-up delay-1" href="/rent-to-own/" >先租后买 &nbsp;<img src="<c:url value='/statics/images/hot.svg' />" alt="hot" style="position: absolute; top: -16px; right: -3px; width: 42px; height: auto;"/></a>
                    </sec:authorize>
                    <sec:authorize access="not (isAuthenticated() or isRememberMe())">
                        <a class="btn btn-primary scale-up delay-1" href='<at:web type="login" value="/login?errorCode=S10"/>'>购买简体版</a>
                        <a class="btn btn-primary scale-up delay-1" href='<at:web type="login" value="/login?errorCode=S10"/>'>购买繁体版</a>
                        <a class="btn btn-primary scale-up delay-1" href='<at:web type="login" value="/login?errorCode=S10"/>'>先租后买 &nbsp;<img src="<c:url value='/statics/images/hot.svg' />" alt="hot" style="position: absolute; top: -16px; right: -3px; width: 42px; height: auto;"/></a>
                    </sec:authorize>
                </div>
                <div class="col-md-5 padding-bottom-2x mb-3"><img class="d-block mx-auto" src="https://inspiratas3.blob.core.windows.net/cbcs/ebook-cbcs/cbcs-all.png" alt="cbcs"></div>
            </div>
        </div>
    </div>
</section>
