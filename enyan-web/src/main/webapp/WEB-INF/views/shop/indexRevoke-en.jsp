<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<!-- Page Title-->
<%--
<div class="page-title">
    <div class="container">
        <div class="column">
            <h1>账户注销</h1>
        </div>
        <div class="column">
            <ul class="breadcrumbs">
                <li><a href="/">首页</a>
                </li>
                <li class="separator">&nbsp;</li>
                <li><a href="/myCenter">个人中心</a>
                </li>
                <li class="separator">&nbsp;</li>
                <li>账户注销</li>
            </ul>
        </div>
    </div>
</div>--%>
<!-- Page Content-->
<div class="container padding-top-3x padding-bottom-3x mb-2">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <form:form action="revokeAction?${_csrf.parameterName}=${_csrf.token}" modelAttribute="authUser" method="post" enctype="multipart/form-data" role="form" cssClass="close-box">
                <h4 class="margin-bottom-2x text-center">Important Notes.</h4>
                <p class="margin-bottom-1x">Cancellation of your Inspirata eBooks account is a non-recoverable operation. Before you request cancellation, please fully read, understand and agree to the following.</p>
                <ol class="list-unstyled">
                    <li><span class="text-primary text-medium">1. All Inspirata platforms are unable to continue using this account.</span>
                        <p>After you cancel your account, you will not be able to sign in and use this account in all Inspirata platforms (website/App). Devices that are already signed into this account will be automatically signed out of the account.</p>
                    </li>
                    <li><span class="text-primary text-medium">2. All purchased eBooks and redeem codes will be automatically forfeited.</span>
                        <p>Please make sure you send all your redeem codes before you cancel your account.</p>
                    </li>
                    <li><span class="text-primary text-medium">3. Account-related information will be cleared and cannot be recovered.</span>
                        <p>The information includes, but is not limited to, personal information, reading data (reading/spiritual progress, underlining/notes, bookmarks, etc.), book purchase history, book gift history, collection data, etc. You are advised to backup all important information by yourself before the cancellation.</p>
                    </li>
                    <li><span class="text-primary text-medium">4. Account cannot be retrieved after cancellation.</span>
                        <p>If you register again with the same email address, you will sign in as a new user and still cannot retrieve your previous account information.</p>
                    </li>
                </ol>
                <div class="d-flex flex-wrap justify-content-between">
                    <label class="custom-control custom-checkbox">
                        <input class="custom-control-input" type="checkbox" id="agree" onchange="warning()"><span class="custom-control-indicator"></span><span class="custom-control-description" >I have read and accept the above items<br><p class="text-primary mt-1" id="warning">You need to accept the above terms to cancel your account</p></span>
                    </label>
                </div>
                <c:if test='${isSaveError }'>
                    <div class="form-group input-group">
                        <h6 class="text-danger">${msg}</h6>
                    </div>
                </c:if>
                <div class="form-group input-group">
                    <input class="form-control" type="email" placeholder="email" required name="email"><span class="input-group-addon"><i class="icon-mail"></i></span>
                </div>
                <div class="form-group input-group">
                    <input class="form-control" type="password" placeholder="Please enter your password" required name="userPassword"><span class="input-group-addon"><i class="icon-lock"></i></span>
                </div>
                <div class="text-center">
                    <button class="btn btn-primary margin-bottom-none" type="submit" id="pay" disabled onclick="javascript:return checkRevoke()">Request for cancellation</button>
                </div>
            </form:form>
        </div>
    </div>
</div>
<script>
    function warning(){
        var checkAllBox = document.getElementById("agree");
        var displayWarning = document.getElementById("warning");
        var pay = document.getElementById("pay");
        if(checkAllBox.checked){
            displayWarning.innerHTML="";
            pay.disabled = false;
        }else{
            displayWarning.innerHTML='You need to accept the above terms to cancel your account';
            pay.disabled = true;
        }
    }
    function checkRevoke() {
        let msg = "Click to confirm the cancellation, your account will be cancelled immediately and cannot be revoked.";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>