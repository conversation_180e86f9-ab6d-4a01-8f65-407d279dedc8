<%--
  User: <PERSON>
  Date: 2020/7/28
  Time: 下午4:23
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<script type="text/javascript" src="<c:url value='/statics/js/jquery-1.10.2.min.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/aaronCartToOrder.js' />"></script>

<script src="<c:url value='/js/vendor.min.js' />"></script>
<!-- Modernizr-->
<script src="<c:url value='/js/modernizr.min.js' />"></script>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<script src="<c:url value='/js/scripts.min.js?v=1' />"></script>
<script src="<c:url value='/statics/js/aaron-js.js?v=230329' />"></script>
<script src="<c:url value='/js/giftcode.js?v=230302' />"></script>
<script src="<c:url value='/statics/js/ui/jquery-ui.js'/>"></script>
<script>
    (function ($) {
        var oldSelected = $.fn.selected;
        //var originalVal = $.fn.val;
        $.fn.selected = function(itemId , itemValue) {
             oldSelected(itemId,itemValue);
        };
    })(jQuery);
</script>
