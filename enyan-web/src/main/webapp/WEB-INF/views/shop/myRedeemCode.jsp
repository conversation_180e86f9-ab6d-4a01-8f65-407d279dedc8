<%--
  User: Aaron
  Date: 2017/12/12
  Time: 下午 19：40
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="my.redeemCode"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2 padding-top-2x">
        <div class="row">
            <div class="col-lg-4">
                <!-- 我的账户侧边栏 -->
                <nav class="list-group navbar-back">
                    <a class="list-group-item" href="#"><h4><spring:message code="info.own"/> </h4></a>
                    <a class="list-group-item with-badge" href="myCenter"><i class="icon-head"></i><spring:message code="my.account"/> </a>
                    <a class="list-group-item " href="myRent"><img src="<c:url value='/statics/images/icon/rent.svg'/>" alt="先租后买"/> <spring:message code="my.rent"/></a>
                    <a class="list-group-item with-badge" href="myDevices"><i class="icon-air-play"></i><spring:message code="my.device"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myOrders"><i class="icon-tag"></i><spring:message code="my.order"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myWishes"><i class="icon-heart"></i><spring:message code="my.wish"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myGiftHistory"><i class="icon-file"></i><spring:message code="my.gift.history"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item active" href="#"><i class="icon-book"></i><spring:message code="my.redeemCode"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myCart"><i class="icon-bag"></i><spring:message code="cart.label"/> <span class="badge badge-primary badge-pill"></span></a>
                </nav>
            </div>
            <div class="col-lg-8">
                <div class="padding-top-2x mt-2 hidden-lg-up"></div>
                <c:if test='${isSaveError }'>
                    <div class="form-group input-group">
                        <span class="label label-danger label-mini text-danger">${msg}</span>
                    </div>
                </c:if>
                <sec:authorize access="isAuthenticated()">
                    <sec:authentication var="user" property="principal" />
                </sec:authorize>
                <form class="column" action="redeemCodeUpdateInfo?${_csrf.parameterName}=${_csrf.token}" method="post" enctype="multipart/form-data">
                    <div class="col-md-8">
                        <div class="form-group row">
                            <label for="account-name-new"><spring:message code="redeemCode.input"/> </label>
                            <div class="col-10">
                                <input class="form-control" type="text" id="account-name-new" name="code" value="${enyanRedeemCode.code}" required size="80">
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex flex-wrap justify-content-between align-items-center">
                            <button class="btn btn-primary margin-right-none" type="submit"><spring:message code="redeemCode.do"/> </button>
                        </div>
                    </div>
                </form>
            </div>

        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
</body>
</html>
