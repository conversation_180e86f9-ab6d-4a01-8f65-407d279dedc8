<%--
  User: Aaron
  Date: 2017/12/4
  Time: 下午6:56
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="s" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>
        ${obj.blogTitle}-<spring:message code="shop.title"/>
    </title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>

<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->

<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-1 ">
        <div class="row justify-content-center">
            <!--  Content -->
            <div class="container padding-bottom-3x mb-2 padding-top-1x">
                <div class="row justify-content-center">
                    <!--  名称 -->
                    <div class="col-lg-7">
                        <h2 class="padding-top-2x">${obj.blogTitle}</h2>
                        <!-- 作者 -->
                        <div class="single-post-meta">
                            <div class="column">
                                <div class="meta-link">${obj.author}</div>
                            </div>
                            <div class="column">
                                <div class="meta-link"><fmt:formatDate value="${obj.createAt}" pattern="yyyy-MM-dd"/></div>
                                <div class="meta-link"><span><i class="icon-eye"></i>${obj.readCount}</span></div>
                            </div>
                        </div>
                        <%--
                        <div class="owl-carousel" data-owl-carousel="{}">
                            <figure><img src="img/blog/single/01.jpg" alt="Image"></figure>
                        </div>--%>
                        <h2 class="padding-top-2x"></h2>
                        ${obj.blogContent}

                        <!-- 点赞 -->
                        <div class="entry-navigation padding-top-2x">
                            <div class="column"><button class="btn btn-outline-secondary view-all" href="#" onclick="like()" id="blogLikeBtn"><i class="icon-heart"></i></button><br /><br /><span id="blogLikes">${obj.likeCount}</span> <spring:message code="blog.likes"/></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
    <jsp:include page="ad-info.jsp"/>
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script>
    function changeSort(sel)
    {
        var url = "/blogs?order="+sel.value;
        //alert(url);
        location.href = url;//location.href实现客户端页面的跳转
    }
    function like(){
        var blogLikes = $("#blogLikes").text();
        url="/blogLike?${_csrf.parameterName}=${_csrf.token}";
        $("#blogLikes").text(Number(blogLikes) + 1);
        $('#blogLikeBtn').addClass('btn-outline-primary')
        $('#blogLikeBtn').removeAttr("onclick");

        $.jpost(url, {
            "blogTitle":${obj.blogId}
        }).then(res => {
            //console.log(res);
            //$('#myModal').modal('hide');
            if(res.success){
                //alert("sss")
                // blogLikes.innerHTML=blogLikes + 1;

                //alert(res.successMessage);

            }else{
                alert(res.errorMessages[0]);
            }
            //alert(res.result);
            //window.location.reload();
            //windows.href("");

        });
    }
</script>
</body>
</html>