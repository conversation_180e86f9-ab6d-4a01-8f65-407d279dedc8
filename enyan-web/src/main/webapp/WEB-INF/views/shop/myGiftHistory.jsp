<%--
  User: Aaron
  Date: 2017/12/12
  Time: 下午 19：40
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="my.gift.history"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="${_csrf.token}">
    <jsp:include page="header-css.jsp"/>
    <script src="<c:url value='/statics/js/moment.js' />"></script>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
    <style>
        td.long-line{
            white-space: unset;
        }
    </style>
</head>
<!-- Body-->
<body>
<div class="modal fade" id="modal-sendcode" tabindex="-1" >
    <div class="modal-dialog-centered modal-dialog" style="top:10%">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5 class="modal-title  w-100"><spring:message code="gift.send.title" /></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body padding-top-2x" >
                <form>
                    <div class="form-group">
                        <label class="padding-left-none"><spring:message code="gift.send.bookname" />：<span id="bookname"></span></label><br/>
                        <input type="hidden" name="giftcode" id="giftcode"/>
                    </div>
                    <div class="form-group">
                        <label for="email" class="padding-left-none"><spring:message code="gift.send.to.email" />：</label>
                        <input type="email" class="form-control form-control-rounded" id="email" placeholder="<spring:message code='gift.email.input' />">
                    </div>
                    <div class="form-group">
                        <label for="say-something" class="padding-left-none"><spring:message code="gift.send.to.text" />：</label>
                        <textarea class="form-control form-control-rounded" id="say-something" rows="4" maxlength="100" style="resize: none;" ><spring:message code="gift.send.default.text" /></textarea>
                        <span class="float-right text-muted" id="count_message" style="margin-top: -25px;margin-right: 10px;color:red;"></span>
                    </div>
                    <div class="form-check">
                        <label class="custom-control custom-radio text-danger" for="check-email">
                            <input type="checkbox" value="" id="check-email" class="custom-control-input">
                            <span class="custom-control-indicator"></span>
                            <spring:message code="gift.send.tip" /><br/>
                            <spring:message code="gift.send.tip.text" />
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline-secondary btn-sm" type="button" data-dismiss="modal"><spring:message code="button.cancel" /></button>
                <a class="btn btn-primary btn-sm"  id="btn-send"><spring:message code="button.confirm" /></a>
            </div>
        </div>
    </div>
</div>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2 padding-top-2x">
        <div class="row">
            <div class="col-lg-4">
                <!-- 我的账户侧边栏 -->
                <nav class="list-group navbar-back">
                    <a class="list-group-item" href="#"><h4><spring:message code="info.own"/> </h4></a>
                    <a class="list-group-item with-badge" href="myCenter"><i class="icon-head"></i><spring:message code="my.account"/> </a>
                    <a class="list-group-item " href="myRent"><img src="<c:url value='/statics/images/icon/rent.svg'/>" alt="先租后买"/> <spring:message code="my.rent"/></a>
                    <a class="list-group-item with-badge" href="myDevices"><i class="icon-air-play"></i><spring:message code="my.device"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myOrders"><i class="icon-tag"></i><spring:message code="my.order"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myWishes"><i class="icon-heart"></i><spring:message code="my.wish"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item active" href="#"><i class="icon-file"></i><spring:message code="my.gift.history"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myRedeemCode"><i class="icon-book"></i><spring:message code="my.redeemCode"/> <span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myCart"><i class="icon-bag"></i><spring:message code="cart.label"/> <span class="badge badge-primary badge-pill"></span></a>
                </nav>
            </div>
            <div class="col-lg-8">
                <div class="padding-top-2x mt-2 hidden-lg-up"></div>
                <div class="table-responsive">
                    <c:if test="${not empty list}">
                        <table class="table margin-bottom-none">
                            <thead>
                            <tr>
                                <th width="150"><spring:message code="gift.send.bookname"/></th>
                                <th><spring:message code="gift.send.code"/></th>
                                <th><spring:message code="gift.send.code.status"/></th>
                                <th><spring:message code="gift.send.code.date"/></th>
                                <th><spring:message code="gift.send.code.email"/></th>
                                <th><spring:message code="gift.send.to.name"/></th>
                                <th><spring:message code="gift.order.time"/></th>
                            </tr>
                            </thead>
                            <tbody>
                                <%--<tr>
                                    <td class="long-line">以弗所書·歌羅西書、腓立門書(繁)[房角石卷16上]</td>
                                    <td class="long-line">JW-0A-c6e081b6-7d6f-43e5-a690-6cc55c832aca</td>
                                    <td>已兑换</td>
                                    <td>2020-06-03</td>
                                    <td><EMAIL></td>
                                    <td class="sendto"><EMAIL></td>
                                </tr>
                                <tr>
                                    <td class="long-line">圣经这场戏222</td>
                                    <td class="long-line">xxxxxxxxxxxx</td>
                                    <td>未兑换</td>
                                    <td>---</td>
                                    <td>---</td>
                                    <td class="sendto"><a class="btn btn-sm btn-outline-primary" data-toggle="modal" data-target="#modal-sendcode"><spring:message code="button.input"/></a></td>
                                </tr>--%>
                            <c:forEach var="redeemCode" items="${list}">
                                <tr>
                                    <td class="long-line">${redeemCode.redeemCodeNoteInfo.bookNameDescription}</td>
                                    <td class="long-line">${redeemCode.code}</td>
                                    <c:choose>
                                        <c:when test="${redeemCode.status == 1}">
                                            <td>
                                                <spring:message code="gift.send.code.status.1"/>
                                            </td>
                                            <td>${redeemCode.redeemCodeNoteInfo.dateToRedeem}</td>
                                            <td>${redeemCode.redeemCodeNoteInfo.emailToRedeem}</td>
                                            <td class="sendto">${redeemCode.redeemCodeNoteInfo.emailToGift}</td>
                                        </c:when>
                                        <c:otherwise>
                                            <td>
                                                <spring:message code="gift.send.code.status.0"/>
                                            </td>
                                            <td>---</td>
                                            <td>---</td>
                                            <td class="sendto">
                                                <c:choose>
                                                    <c:when test="${not empty redeemCode.redeemCodeNoteInfo.emailToGift}">
                                                        ${redeemCode.redeemCodeNoteInfo.emailToGift}
                                                    </c:when>
                                                    <c:otherwise>
                                                        <a class="btn btn-sm btn-outline-primary" data-toggle="modal" data-target="#modal-sendcode"><spring:message code="button.input"/></a>
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:otherwise>
                                    </c:choose>
                                    <td class="long-line">
                                        <script>document.write(moment(${redeemCode.redeemCodeNoteInfo.enyanOrderDetail.purchasedAt.time}).format("YYYY-MM-DD HH:mm:ss"))</script>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </c:if>
                </div>
                <div class="pt-2">
                    <!-- 页码 -->
                    <nav class="pagination">
                        ${pageLand}
                    </nav>
                </div>
            </div>

        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<jsp:include page="footer-js.jsp"/>

</body>
</html>
