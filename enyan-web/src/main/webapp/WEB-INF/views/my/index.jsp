<%--
  User: <PERSON>
  Date: 2017/12/12
  Time: 下午 19：40
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>个人中心</title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书">
    <meta name="keywords" content="恩道电子书">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Favicon Icons-->
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">
    <!-- Main Template Styles-->
    <link rel="stylesheet" media="screen" href="<c:url value='/css/vendor.min.css' />">
    <link id="mainStyles" rel="stylesheet" media="screen" href="<c:url value='/css/styles.css' />">
    <!-- Modernizr-->
    <script src="<c:url value='/js/modernizr.min.js' />"></script>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="../shop/header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1>我的账户</h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="/index">首页</a>
                    </li>
                    <li class="separator">&nbsp;</li>
                    <li><a href="/my/index">个人中心</a>
                    </li>
                    <li class="separator">&nbsp;</li>
                    <li>我的账户</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2">
        <div class="row">
            <div class="col-lg-4">
                <!-- 我的账户侧边栏 -->
                <nav class="list-group">
                    <a class="list-group-item" href="#"><h4>个人中心</h4><span>欢迎，用户昵称</span></a>
                    <a class="list-group-item active" href="#"><i class="icon-head"></i>我的账户</a>
                    <a class="list-group-item with-badge" href="orders"><i class="icon-bag"></i>我的订单<span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="wishs"><i class="icon-heart"></i>我的收藏<span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="cart"><i class="icon-tag"></i>购物车<span class="badge badge-primary badge-pill"></span></a>
                </nav>
            </div>
            <div class="col-lg-8">
                <div class="padding-top-2x mt-2 hidden-lg-up"></div>
                <form class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="account-fn">用户昵称</label>
                            <input class="form-control" type="text" id="account-fn" value="用户昵称" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="account-email">登录邮箱</label>
                            <input class="form-control" type="email" id="account-email" value="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="account-pass">新密码</label>
                            <input class="form-control" type="password" id="account-pass">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="account-confirm-pass">确认新密码</label>
                            <input class="form-control" type="password" id="account-confirm-pass">
                        </div>
                    </div>
                    <div class="col-12">
                        <hr class="mt-2 mb-3">
                        <div class="d-flex flex-wrap justify-content-between align-items-center">
                            <button class="btn btn-primary margin-right-none" type="button" data-toast data-toast-position="topRight" data-toast-type="success" data-toast-icon="icon-circle-check" data-toast-title="用户信息，" data-toast-message="修改成功！">确认更新</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="../shop/footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<script src="<c:url value='/js/vendor.min.js' />"></script>
<script src="<c:url value='/js/scripts.min.js' />"></script>
<script src="<c:url value='/statics/js/aaronCartToOrder.js' />"></script>
</body>
</html>
