<%--
  User: <PERSON>
  Date: 2017/12/12
  Time: 下午 19：40
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>个人中心</title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书">
    <meta name="keywords" content="恩道电子书">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Favicon Icons-->
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">
    <!-- Main Template Styles-->
    <link rel="stylesheet" media="screen" href="<c:url value='/css/vendor.min.css' />">
    <link id="mainStyles" rel="stylesheet" media="screen" href="<c:url value='/css/styles.css' />">
    <!-- Modernizr-->
    <script src="<c:url value='/js/modernizr.min.js' />"></script>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="../shop/header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1>我的账户</h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="/index">首页</a>
                    </li>
                    <li class="separator">&nbsp;</li>
                    <li><a href="/my/index">个人中心</a>
                    </li>
                    <li class="separator">&nbsp;</li>
                    <li>我的账户</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2">
        <div class="row">
            <div class="col-lg-4">
                <!-- 我的账户侧边栏 -->
                <nav class="list-group">
                    <a class="list-group-item" href="#"><h4>个人中心</h4><span>欢迎，用户昵称</span></a>
                    <a class="list-group-item " href="#"><i class="icon-head"></i>我的账户</a>
                    <a class="list-group-item with-badge" href="myOrders"><i class="icon-bag"></i>我的订单<span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item with-badge" href="myWishs"><i class="icon-heart"></i>我的收藏<span class="badge badge-primary badge-pill"></span></a>
                    <a class="list-group-item active with-badge" href="cart"><i class="icon-tag"></i>购物车<span class="badge badge-primary badge-pill"></span></a>
                </nav>
            </div>
            <div class="col-lg-8">

                <div class="padding-top-2x mt-2 hidden-lg-up"></div>
                <div class="table-responsive shopping-cart sp-buttons ">
<c:forEach var="discountList" items="${BUYER_CART.cartDiscountInfoList}" varStatus="status">

                    <table class="table">
                        <thead>
                        <tr>
                            <th><c:out value="${discountList.discountTitle}" default="图书名称"/> </th>
                            <th class="text-center">型号</th>
                            <th class="text-center">金额</th>
                            <th class="text-center">操作</th>
                            <th class="text-center"></th>
                        </tr>
                        </thead>
                        <tbody>
    <c:forEach var="cartLine" items="${discountList.productInfoList}">
                        <tr>
                            <td>
                                <div class="product-item"><a class="product-thumb" href="/book-${cartLine.productInfo.code}"><img src="<at:bookImage imageName='${cartLine.productInfo.productCover}'/>" alt="Product"></a>
                                    <div class="product-info">
                                        <h4 class="product-title"><a href="book-${cartLine.productInfo.code}">
                                            <c:if test="${cartLine.productInfo.salesModel == 1}">
                                                <spring:message code="book.title.presale"/>
                                            </c:if>
                                                ${cartLine.productInfo.name}
                                        </a></h4><span><em>作 者:</em>${cartLine.productInfo.producer}</span>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center text-lg text-medium">eIP021</td>
                            <td class="text-center text-lg text-medium">

        <c:choose>
            <c:when test="${cartLine.productInfo.discountSingleIsValid}">
                <c:choose>
                    <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                        <del>$ ${cartLine.productInfo.priceUsd}</del>$ ${cartLine.productInfo.priceUSDDiscount}
                    </c:when>
                    <c:otherwise>
                        <del>¥${cartLine.productInfo.priceCny}</del>¥${cartLine.productInfo.priceCnyDiscount}
                    </c:otherwise>
                </c:choose>
            </c:when>
            <c:otherwise>
                <c:choose>
                    <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                        $ ${cartLine.productInfo.priceUsd}
                    </c:when>
                    <c:otherwise>
                        ¥${cartLine.productInfo.priceCny}
                    </c:otherwise>
                </c:choose>
            </c:otherwise>
        </c:choose>

                            </td>
                            <td class="text-center"><button class="btn btn-outline-secondary btn-sm btn-wishlist" data-toggle="tooltip" title="<spring:message code='label.wish'/>"><i class="icon-heart"></i></button></td>
                            <td class="text-center"><a class="remove-from-cart" href="/delCart-${cartLine.productInfo.code}" data-toggle="tooltip" title="删除"><i class="icon-cross"></i></a></td>
                        </tr>
    </c:forEach>
                        </tbody>
                    </table>
</c:forEach>
                </div>
                <hr class="mb-4">
                <div class="text-right">
                    <div class="column"><a class="btn btn-outline-secondary" href="shop-grid-ls.html" data-toast-position="topRight"><i class="icon-arrow-left"></i>&nbsp;返回继续购物</a><a class="btn btn-primary" href="checkout.html">立即支付</a></div>
                </div>

            </div>
        </div>
    </div>

    <!-- Site Footer start-->
    <jsp:include page="../shop/footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<script src="<c:url value='/js/vendor.min.js' />"></script>
<script src="<c:url value='/js/scripts.min.js' />"></script>
<script src="<c:url value='/statics/js/aaronCartToOrder.js' />"></script>
</body>
</html>
