body, div, dl, dt, dd, h1, h2, h3, h4, h5, h6, p, pre, code, blockquote {
  line-height: 1.72em;
  font-family: "Times New Roman", Times, "CN-Normal", "SourceHanSansCN-Regular", "Source Han Sans CN", serif;
}

a {
  text-decoration: none;
}

/*上横线*/
.overline {
  text-decoration: overline;
}

/*下划线*/
.underline {
  text-decoration: underline;
}

/*删除线*/
.del {
  text-decoration: line-through;
}

/*加粗字段*/
.bold {
  font-weight: bold;
}

/*斜体字段*/
.italic {
  font-style: italic;
}

/*花体字段*/
.huati {
  font-family: "FrakturFont","Wittenberger Fraktur MT";
}

/*小型大写字母*/
.smcp {
  font-variant-caps: small-caps;
  -moz-font-feature-settings: "smcp";
  -webkit-font-feature-settings: "smcp";
  font-feature-settings: "smcp";
}

/*居中对齐*/
.text-center {
  text-align: center;
  text-indent: 0;
}

/*左对齐*/
.text-left {
  text-align: left;
}

/*右对齐*/
.text-right {
  text-align: right;
}

/*标题*/
h1 {
  font-weight: bold;
  font-size: 1.8em;
  margin: 1rem 0;
}

h2{
  font-weight: bold;
  font-size: 1.6em;
  margin: 0.8rem 0;
}

h3 {
  font-weight: bold;
  font-size: 1.4em;
  margin: 0.6rem 0;
}

h4 {
  font-weight: bold;
  font-size: 1.2em;
  margin: 0.4rem 0;
}

/*正常段落-首行缩进*/
p {
  font-weight: normal;
  font-size: 1em;
  text-indent: 2em;
  text-align: justify;
  margin: 0.65em 0;
}

/*正常段落-标题*/
.style1 {
  font-weight: bold;
  font-size: 1.8em;
  text-indent:0;
  text-align: justify;
  margin:0;
}

.style2 {
  font-weight: bold;
  font-size: 2.5em;
  text-indent:0;
  text-align: justify;
  margin:0;
}

.style3 {
  font-weight: bold;
  font-size: 4em;
  text-indent:0;
  text-align: justify;
  margin:0;
}

/*首行不缩进段落*/
.noindent {
  text-indent: 0em;
}

/*悬挂缩进段落-英文参考书目*/
.hanging {
  text-indent: -2em;
  padding-left: 2em;
}

/*诗歌体段落*/
.poem {
  text-indent: -1em;
  padding-left: 3em;
}

/*左缩进段落*/
.left-indent {
  margin-left: 2em;
}

/*右缩进段落*/
.right-indent {
  margin-right: 2em;
}

/*交错配列（与浅色字段样式同时应用）*/
.text-indent-1 {
  text-indent: 0;
  margin-left: 2em;
}

.text-indent-2 {
  text-indent: 0;
  margin-left: 3em;
}

.text-indent-3 {
  text-indent: 0;
  margin-left: 4em;
}

.text-indent-4 {
  text-indent: 0;
  margin-left: 5em;
}

.text-indent-5 {
  text-indent: 0;
  margin-left: 6em;
}

.text-indent-6 {
  text-indent: 0;
  margin-left: 7em;
}

/*无序列表*/
ul, ol {
  padding-left: 2em;
}

ul.disc {
  list-style-type: disc;
}

ul.circle {
  list-style-type: circle;
}

ul.square {
  list-style-type: square;
}

/*有序列表*/
ol.roman-u {
  list-style-type: upper-roman;
}

ol.roman-l {
  list-style-type: lower-roman;
}

ol.alpha-u {
  list-style-type: upper-alpha;
}

ol.alpha-l {
  list-style-type: lower-alpha;
}

ol.decimal {
  list-style-type: decimal;
}

/*图表*/
table {
  margin: 1em 0;
  border-collapse: collapse;
  width: 100%;
}

td,th {
  text-indent: 0;
  text-align: left;
  padding: 0.1em 0.5em;
  vertical-align: top;
}

/*无边框图表*/
table.table-borderless {
  border: none;
}

.table-borderless th,td {
  border: none;
}

/*默认边框图表*/
.table-bordered td,th {
  border: 1px solid;
}

/*交替填色图表*/
.table-striped {
  border: none;
}

.table-striped th {
  background-color: #dddddd;
  border: none;
}

.table-striped tr:nth-child(odd) {
  background-color: #f9f9f9;
}

.table-striped tr:nth-child(even) {
  background-color: #efefef;
}

/*横向图表-有表头*/
.table-rows-th {
  border: none;
}

.table-rows-th th {
  border: none;
  border-top: 2px solid; 
}
.table-rows-th tr { 
  border-bottom: 1px solid; 
}

/*横向图表-无表头*/
.table-rows { 
  border-top: 2px solid;
  border-left: none;
  border-right: none;
}

.table-rows tr { 
  border-bottom: 1px solid; 
}

/*图片*/
img {
  max-height: 100%;
}

/*display: inline*/
img.inline {
  height: 1em;
  vertical-align: text-top;
}

img.fluid {
  display: inline;
  vertical-align: middle;
}

/*浅色字段*/
.secondary {
  /*filter: opacity(60%);*/
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=60)";
  /* IE 5-7 */
  filter: alpha(opacity=60);
  /* Netscape */
  -moz-opacity: 0.6;
  /* Safari 1.x */
  -khtml-opacity: 0.6;
  /* Good browsers */
  opacity: 0.6;
}

/*背景适配*/
:root[style*="readium-night-on"] .secondary {
  /*filter:brightness(60%);*/
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=60)";
  /* IE 5-7 */
  filter: alpha(opacity=60);
  /* Netscape */
  -moz-opacity: 0.6;
  /* Safari 1.x */
  -khtml-opacity: 0.6;
  /* Good browsers */
  opacity: 0.6;
}

:root .table-striped tr:nth-child(odd) {
  --ED-importance:0.85;
}

:root .table-striped tr:nth-child(even) {
  --ED-importance:0.95;
}

:root .table-striped thead>tr {
  --ED-importance:0.65 !important;
}

h1,h2{
  text-align: center;
}
.quote {
  margin-left: 2em;
}

img.FBD{
  width: 80%;
  display: block;
  margin: 0 auto;
}

section.bordered{
  border: 1px solid;
}

figcaption{
	text-align:center;
	text-indent:0;
}