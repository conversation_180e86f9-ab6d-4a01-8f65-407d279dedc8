/*
	Datepicker for Bootstrap
	Copyright 2012 Stefan <PERSON>
	Licensed under the Apache License v2.0
	http://www.apache.org/licenses/LICENSE-2.0
*/
.datepicker {
    z-index: 1051;
    top: 0;
    left: 0;
    padding: 10px;
    margin-top: 1px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    /*.dow { border-top: 1px solid #ddd !important; }*/;
    background: #65CEA7 !important;
    border-color: #65CEA7 !important;
    color: #fff;
}

.datepicker:before {
    content: '';
    display: inline-block;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #65CEA7;
    border-bottom-color: #65CEA7;
    position: absolute;
    top: -7px;
    left: 6px;
}

.datepicker:after {
    content: '';
    display: inline-block;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #65CEA7;
    position: absolute;
    top: -6px;
    left: 7px;
}

.datepicker > div {
    display: none;
}

.datepicker table {
    width: 100%;
    margin: 0;
}

.datepicker td, .datepicker th {
    text-align: center;
    width: 20px;
    height: 20px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.datepicker td.day:hover {
    background: #42866d;
    cursor: pointer;
}

.datepicker td.old, .datepicker td.new {
    color: #264e3f;
}

.datepicker td.active, .datepicker td.active:hover, .datepicker td span.active {
    background-color: #42866d;
    background-image: -moz-linear-gradient(top, #42866d, #42866d);
    background-image: -ms-linear-gradient(top, #42866d, #42866d);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#42866d), to(#42866d));
    background-image: -webkit-linear-gradient(top, #42866d, #42866d);
    background-image: -o-linear-gradient(top, #42866d, #42866d);
    background-image: linear-gradient(top, #42866d, #42866d);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#42866d', endColorstr='#42866d', GradientType=0);
    border-color: #42866d #42866d #42866d;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.datepicker td.active:hover, .datepicker td.active:hover:hover, .datepicker td.active:active, .datepicker td.active:hover:active, .datepicker td.active.active, .datepicker td.active:hover.active, .datepicker td.active.disabled, .datepicker td.active:hover.disabled, .datepicker td.active[disabled], .datepicker td.active:hover[disabled] {
    background-color: #42866d;
    color: #fff;
}


.datepicker td.active:active, .datepicker td.active:hover:active, .datepicker td.active.active, .datepicker td.active:hover.active {
    background-color: #42866d \9;
    color: #fff;
}

.datepicker td span {
    display: block;
    width: 47px;
    height: 54px;
    line-height: 54px;
    float: left;
    margin: 2px;
    cursor: pointer;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.datepicker td span:hover {
    background: #42866d;
    color: #fff;
}

/*.datepicker td span.active {*/
    /*background-color: #42866d;*/
    /*background-image: -moz-linear-gradient(top, #65CEA7, #65CEA7);*/
    /*background-image: -ms-linear-gradient(top, #65CEA7, #65CEA7);*/
    /*background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#65CEA7), to(#65CEA7));*/
    /*background-image: -webkit-linear-gradient(top, #65CEA7, #65CEA7);*/
    /*background-image: -o-linear-gradient(top, #65CEA7, #65CEA7);*/
    /*background-image: linear-gradient(top, #65CEA7, #65CEA7);*/
    /*background-repeat: repeat-x;*/
    /*filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#65CEA7', endColorstr='#65CEA7', GradientType=0);*/
    /*border-color: #65CEA7 #65CEA7 #65CEA7;*/
    /*border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);*/
    /*filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);*/
    /*color: #fff;*/
    /*text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);*/
/*}*/

.datepicker td span.active:hover, .datepicker td span.active:active, .datepicker td span.active.active, .datepicker td span.active.disabled, .datepicker td span.active[disabled] {
    background-color: #42866d;
    color: #fff;
}

.datepicker td span.active:active, .datepicker td span.active.active {
    background-color: #42866d \9;
    color: #fff;
}

.datepicker td span.old {
    color: #fff;
}

.datepicker th.switch {
    width: 145px;
}

.datepicker th.next, .datepicker th.prev {
    font-size: 19.5px;
}

.datepicker thead tr:first-child th {
    cursor: pointer;
}

.datepicker thead tr:first-child th:hover {
    background: #42866d;
}

.input-append.date .add-on i, .input-prepend.date .add-on i {
    display: block;
    cursor: pointer;
    width: 16px;
    height: 16px;
}