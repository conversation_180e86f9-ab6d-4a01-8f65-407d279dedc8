
/*-- Chart --*/

svg {
  font: 10px sans-serif;
}
path, line {
  fill: none;
  stroke: #000;
  stroke-width: 1px;
}
text {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

/*-- Grid --*/

.grid line {
  stroke: #ccc;
}
.xgrid, .ygrid {
  stroke-dasharray: 3 3;
}
.xgrid-focus {
}

/*-- Line --*/

.-line {
  stroke-width: 1px;
}
/*-- Point --*/

.-circle._expanded_ {
  stroke-width: 1px;
  stroke: white;
}
.selected-circle {
  fill: white;
  stroke-width: 2px;
}

/*-- Bar --*/

.-bar._expanded_ {
  stroke-width: 1px;
  fill-opacity: 0.6;
}
/* TODO: use this*/
.-bar._selected_ {
  stroke-width: 1px;
  fill-opacity: 0.3;
}
.selected-bar {
  fill: white;
  stroke-width: 2px;
}

/*-- Focus --*/

.target.focused path {
  stroke-width: 2px;
}

/*-- Region --*/

.region {
  fill: steelblue;
  fill-opacity: .1;
}

/*-- Brush --*/

.brush .extent {
  fill-opacity: .1;
}

/*-- Select - Drag --*/

.dragarea {
}

/*-- Legend --*/

.legend-item {
  font-size: 12px;
}

/*-- Tooltip --*/

.-tooltip {
  border-collapse:collapse;
  border-spacing:0;
  background-color:#fff;
  empty-cells:show;
  -webkit-box-shadow: 7px 7px 12px -9px rgba(119,119,119,10);
     -moz-box-shadow: 7px 7px 12px -9px rgba(119,119,119,10);
          box-shadow: 7px 7px 12px -9px rgba(119,119,119,10);
  opacity: 0.9;
}
.-tooltip tr {
  border:1px solid #CCC;
}
.-tooltip th {
  background-color: #aaa;
  font-size:14px;
  padding:2px 5px;
  text-align:left;
  color:#FFF;
}
.-tooltip td {
  font-size:13px;
  padding: 3px 6px;
  background-color:#fff;
  border-left:1px dotted #999;
}
.-tooltip td > span {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 6px;
}
.-tooltip td.value{
  text-align: right;
}
