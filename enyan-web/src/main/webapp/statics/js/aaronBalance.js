/**
 *
 */
window.onload = function() {
    //checkTable();
    //console.log("checkAll4.js");
};

var checkAllBox = document.getElementById("checkAllBox");
var checkMyBox = document.getElementsByName("productId");
var priceBox = [];
var sub=0;
var sum=0;

function checkTable(){
    checkAllBox.checked = true;
    /*for(var i=0; i<checkMyBox.length; i++){
        priceBox[i] = document.getElementById("price"+i);
    }*/
    changeAllCheckState();

};

function changeAllCheckState(){
    for (var i=0; i<checkMyBox.length; i++){
        checkMyBox[i].checked=checkAllBox.checked;
    }
    changeSUB();
}

function changeCheckState(){
    sub=0;
    var allState=true;

    for (var i=0; i<checkMyBox.length; i++){
        if (!checkMyBox[i].checked){
            allState = false;
        }
    }

    if(allState){
        checkAllBox.checked=true;
    }else{
        checkAllBox.checked=false;
    }
    changeSUB();
};

function changeSUB(){
    /*sum= new Number(0);
    for(var i=0; i<checkMyBox.length; i++){
        if(checkMyBox[i].checked){
            sum=sum+Number(priceBox[i].innerHTML);
        }
    }
    sumLabel.innerHTML=sum.toFixed(2);
    sumLabel2.innerHTML=sum.toFixed(2);*/
}
