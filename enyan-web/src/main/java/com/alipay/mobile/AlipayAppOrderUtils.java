package com.alipay.mobile;

import com.aaron.spring.controller.ShopController;
import com.aaron.spring.model.EnyanOrder;
import com.alipay.config.AlipayConfig;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;


/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/7/7
 * @Modified By:
 */
public class AlipayAppOrderUtils {
	// 商户PID
	public static final String PID = AlipayConfig.partner;//"2088621891276675"; //sandbox env
	//public static final String PID = "2088021017666931"; //pro env

	// 商户私钥
	// saondbox env
	public static final String RSA_PRIVATE = AlipayConfig.KEY_RSA_PRIVATE;//"MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC8gZtwHZUoP7XCh4JpdqmpyNStUhVs/u3527LcBVLwHLbizlyN8GOOuMBYvgQdMXfvmhqk6BZI8z6Os/JYDjFnp3cBf0NbKb12w/3GNGaIWF0LKSCwmyUVnVyR2XAOU5WR9PhU7Gc7dAJB9YnFXSJfbLu3ljC30mdaI7oyDWtFJXTIR1sNZycNEJIiKAIvzF14UlfGIFTtIwOmE5UxtT/HWwTS8twe7rTylwnxrbJ3BY4Ekz4hbkt6gSFC0wsG0SigK0dF2Vx1Muo49Ben3q04n6feb7RRzMZj6kVJHP/nnH4t9uKHckH3uXMOUH20kLs1CYEgo/OLZ5iVzYwUCIjBAgMBAAECggEBAJxt7YCHfAyvefTZIPbF2xujJiJCWLdfgjpy/6Un4eCR446pcxLyppt8Y1oBLUbQk1fJCU1Jr5yQBpzDZVt/Q65hwRcD9fBD3g8dq418VI3WzjeBxLfpt0eNk5EQ4LVy1U8O+7j1iOFEbs8vpEmc9PY9NDWwM/OHbe7WcSAyFI+GjcnGUAz8Pw/hCm10BnEUy9MrIOfyQTIT/DCy/aN78L1sSSlCnj3nBIiPRVdm7uSKlnFB/iYnvdJ9qOcclC80/M3mL3zuLUJmMepz8BujzFhui+6e2+CQ1R1gr8DVmrmCnRpuS7SX2wxbfD9Igr+8PIcZh3Vxk9hG/7X8XZU0xZUCgYEA5UeotTLBy48RgA8oG6USb10AqfDBSexjaPATztEhzwxKWS7CXlZHomfZcJnBdI2vkR4nApaWbujXi68F7v+bogIoucM+m9YisJqd3QCKWDUeXnYya6ryYDkHng5CGNea4SUZ+It1yOHqpG8R3GmzwfGgCJwOYiOrVIxQg0sYT8sCgYEA0nmBtrHpzeUXsUJieV9aa/7m63p18owsnd3BTx0oL6qXAYVz269U3AZ/fjBzqoD+iIQKaSd8DFOzFhM1S/xlWJYUE2aR8viplC1JAQaOXfJj5epZ/sjh+ED9wHoNUFj04xPG6q1CvR8sMKU873CcMTYXJO7BKOp5Lw1yYRks4CMCgYARRaGu7MDunbt6wymJU5kc7IulQa696+HdQOCtHtIGL+3V5BDeXG+PC3W05tBNAHNuYjAcIM36Uu4R5sByrazCupjozY2E3c/FMBCeWnERjgbYpNzNgZY6q3EodfiCuJNBTLM1JwRberFba8aTrR7OiG4+18q5l+hLOTrDeT++mwKBgQDAvzd0CNA1lxQwZ7vorNMQzZnJkhdLrOT7GWZ7PhdcN5wcb4tfZtyOtoCxHkv+nCz89fbWv6sRWuFJWQPY92YUTzRJZAhKe0BJC4OspcVg8aZcmlYaRvrxrwry5pMkvqJdBfKHXW4d5dtBHUGFgRn40zE0yFUqVDfwSB80HqsDAwKBgQCHGfNqoilQeYfSX9hXgN0hTMidEX2znNbZ1Pc64lx1uXRTGppX95Qt6mrTbUo7zkoXkMlgWVMHG3NJerjyUMHYzB0OF6aDrn75SaF6p5j1rHX4/FXJHBvFxD6qB+IGASIbrsiGkB6p8t+Q6O8rS7MRjLbkirLHrWF/eSbZV/0ivg==";
	//online env
	//public static final String RSA_PRIVATE = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMg9WztcdrttAbB3FQ5gwfuDAwLgwjFcfD8vV+IH6jC8nRi0jz7SHOD7pQ4MBc+Q4PpQWAqjDLTiWNYmAuUVzHni2iylCnpnun3YwXODpJ6oq0QHJaIdk8g8QKd0vMjPBfqXdXQRncscFGaZxsstrRtfRKqLXuyy8sM+AHpwcHfVAgMBAAECgYAWrtym3NKWaMLIOrUn684Nr8mnic51yQRKJBLQiBT/cF5bbhjMBQFAe0E3ViVSXhceQ/u1OMM3umxV1fh9+vouoRYcWW+DeKt54tfFo+SdyOFdtlXdqA7vstQ+Nmd0JwH4uLGaF1WQGO7kPdiAg1N9ZBpQB9CSJJM0kNDstp18AQJBAPNaeiPHxyZ72bwVxbBVthj0hMWE4Vr83W0bP2yuY7Vzyp0BOsPYHNbFK92fnDJEAjPdHVj+YVl/Sc23sA21UisCQQDSpU2OF8W7Nkbtt4ZAMikzDbEym6ZZU+QgqlYCV/ZBor+cTg8QivEUyum+eOUx5KjoYVcN8U3uO29ecJmS7l3/AkEAmLAMMqcGrX7H/tsqTpl4x++j3sqhGxXNWMff47EHnrIoTpqW5IqUjazo+QVMW72QJDp4T35MVnsnM4wtSmyaQQJAEwPRCG6k7s1rgbH7cHgWuAEYadUbIx0rjrdRpEyEclBas6VoProMITBgAU2wgtx9UtzWmu+ZdVPwLbpEYrsZyQJBAJen1uHVmL3j4WSyI5OhNSHW5aAfeLGhbuEEYsDdNYewQ+OUz0ZtWZ2Ta+742yNw130nlckhapbHhtPdHUSayjw=";

	// 商户收款账号
	public static final String SELLER = AlipayConfig.partner;//"2088621891276675";

	public static final String NOTIFY_URL = AlipayConfig.notify_url;//"2088621891276675";

	public static final String RETURN_URL = AlipayConfig.return_url;//"2088621891276675";

	/*
	public static String getSignedOrderInfo(EnyanOrder order){
		String tradeInformation = ShopController.getAlipayGoodsTradeInformation(order);

		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", PID, AlipayConfig.KEY_RSA_PRIVATE, "json", AlipayConfig.input_charset, AlipayConfig.KEY_RSA_PUBLIC, "RSA2");
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
		AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
		//SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
		AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
		model.setBody(tradeInformation);
		model.setSubject(order.getOrderNum());
		model.setOutTradeNo(order.getOrderNum());
		model.setTimeoutExpress("30m");
		model.setTotalAmount(order.getOrderTotal()+"");
		model.setProductCode("QUICK_MSECURITY_PAY");
		request.setBizModel(model);
		request.setNotifyUrl("商户外网可以访问的异步地址");
		try {
			//这里和普通的接口调用不同，使用的是sdkExecute
			AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
			System.out.println(response.getBody());//就是orderString 可以直接给客户端请求，无需再做处理。
			final String signedOrderInfo = response.getBody();
			return signedOrderInfo;
		} catch (AlipayApiException e) {
			e.printStackTrace();
		}

		return "";
	}
*/
}
