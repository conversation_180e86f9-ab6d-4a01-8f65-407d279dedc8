package com.alipay.mobile.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import com.alipay.config.AlipayConfig;
import com.alipay.mobile.SignUtils;
import com.alipay.sign.RSA;
import com.alipay.util.AlipayCore;
import org.apache.commons.lang3.StringUtils;

/* *
 *类名：AlipayNotify
 *功能：支付宝通知处理类
 *详细：处理支付宝各接口通知返回
 *版本：1.0
 *日期：2016-06-06
 *说明：
 *以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己网站的需要，按照技术文档编写,并非一定要使用该代码。
 *该代码仅供学习和研究支付宝接口使用，只是提供一个参考
 *class name:AlipayNotify
 *Function:The class Alipay use to handle notification
 *Detail:Handle the notification of Alipay interfaces
 *version:1.0
 *modify date:2016-06-06
 *instructions:
 *This code below is a sample demo for merchants to do test.Merchants can refer to the integration documents and write your own code to fit your website.Not necessarily to use this code.  
 *Alipay provide this code for you to study and research on Alipay interface, just for your reference.

 *************************注意*************************
 *调试通知返回时，可查看或改写log日志的写入TXT里的数据，来检查通知返回是否正常
 
 *************************Attention*************************
 *When debugging notification feedback，you can check or modify the text included into log to see whether the feedback is normal
 *
 * https://global.alipay.com/docs/ac/app_cn/interaction
 *
 */
public class AlipayNotify {

    /**
     * 支付宝消息验证地址
	 *The URL of verification of Alipay notification.
     */
    private static final String HTTPS_VERIFY_URL = "https://api-sea-global.alipayplus.com/gateway.do?service=notify_verify&";

    /**
     * 根据反馈回来的信息，生成签名结果
	 * Generate sign from feedback
     * @param Params 通知返回来的参数数组the params from the feedback notification
     * @param sign 比对的签名结果the sign to be compared
     * @return 生成的签名结果the result of verification
     */
	public static boolean getSignVeryfy(Map<String, String> Params, String sign) {
		String notifyId = Params.get("notify_id");
		if (StringUtils.isBlank(notifyId)){
			return false;
		}
		if ("true".equals(verifyResponse(notifyId)) == false){
			return false;
		}
    	//过滤空值、sign与sign_type参数
		//Filter parameters with null value ,sign and sign_type
    	Map<String, String> sParaNew = AlipayCore.paraFilter(Params);
        //获取待签名字符串
		//Generate the pre-sign string
        String preSignStr = AlipayCore.createLinkString(sParaNew);
        //获得签名验证结果
		//get the result of verification
//		boolean isSign = RSA.verify(preSignStr, sign, AlipayConfig.KEY_RSA_ALIPAY_PUBLIC, AlipayConfig.input_charset);
		//异步回来的数据，需要使用alipay的公钥验证
		boolean isSign = SignUtils.verify(preSignStr, sign, AlipayConfig.KEY_RSA_ALIPAY_PUBLIC, true);
        return isSign;
    }

    /**
    * 获取远程服务器ATN结果,验证返回URL
	* Get the remote server ATN result,return URL
    * @param notify_id 通知校验IDThe ID for a particular notification. 
    * @return 服务器ATN结果Sever ATN result
    * 验证结果集：
	* Verification result:
    * invalid命令参数不对 出现这个错误，请检测返回处理中partner和key是否为空 
	* invalid Pls check whether the partner and key are null from notification 
    * true 返回正确信息
	* true return the right info
    * false 请检查防火墙或者是服务器阻止端口问题以及验证时间是否超过一分钟
	* false pls check the firewall or the server block certain port,also pls note the expiration time is 1 minute
    */
    public static String verifyResponse(String notify_id) {
        //获取远程服务器ATN结果，验证是否是支付宝服务器发来的请求
		//Get the remote server ATN result,verify whether it's from Alipay

        String partner = AlipayConfig.partner;
        String veryfy_url = HTTPS_VERIFY_URL + "partner=" + partner + "&notify_id=" + notify_id;

        return checkUrl(veryfy_url);
    }

    /**
    * 获取远程服务器ATN结果
	* Get the remote server ATN result
    * @param urlvalue 指定URL路径地址specified URL value
    * @return 服务器ATN结果Sever ATN result
    * 验证结果集：
	* Verification result:
    * invalid命令参数不对 出现这个错误，请检测返回处理中partner和key是否为空 
	* invalid Pls check whether the partner and key are null from notification 
    * true 返回正确信息
	* true return the right info
    * false 请检查防火墙或者是服务器阻止端口问题以及验证时间是否超过一分钟
	* false pls check the firewall or the server block certain port,also pls note the expiration time is 1 minute
    */
    public static String checkUrl(String urlvalue) {
        String inputLine = "";

        try {
            URL url = new URL(urlvalue);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            BufferedReader in = new BufferedReader(new InputStreamReader(urlConnection
                .getInputStream()));
            inputLine = in.readLine().toString();
        } catch (Exception e) {
            e.printStackTrace();
            inputLine = "";
        }

        return inputLine;
    }
	/**
	 notify_id,value:2021070700222171027003991454301445
	 2021-07-07 17:12:48.303 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:notify_type,value:trade_status_sync
	 2021-07-07 17:12:48.303 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:sign,value:VU4hczkbPvW5AbZkbKg3fJLZ6aHQaYa67VdfcoBSOxKo4lnyaPbZJ7YNm3KmOENB9r6dA8MT3Pm6HcFvQK1UXtuHF7b903/ADZ3dhp1MfaZBvcc50pry8leeyLMGvnq1GM3/UfMO78kI40q9KVZoEwfCAhf7CUp+rYj0nRPB5DFMF2AQo3LrK2X2yp6Un9dOvEKyRiwK8FYOxLMd0Sg4gmf64TWM4AhFlXI212suig1pL/zW4oceQBBLLsO4KsPG2qKftz8j+xTPEVwlGIaRSEsAOjLITLVGBexrtZU172XETld3Z8QFq2LAR5CGmLLJyXt42gvVi8Ps62Uc/Cjzpg==
	 2021-07-07 17:12:48.303 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:trade_no,value:2021070722001303991445643591
	 2021-07-07 17:12:48.304 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:total_fee,value:72.25
	 2021-07-07 17:12:48.304 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:out_trade_no,value:E10842226656
	 2021-07-07 17:12:48.304 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:notify_time,value:2021-07-07 17:10:27
	 2021-07-07 17:12:48.305 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:currency,value:HKD
	 2021-07-07 17:12:48.305 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:trade_status,value:TRADE_FINISHED
	 2021-07-07 17:12:48.305 [http-nio-8080-exec-28] ERROR com.aaron.spring.controller.ShopController:1136 - enyanMyNotify name:sign_type,value:RSA2
	 * */
    public static  void main(String[] args) throws Exception {
    	String sign = "GQwJ0FwYpuHOU4zrB36zL3m3D1YW2pHk5a4rLjrU3F7OI/qoVgjFdCBhWXDJhHJo4zb/dTuoFD9+ZkmIaE9P7OF20MC25ZvFwwt40dYnvaHMtWAp6MfrLHfy4nF8mV0a5gSTMhtIvpREi3mYS0U/xdv05GfaxW50p60u+Xq43q4Bql5wTQqsQdkexcLiK93aLsV18Vh9ikG3tjV9GY/wegXdAoHVofoubDWdfFoRgipWvmWlT9TjG9ycQzjsSePl2c3/ULYavuSJwsDLYRVkf7Da0qvMe0UJ5e0fkJJPcUJqcjRrZdEqKsKe60Rmf6czdjEit8ywpJUmgv4DYegeIQ==";

    	Map<String,String> params = new HashMap<>();
	    params.put("notify_id","2021070700222171027003991454301445");
	    params.put("notify_type","trade_status_sync");
	    params.put("sign",sign);
	    params.put("trade_no","2021070722001303991445643591");
	    params.put("total_fee","72.25");
	    params.put("out_trade_no","E10842226656");
	    params.put("notify_time","2021-07-07 20:37:02");
	    params.put("currency","HKD");
	    params.put("trade_status","TRADE_FINISHED");
	    params.put("sign_type","RSA2");

	    boolean result = AlipayNotify.getSignVeryfy(params, sign);
	    System.out.println("result:"+result);

	    /*
	    if (AlipayNotify.verifyResponse(params.get("notify_id")).equals("true")){
		    System.out.println("notify_id:true");
	    }else {
		    System.out.println("notify_id:false");
	    }*/

	    /*boolean flag = AlipaySignature.rsaCheckV2(params,AlipayConfig.KEY_RSA_PUBLIC, AlipayConfig.input_charset,"RSA2");
	    System.out.println("flag:"+flag);*/
    }
}
