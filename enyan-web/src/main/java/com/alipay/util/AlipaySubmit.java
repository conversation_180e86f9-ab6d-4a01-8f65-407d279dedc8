package com.alipay.util;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.http.HttpResult;
import com.alipay.config.AlipayConfig;
import com.alipay.sign.MD5;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.message.BasicNameValuePair;

/* *
 *类名：AlipaySubmit
 *功能：支付宝各接口请求提交类
 *详细：构造支付宝各接口表单HTML文本，获取远程HTTP数据
 *版本：3.3
 *日期：2012-08-13
 *说明：
 *以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己网站的需要，按照技术文档编写,并非一定要使用该代码。
 *该代码仅供学习和研究支付宝接口使用，只是提供一个参考。
 */

public class AlipaySubmit {
    
    /**
     * 支付宝提供给商户的服务接入网关URL(新) TEST
     */
    //private static final String ALIPAY_GATEWAY_NEW = "https://openapi.alipaydev.com/gateway.do?";

    /**
     * 支付宝提供给商户的服务接入网关URL(新) Production
     */
    private static final String ALIPAY_GATEWAY_NEW = "https://api-sea-global.alipayplus.com/gateway.do?";
	
    /**
     * 生成签名结果
     * @param sPara 要签名的数组
     * @return 签名结果字符串
     */
	public static String buildRequestMysign(Map<String, String> sPara) {
    	String prestr = AlipayCore.createLinkString(sPara); //把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
        String mysign = "";
        if(AlipayConfig.sign_type.equals("MD5") ) {
        	mysign = MD5.sign(prestr, AlipayConfig.key, AlipayConfig.input_charset);
        }
        return mysign;
    }
	
    /**
     * 生成要请求给支付宝的参数数组
     * @param sParaTemp 请求前的参数数组
     * @return 要请求的参数数组
     */
    private static Map<String, String> buildRequestPara(Map<String, String> sParaTemp) {
        //除去数组中的空值和签名参数
        Map<String, String> sPara = AlipayCore.paraFilter(sParaTemp);
        //生成签名结果
        String mysign = buildRequestMysign(sPara);

        //签名结果与签名方式加入请求提交参数组中
        sPara.put("sign", mysign);
        sPara.put("sign_type", AlipayConfig.sign_type);

        return sPara;
    }

    /**
     * 建立请求，以表单HTML形式构造（默认）
     * @param sParaTemp 请求参数数组
     * @param strMethod 提交方式。两个值可选：post、get
     * @param strButtonName 确认按钮显示文字
     * @return 提交表单HTML文本
     */
    public static String buildRequest(Map<String, String> sParaTemp, String strMethod, String strButtonName) {
        //待请求参数数组
        Map<String, String> sPara = buildRequestPara(sParaTemp);
        List<String> keys = new ArrayList<>(sPara.keySet());

        StringBuffer sbHtml = new StringBuffer();

        sbHtml.append("<form id=\"alipaysubmit\" name=\"alipaysubmit\" action=\"" + ALIPAY_GATEWAY_NEW
                      + "_input_charset=" + AlipayConfig.input_charset + "\" method=\"" + strMethod
                      + "\">");

        for (int i = 0; i < keys.size(); i++) {
            String name = (String) keys.get(i);
            String value = (String) sPara.get(name);

            sbHtml.append("<input type=\"hidden\" name=\"" + name + "\" value=\'" + value + "\'/>");
        }

        //submit按钮控件请不要含有name属性
        sbHtml.append("<input type=\"submit\" value=\"" + strButtonName + "\" style=\"display:none;\"></form>");
        sbHtml.append("<script>document.forms['alipaysubmit'].submit();</script>");

        return sbHtml.toString();
    }

    private static String rateFile(Map<String, String> sParaTemp) throws IOException{
        //待请求参数数组
        Map<String, String> sPara = buildRequestPara(sParaTemp);

        HttpResult httpResult = HttpProtocolHandler.execute(sPara,ALIPAY_GATEWAY_NEW, HttpMethod.GET);
            //System.out.println(httpResult.getStringResult());
        return httpResult.getStringResult();
    }

    public static String rateFile() throws IOException{
        Map<String,String> sParams = new HashMap<>();
        sParams.put("sendFormat","normal");
        sParams.put("service","forex_rate_file");
        sParams.put("partner",AlipayConfig.partner);

        //待请求参数数组
        Map<String, String> sPara = buildRequestPara(sParams);

        HttpResult httpResult = HttpProtocolHandler.execute(sPara,ALIPAY_GATEWAY_NEW, HttpMethod.GET);
        //System.out.println(httpResult.getStringResult());
        return httpResult.getStringResult();
    }

    /**
     * <p>获取请求的URL链接</p>
     * @param sParaTemp
     * @return java.lang.String
     * @since : 2022/9/19
     **/
    public static String buildRequestMethod(Map<String, String> sParaTemp){
        Map<String, String> sParams = buildRequestPara(sParaTemp);
        List<NameValuePair> params = new ArrayList<>();
        if (null != sParams){
            Set<Map.Entry<String, String>> entrySet = sParams.entrySet();
            for (Map.Entry<String, String> e : entrySet) {
                String name = e.getKey();
                String value = e.getValue();
                NameValuePair pair = new BasicNameValuePair(name, value);
                params.add(pair);
            }
        }
        HttpUriRequest reqMethod = RequestBuilder.get().setUri(ALIPAY_GATEWAY_NEW)
                            .addParameters(params.toArray(new BasicNameValuePair[params.size()])).build();
        return reqMethod.getURI().toString();
    }

    /**
     * 用于防钓鱼，调用接口query_timestamp来获取时间戳的处理函数
     * 注意：远程解析XML出错，与服务器是否支持SSL等配置有关
     * @return 时间戳字符串
     * @throws IOException
     * @throws MalformedURLException
     */
    /*
	public static String query_timestamp() throws MalformedURLException,
                                                        DocumentException, IOException {

        //构造访问query_timestamp接口的URL串
        String strUrl = ALIPAY_GATEWAY_NEW + "service=query_timestamp&partner=" + AlipayConfig.partner + "&_input_charset" +AlipayConfig.input_charset;
        StringBuffer result = new StringBuffer();

        SAXReader reader = new SAXReader();
        Document doc = reader.read(new URL(strUrl).openStream());

        List<Node> nodeList = doc.selectNodes("//alipay/*");

        for (Node node : nodeList) {
            // 截取部分不需要解析的信息
            if (node.getName().equals("is_success") && node.getText().equals("T")) {
                // 判断是否有成功标示
                List<Node> nodeList1 = doc.selectNodes("//response/timestamp/*");
                for (Node node1 : nodeList1) {
                    result.append(node1.getText());
                }
            }
        }

        return result.toString();
    }*/
    public static  void main(String[] args){
        Map<String,String> sParams = new HashMap<>();
        sParams.put("sendFormat","normal");
        sParams.put("service","forex_rate_file");
        sParams.put("partner",AlipayConfig.partner);

        try {
            String result = AlipaySubmit.rateFile();
            List<String> list = IOUtils.readLines(IOUtils.toInputStream(result));
            for (String s:list){
                System.out.println(s);
                String[] values = StringUtils.split(s,"|");
                //System.out.println(values);
                /*
                20180521

                20180521|100510|HKD|0.813800|
                20160716|100500|AUD|5.092300|
                20160716|100500|CAD|5.178200|
                20160716|100500|CHF|6.836600|
                20160716|100500|DKK|0.997400|
                20160716|100500|EUR|7.412300|
                20160716|100500|GBP|8.865400|
                20160716|100500|JPY|0.064090|
                20160716|100500|KRW|0.006065|
                20160716|100500|NOK|0.792300|
                20160716|100500|SEK|0.782500|
                20160716|100500|SGD|4.985800|
                20160716|100500|THB|0.190629|
                20160716|100500|USD|6.706400|

                20180522
                20180522|100510|HKD|0.814000|
                20160716|100500|AUD|5.092300|
                20160716|100500|CAD|5.178200|
                20160716|100500|CHF|6.836600|
                20160716|100500|DKK|0.997400|
                20160716|100500|EUR|7.412300|
                20160716|100500|GBP|8.865400|
                20160716|100500|JPY|0.064090|
                20160716|100500|KRW|0.006065|
                20160716|100500|NOK|0.792300|
                20160716|100500|SEK|0.782500|
                20160716|100500|SGD|4.985800|
                20160716|100500|THB|0.190629|
                20160716|100500|USD|6.706400|
                * */
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
