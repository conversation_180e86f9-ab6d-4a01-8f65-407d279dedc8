package com.alipay.config;

/* *
 *类名：AlipayConfig
 *功能：基础配置类
 *详细：设置帐户有关信息及返回路径
 *版本：3.4
 *修改日期：2016-03-08
 *说明：
 *以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己网站的需要，按照技术文档编写,并非一定要使用该代码。
 *该代码仅供学习和研究支付宝接口使用，只是提供一个参考。
 *
 * https://global.alipay.com/docs/ac/ams/api
 *
 * https://global.alipay.com/docs/ac/wap_cn/demo
 */

public class AlipayConfig {

	/*
	Stripe API (Test)

    Publishable key
    pk_test_o5cIlIHD2BS8x13T0I0QAAjs

    Secret key
    sk_test_5bgExgPEozTe8a9ZlyYGYBJd


    Alipay(Global) API (Production)

    Partner ID
    2088411233796600

    Key
    kg4w9hwd0v5f30vmsgyab4p69un9kubw


	AlipayHK API 文档
	AlipayHK - PC2Mobile:
	https://global.alipay.com/doc/website_hk/intro

	AlipayHK - WAP:
	https://global.alipay.com/doc/wap_hk/intro

	AlipayHK - SDK:
	https://global.alipay.com/doc/app_hk/intro


	PID
	2088441611946675
	key
	8jg2u44gfxu0qttmqpaq79mbuhxy4010
===============================


	* */
	
//↓↓↓↓↓↓↓↓↓↓请在这里配置您的基本信息↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

	// 合作身份者ID，签约账号，以2088开头由16位纯数字组成的字符串，查看地址：https://b.alipay.com/order/pidAndKey.htm
	public static final String partner = "2088441611946675";

	// MD5密钥，安全检验码，由数字和字母组成的32位字符串，查看地址：https://b.alipay.com/order/pidAndKey.htm
	public static final String key = "8jg2u44gfxu0qttmqpaq79mbuhxy4010";

	// RSA public key
	//public static final String KEY_RSA_PUBLIC = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwXsEdksmAR8uCCR4oJu80Hz8ZRERsxSd4oBSUhYjzY3yl/CeNXgxu7/FteLHFeh5Jwyyp09FGL9nO1pxlykc5fCCceM6nfNhcny2caP1dG8YEJWRuGwNTt1icwVH1nGqq32fYfJ8qq4uETzgbHWn6aKpHPuYxk9/lYGcHs3baQKe8hfMkhh+dbFZblE/2a9Gkq9DQfedT7xy0+3O17eDZqUurzKO2s+Awa5wNcfr8opwqdyF+G+ufEmOxdb2B6QeDfANtxYVKjr5I8YxL3TtGsopTBOo5rx+oSyaF4RdTzasGkruN3Ey0X4ZDA7WEKMN0l9nby5WanydWXGjX4BeAQIDAQAB";
											   //MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5QbvJt6VXg9IOCLNuwUMGUVEUb7AQY/HT0yys9UnOtj2THNkFssc1pvlFVLu1ypFUBU4UskyXMBHIFLg7SokQCBgkpe5FEFI+Y2s/b4qV6+H4F2pR8Y+3R+SNdgNdBfG69fLpDmfzqlHfPVn2ifQndP2wmet/8EhHeSrdkr+tbDuppj+Itpm8dExpH+2PNHwBdH9zPzEdOmAOGS2SWEM7dqVUqfIu49dTy7k/XCEXX9czS54MktsOiOKfy1bHXteh6zz4YRrIQN9wyldVoOVYpI6HLGars8D0oGReHnz4sOxHNblVctbmAfQ7161ktHlVf+9MChtd/Gli441Bw1cLQIDAQAB
	// RSA private key private_key_pkcs8.pem
	public static final String KEY_RSA_PRIVATE = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC899GBJQzuB9oBDgEHeD2fZh9eylYOofiOUBvmya4iYI3S6fnmpFfnrYsZEfuweercu3JBYrV6ZRtRlDrmJ9Hp/A7Wc48crSR6rSfJ8lN4+oMET1An7X93wk3SDywxmGYuR78E9geS1EvrgIaQC98reZNAqWph7B6tlOQbO9h52RrZxGZ14DksY4K+M9mgxz7v8vBsiAUhlQ9S9eqP4ssi8e/bjEg/d3T+esb8uO2pXAHr5XUjYm43QgFpVvcbbW1VIaiU+bAg7X91l6kWzg3w9/vWvq6sUZpj2lWefpWucMfWEnWutPGUXx6q5Xja7/8VcsPoV3XDnnoanBCmwuvfAgMBAAECggEANcK3WuuEiy+2qPgn+M0PkwrFv/0GkLEp+qr11SnpnaQklS21yBVXYPUxjpdcKK/2zoeRkOKZSNH8/K+UNih8AvqP6hkETR9BMpmSqVSExPVYUuCRqFpcGmEwLKPeadm2sFH/GVU5gF9fngd5wP9vpXm4F/xa5LL9Y1rpronPI5LQdVkSaZ4wxDT6yeVyB8jEO5op1P0C2eNrZiOD0lGLJxXBiyITZH6Hxiu8iOuC3FMQWHe0uyi1XYZawY5ylD2dkJBJlp19LPLV3C3jjvZPU7Mx6nq9ZJ3U9/xZwzuBPO7uIBOG6qH5IbVDwFc9fmhevK3DaBBD5rnIZS2kxPgiSQKBgQDenksuXD+q3BG4rIGvMzIADrcURbkBqZoKzwUYfHixPo1t0MBWHuY9G9lQKhCATvoJfpDapNljjdS4ND+oQC4bYgzhMuv93TzwRkO3LudZjbkxubZv/uQ2gLBbLw4IM1hn655ZvHz4VT0HXaUSr2VwNZ1bCJUcurzdayRnP22p1QKBgQDZTchH8vRfpyqog6BKPOJ6VyE7KaOkWNm0RK/O/SOXCNV4vA8e3b5KeufV6M3b16iFVUK83OWolWl+GPD4rRVrC8Gj58xs/UtwWLpcviY+f/Yz/dsIcCx3NVI9HXBorYYloYX8a1ymeLLUJxTnbmVDy/yRLLU1XRtYkgB4a2YE4wKBgQCOoTKf+GCeUrNT88p++rkNSmBIL8ex5f0Y8bxdDNqzDNXGRxoD03ZGLsKT1u+NlYwjayDZxQa2/xcMEgW7AavIxZBk9KA4nXe+pfYatoVpXiEKV+coaFHFDbxwH3zJCkSZCGADNH2JCeu4/WmWyCOguekgeyY2Kvp4jn3bXMshJQKBgAkvQnHYEQAh/qmUtqesnx3wpNTtU59Uqg72gD/Nbz2o/+zUdgcl9C8NttLRvjG2kcbg7asx4vsXJZhQ0Y6RMkCOBKc62ucYZaHf/jCBUPh/TBayehmHNDntpQY0QlYKT4szoogiuY3HuDdDV5zYi6sr6gbwzs/smYVK1n5Ae9VlAoGAFV8vf7ApGx72lHPcljfQSmfbhjrzFvWgEC1iW3vf4kR8HtUlx4PyiDz/N4Ix5TaUBptR4PynoWaq3tL7LdmjxC4V0UVZqzMZmE1LScSS5wdFUPthgzCXmjeDCF7IoGg1d5LR4fxEOolRBr6eEBhvm0KLrS6i3YjITnVDA6lZTFE=";

	public static final String KEY_RSA_ALIPAY_PUBLIC = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi95RVkO6IX3i+57pliUUucSL1CRjg5YOwY0f3O8qX2IlghzHXVd5gPjn7n/23TEbB8IkcgQcBW7PZwPASdJCZfQw2RLkkfhf/89JlOAfrdk6R5cjpmPBCbRzO8VnRRhEe2J90hX+lHr5Mk0GDZcgfvTWfv4AAfbXmSesCVKhohCwXU6ipQUwqjZ5UHExNq361usTXpxRtbqFEgDx1IOj/POMQi4dFkEraFlko7KxyR9nW/1gHko9j5iYK0OjQTxyQPBhyhlF81mkB7N8t8YV2p86PZmjIm7zj2ZbK7cuR3zb8fXUiaivMIhW1g6DyoFvaiYTumuDaV8wI7Bwe7QcPQIDAQAB";

//	public static final String RSA_PRIVATE = AlipayConfig.key;//"MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC8gZtwHZUoP7XCh4JpdqmpyNStUhVs/u3527LcBVLwHLbizlyN8GOOuMBYvgQdMXfvmhqk6BZI8z6Os/JYDjFnp3cBf0NbKb12w/3GNGaIWF0LKSCwmyUVnVyR2XAOU5WR9PhU7Gc7dAJB9YnFXSJfbLu3ljC30mdaI7oyDWtFJXTIR1sNZycNEJIiKAIvzF14UlfGIFTtIwOmE5UxtT/HWwTS8twe7rTylwnxrbJ3BY4Ekz4hbkt6gSFC0wsG0SigK0dF2Vx1Muo49Ben3q04n6feb7RRzMZj6kVJHP/nnH4t9uKHckH3uXMOUH20kLs1CYEgo/OLZ5iVzYwUCIjBAgMBAAECggEBAJxt7YCHfAyvefTZIPbF2xujJiJCWLdfgjpy/6Un4eCR446pcxLyppt8Y1oBLUbQk1fJCU1Jr5yQBpzDZVt/Q65hwRcD9fBD3g8dq418VI3WzjeBxLfpt0eNk5EQ4LVy1U8O+7j1iOFEbs8vpEmc9PY9NDWwM/OHbe7WcSAyFI+GjcnGUAz8Pw/hCm10BnEUy9MrIOfyQTIT/DCy/aN78L1sSSlCnj3nBIiPRVdm7uSKlnFB/iYnvdJ9qOcclC80/M3mL3zuLUJmMepz8BujzFhui+6e2+CQ1R1gr8DVmrmCnRpuS7SX2wxbfD9Igr+8PIcZh3Vxk9hG/7X8XZU0xZUCgYEA5UeotTLBy48RgA8oG6USb10AqfDBSexjaPATztEhzwxKWS7CXlZHomfZcJnBdI2vkR4nApaWbujXi68F7v+bogIoucM+m9YisJqd3QCKWDUeXnYya6ryYDkHng5CGNea4SUZ+It1yOHqpG8R3GmzwfGgCJwOYiOrVIxQg0sYT8sCgYEA0nmBtrHpzeUXsUJieV9aa/7m63p18owsnd3BTx0oL6qXAYVz269U3AZ/fjBzqoD+iIQKaSd8DFOzFhM1S/xlWJYUE2aR8viplC1JAQaOXfJj5epZ/sjh+ED9wHoNUFj04xPG6q1CvR8sMKU873CcMTYXJO7BKOp5Lw1yYRks4CMCgYARRaGu7MDunbt6wymJU5kc7IulQa696+HdQOCtHtIGL+3V5BDeXG+PC3W05tBNAHNuYjAcIM36Uu4R5sByrazCupjozY2E3c/FMBCeWnERjgbYpNzNgZY6q3EodfiCuJNBTLM1JwRberFba8aTrR7OiG4+18q5l+hLOTrDeT++mwKBgQDAvzd0CNA1lxQwZ7vorNMQzZnJkhdLrOT7GWZ7PhdcN5wcb4tfZtyOtoCxHkv+nCz89fbWv6sRWuFJWQPY92YUTzRJZAhKe0BJC4OspcVg8aZcmlYaRvrxrwry5pMkvqJdBfKHXW4d5dtBHUGFgRn40zE0yFUqVDfwSB80HqsDAwKBgQCHGfNqoilQeYfSX9hXgN0hTMidEX2znNbZ1Pc64lx1uXRTGppX95Qt6mrTbUo7zkoXkMlgWVMHG3NJerjyUMHYzB0OF6aDrn75SaF6p5j1rHX4/FXJHBvFxD6qB+IGASIbrsiGkB6p8t+Q6O8rS7MRjLbkirLHrWF/eSbZV/0ivg==";

	// 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
	public static final String notify_url = "https://edbook.click/enyanMyNotify"; //域名不一定与正式域名一致

	// 页面跳转同步通知页面路径 需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
	public static final String return_url = "https://edbook.click/checkoutReturn";//买家地址

	public static final String refer_url = "https://edbook.click";//refer地址

	// 签名方式
	public static final String sign_type = "MD5";
	
	// 调试用，创建TXT日志文件夹路径，见AlipayCore.java类中的logResult(String sWord)打印方法。
	public static final String log_path = "C:\\";
		
	// 字符编码格式 目前支持 gbk 或 utf-8
	public static final String input_charset = "utf-8";
		

	// 调用的接口名，无需修改
	public static final String service = "create_forex_trade";

	public static final String service_mobile = "create_forex_trade_wap";

	public static final String currency = "HKD";


	public static final String payment_inst_HK = "ALIPAYHK";

	public static final String payment_inst_CN = "ALIPAYCN";

	public static final String product_code = "NEW_WAP_OVERSEAS_SELLER";
//↑↑↑↑↑↑↑↑↑↑请在这里配置您的基本信息↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
	
}

