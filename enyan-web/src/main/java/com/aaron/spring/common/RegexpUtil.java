package com.aaron.spring.common;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/8/31
 * @Modified By:
 */
public class RegexpUtil {
	/**
	 * <p>过滤特殊字符</p>
	 * @param str
	 * @return java.lang.String
	 * @since : 2022/8/31
	 **/
	public static String stringFilter(String str) throws PatternSyntaxException {
		// 只允许字母和数字 // String regEx ="[^a-zA-Z0-9]";
		// 清除掉所有特殊字符
		String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(str);
		return m.replaceAll("").trim();
	}
}
