package com.aaron.spring.common;

import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.DeviceResolver;
import org.springframework.mobile.device.LiteDeviceResolver;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * 使用Spring的LiteDeviceResolver类解析设备类型
 * @Author: <PERSON>
 * @Date: Created in  2018/5/3
 * @Modified By:
 */
public class AaronDeviceUtils {
    public static final DeviceResolver deviceResolver = new LiteDeviceResolver();
    public static Device getCurrentDevice(HttpServletRequest request){
        return deviceResolver.resolveDevice(request);
    }
}
