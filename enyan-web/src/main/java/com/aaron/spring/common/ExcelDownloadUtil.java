package com.aaron.spring.common;

import com.aaron.excel.ExcelExportUtil;
import com.aaron.excel.ExcelTemplateUtil;
import com.aaron.excel.FileUtil;
import com.aaron.excel.GroupCode;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/2/15
 * @Modified By:
 */
public class ExcelDownloadUtil {
	/**
	 * <p>生成下载的Excel文件</p>
	 * @param dataList
	 * @param excelHeaders
	 * @param code
	 * @return java.io.File
	 * @since : 2022/2/15
	 **/
	public static File getExcelFile(List<List<String>> dataList, List<String> excelHeaders, GroupCode code) throws Exception {
		XSSFWorkbook book = ExcelTemplateUtil.createHeaders(excelHeaders);
		if (book.getNumberOfSheets() > 0) {
			Row rowDel = book.getSheetAt(0).getRow(2);
			if (rowDel != null) {
				book.getSheetAt(0).removeRow(rowDel);
			}
		}
		SXSSFWorkbook workbook = new SXSSFWorkbook(book);
		String filePath = FileUtil.getExportPath(code, DateFormatUtils.format(new Date(),"yyyyMMddHHmmss"))+".xlsx";
		File file = new File(filePath);
		ExcelExportUtil.createExcel(dataList, filePath, 2, workbook);
		return file;
	}
}
