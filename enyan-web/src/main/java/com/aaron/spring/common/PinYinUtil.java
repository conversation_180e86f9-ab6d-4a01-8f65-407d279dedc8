package com.aaron.spring.common;

import com.github.stuxuhai.jpinyin.ChineseHelper;
import com.github.stuxuhai.jpinyin.PinyinException;
import com.github.stuxuhai.jpinyin.PinyinFormat;
import com.github.stuxuhai.jpinyin.PinyinHelper;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/1/17
 * @Modified By:
 */
public class PinYinUtil {
    public static String covertToPinYinString(String str){
        try {
            if (ChineseHelper.containsChinese(str)){
                return PinyinHelper.convertToPinyinString(str,"-", PinyinFormat.WITHOUT_TONE);
            }else {
                String[] strings = StringUtils.split(str," ");
                StringBuffer stringBuffer = new StringBuffer();
                for (int i = 0; i < strings.length; i++) {
                    String s = strings[i];
                    if (StringUtils.isBlank(s)){
                        continue;
                    }
                    if (i > 0){
                        stringBuffer.append("-");
                    }
                    stringBuffer.append(s);
                }
                return stringBuffer.toString();
            }
        } catch (PinyinException e) {
            e.printStackTrace();
        }
        return null;
    }
}
