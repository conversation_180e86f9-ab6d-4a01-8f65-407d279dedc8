package com.aaron.spring.common;

import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/3/8
 * @Modified By:
 */
public class MapUtil {
    /**
     *
     * <p>String to HashMap</p>
     * <p>SALES=0,SALE_PRODUCTS=1,EXPENSES=2,EXPENSES_ITEMS=3</p>
     * <p>keySeperator = "=";</p>
     * <p>split = ","</p>
     * @param mapString
     * @param keySplit
     * @param split
     * @return: java.util.HashMap<java.lang.String,java.lang.String>
     * @since : 2021/3/8
     */
    public static HashMap<String,String> stringToHashMap(String mapString, String keySplit, String split){
        HashMap<String, String> map = (HashMap<String, String>) Arrays.asList(mapString.split(split))
                .stream().map(s -> s.split(keySplit)).collect(Collectors.toMap(e -> e[0], e -> e[1]));
        return map;
    }
}
