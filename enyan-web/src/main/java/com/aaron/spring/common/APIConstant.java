package com.aaron.spring.common;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/9/13
 * @Modified By:
 */
public final class APIConstant {
	/**
	 * 上一个用到的package前缀
	 * */
	public static final String PACKAGE_USE_BEGIN_PREV = "com.aaron.spring.api.v3.controller";
	/**
	 * 当前用到的package前缀
	 * */
	public static final String PACKAGE_USE_BEGIN = "com.aaron.spring.api.v4.controller";

	/**
	 * 优惠码适用的书籍范围
	 * */
	public interface CouponType {
		/**
		 * 所有数据
		 * */
		int ALL = 0;

		/**
		 * 特定数据
		 * */
		int SPECIAL = 0;
	}

	/**
	 * 书籍折扣的区域范围
	 *
	 * 1、只分两个区域
	 * 2、每个city只能自己单独集合，或只能从属于一个集合
	 * */
	public interface BookDiscountArea {
		/**
		 * 无
		 * */
		String NONE = "0";

		/**
		 * 大陆
		 * */
		String CN = "1";

		/**
		 * 东南亚
		 * */
		String SOUTH_EAST_ASIA = "2";

		/**
		 * 大陆Set
		 * */
		Set<String> CN_SET = new HashSet<>(Arrays.asList("CN"));

		/**
		 * 东南亚Set
		 * */
		Set<String> SOUTH_EAST_ASIA_SET = new HashSet<>(Arrays.asList("CN"));
	}

	/**
	 * 设备类型
	 * i: iPhone; a:android; p:pc; m:mac
	 *
	 * */
	public interface DeviceFrom {
		/**
		 * iOS
		 * */
		String iOS = "i";

		/**
		 * android
		 * */
		String android = "a";

		/**
		 * 桌面端PC
		 * */
		String pc = "p";

		String mac = "m";

		/**
		 * 订单来源-web
		 * */
		int Order_From_Web = 0;

		/**
		 * 订单来源-App
		 * */
		int Order_From_App = 1;

		/**
		 * 订单来源-iOS
		 * */
		int Order_From_iOS = 2;

		/**
		 * 订单来源-android
		 * */
		int Order_From_Android = 3;
	}
}
