package com.aaron.spring.common;

import com.aaron.common.Money;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.*;
import com.aaron.util.IdUtil;
import com.aaron.util.PasswordUtil;

import java.math.BigDecimal;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-06-24
 * @Modified By:
 */
public class OrderUtil {
    /**
     * 正常电子书订单
     * */
    public static final String PRE = "E";//正常电子书订单

    /**
     * 先租后买订单
     * */
    public static final String PRE_RENT = "R";//先租后买订单

    /**
     * 先租后之后的购买电子书订单
     * */
    public static final String PRE_RENT_TO_BUY = "O";//先租后之后的购买电子书订单

    /**
     * <p>正常电子书订单</p>
     * @param
     * @return java.lang.String
     * @since : 2022/10/21
     **/
    public static String getOrderId() throws Exception {
        return IdUtil.getOrderIdByPre(PRE);
    }

    /**
     * <p>先租后买订单"R+数字"</p>
     * @param
     * @return java.lang.String
     * @since : 2022/10/21
     **/
    public static String getOrderIdFromRent() throws Exception {
        return IdUtil.getOrderIdByPre(PRE_RENT);
    }

    /**
     * <p>先租后买的金额转为电子书订单"O+数字"</p>
     * @param
     * @return java.lang.String
     * @since : 2022/10/21
     **/
    public static String getOrderIdFromRentToBuy() throws Exception {
        return IdUtil.getOrderIdByPre(PRE_RENT_TO_BUY);
    }

    /**
     * <p>返回信用卡支付的加密密钥</p>
     * @param order
     * @return java.lang.String
     * @since : 2021/6/3
     **/
    public static String creditSign(EnyanOrder order){
        String sign = PasswordUtil.encrypt(order.getOrderId()+"#"+order.getOrderNum()+"%"+order.getOrderType());
        return sign.substring(3, 19);
    }
    /**
     *
     * 获取计算的OrderDetail
     * @param productInfo
     * @Date: 2018/6/11
     */
    public static EnyanOrderDetail getCalcOrder(ProductInfo productInfo, EnyanOrderDetail inputOrderDetail){
        EnyanOrderDetail orderDetail = new EnyanOrderDetail();
        if (EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE == inputOrderDetail.getOrderType()){//兑换码兑换订单，直接返回0
            BigDecimal value = Constant.VALUE_0;
            orderDetail.setPriceFixed(value);
            orderDetail.setPriceSelling(value);
            orderDetail.setIncomeTotal(value);
            orderDetail.setIncomeReal(value);
            orderDetail.setIncomeVendor(value);
            orderDetail.setIncomePlat(value);
            orderDetail.setNetSales(value);
            return orderDetail;
        }
        if (productInfo.isDiscountAnyIsValid()){//单个折扣或者N件折
            orderDetail.setPriceFixed(productInfo.getPriceHkd());
            orderDetail.setPriceSelling(productInfo.getRealPriceHKD());
            orderDetail.setIncomeTotal(productInfo.getRealPriceHKD());
            orderDetail.setIncomeReal(productInfo.getRealPriceHKD());
        }else {
            orderDetail.setPriceFixed(productInfo.getPriceHkd());
            orderDetail.setPriceSelling(productInfo.getRealPriceHKD());
            orderDetail.setIncomeTotal(productInfo.getRealPriceHKD());
            orderDetail.setIncomeReal(productInfo.getRealPriceHKD());
        }
        BigDecimal incomeSubtractPayFee = orderDetail.getIncomeTotal().subtract(inputOrderDetail.getPayFee());//销售额减去支付的费用
        orderDetail.setNetSales(incomeSubtractPayFee);
        //EnyanPublisher publisher = enyanPublisherService.queryRecordByPrimaryKey(productInfo.getPublisherId()).getResult();
        //TODO 更新：版税金额（incomeVendor） =（收入 - 支付费用）* 版税费率（2020/04/10）
        int vendorPercent = 0;
        if (null != inputOrderDetail.getVendorPercent()){
            vendorPercent = inputOrderDetail.getVendorPercent();
        }
        BigDecimal value = incomeSubtractPayFee.multiply(new BigDecimal(vendorPercent))
                .divide(Constant.VALUE_100, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
        orderDetail.setIncomeVendor(value);
        orderDetail.setIncomePlat(incomeSubtractPayFee.subtract(value));

        if (orderDetail.getIncomeTotal().doubleValue()>0){//盈利

        }else {//不盈利
            orderDetail.setIncomeVendor(Constant.VALUE_0);
            orderDetail.setIncomePlat(Constant.VALUE_0);
        }

        return orderDetail;
    }

    public static EnyanRentDetail getCalcRentOrder(EnyanRentDetail inputRentDetail){
        //EnyanOrderDetail orderDetail = new EnyanOrderDetail();
        EnyanRentDetail enyanRentDetail = new EnyanRentDetail();

        enyanRentDetail.setIncomeTotal(inputRentDetail.getIncomeTotal());
        enyanRentDetail.setIncomeReal(inputRentDetail.getIncomeReal());

        BigDecimal payFee = getPayFee(enyanRentDetail.getIncomeTotal(),inputRentDetail.getPayType());
        enyanRentDetail.setPayFee(payFee);

        BigDecimal incomeSubtractPayFee = enyanRentDetail.getIncomeTotal().subtract(payFee);//销售额减去支付的费用
        enyanRentDetail.setNetSales(incomeSubtractPayFee);
        //EnyanPublisher publisher = enyanPublisherService.queryRecordByPrimaryKey(productInfo.getPublisherId()).getResult();
        int vendorPercent = 0;
        if (null != inputRentDetail.getVendorPercent()){
            vendorPercent = inputRentDetail.getVendorPercent();
        }
        BigDecimal value = incomeSubtractPayFee.multiply(new BigDecimal(vendorPercent))
                                   .divide(Constant.VALUE_100, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
        enyanRentDetail.setIncomeVendor(value);
        enyanRentDetail.setIncomePlat(incomeSubtractPayFee.subtract(value));

        if (enyanRentDetail.getIncomeTotal().doubleValue()>0){//盈利

        }else {//不盈利
            enyanRentDetail.setIncomeVendor(Constant.VALUE_0);
            enyanRentDetail.setIncomePlat(Constant.VALUE_0);
        }

        return enyanRentDetail;
    }

    /**
     * <p>根据支付类型，获取支付费用</p>
     * <p>信用卡：香港本地卡是2.9%+2.35，其他全部是4.4%+2.35的手续费</p>
     * <p>支付宝：*3%</p>
     * <p>label.pay.1=支付宝</p>
     * <p>label.pay.2=信用卡</p>
     * <p>label.pay.3=免费</p>
     * <p>label.pay.4=兑换码</p>
     * <p>label.pay.5=直接支付</p>
     * <p>label.pay.21=信用卡(非香港)</p>
     * <p>label.pay.22=信用卡(香港)</p>
     * @param enyanOrder
     * @return: BigDecimal
     * @date: 2020-04-09
     */
    public static  BigDecimal getPayFee(EnyanOrder enyanOrder){
        if (EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE == enyanOrder.getOrderType()){//兑换码兑换订单，直接返回0
            return Constant.VALUE_0;
        }
        int payType = enyanOrder.getOrderPayInfo().getCharge().getPayType();
        return getPayFee(enyanOrder.getOrderTotal(), payType);
    }

    /**
     * <p>根据总价格及支付类型获取手续费</p>
     * @param totalFee
     * @param payType
     * @return java.math.BigDecimal
     * @since : 2022/11/4
     **/
    public static BigDecimal getPayFee(BigDecimal totalFee, int payType){
        switch (payType){
            case EBookConstant.PayType.ALI_PAY://支付宝
                return totalFee.multiply(new BigDecimal("3"))
                               .divide(Constant.VALUE_100, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
            case EBookConstant.PayType.ALI_PAY_HK://支付宝HK
            case EBookConstant.PayType.DIRECT_PAY_ALIPAY://使用支付宝支付宝HK费用
                return totalFee.multiply(new BigDecimal("1.7"))
                               .divide(Constant.VALUE_100, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
            case EBookConstant.PayType.STRIPE_PAY://信用卡

            case EBookConstant.PayType.STRIPE_PAY_OUTSIDE_HK://信用卡(非香港)
                return totalFee.multiply(new BigDecimal("4.4"))
                               .divide(Constant.VALUE_100, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING)
                               .add(new BigDecimal("2.35"));
            case EBookConstant.PayType.STRIPE_PAY_IN_HK://信用卡(香港)
                return totalFee.multiply(new BigDecimal("2.9"))
                               .divide(Constant.VALUE_100, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING)
                               .add(new BigDecimal("2.35"));
            case EBookConstant.PayType.DIRECT_PAY:
                return Constant.VALUE_0;
        }
        return Constant.VALUE_0;
    }

    /**
     * <p>根据单位占比获取新的数值</p>
     * @param oldValue 旧单位值
     * @param oldTotal 旧单位总数
     * @param newTotal 新单位总数
     * @return: java.math.BigDecimal 新的数值
     * @since : 2020-09-09
     */
    public static BigDecimal getNewValuePart(BigDecimal oldValue, BigDecimal oldTotal, BigDecimal newTotal){
        if (oldTotal.doubleValue() > 0){
            return oldValue.multiply(newTotal).divide(oldTotal, Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
        }
        return Constant.VALUE_0;
    }

    /**
     * <p>这是一个测试的数据</p>
     * @param i 整型
     * @param a 字符串
     * @Return: java.lang.String
     * @Date: 2020-07-03
     */
    public static String tmp(int i, String a){
        return "";
    }

}
