package com.aaron.spring.common;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-03-28
 * @Modified By:
 */
public class SlackMsgBuild {
    private StringBuffer textBuild;

    public SlackMsgBuild() {
        this.textBuild = new StringBuffer();
    }
    /**
     *
     *  加粗
     * @param text
     * @Date: 2020-03-28
     */
    public void addBoldText(String text){
        textBuild.append("*");
        textBuild.append(text);
        textBuild.append("*");
    }

    /**
     *
     *  text
     * @param text
     * @Date: 2020-03-28
     */
    public void addText(String text){
        textBuild.append(text);
    }

    /**
     *
     *  加红
     * @param text
     * @Date: 2020-03-28
     */
    public void addRedText(String text){
        textBuild.append(" `");
        textBuild.append(text);
        textBuild.append("` ");
    }

    /**
     *
     *  换行 \n
     * @Date: 2020-03-28
     */
    public void addBrText(){
        textBuild.append("\n\n");
    }

    /**
     *
     *  引用换行 \n
     * @Date: 2020-03-28
     */
    public void addQuoteBrText(){
        textBuild.append("\n>\n");
    }

    /**
     *
     *  斜体
     * @param text
     * @Date: 2020-03-28
     */
    public void addStrokeText(String text){
        textBuild.append("~");
        textBuild.append(text);
        textBuild.append("~");
    }
    /**
     *
     *  引用
     * @param text
     * @Date: 2020-03-28
     */
    public void addQuoteText(String text){
        textBuild.append(">");
        textBuild.append(text);
    }

    public String getText() {
        return textBuild.toString();
    }


}
