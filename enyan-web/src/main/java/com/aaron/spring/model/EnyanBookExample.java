package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class EnyanBookExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanBookExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andBookIdIsNull() {
            addCriterion("book_id is null");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNotNull() {
            addCriterion("book_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookIdEqualTo(Long value) {
            addCriterion("book_id =", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotEqualTo(Long value) {
            addCriterion("book_id <>", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThan(Long value) {
            addCriterion("book_id >", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThanOrEqualTo(Long value) {
            addCriterion("book_id >=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThan(Long value) {
            addCriterion("book_id <", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThanOrEqualTo(Long value) {
            addCriterion("book_id <=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdIn(List<Long> values) {
            addCriterion("book_id in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotIn(List<Long> values) {
            addCriterion("book_id not in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdBetween(Long value1, Long value2) {
            addCriterion("book_id between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotBetween(Long value1, Long value2) {
            addCriterion("book_id not between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookTitleIsNull() {
            addCriterion("book_title is null");
            return (Criteria) this;
        }

        public Criteria andBookTitleIsNotNull() {
            addCriterion("book_title is not null");
            return (Criteria) this;
        }

        public Criteria andBookTitleEqualTo(String value) {
            addCriterion("book_title =", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotEqualTo(String value) {
            addCriterion("book_title <>", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleGreaterThan(String value) {
            addCriterion("book_title >", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleGreaterThanOrEqualTo(String value) {
            addCriterion("book_title >=", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleLessThan(String value) {
            addCriterion("book_title <", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleLessThanOrEqualTo(String value) {
            addCriterion("book_title <=", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleLike(String value) {
            addCriterion("book_title like", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleRegexp(String value) {
            addCriterion("book_title REGEXP", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotLike(String value) {
            addCriterion("book_title not like", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleIn(List<String> values) {
            addCriterion("book_title in", values, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotIn(List<String> values) {
            addCriterion("book_title not in", values, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleBetween(String value1, String value2) {
            addCriterion("book_title between", value1, value2, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotBetween(String value1, String value2) {
            addCriterion("book_title not between", value1, value2, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookPinyinIsNull() {
            addCriterion("book_pinyin is null");
            return (Criteria) this;
        }

        public Criteria andBookPinyinIsNotNull() {
            addCriterion("book_pinyin is not null");
            return (Criteria) this;
        }

        public Criteria andBookPinyinEqualTo(String value) {
            addCriterion("book_pinyin =", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinNotEqualTo(String value) {
            addCriterion("book_pinyin <>", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinGreaterThan(String value) {
            addCriterion("book_pinyin >", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinGreaterThanOrEqualTo(String value) {
            addCriterion("book_pinyin >=", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinLessThan(String value) {
            addCriterion("book_pinyin <", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinLessThanOrEqualTo(String value) {
            addCriterion("book_pinyin <=", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinLike(String value) {
            addCriterion("book_pinyin like", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinNotLike(String value) {
            addCriterion("book_pinyin not like", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinIn(List<String> values) {
            addCriterion("book_pinyin in", values, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinNotIn(List<String> values) {
            addCriterion("book_pinyin not in", values, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinBetween(String value1, String value2) {
            addCriterion("book_pinyin between", value1, value2, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinNotBetween(String value1, String value2) {
            addCriterion("book_pinyin not between", value1, value2, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andBookPinyinRegexp(String value) {
            addCriterion("book_pinyin REGEXP", value, "bookPinyin");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNull() {
            addCriterion("author is null");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNotNull() {
            addCriterion("author is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorEqualTo(String value) {
            addCriterion("author =", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotEqualTo(String value) {
            addCriterion("author <>", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThan(String value) {
            addCriterion("author >", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThanOrEqualTo(String value) {
            addCriterion("author >=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThan(String value) {
            addCriterion("author <", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThanOrEqualTo(String value) {
            addCriterion("author <=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLike(String value) {
            addCriterion("author like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorRegexp(String value) {
            addCriterion("author REGEXP", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotLike(String value) {
            addCriterion("author not like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorIn(List<String> values) {
            addCriterion("author in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotIn(List<String> values) {
            addCriterion("author not in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorBetween(String value1, String value2) {
            addCriterion("author between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotBetween(String value1, String value2) {
            addCriterion("author not between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorBioIsNull() {
            addCriterion("author_bio is null");
            return (Criteria) this;
        }

        public Criteria andAuthorBioIsNotNull() {
            addCriterion("author_bio is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorBioEqualTo(String value) {
            addCriterion("author_bio =", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioNotEqualTo(String value) {
            addCriterion("author_bio <>", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioGreaterThan(String value) {
            addCriterion("author_bio >", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioGreaterThanOrEqualTo(String value) {
            addCriterion("author_bio >=", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioLessThan(String value) {
            addCriterion("author_bio <", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioLessThanOrEqualTo(String value) {
            addCriterion("author_bio <=", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioLike(String value) {
            addCriterion("author_bio like", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioNotLike(String value) {
            addCriterion("author_bio not like", value, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioIn(List<String> values) {
            addCriterion("author_bio in", values, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioNotIn(List<String> values) {
            addCriterion("author_bio not in", values, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioBetween(String value1, String value2) {
            addCriterion("author_bio between", value1, value2, "authorBio");
            return (Criteria) this;
        }

        public Criteria andAuthorBioNotBetween(String value1, String value2) {
            addCriterion("author_bio not between", value1, value2, "authorBio");
            return (Criteria) this;
        }

        public Criteria andTranslatorIsNull() {
            addCriterion("translator is null");
            return (Criteria) this;
        }

        public Criteria andTranslatorIsNotNull() {
            addCriterion("translator is not null");
            return (Criteria) this;
        }

        public Criteria andTranslatorEqualTo(String value) {
            addCriterion("translator =", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorNotEqualTo(String value) {
            addCriterion("translator <>", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorGreaterThan(String value) {
            addCriterion("translator >", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorGreaterThanOrEqualTo(String value) {
            addCriterion("translator >=", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorLessThan(String value) {
            addCriterion("translator <", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorLessThanOrEqualTo(String value) {
            addCriterion("translator <=", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorLike(String value) {
            addCriterion("translator like", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorNotLike(String value) {
            addCriterion("translator not like", value, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorIn(List<String> values) {
            addCriterion("translator in", values, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorNotIn(List<String> values) {
            addCriterion("translator not in", values, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorBetween(String value1, String value2) {
            addCriterion("translator between", value1, value2, "translator");
            return (Criteria) this;
        }

        public Criteria andTranslatorNotBetween(String value1, String value2) {
            addCriterion("translator not between", value1, value2, "translator");
            return (Criteria) this;
        }

        public Criteria andWordCountIsNull() {
            addCriterion("word_count is null");
            return (Criteria) this;
        }

        public Criteria andWordCountIsNotNull() {
            addCriterion("word_count is not null");
            return (Criteria) this;
        }

        public Criteria andWordCountEqualTo(String value) {
            addCriterion("word_count =", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountNotEqualTo(String value) {
            addCriterion("word_count <>", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountGreaterThan(String value) {
            addCriterion("word_count >", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountGreaterThanOrEqualTo(String value) {
            addCriterion("word_count >=", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountLessThan(String value) {
            addCriterion("word_count <", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountLessThanOrEqualTo(String value) {
            addCriterion("word_count <=", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountLike(String value) {
            addCriterion("word_count like", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountNotLike(String value) {
            addCriterion("word_count not like", value, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountIn(List<String> values) {
            addCriterion("word_count in", values, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountNotIn(List<String> values) {
            addCriterion("word_count not in", values, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountBetween(String value1, String value2) {
            addCriterion("word_count between", value1, value2, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountNotBetween(String value1, String value2) {
            addCriterion("word_count not between", value1, value2, "wordCount");
            return (Criteria) this;
        }

        public Criteria andWordCountShowIsNull() {
            addCriterion("word_count_show is null");
            return (Criteria) this;
        }

        public Criteria andWordCountShowIsNotNull() {
            addCriterion("word_count_show is not null");
            return (Criteria) this;
        }

        public Criteria andWordCountShowEqualTo(String value) {
            addCriterion("word_count_show =", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowNotEqualTo(String value) {
            addCriterion("word_count_show <>", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowGreaterThan(String value) {
            addCriterion("word_count_show >", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowGreaterThanOrEqualTo(String value) {
            addCriterion("word_count_show >=", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowLessThan(String value) {
            addCriterion("word_count_show <", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowLessThanOrEqualTo(String value) {
            addCriterion("word_count_show <=", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowLike(String value) {
            addCriterion("word_count_show like", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowNotLike(String value) {
            addCriterion("word_count_show not like", value, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowIn(List<String> values) {
            addCriterion("word_count_show in", values, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowNotIn(List<String> values) {
            addCriterion("word_count_show not in", values, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowBetween(String value1, String value2) {
            addCriterion("word_count_show between", value1, value2, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andWordCountShowNotBetween(String value1, String value2) {
            addCriterion("word_count_show not between", value1, value2, "wordCountShow");
            return (Criteria) this;
        }

        public Criteria andProductWebIsNull() {
            addCriterion("product_web is null");
            return (Criteria) this;
        }

        public Criteria andProductWebIsNotNull() {
            addCriterion("product_web is not null");
            return (Criteria) this;
        }

        public Criteria andProductWebEqualTo(String value) {
            addCriterion("product_web =", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebNotEqualTo(String value) {
            addCriterion("product_web <>", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebGreaterThan(String value) {
            addCriterion("product_web >", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebGreaterThanOrEqualTo(String value) {
            addCriterion("product_web >=", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebLessThan(String value) {
            addCriterion("product_web <", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebLessThanOrEqualTo(String value) {
            addCriterion("product_web <=", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebLike(String value) {
            addCriterion("product_web like", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebNotLike(String value) {
            addCriterion("product_web not like", value, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebIn(List<String> values) {
            addCriterion("product_web in", values, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebNotIn(List<String> values) {
            addCriterion("product_web not in", values, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebBetween(String value1, String value2) {
            addCriterion("product_web between", value1, value2, "productWeb");
            return (Criteria) this;
        }

        public Criteria andProductWebNotBetween(String value1, String value2) {
            addCriterion("product_web not between", value1, value2, "productWeb");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(BigDecimal value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(BigDecimal value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(BigDecimal value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(BigDecimal value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<BigDecimal> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<BigDecimal> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeIsNull() {
            addCriterion("sales_volume is null");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeIsNotNull() {
            addCriterion("sales_volume is not null");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeEqualTo(Long value) {
            addCriterion("sales_volume =", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeNotEqualTo(Long value) {
            addCriterion("sales_volume <>", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeGreaterThan(Long value) {
            addCriterion("sales_volume >", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeGreaterThanOrEqualTo(Long value) {
            addCriterion("sales_volume >=", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeLessThan(Long value) {
            addCriterion("sales_volume <", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeLessThanOrEqualTo(Long value) {
            addCriterion("sales_volume <=", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeIn(List<Long> values) {
            addCriterion("sales_volume in", values, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeNotIn(List<Long> values) {
            addCriterion("sales_volume not in", values, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeBetween(Long value1, Long value2) {
            addCriterion("sales_volume between", value1, value2, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeNotBetween(Long value1, Long value2) {
            addCriterion("sales_volume not between", value1, value2, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andPriceCnyIsNull() {
            addCriterion("price_cny is null");
            return (Criteria) this;
        }

        public Criteria andPriceCnyIsNotNull() {
            addCriterion("price_cny is not null");
            return (Criteria) this;
        }

        public Criteria andPriceCnyEqualTo(BigDecimal value) {
            addCriterion("price_cny =", value, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyNotEqualTo(BigDecimal value) {
            addCriterion("price_cny <>", value, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyGreaterThan(BigDecimal value) {
            addCriterion("price_cny >", value, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price_cny >=", value, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyLessThan(BigDecimal value) {
            addCriterion("price_cny <", value, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price_cny <=", value, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyIn(List<BigDecimal> values) {
            addCriterion("price_cny in", values, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyNotIn(List<BigDecimal> values) {
            addCriterion("price_cny not in", values, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_cny between", value1, value2, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceCnyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_cny not between", value1, value2, "priceCny");
            return (Criteria) this;
        }

        public Criteria andPriceUsdIsNull() {
            addCriterion("price_usd is null");
            return (Criteria) this;
        }

        public Criteria andPriceUsdIsNotNull() {
            addCriterion("price_usd is not null");
            return (Criteria) this;
        }

        public Criteria andPriceUsdEqualTo(BigDecimal value) {
            addCriterion("price_usd =", value, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdNotEqualTo(BigDecimal value) {
            addCriterion("price_usd <>", value, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdGreaterThan(BigDecimal value) {
            addCriterion("price_usd >", value, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price_usd >=", value, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdLessThan(BigDecimal value) {
            addCriterion("price_usd <", value, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price_usd <=", value, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdIn(List<BigDecimal> values) {
            addCriterion("price_usd in", values, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdNotIn(List<BigDecimal> values) {
            addCriterion("price_usd not in", values, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_usd between", value1, value2, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceUsdNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_usd not between", value1, value2, "priceUsd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdIsNull() {
            addCriterion("price_hkd is null");
            return (Criteria) this;
        }

        public Criteria andPriceHkdIsNotNull() {
            addCriterion("price_hkd is not null");
            return (Criteria) this;
        }

        public Criteria andPriceHkdEqualTo(BigDecimal value) {
            addCriterion("price_hkd =", value, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdNotEqualTo(BigDecimal value) {
            addCriterion("price_hkd <>", value, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdGreaterThan(BigDecimal value) {
            addCriterion("price_hkd >", value, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price_hkd >=", value, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdLessThan(BigDecimal value) {
            addCriterion("price_hkd <", value, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price_hkd <=", value, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdIn(List<BigDecimal> values) {
            addCriterion("price_hkd in", values, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdNotIn(List<BigDecimal> values) {
            addCriterion("price_hkd not in", values, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_hkd between", value1, value2, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andPriceHkdNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_hkd not between", value1, value2, "priceHkd");
            return (Criteria) this;
        }

        public Criteria andBookCostIsNull() {
            addCriterion("book_cost is null");
            return (Criteria) this;
        }

        public Criteria andBookCostIsNotNull() {
            addCriterion("book_cost is not null");
            return (Criteria) this;
        }

        public Criteria andBookCostEqualTo(Integer value) {
            addCriterion("book_cost =", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostNotEqualTo(Integer value) {
            addCriterion("book_cost <>", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostGreaterThan(Integer value) {
            addCriterion("book_cost >", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostGreaterThanOrEqualTo(Integer value) {
            addCriterion("book_cost >=", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostLessThan(Integer value) {
            addCriterion("book_cost <", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostLessThanOrEqualTo(Integer value) {
            addCriterion("book_cost <=", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostIn(List<Integer> values) {
            addCriterion("book_cost in", values, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostNotIn(List<Integer> values) {
            addCriterion("book_cost not in", values, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostBetween(Integer value1, Integer value2) {
            addCriterion("book_cost between", value1, value2, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostNotBetween(Integer value1, Integer value2) {
            addCriterion("book_cost not between", value1, value2, "bookCost");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Long value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Long value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Long value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Long value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Long> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Long> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Long value1, Long value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameRegexp(String value) {
            addCriterion("category_name REGEXP", value, "category_name");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNull() {
            addCriterion("publisher_id is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNotNull() {
            addCriterion("publisher_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdEqualTo(Long value) {
            addCriterion("publisher_id =", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotEqualTo(Long value) {
            addCriterion("publisher_id <>", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThan(Long value) {
            addCriterion("publisher_id >", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThanOrEqualTo(Long value) {
            addCriterion("publisher_id >=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThan(Long value) {
            addCriterion("publisher_id <", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThanOrEqualTo(Long value) {
            addCriterion("publisher_id <=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIn(List<Long> values) {
            addCriterion("publisher_id in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotIn(List<Long> values) {
            addCriterion("publisher_id not in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdBetween(Long value1, Long value2) {
            addCriterion("publisher_id between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotBetween(Long value1, Long value2) {
            addCriterion("publisher_id not between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherNameIsNull() {
            addCriterion("publisher_name is null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameIsNotNull() {
            addCriterion("publisher_name is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEqualTo(String value) {
            addCriterion("publisher_name =", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotEqualTo(String value) {
            addCriterion("publisher_name <>", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameGreaterThan(String value) {
            addCriterion("publisher_name >", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameGreaterThanOrEqualTo(String value) {
            addCriterion("publisher_name >=", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameLessThan(String value) {
            addCriterion("publisher_name <", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameLessThanOrEqualTo(String value) {
            addCriterion("publisher_name <=", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameLike(String value) {
            addCriterion("publisher_name like", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameRegexp(String value) {
            addCriterion("publisher_name REGEXP", value, "publisher_name");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotLike(String value) {
            addCriterion("publisher_name not like", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameIn(List<String> values) {
            addCriterion("publisher_name in", values, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotIn(List<String> values) {
            addCriterion("publisher_name not in", values, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameBetween(String value1, String value2) {
            addCriterion("publisher_name between", value1, value2, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotBetween(String value1, String value2) {
            addCriterion("publisher_name not between", value1, value2, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublishedAtIsNull() {
            addCriterion("published_at is null");
            return (Criteria) this;
        }

        public Criteria andPublishedAtIsNotNull() {
            addCriterion("published_at is not null");
            return (Criteria) this;
        }

        public Criteria andPublishedAtEqualTo(String value) {
            addCriterion("published_at =", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtNotEqualTo(String value) {
            addCriterion("published_at <>", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtGreaterThan(String value) {
            addCriterion("published_at >", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtGreaterThanOrEqualTo(String value) {
            addCriterion("published_at >=", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtLessThan(String value) {
            addCriterion("published_at <", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtLessThanOrEqualTo(String value) {
            addCriterion("published_at <=", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtLike(String value) {
            addCriterion("published_at like", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtNotLike(String value) {
            addCriterion("published_at not like", value, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtIn(List<String> values) {
            addCriterion("published_at in", values, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtNotIn(List<String> values) {
            addCriterion("published_at not in", values, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtBetween(String value1, String value2) {
            addCriterion("published_at between", value1, value2, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andPublishedAtNotBetween(String value1, String value2) {
            addCriterion("published_at not between", value1, value2, "publishedAt");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueIsNull() {
            addCriterion("book_catalogue is null");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueIsNotNull() {
            addCriterion("book_catalogue is not null");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueEqualTo(String value) {
            addCriterion("book_catalogue =", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueNotEqualTo(String value) {
            addCriterion("book_catalogue <>", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueGreaterThan(String value) {
            addCriterion("book_catalogue >", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueGreaterThanOrEqualTo(String value) {
            addCriterion("book_catalogue >=", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueLessThan(String value) {
            addCriterion("book_catalogue <", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueLessThanOrEqualTo(String value) {
            addCriterion("book_catalogue <=", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueLike(String value) {
            addCriterion("book_catalogue like", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueNotLike(String value) {
            addCriterion("book_catalogue not like", value, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueIn(List<String> values) {
            addCriterion("book_catalogue in", values, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueNotIn(List<String> values) {
            addCriterion("book_catalogue not in", values, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueBetween(String value1, String value2) {
            addCriterion("book_catalogue between", value1, value2, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookCatalogueNotBetween(String value1, String value2) {
            addCriterion("book_catalogue not between", value1, value2, "bookCatalogue");
            return (Criteria) this;
        }

        public Criteria andBookAbstractIsNull() {
            addCriterion("book_abstract is null");
            return (Criteria) this;
        }

        public Criteria andBookAbstractIsNotNull() {
            addCriterion("book_abstract is not null");
            return (Criteria) this;
        }

        public Criteria andBookAbstractEqualTo(String value) {
            addCriterion("book_abstract =", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractNotEqualTo(String value) {
            addCriterion("book_abstract <>", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractGreaterThan(String value) {
            addCriterion("book_abstract >", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractGreaterThanOrEqualTo(String value) {
            addCriterion("book_abstract >=", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractLessThan(String value) {
            addCriterion("book_abstract <", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractLessThanOrEqualTo(String value) {
            addCriterion("book_abstract <=", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractLike(String value) {
            addCriterion("book_abstract like", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractNotLike(String value) {
            addCriterion("book_abstract not like", value, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractIn(List<String> values) {
            addCriterion("book_abstract in", values, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractNotIn(List<String> values) {
            addCriterion("book_abstract not in", values, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractBetween(String value1, String value2) {
            addCriterion("book_abstract between", value1, value2, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookAbstractNotBetween(String value1, String value2) {
            addCriterion("book_abstract not between", value1, value2, "bookAbstract");
            return (Criteria) this;
        }

        public Criteria andBookCoverIsNull() {
            addCriterion("book_cover is null");
            return (Criteria) this;
        }

        public Criteria andBookCoverIsNotNull() {
            addCriterion("book_cover is not null");
            return (Criteria) this;
        }

        public Criteria andBookCoverEqualTo(String value) {
            addCriterion("book_cover =", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotEqualTo(String value) {
            addCriterion("book_cover <>", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverGreaterThan(String value) {
            addCriterion("book_cover >", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverGreaterThanOrEqualTo(String value) {
            addCriterion("book_cover >=", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverLessThan(String value) {
            addCriterion("book_cover <", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverLessThanOrEqualTo(String value) {
            addCriterion("book_cover <=", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverLike(String value) {
            addCriterion("book_cover like", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotLike(String value) {
            addCriterion("book_cover not like", value, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverIn(List<String> values) {
            addCriterion("book_cover in", values, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotIn(List<String> values) {
            addCriterion("book_cover not in", values, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverBetween(String value1, String value2) {
            addCriterion("book_cover between", value1, value2, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverNotBetween(String value1, String value2) {
            addCriterion("book_cover not between", value1, value2, "bookCover");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppIsNull() {
            addCriterion("book_cover_app is null");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppIsNotNull() {
            addCriterion("book_cover_app is not null");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppEqualTo(String value) {
            addCriterion("book_cover_app =", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppNotEqualTo(String value) {
            addCriterion("book_cover_app <>", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppGreaterThan(String value) {
            addCriterion("book_cover_app >", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppGreaterThanOrEqualTo(String value) {
            addCriterion("book_cover_app >=", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppLessThan(String value) {
            addCriterion("book_cover_app <", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppLessThanOrEqualTo(String value) {
            addCriterion("book_cover_app <=", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppLike(String value) {
            addCriterion("book_cover_app like", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppNotLike(String value) {
            addCriterion("book_cover_app not like", value, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppIn(List<String> values) {
            addCriterion("book_cover_app in", values, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppNotIn(List<String> values) {
            addCriterion("book_cover_app not in", values, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppBetween(String value1, String value2) {
            addCriterion("book_cover_app between", value1, value2, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookCoverAppNotBetween(String value1, String value2) {
            addCriterion("book_cover_app not between", value1, value2, "bookCoverApp");
            return (Criteria) this;
        }

        public Criteria andBookSampleIsNull() {
            addCriterion("book_sample is null");
            return (Criteria) this;
        }

        public Criteria andBookSampleIsNotNull() {
            addCriterion("book_sample is not null");
            return (Criteria) this;
        }

        public Criteria andBookSampleEqualTo(String value) {
            addCriterion("book_sample =", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleNotEqualTo(String value) {
            addCriterion("book_sample <>", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleGreaterThan(String value) {
            addCriterion("book_sample >", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleGreaterThanOrEqualTo(String value) {
            addCriterion("book_sample >=", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleLessThan(String value) {
            addCriterion("book_sample <", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleLessThanOrEqualTo(String value) {
            addCriterion("book_sample <=", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleLike(String value) {
            addCriterion("book_sample like", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleNotLike(String value) {
            addCriterion("book_sample not like", value, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleIn(List<String> values) {
            addCriterion("book_sample in", values, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleNotIn(List<String> values) {
            addCriterion("book_sample not in", values, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleBetween(String value1, String value2) {
            addCriterion("book_sample between", value1, value2, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookSampleNotBetween(String value1, String value2) {
            addCriterion("book_sample not between", value1, value2, "bookSample");
            return (Criteria) this;
        }

        public Criteria andBookFullIsNull() {
            addCriterion("book_full is null");
            return (Criteria) this;
        }

        public Criteria andBookFullIsNotNull() {
            addCriterion("book_full is not null");
            return (Criteria) this;
        }

        public Criteria andBookFullEqualTo(String value) {
            addCriterion("book_full =", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullNotEqualTo(String value) {
            addCriterion("book_full <>", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullGreaterThan(String value) {
            addCriterion("book_full >", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullGreaterThanOrEqualTo(String value) {
            addCriterion("book_full >=", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullLessThan(String value) {
            addCriterion("book_full <", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullLessThanOrEqualTo(String value) {
            addCriterion("book_full <=", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullLike(String value) {
            addCriterion("book_full like", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullNotLike(String value) {
            addCriterion("book_full not like", value, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullIn(List<String> values) {
            addCriterion("book_full in", values, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullNotIn(List<String> values) {
            addCriterion("book_full not in", values, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullBetween(String value1, String value2) {
            addCriterion("book_full between", value1, value2, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookFullNotBetween(String value1, String value2) {
            addCriterion("book_full not between", value1, value2, "bookFull");
            return (Criteria) this;
        }

        public Criteria andBookHashIsNull() {
            addCriterion("book_hash is null");
            return (Criteria) this;
        }

        public Criteria andBookHashIsNotNull() {
            addCriterion("book_hash is not null");
            return (Criteria) this;
        }

        public Criteria andBookHashEqualTo(String value) {
            addCriterion("book_hash =", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashNotEqualTo(String value) {
            addCriterion("book_hash <>", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashGreaterThan(String value) {
            addCriterion("book_hash >", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashGreaterThanOrEqualTo(String value) {
            addCriterion("book_hash >=", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashLessThan(String value) {
            addCriterion("book_hash <", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashLessThanOrEqualTo(String value) {
            addCriterion("book_hash <=", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashLike(String value) {
            addCriterion("book_hash like", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashNotLike(String value) {
            addCriterion("book_hash not like", value, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashIn(List<String> values) {
            addCriterion("book_hash in", values, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashNotIn(List<String> values) {
            addCriterion("book_hash not in", values, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashBetween(String value1, String value2) {
            addCriterion("book_hash between", value1, value2, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookHashNotBetween(String value1, String value2) {
            addCriterion("book_hash not between", value1, value2, "bookHash");
            return (Criteria) this;
        }

        public Criteria andBookIsbnIsNull() {
            addCriterion("book_isbn is null");
            return (Criteria) this;
        }

        public Criteria andBookIsbnIsNotNull() {
            addCriterion("book_isbn is not null");
            return (Criteria) this;
        }

        public Criteria andBookIsbnEqualTo(String value) {
            addCriterion("book_isbn =", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnNotEqualTo(String value) {
            addCriterion("book_isbn <>", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnGreaterThan(String value) {
            addCriterion("book_isbn >", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnGreaterThanOrEqualTo(String value) {
            addCriterion("book_isbn >=", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnLessThan(String value) {
            addCriterion("book_isbn <", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnLessThanOrEqualTo(String value) {
            addCriterion("book_isbn <=", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnLike(String value) {
            addCriterion("book_isbn like", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnNotLike(String value) {
            addCriterion("book_isbn not like", value, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnIn(List<String> values) {
            addCriterion("book_isbn in", values, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnNotIn(List<String> values) {
            addCriterion("book_isbn not in", values, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnBetween(String value1, String value2) {
            addCriterion("book_isbn between", value1, value2, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookIsbnNotBetween(String value1, String value2) {
            addCriterion("book_isbn not between", value1, value2, "bookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnIsNull() {
            addCriterion("ebook_isbn is null");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnIsNotNull() {
            addCriterion("ebook_isbn is not null");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnEqualTo(String value) {
            addCriterion("ebook_isbn =", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnNotEqualTo(String value) {
            addCriterion("ebook_isbn <>", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnGreaterThan(String value) {
            addCriterion("ebook_isbn >", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnGreaterThanOrEqualTo(String value) {
            addCriterion("ebook_isbn >=", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnLessThan(String value) {
            addCriterion("ebook_isbn <", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnLessThanOrEqualTo(String value) {
            addCriterion("ebook_isbn <=", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnLike(String value) {
            addCriterion("ebook_isbn like", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnNotLike(String value) {
            addCriterion("ebook_isbn not like", value, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnIn(List<String> values) {
            addCriterion("ebook_isbn in", values, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnNotIn(List<String> values) {
            addCriterion("ebook_isbn not in", values, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnBetween(String value1, String value2) {
            addCriterion("ebook_isbn between", value1, value2, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andEbookIsbnNotBetween(String value1, String value2) {
            addCriterion("ebook_isbn not between", value1, value2, "ebookIsbn");
            return (Criteria) this;
        }

        public Criteria andBookEsinIsNull() {
            addCriterion("book_esin is null");
            return (Criteria) this;
        }

        public Criteria andBookEsinIsNotNull() {
            addCriterion("book_esin is not null");
            return (Criteria) this;
        }

        public Criteria andBookEsinEqualTo(String value) {
            addCriterion("book_esin =", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinNotEqualTo(String value) {
            addCriterion("book_esin <>", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinGreaterThan(String value) {
            addCriterion("book_esin >", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinGreaterThanOrEqualTo(String value) {
            addCriterion("book_esin >=", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinLessThan(String value) {
            addCriterion("book_esin <", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinLessThanOrEqualTo(String value) {
            addCriterion("book_esin <=", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinLike(String value) {
            addCriterion("book_esin like", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinNotLike(String value) {
            addCriterion("book_esin not like", value, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinIn(List<String> values) {
            addCriterion("book_esin in", values, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinNotIn(List<String> values) {
            addCriterion("book_esin not in", values, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinBetween(String value1, String value2) {
            addCriterion("book_esin between", value1, value2, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andBookEsinNotBetween(String value1, String value2) {
            addCriterion("book_esin not between", value1, value2, "bookEsin");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationIsNull() {
            addCriterion("has_book_pagination is null");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationIsNotNull() {
            addCriterion("has_book_pagination is not null");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationEqualTo(Byte value) {
            addCriterion("has_book_pagination =", value, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationNotEqualTo(Byte value) {
            addCriterion("has_book_pagination <>", value, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationGreaterThan(Byte value) {
            addCriterion("has_book_pagination >", value, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationGreaterThanOrEqualTo(Byte value) {
            addCriterion("has_book_pagination >=", value, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationLessThan(Byte value) {
            addCriterion("has_book_pagination <", value, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationLessThanOrEqualTo(Byte value) {
            addCriterion("has_book_pagination <=", value, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationIn(List<Byte> values) {
            addCriterion("has_book_pagination in", values, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationNotIn(List<Byte> values) {
            addCriterion("has_book_pagination not in", values, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationBetween(Byte value1, Byte value2) {
            addCriterion("has_book_pagination between", value1, value2, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andHasBookPaginationNotBetween(Byte value1, Byte value2) {
            addCriterion("has_book_pagination not between", value1, value2, "hasBookPagination");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeIsNull() {
            addCriterion("book_pub_code is null");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeIsNotNull() {
            addCriterion("book_pub_code is not null");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeEqualTo(String value) {
            addCriterion("book_pub_code =", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeNotEqualTo(String value) {
            addCriterion("book_pub_code <>", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeGreaterThan(String value) {
            addCriterion("book_pub_code >", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeGreaterThanOrEqualTo(String value) {
            addCriterion("book_pub_code >=", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeLessThan(String value) {
            addCriterion("book_pub_code <", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeLessThanOrEqualTo(String value) {
            addCriterion("book_pub_code <=", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeLike(String value) {
            addCriterion("book_pub_code like", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeNotLike(String value) {
            addCriterion("book_pub_code not like", value, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeIn(List<String> values) {
            addCriterion("book_pub_code in", values, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeNotIn(List<String> values) {
            addCriterion("book_pub_code not in", values, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeBetween(String value1, String value2) {
            addCriterion("book_pub_code between", value1, value2, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookPubCodeNotBetween(String value1, String value2) {
            addCriterion("book_pub_code not between", value1, value2, "bookPubCode");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsIsNull() {
            addCriterion("book_keywords is null");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsIsNotNull() {
            addCriterion("book_keywords is not null");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsEqualTo(String value) {
            addCriterion("book_keywords =", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsNotEqualTo(String value) {
            addCriterion("book_keywords <>", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsGreaterThan(String value) {
            addCriterion("book_keywords >", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsGreaterThanOrEqualTo(String value) {
            addCriterion("book_keywords >=", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsLessThan(String value) {
            addCriterion("book_keywords <", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsLessThanOrEqualTo(String value) {
            addCriterion("book_keywords <=", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsLike(String value) {
            addCriterion("book_keywords like", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsNotLike(String value) {
            addCriterion("book_keywords not like", value, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsIn(List<String> values) {
            addCriterion("book_keywords in", values, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsNotIn(List<String> values) {
            addCriterion("book_keywords not in", values, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsBetween(String value1, String value2) {
            addCriterion("book_keywords between", value1, value2, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookKeywordsNotBetween(String value1, String value2) {
            addCriterion("book_keywords not between", value1, value2, "bookKeywords");
            return (Criteria) this;
        }

        public Criteria andBookTypeIsNull() {
            addCriterion("book_type is null");
            return (Criteria) this;
        }

        public Criteria andBookTypeIsNotNull() {
            addCriterion("book_type is not null");
            return (Criteria) this;
        }

        public Criteria andBookTypeEqualTo(Integer value) {
            addCriterion("book_type =", value, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeNotEqualTo(Integer value) {
            addCriterion("book_type <>", value, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeGreaterThan(Integer value) {
            addCriterion("book_type >", value, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("book_type >=", value, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeLessThan(Integer value) {
            addCriterion("book_type <", value, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeLessThanOrEqualTo(Integer value) {
            addCriterion("book_type <=", value, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeIn(List<Integer> values) {
            addCriterion("book_type in", values, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeNotIn(List<Integer> values) {
            addCriterion("book_type not in", values, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeBetween(Integer value1, Integer value2) {
            addCriterion("book_type between", value1, value2, "bookType");
            return (Criteria) this;
        }

        public Criteria andBookTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("book_type not between", value1, value2, "bookType");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferIsNull() {
            addCriterion("special_offer is null");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferIsNotNull() {
            addCriterion("special_offer is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferEqualTo(Integer value) {
            addCriterion("special_offer =", value, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferNotEqualTo(Integer value) {
            addCriterion("special_offer <>", value, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferGreaterThan(Integer value) {
            addCriterion("special_offer >", value, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferGreaterThanOrEqualTo(Integer value) {
            addCriterion("special_offer >=", value, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferLessThan(Integer value) {
            addCriterion("special_offer <", value, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferLessThanOrEqualTo(Integer value) {
            addCriterion("special_offer <=", value, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferIn(List<Integer> values) {
            addCriterion("special_offer in", values, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferNotIn(List<Integer> values) {
            addCriterion("special_offer not in", values, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferBetween(Integer value1, Integer value2) {
            addCriterion("special_offer between", value1, value2, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andSpecialOfferNotBetween(Integer value1, Integer value2) {
            addCriterion("special_offer not between", value1, value2, "specialOffer");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountIsNull() {
            addCriterion("area_discount is null");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountIsNotNull() {
            addCriterion("area_discount is not null");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountEqualTo(Integer value) {
            addCriterion("area_discount =", value, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountNotEqualTo(Integer value) {
            addCriterion("area_discount <>", value, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountGreaterThan(Integer value) {
            addCriterion("area_discount >", value, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountGreaterThanOrEqualTo(Integer value) {
            addCriterion("area_discount >=", value, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountLessThan(Integer value) {
            addCriterion("area_discount <", value, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountLessThanOrEqualTo(Integer value) {
            addCriterion("area_discount <=", value, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountIn(List<Integer> values) {
            addCriterion("area_discount in", values, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountNotIn(List<Integer> values) {
            addCriterion("area_discount not in", values, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountBetween(Integer value1, Integer value2) {
            addCriterion("area_discount between", value1, value2, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andAreaDiscountNotBetween(Integer value1, Integer value2) {
            addCriterion("area_discount not between", value1, value2, "areaDiscount");
            return (Criteria) this;
        }

        public Criteria andCanTtsIsNull() {
            addCriterion("can_tts is null");
            return (Criteria) this;
        }

        public Criteria andCanTtsIsNotNull() {
            addCriterion("can_tts is not null");
            return (Criteria) this;
        }

        public Criteria andCanTtsEqualTo(Integer value) {
            addCriterion("can_tts =", value, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsNotEqualTo(Integer value) {
            addCriterion("can_tts <>", value, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsGreaterThan(Integer value) {
            addCriterion("can_tts >", value, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsGreaterThanOrEqualTo(Integer value) {
            addCriterion("can_tts >=", value, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsLessThan(Integer value) {
            addCriterion("can_tts <", value, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsLessThanOrEqualTo(Integer value) {
            addCriterion("can_tts <=", value, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsIn(List<Integer> values) {
            addCriterion("can_tts in", values, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsNotIn(List<Integer> values) {
            addCriterion("can_tts not in", values, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsBetween(Integer value1, Integer value2) {
            addCriterion("can_tts between", value1, value2, "canTts");
            return (Criteria) this;
        }

        public Criteria andCanTtsNotBetween(Integer value1, Integer value2) {
            addCriterion("can_tts not between", value1, value2, "canTts");
            return (Criteria) this;
        }

        public Criteria andSizeIsNull() {
            addCriterion("size is null");
            return (Criteria) this;
        }

        public Criteria andSizeIsNotNull() {
            addCriterion("size is not null");
            return (Criteria) this;
        }

        public Criteria andSizeEqualTo(String value) {
            addCriterion("size =", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotEqualTo(String value) {
            addCriterion("size <>", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThan(String value) {
            addCriterion("size >", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanOrEqualTo(String value) {
            addCriterion("size >=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThan(String value) {
            addCriterion("size <", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThanOrEqualTo(String value) {
            addCriterion("size <=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLike(String value) {
            addCriterion("size like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotLike(String value) {
            addCriterion("size not like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeIn(List<String> values) {
            addCriterion("size in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotIn(List<String> values) {
            addCriterion("size not in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeBetween(String value1, String value2) {
            addCriterion("size between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotBetween(String value1, String value2) {
            addCriterion("size not between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefIsNull() {
            addCriterion("book_drm_ref is null");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefIsNotNull() {
            addCriterion("book_drm_ref is not null");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefEqualTo(String value) {
            addCriterion("book_drm_ref =", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefNotEqualTo(String value) {
            addCriterion("book_drm_ref <>", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefGreaterThan(String value) {
            addCriterion("book_drm_ref >", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefGreaterThanOrEqualTo(String value) {
            addCriterion("book_drm_ref >=", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefLessThan(String value) {
            addCriterion("book_drm_ref <", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefLessThanOrEqualTo(String value) {
            addCriterion("book_drm_ref <=", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefLike(String value) {
            addCriterion("book_drm_ref like", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefNotLike(String value) {
            addCriterion("book_drm_ref not like", value, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefIn(List<String> values) {
            addCriterion("book_drm_ref in", values, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefNotIn(List<String> values) {
            addCriterion("book_drm_ref not in", values, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefBetween(String value1, String value2) {
            addCriterion("book_drm_ref between", value1, value2, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andBookDrmRefNotBetween(String value1, String value2) {
            addCriterion("book_drm_ref not between", value1, value2, "bookDrmRef");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdIsNull() {
            addCriterion("discount_single_id is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdIsNotNull() {
            addCriterion("discount_single_id is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdEqualTo(Long value) {
            addCriterion("discount_single_id =", value, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdNotEqualTo(Long value) {
            addCriterion("discount_single_id <>", value, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdGreaterThan(Long value) {
            addCriterion("discount_single_id >", value, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("discount_single_id >=", value, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdLessThan(Long value) {
            addCriterion("discount_single_id <", value, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdLessThanOrEqualTo(Long value) {
            addCriterion("discount_single_id <=", value, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdIn(List<Long> values) {
            addCriterion("discount_single_id in", values, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdNotIn(List<Long> values) {
            addCriterion("discount_single_id not in", values, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdBetween(Long value1, Long value2) {
            addCriterion("discount_single_id between", value1, value2, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIdNotBetween(Long value1, Long value2) {
            addCriterion("discount_single_id not between", value1, value2, "discountSingleId");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeIsNull() {
            addCriterion("discount_single_type is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeIsNotNull() {
            addCriterion("discount_single_type is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeEqualTo(Byte value) {
            addCriterion("discount_single_type =", value, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeNotEqualTo(Byte value) {
            addCriterion("discount_single_type <>", value, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeGreaterThan(Byte value) {
            addCriterion("discount_single_type >", value, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("discount_single_type >=", value, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeLessThan(Byte value) {
            addCriterion("discount_single_type <", value, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeLessThanOrEqualTo(Byte value) {
            addCriterion("discount_single_type <=", value, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeIn(List<Byte> values) {
            addCriterion("discount_single_type in", values, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeNotIn(List<Byte> values) {
            addCriterion("discount_single_type not in", values, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeBetween(Byte value1, Byte value2) {
            addCriterion("discount_single_type between", value1, value2, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("discount_single_type not between", value1, value2, "discountSingleType");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueIsNull() {
            addCriterion("discount_single_value is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueIsNotNull() {
            addCriterion("discount_single_value is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueEqualTo(Integer value) {
            addCriterion("discount_single_value =", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueNotEqualTo(Integer value) {
            addCriterion("discount_single_value <>", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueGreaterThan(Integer value) {
            addCriterion("discount_single_value >", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("discount_single_value >=", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueLessThan(Integer value) {
            addCriterion("discount_single_value <", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueLessThanOrEqualTo(Integer value) {
            addCriterion("discount_single_value <=", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueIn(List<Integer> values) {
            addCriterion("discount_single_value in", values, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueNotIn(List<Integer> values) {
            addCriterion("discount_single_value not in", values, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueBetween(Integer value1, Integer value2) {
            addCriterion("discount_single_value between", value1, value2, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueNotBetween(Integer value1, Integer value2) {
            addCriterion("discount_single_value not between", value1, value2, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeIsNull() {
            addCriterion("discount_single_start_time is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeIsNotNull() {
            addCriterion("discount_single_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_start_time =", value, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_start_time <>", value, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("discount_single_start_time >", value, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_start_time >=", value, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeLessThan(Date value) {
            addCriterionForJDBCDate("discount_single_start_time <", value, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_start_time <=", value, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeIn(List<Date> values) {
            addCriterionForJDBCDate("discount_single_start_time in", values, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("discount_single_start_time not in", values, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("discount_single_start_time between", value1, value2, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleStartTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("discount_single_start_time not between", value1, value2, "discountSingleStartTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeIsNull() {
            addCriterion("discount_single_end_time is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeIsNotNull() {
            addCriterion("discount_single_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_end_time =", value, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_end_time <>", value, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("discount_single_end_time >", value, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_end_time >=", value, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeLessThan(Date value) {
            addCriterionForJDBCDate("discount_single_end_time <", value, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("discount_single_end_time <=", value, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeIn(List<Date> values) {
            addCriterionForJDBCDate("discount_single_end_time in", values, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("discount_single_end_time not in", values, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("discount_single_end_time between", value1, value2, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleEndTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("discount_single_end_time not between", value1, value2, "discountSingleEndTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidIsNull() {
            addCriterion("discount_single_is_valid is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidIsNotNull() {
            addCriterion("discount_single_is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidEqualTo(Byte value) {
            addCriterion("discount_single_is_valid =", value, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidNotEqualTo(Byte value) {
            addCriterion("discount_single_is_valid <>", value, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidGreaterThan(Byte value) {
            addCriterion("discount_single_is_valid >", value, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidGreaterThanOrEqualTo(Byte value) {
            addCriterion("discount_single_is_valid >=", value, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidLessThan(Byte value) {
            addCriterion("discount_single_is_valid <", value, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidLessThanOrEqualTo(Byte value) {
            addCriterion("discount_single_is_valid <=", value, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidIn(List<Byte> values) {
            addCriterion("discount_single_is_valid in", values, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidNotIn(List<Byte> values) {
            addCriterion("discount_single_is_valid not in", values, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidBetween(Byte value1, Byte value2) {
            addCriterion("discount_single_is_valid between", value1, value2, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleIsValidNotBetween(Byte value1, Byte value2) {
            addCriterion("discount_single_is_valid not between", value1, value2, "discountSingleIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionIsNull() {
            addCriterion("discount_single_description is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionIsNotNull() {
            addCriterion("discount_single_description is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionEqualTo(String value) {
            addCriterion("discount_single_description =", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionNotEqualTo(String value) {
            addCriterion("discount_single_description <>", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionGreaterThan(String value) {
            addCriterion("discount_single_description >", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("discount_single_description >=", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionLessThan(String value) {
            addCriterion("discount_single_description <", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionLessThanOrEqualTo(String value) {
            addCriterion("discount_single_description <=", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionLike(String value) {
            addCriterion("discount_single_description like", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionNotLike(String value) {
            addCriterion("discount_single_description not like", value, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionIn(List<String> values) {
            addCriterion("discount_single_description in", values, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionNotIn(List<String> values) {
            addCriterion("discount_single_description not in", values, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionBetween(String value1, String value2) {
            addCriterion("discount_single_description between", value1, value2, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleDescriptionNotBetween(String value1, String value2) {
            addCriterion("discount_single_description not between", value1, value2, "discountSingleDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountIdIsNull() {
            addCriterion("discount_id is null");
            return (Criteria) this;
        }

        public Criteria andDiscountIdIsNotNull() {
            addCriterion("discount_id is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountIdEqualTo(Long value) {
            addCriterion("discount_id =", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdNotEqualTo(Long value) {
            addCriterion("discount_id <>", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdGreaterThan(Long value) {
            addCriterion("discount_id >", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("discount_id >=", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdLessThan(Long value) {
            addCriterion("discount_id <", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdLessThanOrEqualTo(Long value) {
            addCriterion("discount_id <=", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdIn(List<Long> values) {
            addCriterion("discount_id in", values, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdNotIn(List<Long> values) {
            addCriterion("discount_id not in", values, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdBetween(Long value1, Long value2) {
            addCriterion("discount_id between", value1, value2, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdNotBetween(Long value1, Long value2) {
            addCriterion("discount_id not between", value1, value2, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionIsNull() {
            addCriterion("discount_description is null");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionIsNotNull() {
            addCriterion("discount_description is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionEqualTo(String value) {
            addCriterion("discount_description =", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionNotEqualTo(String value) {
            addCriterion("discount_description <>", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionGreaterThan(String value) {
            addCriterion("discount_description >", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("discount_description >=", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionLessThan(String value) {
            addCriterion("discount_description <", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionLessThanOrEqualTo(String value) {
            addCriterion("discount_description <=", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionLike(String value) {
            addCriterion("discount_description like", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionNotLike(String value) {
            addCriterion("discount_description not like", value, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionIn(List<String> values) {
            addCriterion("discount_description in", values, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionNotIn(List<String> values) {
            addCriterion("discount_description not in", values, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionBetween(String value1, String value2) {
            addCriterion("discount_description between", value1, value2, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountDescriptionNotBetween(String value1, String value2) {
            addCriterion("discount_description not between", value1, value2, "discountDescription");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidIsNull() {
            addCriterion("discount_is_valid is null");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidIsNotNull() {
            addCriterion("discount_is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidEqualTo(Byte value) {
            addCriterion("discount_is_valid =", value, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidNotEqualTo(Byte value) {
            addCriterion("discount_is_valid <>", value, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidGreaterThan(Byte value) {
            addCriterion("discount_is_valid >", value, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidGreaterThanOrEqualTo(Byte value) {
            addCriterion("discount_is_valid >=", value, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidLessThan(Byte value) {
            addCriterion("discount_is_valid <", value, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidLessThanOrEqualTo(Byte value) {
            addCriterion("discount_is_valid <=", value, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidIn(List<Byte> values) {
            addCriterion("discount_is_valid in", values, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidNotIn(List<Byte> values) {
            addCriterion("discount_is_valid not in", values, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidBetween(Byte value1, Byte value2) {
            addCriterion("discount_is_valid between", value1, value2, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andDiscountIsValidNotBetween(Byte value1, Byte value2) {
            addCriterion("discount_is_valid not between", value1, value2, "discountIsValid");
            return (Criteria) this;
        }

        public Criteria andSetNameIsNull() {
            addCriterion("set_name is null");
            return (Criteria) this;
        }

        public Criteria andSetNameIsNotNull() {
            addCriterion("set_name is not null");
            return (Criteria) this;
        }

        public Criteria andSetNameEqualTo(String value) {
            addCriterion("set_name =", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotEqualTo(String value) {
            addCriterion("set_name <>", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameGreaterThan(String value) {
            addCriterion("set_name >", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameGreaterThanOrEqualTo(String value) {
            addCriterion("set_name >=", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameLessThan(String value) {
            addCriterion("set_name <", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameLessThanOrEqualTo(String value) {
            addCriterion("set_name <=", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameLike(String value) {
            addCriterion("set_name like", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotLike(String value) {
            addCriterion("set_name not like", value, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameIn(List<String> values) {
            addCriterion("set_name in", values, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotIn(List<String> values) {
            addCriterion("set_name not in", values, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameBetween(String value1, String value2) {
            addCriterion("set_name between", value1, value2, "setName");
            return (Criteria) this;
        }

        public Criteria andSetNameNotBetween(String value1, String value2) {
            addCriterion("set_name not between", value1, value2, "setName");
            return (Criteria) this;
        }

        public Criteria andSetIdIsNull() {
            addCriterion("set_id is null");
            return (Criteria) this;
        }

        public Criteria andSetIdIsNotNull() {
            addCriterion("set_id is not null");
            return (Criteria) this;
        }

        public Criteria andSetIdEqualTo(Long value) {
            addCriterion("set_id =", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdNotEqualTo(Long value) {
            addCriterion("set_id <>", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdGreaterThan(Long value) {
            addCriterion("set_id >", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("set_id >=", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdLessThan(Long value) {
            addCriterion("set_id <", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdLessThanOrEqualTo(Long value) {
            addCriterion("set_id <=", value, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdIn(List<Long> values) {
            addCriterion("set_id in", values, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdNotIn(List<Long> values) {
            addCriterion("set_id not in", values, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdBetween(Long value1, Long value2) {
            addCriterion("set_id between", value1, value2, "setId");
            return (Criteria) this;
        }

        public Criteria andSetIdNotBetween(Long value1, Long value2) {
            addCriterion("set_id not between", value1, value2, "setId");
            return (Criteria) this;
        }

        public Criteria andIsInCnIsNull() {
            addCriterion("is_in_cn is null");
            return (Criteria) this;
        }

        public Criteria andIsInCnIsNotNull() {
            addCriterion("is_in_cn is not null");
            return (Criteria) this;
        }

        public Criteria andIsInCnEqualTo(Byte value) {
            addCriterion("is_in_cn =", value, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnNotEqualTo(Byte value) {
            addCriterion("is_in_cn <>", value, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnGreaterThan(Byte value) {
            addCriterion("is_in_cn >", value, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_in_cn >=", value, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnLessThan(Byte value) {
            addCriterion("is_in_cn <", value, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnLessThanOrEqualTo(Byte value) {
            addCriterion("is_in_cn <=", value, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnIn(List<Byte> values) {
            addCriterion("is_in_cn in", values, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnNotIn(List<Byte> values) {
            addCriterion("is_in_cn not in", values, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnBetween(Byte value1, Byte value2) {
            addCriterion("is_in_cn between", value1, value2, "isInCn");
            return (Criteria) this;
        }

        public Criteria andIsInCnNotBetween(Byte value1, Byte value2) {
            addCriterion("is_in_cn not between", value1, value2, "isInCn");
            return (Criteria) this;
        }

        public Criteria andBibleVersionIsNull() {
            addCriterion("bible_version is null");
            return (Criteria) this;
        }

        public Criteria andBibleVersionIsNotNull() {
            addCriterion("bible_version is not null");
            return (Criteria) this;
        }

        public Criteria andBibleVersionEqualTo(Integer value) {
            addCriterion("bible_version =", value, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionNotEqualTo(Integer value) {
            addCriterion("bible_version <>", value, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionGreaterThan(Integer value) {
            addCriterion("bible_version >", value, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("bible_version >=", value, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionLessThan(Integer value) {
            addCriterion("bible_version <", value, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionLessThanOrEqualTo(Integer value) {
            addCriterion("bible_version <=", value, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionIn(List<Integer> values) {
            addCriterion("bible_version in", values, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionNotIn(List<Integer> values) {
            addCriterion("bible_version not in", values, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionBetween(Integer value1, Integer value2) {
            addCriterion("bible_version between", value1, value2, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andBibleVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("bible_version not between", value1, value2, "bibleVersion");
            return (Criteria) this;
        }

        public Criteria andCanMemberIsNull() {
            addCriterion("can_member is null");
            return (Criteria) this;
        }

        public Criteria andCanMemberIsNotNull() {
            addCriterion("can_member is not null");
            return (Criteria) this;
        }

        public Criteria andCanMemberEqualTo(Integer value) {
            addCriterion("can_member =", value, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberNotEqualTo(Integer value) {
            addCriterion("can_member <>", value, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberGreaterThan(Integer value) {
            addCriterion("can_member >", value, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberGreaterThanOrEqualTo(Integer value) {
            addCriterion("can_member >=", value, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberLessThan(Integer value) {
            addCriterion("can_member <", value, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberLessThanOrEqualTo(Integer value) {
            addCriterion("can_member <=", value, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberIn(List<Integer> values) {
            addCriterion("can_member in", values, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberNotIn(List<Integer> values) {
            addCriterion("can_member not in", values, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberBetween(Integer value1, Integer value2) {
            addCriterion("can_member between", value1, value2, "canMember");
            return (Criteria) this;
        }

        public Criteria andCanMemberNotBetween(Integer value1, Integer value2) {
            addCriterion("can_member not between", value1, value2, "canMember");
            return (Criteria) this;
        }

        public Criteria andShowPublisherIsNull() {
            addCriterion("show_publisher is null");
            return (Criteria) this;
        }

        public Criteria andShowPublisherIsNotNull() {
            addCriterion("show_publisher is not null");
            return (Criteria) this;
        }

        public Criteria andShowPublisherEqualTo(Byte value) {
            addCriterion("show_publisher =", value, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherNotEqualTo(Byte value) {
            addCriterion("show_publisher <>", value, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherGreaterThan(Byte value) {
            addCriterion("show_publisher >", value, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherGreaterThanOrEqualTo(Byte value) {
            addCriterion("show_publisher >=", value, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherLessThan(Byte value) {
            addCriterion("show_publisher <", value, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherLessThanOrEqualTo(Byte value) {
            addCriterion("show_publisher <=", value, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherIn(List<Byte> values) {
            addCriterion("show_publisher in", values, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherNotIn(List<Byte> values) {
            addCriterion("show_publisher not in", values, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherBetween(Byte value1, Byte value2) {
            addCriterion("show_publisher between", value1, value2, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andShowPublisherNotBetween(Byte value1, Byte value2) {
            addCriterion("show_publisher not between", value1, value2, "showPublisher");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIsNull() {
            addCriterion("recommended_order is null");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIsNotNull() {
            addCriterion("recommended_order is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderEqualTo(Integer value) {
            addCriterion("recommended_order =", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotEqualTo(Integer value) {
            addCriterion("recommended_order <>", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderGreaterThan(Integer value) {
            addCriterion("recommended_order >", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("recommended_order >=", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderLessThan(Integer value) {
            addCriterion("recommended_order <", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderLessThanOrEqualTo(Integer value) {
            addCriterion("recommended_order <=", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIn(List<Integer> values) {
            addCriterion("recommended_order in", values, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotIn(List<Integer> values) {
            addCriterion("recommended_order not in", values, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderBetween(Integer value1, Integer value2) {
            addCriterion("recommended_order between", value1, value2, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("recommended_order not between", value1, value2, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionIsNull() {
            addCriterion("recommended_caption is null");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionIsNotNull() {
            addCriterion("recommended_caption is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionEqualTo(String value) {
            addCriterion("recommended_caption =", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotEqualTo(String value) {
            addCriterion("recommended_caption <>", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionGreaterThan(String value) {
            addCriterion("recommended_caption >", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionGreaterThanOrEqualTo(String value) {
            addCriterion("recommended_caption >=", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionLessThan(String value) {
            addCriterion("recommended_caption <", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionLessThanOrEqualTo(String value) {
            addCriterion("recommended_caption <=", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionLike(String value) {
            addCriterion("recommended_caption like", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotLike(String value) {
            addCriterion("recommended_caption not like", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionIn(List<String> values) {
            addCriterion("recommended_caption in", values, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotIn(List<String> values) {
            addCriterion("recommended_caption not in", values, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionBetween(String value1, String value2) {
            addCriterion("recommended_caption between", value1, value2, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotBetween(String value1, String value2) {
            addCriterion("recommended_caption not between", value1, value2, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionIsNull() {
            addCriterion("print_permission is null");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionIsNotNull() {
            addCriterion("print_permission is not null");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionEqualTo(Byte value) {
            addCriterion("print_permission =", value, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionNotEqualTo(Byte value) {
            addCriterion("print_permission <>", value, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionGreaterThan(Byte value) {
            addCriterion("print_permission >", value, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionGreaterThanOrEqualTo(Byte value) {
            addCriterion("print_permission >=", value, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionLessThan(Byte value) {
            addCriterion("print_permission <", value, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionLessThanOrEqualTo(Byte value) {
            addCriterion("print_permission <=", value, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionIn(List<Byte> values) {
            addCriterion("print_permission in", values, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionNotIn(List<Byte> values) {
            addCriterion("print_permission not in", values, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionBetween(Byte value1, Byte value2) {
            addCriterion("print_permission between", value1, value2, "printPermission");
            return (Criteria) this;
        }

        public Criteria andPrintPermissionNotBetween(Byte value1, Byte value2) {
            addCriterion("print_permission not between", value1, value2, "printPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionIsNull() {
            addCriterion("copy_permission is null");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionIsNotNull() {
            addCriterion("copy_permission is not null");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionEqualTo(Byte value) {
            addCriterion("copy_permission =", value, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionNotEqualTo(Byte value) {
            addCriterion("copy_permission <>", value, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionGreaterThan(Byte value) {
            addCriterion("copy_permission >", value, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionGreaterThanOrEqualTo(Byte value) {
            addCriterion("copy_permission >=", value, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionLessThan(Byte value) {
            addCriterion("copy_permission <", value, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionLessThanOrEqualTo(Byte value) {
            addCriterion("copy_permission <=", value, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionIn(List<Byte> values) {
            addCriterion("copy_permission in", values, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionNotIn(List<Byte> values) {
            addCriterion("copy_permission not in", values, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionBetween(Byte value1, Byte value2) {
            addCriterion("copy_permission between", value1, value2, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andCopyPermissionNotBetween(Byte value1, Byte value2) {
            addCriterion("copy_permission not between", value1, value2, "copyPermission");
            return (Criteria) this;
        }

        public Criteria andEbookFormatIsNull() {
            addCriterion("ebook_format is null");
            return (Criteria) this;
        }

        public Criteria andEbookFormatIsNotNull() {
            addCriterion("ebook_format is not null");
            return (Criteria) this;
        }

        public Criteria andEbookFormatEqualTo(Byte value) {
            addCriterion("ebook_format =", value, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatNotEqualTo(Byte value) {
            addCriterion("ebook_format <>", value, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatGreaterThan(Byte value) {
            addCriterion("ebook_format >", value, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatGreaterThanOrEqualTo(Byte value) {
            addCriterion("ebook_format >=", value, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatLessThan(Byte value) {
            addCriterion("ebook_format <", value, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatLessThanOrEqualTo(Byte value) {
            addCriterion("ebook_format <=", value, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatIn(List<Byte> values) {
            addCriterion("ebook_format in", values, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatNotIn(List<Byte> values) {
            addCriterion("ebook_format not in", values, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatBetween(Byte value1, Byte value2) {
            addCriterion("ebook_format between", value1, value2, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andEbookFormatNotBetween(Byte value1, Byte value2) {
            addCriterion("ebook_format not between", value1, value2, "ebookFormat");
            return (Criteria) this;
        }

        public Criteria andShelfStatusIsNull() {
            addCriterion("shelf_status is null");
            return (Criteria) this;
        }

        public Criteria andShelfStatusIsNotNull() {
            addCriterion("shelf_status is not null");
            return (Criteria) this;
        }

        public Criteria andShelfStatusEqualTo(Byte value) {
            addCriterion("shelf_status =", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusNotEqualTo(Byte value) {
            addCriterion("shelf_status <>", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusGreaterThan(Byte value) {
            addCriterion("shelf_status >", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("shelf_status >=", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusLessThan(Byte value) {
            addCriterion("shelf_status <", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusLessThanOrEqualTo(Byte value) {
            addCriterion("shelf_status <=", value, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusIn(List<Byte> values) {
            addCriterion("shelf_status in", values, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusNotIn(List<Byte> values) {
            addCriterion("shelf_status not in", values, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusBetween(Byte value1, Byte value2) {
            addCriterion("shelf_status between", value1, value2, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andShelfStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("shelf_status not between", value1, value2, "shelfStatus");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIsNull() {
            addCriterion("vendor_percent is null");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIsNotNull() {
            addCriterion("vendor_percent is not null");
            return (Criteria) this;
        }

        public Criteria andVendorPercentEqualTo(Integer value) {
            addCriterion("vendor_percent =", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotEqualTo(Integer value) {
            addCriterion("vendor_percent <>", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentGreaterThan(Integer value) {
            addCriterion("vendor_percent >", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentGreaterThanOrEqualTo(Integer value) {
            addCriterion("vendor_percent >=", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentLessThan(Integer value) {
            addCriterion("vendor_percent <", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentLessThanOrEqualTo(Integer value) {
            addCriterion("vendor_percent <=", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIn(List<Integer> values) {
            addCriterion("vendor_percent in", values, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotIn(List<Integer> values) {
            addCriterion("vendor_percent not in", values, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentBetween(Integer value1, Integer value2) {
            addCriterion("vendor_percent between", value1, value2, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotBetween(Integer value1, Integer value2) {
            addCriterion("vendor_percent not between", value1, value2, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andSalesModelIsNull() {
            addCriterion("sales_model is null");
            return (Criteria) this;
        }

        public Criteria andSalesModelIsNotNull() {
            addCriterion("sales_model is not null");
            return (Criteria) this;
        }

        public Criteria andSalesModelEqualTo(Integer value) {
            addCriterion("sales_model =", value, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelNotEqualTo(Integer value) {
            addCriterion("sales_model <>", value, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelGreaterThan(Integer value) {
            addCriterion("sales_model >", value, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_model >=", value, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelLessThan(Integer value) {
            addCriterion("sales_model <", value, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelLessThanOrEqualTo(Integer value) {
            addCriterion("sales_model <=", value, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelIn(List<Integer> values) {
            addCriterion("sales_model in", values, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelNotIn(List<Integer> values) {
            addCriterion("sales_model not in", values, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelBetween(Integer value1, Integer value2) {
            addCriterion("sales_model between", value1, value2, "salesModel");
            return (Criteria) this;
        }

        public Criteria andSalesModelNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_model not between", value1, value2, "salesModel");
            return (Criteria) this;
        }

        public Criteria andStarIsNull() {
            addCriterion("star is null");
            return (Criteria) this;
        }

        public Criteria andStarIsNotNull() {
            addCriterion("star is not null");
            return (Criteria) this;
        }

        public Criteria andStarEqualTo(String value) {
            addCriterion("star =", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarNotEqualTo(String value) {
            addCriterion("star <>", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarGreaterThan(String value) {
            addCriterion("star >", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarGreaterThanOrEqualTo(String value) {
            addCriterion("star >=", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarLessThan(String value) {
            addCriterion("star <", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarLessThanOrEqualTo(String value) {
            addCriterion("star <=", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarLike(String value) {
            addCriterion("star like", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarNotLike(String value) {
            addCriterion("star not like", value, "star");
            return (Criteria) this;
        }

        public Criteria andStarIn(List<String> values) {
            addCriterion("star in", values, "star");
            return (Criteria) this;
        }

        public Criteria andStarNotIn(List<String> values) {
            addCriterion("star not in", values, "star");
            return (Criteria) this;
        }

        public Criteria andStarBetween(String value1, String value2) {
            addCriterion("star between", value1, value2, "star");
            return (Criteria) this;
        }

        public Criteria andStarNotBetween(String value1, String value2) {
            addCriterion("star not between", value1, value2, "star");
            return (Criteria) this;
        }

        public Criteria andStarCountIsNull() {
            addCriterion("star_count is null");
            return (Criteria) this;
        }

        public Criteria andStarCountIsNotNull() {
            addCriterion("star_count is not null");
            return (Criteria) this;
        }

        public Criteria andStarCountEqualTo(Integer value) {
            addCriterion("star_count =", value, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountNotEqualTo(Integer value) {
            addCriterion("star_count <>", value, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountGreaterThan(Integer value) {
            addCriterion("star_count >", value, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("star_count >=", value, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountLessThan(Integer value) {
            addCriterion("star_count <", value, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountLessThanOrEqualTo(Integer value) {
            addCriterion("star_count <=", value, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountIn(List<Integer> values) {
            addCriterion("star_count in", values, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountNotIn(List<Integer> values) {
            addCriterion("star_count not in", values, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountBetween(Integer value1, Integer value2) {
            addCriterion("star_count between", value1, value2, "starCount");
            return (Criteria) this;
        }

        public Criteria andStarCountNotBetween(Integer value1, Integer value2) {
            addCriterion("star_count not between", value1, value2, "starCount");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtIsNull() {
            addCriterion("opensale_at is null");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtIsNotNull() {
            addCriterion("opensale_at is not null");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtEqualTo(Date value) {
            addCriterion("opensale_at =", value, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtNotEqualTo(Date value) {
            addCriterion("opensale_at <>", value, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtGreaterThan(Date value) {
            addCriterion("opensale_at >", value, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtGreaterThanOrEqualTo(Date value) {
            addCriterion("opensale_at >=", value, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtLessThan(Date value) {
            addCriterion("opensale_at <", value, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtLessThanOrEqualTo(Date value) {
            addCriterion("opensale_at <=", value, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtIn(List<Date> values) {
            addCriterion("opensale_at in", values, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtNotIn(List<Date> values) {
            addCriterion("opensale_at not in", values, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtBetween(Date value1, Date value2) {
            addCriterion("opensale_at between", value1, value2, "opensaleAt");
            return (Criteria) this;
        }

        public Criteria andOpensaleAtNotBetween(Date value1, Date value2) {
            addCriterion("opensale_at not between", value1, value2, "opensaleAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}