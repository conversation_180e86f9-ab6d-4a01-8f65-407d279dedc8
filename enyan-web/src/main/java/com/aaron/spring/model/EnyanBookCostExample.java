package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EnyanBookCostExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanBookCostExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBookCostIdIsNull() {
            addCriterion("book_cost_id is null");
            return (Criteria) this;
        }

        public Criteria andBookCostIdIsNotNull() {
            addCriterion("book_cost_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookCostIdEqualTo(Long value) {
            addCriterion("book_cost_id =", value, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdNotEqualTo(Long value) {
            addCriterion("book_cost_id <>", value, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdGreaterThan(Long value) {
            addCriterion("book_cost_id >", value, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdGreaterThanOrEqualTo(Long value) {
            addCriterion("book_cost_id >=", value, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdLessThan(Long value) {
            addCriterion("book_cost_id <", value, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdLessThanOrEqualTo(Long value) {
            addCriterion("book_cost_id <=", value, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdIn(List<Long> values) {
            addCriterion("book_cost_id in", values, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdNotIn(List<Long> values) {
            addCriterion("book_cost_id not in", values, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdBetween(Long value1, Long value2) {
            addCriterion("book_cost_id between", value1, value2, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookCostIdNotBetween(Long value1, Long value2) {
            addCriterion("book_cost_id not between", value1, value2, "bookCostId");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNull() {
            addCriterion("book_id is null");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNotNull() {
            addCriterion("book_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookIdEqualTo(Long value) {
            addCriterion("book_id =", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotEqualTo(Long value) {
            addCriterion("book_id <>", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThan(Long value) {
            addCriterion("book_id >", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThanOrEqualTo(Long value) {
            addCriterion("book_id >=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThan(Long value) {
            addCriterion("book_id <", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThanOrEqualTo(Long value) {
            addCriterion("book_id <=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdIn(List<Long> values) {
            addCriterion("book_id in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotIn(List<Long> values) {
            addCriterion("book_id not in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdBetween(Long value1, Long value2) {
            addCriterion("book_id between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotBetween(Long value1, Long value2) {
            addCriterion("book_id not between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookTitleIsNull() {
            addCriterion("book_title is null");
            return (Criteria) this;
        }

        public Criteria andBookTitleIsNotNull() {
            addCriterion("book_title is not null");
            return (Criteria) this;
        }

        public Criteria andBookTitleEqualTo(String value) {
            addCriterion("book_title =", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotEqualTo(String value) {
            addCriterion("book_title <>", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleGreaterThan(String value) {
            addCriterion("book_title >", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleGreaterThanOrEqualTo(String value) {
            addCriterion("book_title >=", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleLessThan(String value) {
            addCriterion("book_title <", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleLessThanOrEqualTo(String value) {
            addCriterion("book_title <=", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleLike(String value) {
            addCriterion("book_title like", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotLike(String value) {
            addCriterion("book_title not like", value, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleIn(List<String> values) {
            addCriterion("book_title in", values, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotIn(List<String> values) {
            addCriterion("book_title not in", values, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleBetween(String value1, String value2) {
            addCriterion("book_title between", value1, value2, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andBookTitleNotBetween(String value1, String value2) {
            addCriterion("book_title not between", value1, value2, "bookTitle");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNull() {
            addCriterion("publisher_id is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNotNull() {
            addCriterion("publisher_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdEqualTo(Long value) {
            addCriterion("publisher_id =", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotEqualTo(Long value) {
            addCriterion("publisher_id <>", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThan(Long value) {
            addCriterion("publisher_id >", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThanOrEqualTo(Long value) {
            addCriterion("publisher_id >=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThan(Long value) {
            addCriterion("publisher_id <", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThanOrEqualTo(Long value) {
            addCriterion("publisher_id <=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIn(List<Long> values) {
            addCriterion("publisher_id in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotIn(List<Long> values) {
            addCriterion("publisher_id not in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdBetween(Long value1, Long value2) {
            addCriterion("publisher_id between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotBetween(Long value1, Long value2) {
            addCriterion("publisher_id not between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andBookCostIsNull() {
            addCriterion("book_cost is null");
            return (Criteria) this;
        }

        public Criteria andBookCostIsNotNull() {
            addCriterion("book_cost is not null");
            return (Criteria) this;
        }

        public Criteria andBookCostEqualTo(Integer value) {
            addCriterion("book_cost =", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostNotEqualTo(Integer value) {
            addCriterion("book_cost <>", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostGreaterThan(Integer value) {
            addCriterion("book_cost >", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostGreaterThanOrEqualTo(Integer value) {
            addCriterion("book_cost >=", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostLessThan(Integer value) {
            addCriterion("book_cost <", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostLessThanOrEqualTo(Integer value) {
            addCriterion("book_cost <=", value, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostIn(List<Integer> values) {
            addCriterion("book_cost in", values, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostNotIn(List<Integer> values) {
            addCriterion("book_cost not in", values, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostBetween(Integer value1, Integer value2) {
            addCriterion("book_cost between", value1, value2, "bookCost");
            return (Criteria) this;
        }

        public Criteria andBookCostNotBetween(Integer value1, Integer value2) {
            addCriterion("book_cost not between", value1, value2, "bookCost");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtIsNull() {
            addCriterion("purchased_at is null");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtIsNotNull() {
            addCriterion("purchased_at is not null");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtEqualTo(Date value) {
            addCriterion("purchased_at =", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtNotEqualTo(Date value) {
            addCriterion("purchased_at <>", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtGreaterThan(Date value) {
            addCriterion("purchased_at >", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("purchased_at >=", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtLessThan(Date value) {
            addCriterion("purchased_at <", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtLessThanOrEqualTo(Date value) {
            addCriterion("purchased_at <=", value, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtIn(List<Date> values) {
            addCriterion("purchased_at in", values, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtNotIn(List<Date> values) {
            addCriterion("purchased_at not in", values, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtBetween(Date value1, Date value2) {
            addCriterion("purchased_at between", value1, value2, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andPurchasedAtNotBetween(Date value1, Date value2) {
            addCriterion("purchased_at not between", value1, value2, "purchasedAt");
            return (Criteria) this;
        }

        public Criteria andIsCountedIsNull() {
            addCriterion("is_counted is null");
            return (Criteria) this;
        }

        public Criteria andIsCountedIsNotNull() {
            addCriterion("is_counted is not null");
            return (Criteria) this;
        }

        public Criteria andIsCountedEqualTo(Byte value) {
            addCriterion("is_counted =", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedNotEqualTo(Byte value) {
            addCriterion("is_counted <>", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedGreaterThan(Byte value) {
            addCriterion("is_counted >", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_counted >=", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedLessThan(Byte value) {
            addCriterion("is_counted <", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedLessThanOrEqualTo(Byte value) {
            addCriterion("is_counted <=", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedIn(List<Byte> values) {
            addCriterion("is_counted in", values, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedNotIn(List<Byte> values) {
            addCriterion("is_counted not in", values, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedBetween(Byte value1, Byte value2) {
            addCriterion("is_counted between", value1, value2, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedNotBetween(Byte value1, Byte value2) {
            addCriterion("is_counted not between", value1, value2, "isCounted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}