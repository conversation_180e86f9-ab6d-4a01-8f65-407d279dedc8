package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.util.Date;

public class EnyanBalanceHistory extends BaseDTO{
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long balanceHistoryId;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long publisherId;

    private BigDecimal incomeVendorTotal;

    private Integer balanceDay;

    private Date balanceAt;

    private Byte isCounted;

    private String balanceDetail;

    public Long getBalanceHistoryId() {
        return balanceHistoryId;
    }

    public void setBalanceHistoryId(Long balanceHistoryId) {
        this.balanceHistoryId = balanceHistoryId;
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public BigDecimal getIncomeVendorTotal() {
        return incomeVendorTotal;
    }

    public void setIncomeVendorTotal(BigDecimal incomeVendorTotal) {
        this.incomeVendorTotal = incomeVendorTotal;
    }

    public Integer getBalanceDay() {
        return balanceDay;
    }

    public void setBalanceDay(Integer balanceDay) {
        this.balanceDay = balanceDay;
    }

    public Date getBalanceAt() {
        return balanceAt;
    }

    public void setBalanceAt(Date balanceAt) {
        this.balanceAt = balanceAt;
    }

    public Byte getIsCounted() {
        return isCounted;
    }

    public void setIsCounted(Byte isCounted) {
        this.isCounted = isCounted;
    }

    public String getBalanceDetail() {
        return balanceDetail;
    }

    public void setBalanceDetail(String balanceDetail) {
        this.balanceDetail = balanceDetail == null ? null : balanceDetail.trim();
    }
}