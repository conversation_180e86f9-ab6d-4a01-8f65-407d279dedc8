package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class EnyanPublisherExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanPublisherExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andPublisherIdIsNull() {
            addCriterion("publisher_id is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNotNull() {
            addCriterion("publisher_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdEqualTo(Long value) {
            addCriterion("publisher_id =", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotEqualTo(Long value) {
            addCriterion("publisher_id <>", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThan(Long value) {
            addCriterion("publisher_id >", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThanOrEqualTo(Long value) {
            addCriterion("publisher_id >=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThan(Long value) {
            addCriterion("publisher_id <", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThanOrEqualTo(Long value) {
            addCriterion("publisher_id <=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIn(List<Long> values) {
            addCriterion("publisher_id in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotIn(List<Long> values) {
            addCriterion("publisher_id not in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdBetween(Long value1, Long value2) {
            addCriterion("publisher_id between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotBetween(Long value1, Long value2) {
            addCriterion("publisher_id not between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherNameIsNull() {
            addCriterion("publisher_name is null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameIsNotNull() {
            addCriterion("publisher_name is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEqualTo(String value) {
            addCriterion("publisher_name =", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotEqualTo(String value) {
            addCriterion("publisher_name <>", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameGreaterThan(String value) {
            addCriterion("publisher_name >", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameGreaterThanOrEqualTo(String value) {
            addCriterion("publisher_name >=", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameLessThan(String value) {
            addCriterion("publisher_name <", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameLessThanOrEqualTo(String value) {
            addCriterion("publisher_name <=", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameLike(String value) {
            addCriterion("publisher_name like", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotLike(String value) {
            addCriterion("publisher_name not like", value, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameIn(List<String> values) {
            addCriterion("publisher_name in", values, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotIn(List<String> values) {
            addCriterion("publisher_name not in", values, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameBetween(String value1, String value2) {
            addCriterion("publisher_name between", value1, value2, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameNotBetween(String value1, String value2) {
            addCriterion("publisher_name not between", value1, value2, "publisherName");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcIsNull() {
            addCriterion("publisher_name_tc is null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcIsNotNull() {
            addCriterion("publisher_name_tc is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcEqualTo(String value) {
            addCriterion("publisher_name_tc =", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcNotEqualTo(String value) {
            addCriterion("publisher_name_tc <>", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcGreaterThan(String value) {
            addCriterion("publisher_name_tc >", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcGreaterThanOrEqualTo(String value) {
            addCriterion("publisher_name_tc >=", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcLessThan(String value) {
            addCriterion("publisher_name_tc <", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcLessThanOrEqualTo(String value) {
            addCriterion("publisher_name_tc <=", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcLike(String value) {
            addCriterion("publisher_name_tc like", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcNotLike(String value) {
            addCriterion("publisher_name_tc not like", value, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcIn(List<String> values) {
            addCriterion("publisher_name_tc in", values, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcNotIn(List<String> values) {
            addCriterion("publisher_name_tc not in", values, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcBetween(String value1, String value2) {
            addCriterion("publisher_name_tc between", value1, value2, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameTcNotBetween(String value1, String value2) {
            addCriterion("publisher_name_tc not between", value1, value2, "publisherNameTc");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnIsNull() {
            addCriterion("publisher_name_en is null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnIsNotNull() {
            addCriterion("publisher_name_en is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnEqualTo(String value) {
            addCriterion("publisher_name_en =", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnNotEqualTo(String value) {
            addCriterion("publisher_name_en <>", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnGreaterThan(String value) {
            addCriterion("publisher_name_en >", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnGreaterThanOrEqualTo(String value) {
            addCriterion("publisher_name_en >=", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnLessThan(String value) {
            addCriterion("publisher_name_en <", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnLessThanOrEqualTo(String value) {
            addCriterion("publisher_name_en <=", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnLike(String value) {
            addCriterion("publisher_name_en like", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnNotLike(String value) {
            addCriterion("publisher_name_en not like", value, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnIn(List<String> values) {
            addCriterion("publisher_name_en in", values, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnNotIn(List<String> values) {
            addCriterion("publisher_name_en not in", values, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnBetween(String value1, String value2) {
            addCriterion("publisher_name_en between", value1, value2, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherNameEnNotBetween(String value1, String value2) {
            addCriterion("publisher_name_en not between", value1, value2, "publisherNameEn");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarIsNull() {
            addCriterion("publisher_avatar is null");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarIsNotNull() {
            addCriterion("publisher_avatar is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarEqualTo(String value) {
            addCriterion("publisher_avatar =", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarNotEqualTo(String value) {
            addCriterion("publisher_avatar <>", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarGreaterThan(String value) {
            addCriterion("publisher_avatar >", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarGreaterThanOrEqualTo(String value) {
            addCriterion("publisher_avatar >=", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarLessThan(String value) {
            addCriterion("publisher_avatar <", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarLessThanOrEqualTo(String value) {
            addCriterion("publisher_avatar <=", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarLike(String value) {
            addCriterion("publisher_avatar like", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarNotLike(String value) {
            addCriterion("publisher_avatar not like", value, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarIn(List<String> values) {
            addCriterion("publisher_avatar in", values, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarNotIn(List<String> values) {
            addCriterion("publisher_avatar not in", values, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarBetween(String value1, String value2) {
            addCriterion("publisher_avatar between", value1, value2, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andPublisherAvatarNotBetween(String value1, String value2) {
            addCriterion("publisher_avatar not between", value1, value2, "publisherAvatar");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterionForJDBCDate("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterionForJDBCDate("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterionForJDBCDate("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIsNull() {
            addCriterion("vendor_percent is null");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIsNotNull() {
            addCriterion("vendor_percent is not null");
            return (Criteria) this;
        }

        public Criteria andVendorPercentEqualTo(Integer value) {
            addCriterion("vendor_percent =", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotEqualTo(Integer value) {
            addCriterion("vendor_percent <>", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentGreaterThan(Integer value) {
            addCriterion("vendor_percent >", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentGreaterThanOrEqualTo(Integer value) {
            addCriterion("vendor_percent >=", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentLessThan(Integer value) {
            addCriterion("vendor_percent <", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentLessThanOrEqualTo(Integer value) {
            addCriterion("vendor_percent <=", value, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentIn(List<Integer> values) {
            addCriterion("vendor_percent in", values, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotIn(List<Integer> values) {
            addCriterion("vendor_percent not in", values, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentBetween(Integer value1, Integer value2) {
            addCriterion("vendor_percent between", value1, value2, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andVendorPercentNotBetween(Integer value1, Integer value2) {
            addCriterion("vendor_percent not between", value1, value2, "vendorPercent");
            return (Criteria) this;
        }

        public Criteria andBankNationalIsNull() {
            addCriterion("bank_national is null");
            return (Criteria) this;
        }

        public Criteria andBankNationalIsNotNull() {
            addCriterion("bank_national is not null");
            return (Criteria) this;
        }

        public Criteria andBankNationalEqualTo(Integer value) {
            addCriterion("bank_national =", value, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalNotEqualTo(Integer value) {
            addCriterion("bank_national <>", value, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalGreaterThan(Integer value) {
            addCriterion("bank_national >", value, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalGreaterThanOrEqualTo(Integer value) {
            addCriterion("bank_national >=", value, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalLessThan(Integer value) {
            addCriterion("bank_national <", value, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalLessThanOrEqualTo(Integer value) {
            addCriterion("bank_national <=", value, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalIn(List<Integer> values) {
            addCriterion("bank_national in", values, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalNotIn(List<Integer> values) {
            addCriterion("bank_national not in", values, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalBetween(Integer value1, Integer value2) {
            addCriterion("bank_national between", value1, value2, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNationalNotBetween(Integer value1, Integer value2) {
            addCriterion("bank_national not between", value1, value2, "bankNational");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNull() {
            addCriterion("bank_name is null");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNotNull() {
            addCriterion("bank_name is not null");
            return (Criteria) this;
        }

        public Criteria andBankNameEqualTo(String value) {
            addCriterion("bank_name =", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotEqualTo(String value) {
            addCriterion("bank_name <>", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThan(String value) {
            addCriterion("bank_name >", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThanOrEqualTo(String value) {
            addCriterion("bank_name >=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThan(String value) {
            addCriterion("bank_name <", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThanOrEqualTo(String value) {
            addCriterion("bank_name <=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLike(String value) {
            addCriterion("bank_name like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotLike(String value) {
            addCriterion("bank_name not like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameIn(List<String> values) {
            addCriterion("bank_name in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotIn(List<String> values) {
            addCriterion("bank_name not in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameBetween(String value1, String value2) {
            addCriterion("bank_name between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotBetween(String value1, String value2) {
            addCriterion("bank_name not between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankAddressIsNull() {
            addCriterion("bank_address is null");
            return (Criteria) this;
        }

        public Criteria andBankAddressIsNotNull() {
            addCriterion("bank_address is not null");
            return (Criteria) this;
        }

        public Criteria andBankAddressEqualTo(String value) {
            addCriterion("bank_address =", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressNotEqualTo(String value) {
            addCriterion("bank_address <>", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressGreaterThan(String value) {
            addCriterion("bank_address >", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressGreaterThanOrEqualTo(String value) {
            addCriterion("bank_address >=", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressLessThan(String value) {
            addCriterion("bank_address <", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressLessThanOrEqualTo(String value) {
            addCriterion("bank_address <=", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressLike(String value) {
            addCriterion("bank_address like", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressNotLike(String value) {
            addCriterion("bank_address not like", value, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressIn(List<String> values) {
            addCriterion("bank_address in", values, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressNotIn(List<String> values) {
            addCriterion("bank_address not in", values, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressBetween(String value1, String value2) {
            addCriterion("bank_address between", value1, value2, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankAddressNotBetween(String value1, String value2) {
            addCriterion("bank_address not between", value1, value2, "bankAddress");
            return (Criteria) this;
        }

        public Criteria andBankCodeIsNull() {
            addCriterion("bank_code is null");
            return (Criteria) this;
        }

        public Criteria andBankCodeIsNotNull() {
            addCriterion("bank_code is not null");
            return (Criteria) this;
        }

        public Criteria andBankCodeEqualTo(String value) {
            addCriterion("bank_code =", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotEqualTo(String value) {
            addCriterion("bank_code <>", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeGreaterThan(String value) {
            addCriterion("bank_code >", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bank_code >=", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeLessThan(String value) {
            addCriterion("bank_code <", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeLessThanOrEqualTo(String value) {
            addCriterion("bank_code <=", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeLike(String value) {
            addCriterion("bank_code like", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotLike(String value) {
            addCriterion("bank_code not like", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeIn(List<String> values) {
            addCriterion("bank_code in", values, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotIn(List<String> values) {
            addCriterion("bank_code not in", values, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeBetween(String value1, String value2) {
            addCriterion("bank_code between", value1, value2, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotBetween(String value1, String value2) {
            addCriterion("bank_code not between", value1, value2, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankTitleIsNull() {
            addCriterion("bank_title is null");
            return (Criteria) this;
        }

        public Criteria andBankTitleIsNotNull() {
            addCriterion("bank_title is not null");
            return (Criteria) this;
        }

        public Criteria andBankTitleEqualTo(String value) {
            addCriterion("bank_title =", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleNotEqualTo(String value) {
            addCriterion("bank_title <>", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleGreaterThan(String value) {
            addCriterion("bank_title >", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleGreaterThanOrEqualTo(String value) {
            addCriterion("bank_title >=", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleLessThan(String value) {
            addCriterion("bank_title <", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleLessThanOrEqualTo(String value) {
            addCriterion("bank_title <=", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleLike(String value) {
            addCriterion("bank_title like", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleNotLike(String value) {
            addCriterion("bank_title not like", value, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleIn(List<String> values) {
            addCriterion("bank_title in", values, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleNotIn(List<String> values) {
            addCriterion("bank_title not in", values, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleBetween(String value1, String value2) {
            addCriterion("bank_title between", value1, value2, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankTitleNotBetween(String value1, String value2) {
            addCriterion("bank_title not between", value1, value2, "bankTitle");
            return (Criteria) this;
        }

        public Criteria andBankNumIsNull() {
            addCriterion("bank_num is null");
            return (Criteria) this;
        }

        public Criteria andBankNumIsNotNull() {
            addCriterion("bank_num is not null");
            return (Criteria) this;
        }

        public Criteria andBankNumEqualTo(String value) {
            addCriterion("bank_num =", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumNotEqualTo(String value) {
            addCriterion("bank_num <>", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumGreaterThan(String value) {
            addCriterion("bank_num >", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumGreaterThanOrEqualTo(String value) {
            addCriterion("bank_num >=", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumLessThan(String value) {
            addCriterion("bank_num <", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumLessThanOrEqualTo(String value) {
            addCriterion("bank_num <=", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumLike(String value) {
            addCriterion("bank_num like", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumNotLike(String value) {
            addCriterion("bank_num not like", value, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumIn(List<String> values) {
            addCriterion("bank_num in", values, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumNotIn(List<String> values) {
            addCriterion("bank_num not in", values, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumBetween(String value1, String value2) {
            addCriterion("bank_num between", value1, value2, "bankNum");
            return (Criteria) this;
        }

        public Criteria andBankNumNotBetween(String value1, String value2) {
            addCriterion("bank_num not between", value1, value2, "bankNum");
            return (Criteria) this;
        }

        public Criteria andFundTotalIsNull() {
            addCriterion("fund_total is null");
            return (Criteria) this;
        }

        public Criteria andFundTotalIsNotNull() {
            addCriterion("fund_total is not null");
            return (Criteria) this;
        }

        public Criteria andFundTotalEqualTo(BigDecimal value) {
            addCriterion("fund_total =", value, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalNotEqualTo(BigDecimal value) {
            addCriterion("fund_total <>", value, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalGreaterThan(BigDecimal value) {
            addCriterion("fund_total >", value, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fund_total >=", value, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalLessThan(BigDecimal value) {
            addCriterion("fund_total <", value, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fund_total <=", value, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalIn(List<BigDecimal> values) {
            addCriterion("fund_total in", values, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalNotIn(List<BigDecimal> values) {
            addCriterion("fund_total not in", values, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fund_total between", value1, value2, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fund_total not between", value1, value2, "fundTotal");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnIsNull() {
            addCriterion("fund_withdrawn is null");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnIsNotNull() {
            addCriterion("fund_withdrawn is not null");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnEqualTo(BigDecimal value) {
            addCriterion("fund_withdrawn =", value, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnNotEqualTo(BigDecimal value) {
            addCriterion("fund_withdrawn <>", value, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnGreaterThan(BigDecimal value) {
            addCriterion("fund_withdrawn >", value, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fund_withdrawn >=", value, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnLessThan(BigDecimal value) {
            addCriterion("fund_withdrawn <", value, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fund_withdrawn <=", value, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnIn(List<BigDecimal> values) {
            addCriterion("fund_withdrawn in", values, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnNotIn(List<BigDecimal> values) {
            addCriterion("fund_withdrawn not in", values, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fund_withdrawn between", value1, value2, "fundWithdrawn");
            return (Criteria) this;
        }

        public Criteria andFundWithdrawnNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fund_withdrawn not between", value1, value2, "fundWithdrawn");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}