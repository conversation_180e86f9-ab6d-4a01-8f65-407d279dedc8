package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.List;

public class EnyanUserBookmarksExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanUserBookmarksExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBookmarkIdIsNull() {
            addCriterion("bookmark_id is null");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdIsNotNull() {
            addCriterion("bookmark_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdEqualTo(String value) {
            addCriterion("bookmark_id =", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdNotEqualTo(String value) {
            addCriterion("bookmark_id <>", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdGreaterThan(String value) {
            addCriterion("bookmark_id >", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdGreaterThanOrEqualTo(String value) {
            addCriterion("bookmark_id >=", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdLessThan(String value) {
            addCriterion("bookmark_id <", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdLessThanOrEqualTo(String value) {
            addCriterion("bookmark_id <=", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdLike(String value) {
            addCriterion("bookmark_id like", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdNotLike(String value) {
            addCriterion("bookmark_id not like", value, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdIn(List<String> values) {
            addCriterion("bookmark_id in", values, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdNotIn(List<String> values) {
            addCriterion("bookmark_id not in", values, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdBetween(String value1, String value2) {
            addCriterion("bookmark_id between", value1, value2, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andBookmarkIdNotBetween(String value1, String value2) {
            addCriterion("bookmark_id not between", value1, value2, "bookmarkId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNull() {
            addCriterion("user_email is null");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNotNull() {
            addCriterion("user_email is not null");
            return (Criteria) this;
        }

        public Criteria andUserEmailEqualTo(String value) {
            addCriterion("user_email =", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotEqualTo(String value) {
            addCriterion("user_email <>", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThan(String value) {
            addCriterion("user_email >", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("user_email >=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThan(String value) {
            addCriterion("user_email <", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThanOrEqualTo(String value) {
            addCriterion("user_email <=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLike(String value) {
            addCriterion("user_email like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotLike(String value) {
            addCriterion("user_email not like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailIn(List<String> values) {
            addCriterion("user_email in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotIn(List<String> values) {
            addCriterion("user_email not in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailBetween(String value1, String value2) {
            addCriterion("user_email between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotBetween(String value1, String value2) {
            addCriterion("user_email not between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNull() {
            addCriterion("book_id is null");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNotNull() {
            addCriterion("book_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookIdEqualTo(Long value) {
            addCriterion("book_id =", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotEqualTo(Long value) {
            addCriterion("book_id <>", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThan(Long value) {
            addCriterion("book_id >", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThanOrEqualTo(Long value) {
            addCriterion("book_id >=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThan(Long value) {
            addCriterion("book_id <", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThanOrEqualTo(Long value) {
            addCriterion("book_id <=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdIn(List<Long> values) {
            addCriterion("book_id in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotIn(List<Long> values) {
            addCriterion("book_id not in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdBetween(Long value1, Long value2) {
            addCriterion("book_id between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotBetween(Long value1, Long value2) {
            addCriterion("book_id not between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andChapterNameIsNull() {
            addCriterion("chapter_name is null");
            return (Criteria) this;
        }

        public Criteria andChapterNameIsNotNull() {
            addCriterion("chapter_name is not null");
            return (Criteria) this;
        }

        public Criteria andChapterNameEqualTo(String value) {
            addCriterion("chapter_name =", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameNotEqualTo(String value) {
            addCriterion("chapter_name <>", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameGreaterThan(String value) {
            addCriterion("chapter_name >", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameGreaterThanOrEqualTo(String value) {
            addCriterion("chapter_name >=", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameLessThan(String value) {
            addCriterion("chapter_name <", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameLessThanOrEqualTo(String value) {
            addCriterion("chapter_name <=", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameLike(String value) {
            addCriterion("chapter_name like", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameNotLike(String value) {
            addCriterion("chapter_name not like", value, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameIn(List<String> values) {
            addCriterion("chapter_name in", values, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameNotIn(List<String> values) {
            addCriterion("chapter_name not in", values, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameBetween(String value1, String value2) {
            addCriterion("chapter_name between", value1, value2, "chapterName");
            return (Criteria) this;
        }

        public Criteria andChapterNameNotBetween(String value1, String value2) {
            addCriterion("chapter_name not between", value1, value2, "chapterName");
            return (Criteria) this;
        }

        public Criteria andPageChapterIsNull() {
            addCriterion("page_chapter is null");
            return (Criteria) this;
        }

        public Criteria andPageChapterIsNotNull() {
            addCriterion("page_chapter is not null");
            return (Criteria) this;
        }

        public Criteria andPageChapterEqualTo(Integer value) {
            addCriterion("page_chapter =", value, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterNotEqualTo(Integer value) {
            addCriterion("page_chapter <>", value, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterGreaterThan(Integer value) {
            addCriterion("page_chapter >", value, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterGreaterThanOrEqualTo(Integer value) {
            addCriterion("page_chapter >=", value, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterLessThan(Integer value) {
            addCriterion("page_chapter <", value, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterLessThanOrEqualTo(Integer value) {
            addCriterion("page_chapter <=", value, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterIn(List<Integer> values) {
            addCriterion("page_chapter in", values, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterNotIn(List<Integer> values) {
            addCriterion("page_chapter not in", values, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterBetween(Integer value1, Integer value2) {
            addCriterion("page_chapter between", value1, value2, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andPageChapterNotBetween(Integer value1, Integer value2) {
            addCriterion("page_chapter not between", value1, value2, "pageChapter");
            return (Criteria) this;
        }

        public Criteria andCurrentPageIsNull() {
            addCriterion("current_page is null");
            return (Criteria) this;
        }

        public Criteria andCurrentPageIsNotNull() {
            addCriterion("current_page is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentPageEqualTo(Integer value) {
            addCriterion("current_page =", value, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageNotEqualTo(Integer value) {
            addCriterion("current_page <>", value, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageGreaterThan(Integer value) {
            addCriterion("current_page >", value, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageGreaterThanOrEqualTo(Integer value) {
            addCriterion("current_page >=", value, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageLessThan(Integer value) {
            addCriterion("current_page <", value, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageLessThanOrEqualTo(Integer value) {
            addCriterion("current_page <=", value, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageIn(List<Integer> values) {
            addCriterion("current_page in", values, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageNotIn(List<Integer> values) {
            addCriterion("current_page not in", values, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageBetween(Integer value1, Integer value2) {
            addCriterion("current_page between", value1, value2, "currentPage");
            return (Criteria) this;
        }

        public Criteria andCurrentPageNotBetween(Integer value1, Integer value2) {
            addCriterion("current_page not between", value1, value2, "currentPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageIsNull() {
            addCriterion("total_page is null");
            return (Criteria) this;
        }

        public Criteria andTotalPageIsNotNull() {
            addCriterion("total_page is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPageEqualTo(Integer value) {
            addCriterion("total_page =", value, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageNotEqualTo(Integer value) {
            addCriterion("total_page <>", value, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageGreaterThan(Integer value) {
            addCriterion("total_page >", value, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_page >=", value, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageLessThan(Integer value) {
            addCriterion("total_page <", value, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageLessThanOrEqualTo(Integer value) {
            addCriterion("total_page <=", value, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageIn(List<Integer> values) {
            addCriterion("total_page in", values, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageNotIn(List<Integer> values) {
            addCriterion("total_page not in", values, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageBetween(Integer value1, Integer value2) {
            addCriterion("total_page between", value1, value2, "totalPage");
            return (Criteria) this;
        }

        public Criteria andTotalPageNotBetween(Integer value1, Integer value2) {
            addCriterion("total_page not between", value1, value2, "totalPage");
            return (Criteria) this;
        }

        public Criteria andPageProcessIsNull() {
            addCriterion("page_process is null");
            return (Criteria) this;
        }

        public Criteria andPageProcessIsNotNull() {
            addCriterion("page_process is not null");
            return (Criteria) this;
        }

        public Criteria andPageProcessEqualTo(Integer value) {
            addCriterion("page_process =", value, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessNotEqualTo(Integer value) {
            addCriterion("page_process <>", value, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessGreaterThan(Integer value) {
            addCriterion("page_process >", value, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessGreaterThanOrEqualTo(Integer value) {
            addCriterion("page_process >=", value, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessLessThan(Integer value) {
            addCriterion("page_process <", value, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessLessThanOrEqualTo(Integer value) {
            addCriterion("page_process <=", value, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessIn(List<Integer> values) {
            addCriterion("page_process in", values, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessNotIn(List<Integer> values) {
            addCriterion("page_process not in", values, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessBetween(Integer value1, Integer value2) {
            addCriterion("page_process between", value1, value2, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andPageProcessNotBetween(Integer value1, Integer value2) {
            addCriterion("page_process not between", value1, value2, "pageProcess");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}