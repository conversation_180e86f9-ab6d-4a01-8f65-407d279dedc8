package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DataStatExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public DataStatExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDataIdIsNull() {
            addCriterion("data_id is null");
            return (Criteria) this;
        }

        public Criteria andDataIdIsNotNull() {
            addCriterion("data_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataIdEqualTo(Long value) {
            addCriterion("data_id =", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotEqualTo(Long value) {
            addCriterion("data_id <>", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThan(Long value) {
            addCriterion("data_id >", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_id >=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThan(Long value) {
            addCriterion("data_id <", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThanOrEqualTo(Long value) {
            addCriterion("data_id <=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdIn(List<Long> values) {
            addCriterion("data_id in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotIn(List<Long> values) {
            addCriterion("data_id not in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdBetween(Long value1, Long value2) {
            addCriterion("data_id between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotBetween(Long value1, Long value2) {
            addCriterion("data_id not between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeIsNull() {
            addCriterion("sales_volume is null");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeIsNotNull() {
            addCriterion("sales_volume is not null");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeEqualTo(Integer value) {
            addCriterion("sales_volume =", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeNotEqualTo(Integer value) {
            addCriterion("sales_volume <>", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeGreaterThan(Integer value) {
            addCriterion("sales_volume >", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_volume >=", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeLessThan(Integer value) {
            addCriterion("sales_volume <", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeLessThanOrEqualTo(Integer value) {
            addCriterion("sales_volume <=", value, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeIn(List<Integer> values) {
            addCriterion("sales_volume in", values, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeNotIn(List<Integer> values) {
            addCriterion("sales_volume not in", values, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeBetween(Integer value1, Integer value2) {
            addCriterion("sales_volume between", value1, value2, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andSalesVolumeNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_volume not between", value1, value2, "salesVolume");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalIsNull() {
            addCriterion("income_total is null");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalIsNotNull() {
            addCriterion("income_total is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalEqualTo(BigDecimal value) {
            addCriterion("income_total =", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalNotEqualTo(BigDecimal value) {
            addCriterion("income_total <>", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalGreaterThan(BigDecimal value) {
            addCriterion("income_total >", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_total >=", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalLessThan(BigDecimal value) {
            addCriterion("income_total <", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_total <=", value, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalIn(List<BigDecimal> values) {
            addCriterion("income_total in", values, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalNotIn(List<BigDecimal> values) {
            addCriterion("income_total not in", values, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_total between", value1, value2, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_total not between", value1, value2, "incomeTotal");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNull() {
            addCriterion("order_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNotNull() {
            addCriterion("order_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualTo(Integer value) {
            addCriterion("order_count =", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualTo(Integer value) {
            addCriterion("order_count <>", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThan(Integer value) {
            addCriterion("order_count >", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_count >=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThan(Integer value) {
            addCriterion("order_count <", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_count <=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountIn(List<Integer> values) {
            addCriterion("order_count in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotIn(List<Integer> values) {
            addCriterion("order_count not in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("order_count between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_count not between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountIsNull() {
            addCriterion("order_fee_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountIsNotNull() {
            addCriterion("order_fee_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountEqualTo(Integer value) {
            addCriterion("order_fee_count =", value, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountNotEqualTo(Integer value) {
            addCriterion("order_fee_count <>", value, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountGreaterThan(Integer value) {
            addCriterion("order_fee_count >", value, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_fee_count >=", value, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountLessThan(Integer value) {
            addCriterion("order_fee_count <", value, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_fee_count <=", value, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountIn(List<Integer> values) {
            addCriterion("order_fee_count in", values, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountNotIn(List<Integer> values) {
            addCriterion("order_fee_count not in", values, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountBetween(Integer value1, Integer value2) {
            addCriterion("order_fee_count between", value1, value2, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFeeCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_fee_count not between", value1, value2, "orderFeeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountIsNull() {
            addCriterion("order_free_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountIsNotNull() {
            addCriterion("order_free_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountEqualTo(Integer value) {
            addCriterion("order_free_count =", value, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountNotEqualTo(Integer value) {
            addCriterion("order_free_count <>", value, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountGreaterThan(Integer value) {
            addCriterion("order_free_count >", value, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_free_count >=", value, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountLessThan(Integer value) {
            addCriterion("order_free_count <", value, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_free_count <=", value, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountIn(List<Integer> values) {
            addCriterion("order_free_count in", values, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountNotIn(List<Integer> values) {
            addCriterion("order_free_count not in", values, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountBetween(Integer value1, Integer value2) {
            addCriterion("order_free_count between", value1, value2, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andOrderFreeCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_free_count not between", value1, value2, "orderFreeCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountIsNull() {
            addCriterion("user_buy_count is null");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountIsNotNull() {
            addCriterion("user_buy_count is not null");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountEqualTo(Integer value) {
            addCriterion("user_buy_count =", value, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountNotEqualTo(Integer value) {
            addCriterion("user_buy_count <>", value, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountGreaterThan(Integer value) {
            addCriterion("user_buy_count >", value, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_buy_count >=", value, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountLessThan(Integer value) {
            addCriterion("user_buy_count <", value, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountLessThanOrEqualTo(Integer value) {
            addCriterion("user_buy_count <=", value, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountIn(List<Integer> values) {
            addCriterion("user_buy_count in", values, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountNotIn(List<Integer> values) {
            addCriterion("user_buy_count not in", values, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountBetween(Integer value1, Integer value2) {
            addCriterion("user_buy_count between", value1, value2, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserBuyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("user_buy_count not between", value1, value2, "userBuyCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountIsNull() {
            addCriterion("user_new_count is null");
            return (Criteria) this;
        }

        public Criteria andUserNewCountIsNotNull() {
            addCriterion("user_new_count is not null");
            return (Criteria) this;
        }

        public Criteria andUserNewCountEqualTo(Integer value) {
            addCriterion("user_new_count =", value, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountNotEqualTo(Integer value) {
            addCriterion("user_new_count <>", value, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountGreaterThan(Integer value) {
            addCriterion("user_new_count >", value, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_new_count >=", value, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountLessThan(Integer value) {
            addCriterion("user_new_count <", value, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountLessThanOrEqualTo(Integer value) {
            addCriterion("user_new_count <=", value, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountIn(List<Integer> values) {
            addCriterion("user_new_count in", values, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountNotIn(List<Integer> values) {
            addCriterion("user_new_count not in", values, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountBetween(Integer value1, Integer value2) {
            addCriterion("user_new_count between", value1, value2, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andUserNewCountNotBetween(Integer value1, Integer value2) {
            addCriterion("user_new_count not between", value1, value2, "userNewCount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}