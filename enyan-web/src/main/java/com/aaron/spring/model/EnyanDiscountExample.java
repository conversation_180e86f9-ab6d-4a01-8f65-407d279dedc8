package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class EnyanDiscountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanDiscountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andDiscountIdIsNull() {
            addCriterion("discount_id is null");
            return (Criteria) this;
        }

        public Criteria andDiscountIdIsNotNull() {
            addCriterion("discount_id is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountIdEqualTo(Long value) {
            addCriterion("discount_id =", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdNotEqualTo(Long value) {
            addCriterion("discount_id <>", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdGreaterThan(Long value) {
            addCriterion("discount_id >", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("discount_id >=", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdLessThan(Long value) {
            addCriterion("discount_id <", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdLessThanOrEqualTo(Long value) {
            addCriterion("discount_id <=", value, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdIn(List<Long> values) {
            addCriterion("discount_id in", values, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdNotIn(List<Long> values) {
            addCriterion("discount_id not in", values, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdBetween(Long value1, Long value2) {
            addCriterion("discount_id between", value1, value2, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountIdNotBetween(Long value1, Long value2) {
            addCriterion("discount_id not between", value1, value2, "discountId");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleIsNull() {
            addCriterion("discount_title is null");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleIsNotNull() {
            addCriterion("discount_title is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleEqualTo(String value) {
            addCriterion("discount_title =", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleNotEqualTo(String value) {
            addCriterion("discount_title <>", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleGreaterThan(String value) {
            addCriterion("discount_title >", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleGreaterThanOrEqualTo(String value) {
            addCriterion("discount_title >=", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleLessThan(String value) {
            addCriterion("discount_title <", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleLessThanOrEqualTo(String value) {
            addCriterion("discount_title <=", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleLike(String value) {
            addCriterion("discount_title like", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleNotLike(String value) {
            addCriterion("discount_title not like", value, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleIn(List<String> values) {
            addCriterion("discount_title in", values, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleNotIn(List<String> values) {
            addCriterion("discount_title not in", values, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleBetween(String value1, String value2) {
            addCriterion("discount_title between", value1, value2, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTitleNotBetween(String value1, String value2) {
            addCriterion("discount_title not between", value1, value2, "discountTitle");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeIsNull() {
            addCriterion("discount_type is null");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeIsNotNull() {
            addCriterion("discount_type is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeEqualTo(Byte value) {
            addCriterion("discount_type =", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeNotEqualTo(Byte value) {
            addCriterion("discount_type <>", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeGreaterThan(Byte value) {
            addCriterion("discount_type >", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("discount_type >=", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeLessThan(Byte value) {
            addCriterion("discount_type <", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeLessThanOrEqualTo(Byte value) {
            addCriterion("discount_type <=", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeIn(List<Byte> values) {
            addCriterion("discount_type in", values, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeNotIn(List<Byte> values) {
            addCriterion("discount_type not in", values, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeBetween(Byte value1, Byte value2) {
            addCriterion("discount_type between", value1, value2, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("discount_type not between", value1, value2, "discountType");
            return (Criteria) this;
        }

        public Criteria andBookCountIsNull() {
            addCriterion("book_count is null");
            return (Criteria) this;
        }

        public Criteria andBookCountIsNotNull() {
            addCriterion("book_count is not null");
            return (Criteria) this;
        }

        public Criteria andBookCountEqualTo(Integer value) {
            addCriterion("book_count =", value, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountNotEqualTo(Integer value) {
            addCriterion("book_count <>", value, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountGreaterThan(Integer value) {
            addCriterion("book_count >", value, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("book_count >=", value, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountLessThan(Integer value) {
            addCriterion("book_count <", value, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountLessThanOrEqualTo(Integer value) {
            addCriterion("book_count <=", value, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountIn(List<Integer> values) {
            addCriterion("book_count in", values, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountNotIn(List<Integer> values) {
            addCriterion("book_count not in", values, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountBetween(Integer value1, Integer value2) {
            addCriterion("book_count between", value1, value2, "bookCount");
            return (Criteria) this;
        }

        public Criteria andBookCountNotBetween(Integer value1, Integer value2) {
            addCriterion("book_count not between", value1, value2, "bookCount");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageIsNull() {
            addCriterion("cumulate_package is null");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageIsNotNull() {
            addCriterion("cumulate_package is not null");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageEqualTo(Integer value) {
            addCriterion("cumulate_package =", value, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageNotEqualTo(Integer value) {
            addCriterion("cumulate_package <>", value, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageGreaterThan(Integer value) {
            addCriterion("cumulate_package >", value, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageGreaterThanOrEqualTo(Integer value) {
            addCriterion("cumulate_package >=", value, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageLessThan(Integer value) {
            addCriterion("cumulate_package <", value, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageLessThanOrEqualTo(Integer value) {
            addCriterion("cumulate_package <=", value, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageIn(List<Integer> values) {
            addCriterion("cumulate_package in", values, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageNotIn(List<Integer> values) {
            addCriterion("cumulate_package not in", values, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_package between", value1, value2, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageNotBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_package not between", value1, value2, "cumulatePackage");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountIsNull() {
            addCriterion("cumulate_discount is null");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountIsNotNull() {
            addCriterion("cumulate_discount is not null");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountEqualTo(Integer value) {
            addCriterion("cumulate_discount =", value, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountNotEqualTo(Integer value) {
            addCriterion("cumulate_discount <>", value, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountGreaterThan(Integer value) {
            addCriterion("cumulate_discount >", value, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountGreaterThanOrEqualTo(Integer value) {
            addCriterion("cumulate_discount >=", value, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountLessThan(Integer value) {
            addCriterion("cumulate_discount <", value, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountLessThanOrEqualTo(Integer value) {
            addCriterion("cumulate_discount <=", value, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountIn(List<Integer> values) {
            addCriterion("cumulate_discount in", values, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountNotIn(List<Integer> values) {
            addCriterion("cumulate_discount not in", values, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_discount between", value1, value2, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountNotBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_discount not between", value1, value2, "cumulateDiscount");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiIsNull() {
            addCriterion("cumulate_package_muti is null");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiIsNotNull() {
            addCriterion("cumulate_package_muti is not null");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiEqualTo(Integer value) {
            addCriterion("cumulate_package_muti =", value, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiNotEqualTo(Integer value) {
            addCriterion("cumulate_package_muti <>", value, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiGreaterThan(Integer value) {
            addCriterion("cumulate_package_muti >", value, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiGreaterThanOrEqualTo(Integer value) {
            addCriterion("cumulate_package_muti >=", value, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiLessThan(Integer value) {
            addCriterion("cumulate_package_muti <", value, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiLessThanOrEqualTo(Integer value) {
            addCriterion("cumulate_package_muti <=", value, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiIn(List<Integer> values) {
            addCriterion("cumulate_package_muti in", values, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiNotIn(List<Integer> values) {
            addCriterion("cumulate_package_muti not in", values, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_package_muti between", value1, value2, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulatePackageMutiNotBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_package_muti not between", value1, value2, "cumulatePackageMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiIsNull() {
            addCriterion("cumulate_discount_muti is null");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiIsNotNull() {
            addCriterion("cumulate_discount_muti is not null");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiEqualTo(Integer value) {
            addCriterion("cumulate_discount_muti =", value, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiNotEqualTo(Integer value) {
            addCriterion("cumulate_discount_muti <>", value, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiGreaterThan(Integer value) {
            addCriterion("cumulate_discount_muti >", value, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiGreaterThanOrEqualTo(Integer value) {
            addCriterion("cumulate_discount_muti >=", value, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiLessThan(Integer value) {
            addCriterion("cumulate_discount_muti <", value, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiLessThanOrEqualTo(Integer value) {
            addCriterion("cumulate_discount_muti <=", value, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiIn(List<Integer> values) {
            addCriterion("cumulate_discount_muti in", values, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiNotIn(List<Integer> values) {
            addCriterion("cumulate_discount_muti not in", values, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_discount_muti between", value1, value2, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andCumulateDiscountMutiNotBetween(Integer value1, Integer value2) {
            addCriterion("cumulate_discount_muti not between", value1, value2, "cumulateDiscountMuti");
            return (Criteria) this;
        }

        public Criteria andFullBaseIsNull() {
            addCriterion("full_base is null");
            return (Criteria) this;
        }

        public Criteria andFullBaseIsNotNull() {
            addCriterion("full_base is not null");
            return (Criteria) this;
        }

        public Criteria andFullBaseEqualTo(Integer value) {
            addCriterion("full_base =", value, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseNotEqualTo(Integer value) {
            addCriterion("full_base <>", value, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseGreaterThan(Integer value) {
            addCriterion("full_base >", value, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseGreaterThanOrEqualTo(Integer value) {
            addCriterion("full_base >=", value, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseLessThan(Integer value) {
            addCriterion("full_base <", value, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseLessThanOrEqualTo(Integer value) {
            addCriterion("full_base <=", value, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseIn(List<Integer> values) {
            addCriterion("full_base in", values, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseNotIn(List<Integer> values) {
            addCriterion("full_base not in", values, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseBetween(Integer value1, Integer value2) {
            addCriterion("full_base between", value1, value2, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullBaseNotBetween(Integer value1, Integer value2) {
            addCriterion("full_base not between", value1, value2, "fullBase");
            return (Criteria) this;
        }

        public Criteria andFullMinusIsNull() {
            addCriterion("full_minus is null");
            return (Criteria) this;
        }

        public Criteria andFullMinusIsNotNull() {
            addCriterion("full_minus is not null");
            return (Criteria) this;
        }

        public Criteria andFullMinusEqualTo(Integer value) {
            addCriterion("full_minus =", value, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusNotEqualTo(Integer value) {
            addCriterion("full_minus <>", value, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusGreaterThan(Integer value) {
            addCriterion("full_minus >", value, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusGreaterThanOrEqualTo(Integer value) {
            addCriterion("full_minus >=", value, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusLessThan(Integer value) {
            addCriterion("full_minus <", value, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusLessThanOrEqualTo(Integer value) {
            addCriterion("full_minus <=", value, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusIn(List<Integer> values) {
            addCriterion("full_minus in", values, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusNotIn(List<Integer> values) {
            addCriterion("full_minus not in", values, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusBetween(Integer value1, Integer value2) {
            addCriterion("full_minus between", value1, value2, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andFullMinusNotBetween(Integer value1, Integer value2) {
            addCriterion("full_minus not between", value1, value2, "fullMinus");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueIsNull() {
            addCriterion("discount_single_value is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueIsNotNull() {
            addCriterion("discount_single_value is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueEqualTo(Integer value) {
            addCriterion("discount_single_value =", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueNotEqualTo(Integer value) {
            addCriterion("discount_single_value <>", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueGreaterThan(Integer value) {
            addCriterion("discount_single_value >", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("discount_single_value >=", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueLessThan(Integer value) {
            addCriterion("discount_single_value <", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueLessThanOrEqualTo(Integer value) {
            addCriterion("discount_single_value <=", value, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueIn(List<Integer> values) {
            addCriterion("discount_single_value in", values, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueNotIn(List<Integer> values) {
            addCriterion("discount_single_value not in", values, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueBetween(Integer value1, Integer value2) {
            addCriterion("discount_single_value between", value1, value2, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andDiscountSingleValueNotBetween(Integer value1, Integer value2) {
            addCriterion("discount_single_value not between", value1, value2, "discountSingleValue");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNull() {
            addCriterion("is_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNotNull() {
            addCriterion("is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsValidEqualTo(Byte value) {
            addCriterion("is_valid =", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotEqualTo(Byte value) {
            addCriterion("is_valid <>", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThan(Byte value) {
            addCriterion("is_valid >", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_valid >=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThan(Byte value) {
            addCriterion("is_valid <", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThanOrEqualTo(Byte value) {
            addCriterion("is_valid <=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidIn(List<Byte> values) {
            addCriterion("is_valid in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotIn(List<Byte> values) {
            addCriterion("is_valid not in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidBetween(Byte value1, Byte value2) {
            addCriterion("is_valid between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotBetween(Byte value1, Byte value2) {
            addCriterion("is_valid not between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNull() {
            addCriterion("is_show is null");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNotNull() {
            addCriterion("is_show is not null");
            return (Criteria) this;
        }

        public Criteria andIsShowEqualTo(Byte value) {
            addCriterion("is_show =", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotEqualTo(Byte value) {
            addCriterion("is_show <>", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThan(Byte value) {
            addCriterion("is_show >", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_show >=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThan(Byte value) {
            addCriterion("is_show <", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThanOrEqualTo(Byte value) {
            addCriterion("is_show <=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowIn(List<Byte> values) {
            addCriterion("is_show in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotIn(List<Byte> values) {
            addCriterion("is_show not in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowBetween(Byte value1, Byte value2) {
            addCriterion("is_show between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotBetween(Byte value1, Byte value2) {
            addCriterion("is_show not between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterionForJDBCDate("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterionForJDBCDate("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterionForJDBCDate("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterionForJDBCDate("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterionForJDBCDate("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterionForJDBCDate("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}