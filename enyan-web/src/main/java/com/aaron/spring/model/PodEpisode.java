package com.aaron.spring.model;

import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class PodEpisode {
    private Long episodeId;
    private Long podcastId;
    private Long topicId;
    private String title;
    private String description;
    private String audioFileUrl;
    private Integer durationSeconds;
    private Integer isPublished;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date publicationDate;
    private Integer episodeNumber;
    private Integer episodeCount;
    private Integer listenCount;
    private Integer likeCount;
    private Integer isDeleted;
    private Date createdAt;
    private String coverImageUrl;  // 封面图片URL，与所属播客的cover_image_url保持一致
    private String coverImageUrl2; // 详情页长条图片URL，与所属播客的cover_image_url2保持一致
    /**
     * 累计播放时长 (秒) 。pod_user_episode_interaction表会记录用户播放开始时间，然后前端触发结束播放请求：有三种：暂停，播完，杀掉app后重新启动后。这三种情况要向服务更新播放时长信息。仅放在单集上累计
     */
    private Integer cumulativePlaybackSeconds;
    private Page page;
    private List<OrderObj> orderObjList = new ArrayList<>();
    private String orderByClause;



    public List<OrderObj> getOrderObjList() {
        return orderObjList;
    }

    public void setOrderObjList(List<OrderObj> orderObjList) {
        this.orderObjList = orderObjList;
    }

    public void addOrderObj(OrderObj orderObj) {
        this.orderObjList.add(orderObj);
    }

    public void addOrder(OrderObj order) {
        if (this.orderObjList == null) {
            this.orderObjList = new ArrayList<>();
        }
        this.orderObjList.add(order);
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
