package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StuEnrollExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public StuEnrollExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDataIdIsNull() {
            addCriterion("data_id is null");
            return (Criteria) this;
        }

        public Criteria andDataIdIsNotNull() {
            addCriterion("data_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataIdEqualTo(Long value) {
            addCriterion("data_id =", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotEqualTo(Long value) {
            addCriterion("data_id <>", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThan(Long value) {
            addCriterion("data_id >", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_id >=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThan(Long value) {
            addCriterion("data_id <", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThanOrEqualTo(Long value) {
            addCriterion("data_id <=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdIn(List<Long> values) {
            addCriterion("data_id in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotIn(List<Long> values) {
            addCriterion("data_id not in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdBetween(Long value1, Long value2) {
            addCriterion("data_id between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotBetween(Long value1, Long value2) {
            addCriterion("data_id not between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andStuIdIsNull() {
            addCriterion("stu_id is null");
            return (Criteria) this;
        }

        public Criteria andStuIdIsNotNull() {
            addCriterion("stu_id is not null");
            return (Criteria) this;
        }

        public Criteria andStuIdEqualTo(Integer value) {
            addCriterion("stu_id =", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdNotEqualTo(Integer value) {
            addCriterion("stu_id <>", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdGreaterThan(Integer value) {
            addCriterion("stu_id >", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("stu_id >=", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdLessThan(Integer value) {
            addCriterion("stu_id <", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdLessThanOrEqualTo(Integer value) {
            addCriterion("stu_id <=", value, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdIn(List<Integer> values) {
            addCriterion("stu_id in", values, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdNotIn(List<Integer> values) {
            addCriterion("stu_id not in", values, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdBetween(Integer value1, Integer value2) {
            addCriterion("stu_id between", value1, value2, "stuId");
            return (Criteria) this;
        }

        public Criteria andStuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("stu_id not between", value1, value2, "stuId");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNull() {
            addCriterion("last_name is null");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNotNull() {
            addCriterion("last_name is not null");
            return (Criteria) this;
        }

        public Criteria andLastNameEqualTo(String value) {
            addCriterion("last_name =", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotEqualTo(String value) {
            addCriterion("last_name <>", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThan(String value) {
            addCriterion("last_name >", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("last_name >=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThan(String value) {
            addCriterion("last_name <", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThanOrEqualTo(String value) {
            addCriterion("last_name <=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLike(String value) {
            addCriterion("last_name like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotLike(String value) {
            addCriterion("last_name not like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameIn(List<String> values) {
            addCriterion("last_name in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotIn(List<String> values) {
            addCriterion("last_name not in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameBetween(String value1, String value2) {
            addCriterion("last_name between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotBetween(String value1, String value2) {
            addCriterion("last_name not between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNull() {
            addCriterion("first_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNotNull() {
            addCriterion("first_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstNameEqualTo(String value) {
            addCriterion("first_name =", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotEqualTo(String value) {
            addCriterion("first_name <>", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThan(String value) {
            addCriterion("first_name >", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_name >=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThan(String value) {
            addCriterion("first_name <", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThanOrEqualTo(String value) {
            addCriterion("first_name <=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLike(String value) {
            addCriterion("first_name like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotLike(String value) {
            addCriterion("first_name not like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIn(List<String> values) {
            addCriterion("first_name in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotIn(List<String> values) {
            addCriterion("first_name not in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameBetween(String value1, String value2) {
            addCriterion("first_name between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotBetween(String value1, String value2) {
            addCriterion("first_name not between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEnrollAtIsNull() {
            addCriterion("enroll_at is null");
            return (Criteria) this;
        }

        public Criteria andEnrollAtIsNotNull() {
            addCriterion("enroll_at is not null");
            return (Criteria) this;
        }

        public Criteria andEnrollAtEqualTo(Date value) {
            addCriterion("enroll_at =", value, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtNotEqualTo(Date value) {
            addCriterion("enroll_at <>", value, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtGreaterThan(Date value) {
            addCriterion("enroll_at >", value, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtGreaterThanOrEqualTo(Date value) {
            addCriterion("enroll_at >=", value, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtLessThan(Date value) {
            addCriterion("enroll_at <", value, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtLessThanOrEqualTo(Date value) {
            addCriterion("enroll_at <=", value, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtIn(List<Date> values) {
            addCriterion("enroll_at in", values, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtNotIn(List<Date> values) {
            addCriterion("enroll_at not in", values, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtBetween(Date value1, Date value2) {
            addCriterion("enroll_at between", value1, value2, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollAtNotBetween(Date value1, Date value2) {
            addCriterion("enroll_at not between", value1, value2, "enrollAt");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusIsNull() {
            addCriterion("enroll_status is null");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusIsNotNull() {
            addCriterion("enroll_status is not null");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusEqualTo(Integer value) {
            addCriterion("enroll_status =", value, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusNotEqualTo(Integer value) {
            addCriterion("enroll_status <>", value, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusGreaterThan(Integer value) {
            addCriterion("enroll_status >", value, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("enroll_status >=", value, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusLessThan(Integer value) {
            addCriterion("enroll_status <", value, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusLessThanOrEqualTo(Integer value) {
            addCriterion("enroll_status <=", value, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusIn(List<Integer> values) {
            addCriterion("enroll_status in", values, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusNotIn(List<Integer> values) {
            addCriterion("enroll_status not in", values, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusBetween(Integer value1, Integer value2) {
            addCriterion("enroll_status between", value1, value2, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("enroll_status not between", value1, value2, "enrollStatus");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeIsNull() {
            addCriterion("enroll_code is null");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeIsNotNull() {
            addCriterion("enroll_code is not null");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeEqualTo(String value) {
            addCriterion("enroll_code =", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotEqualTo(String value) {
            addCriterion("enroll_code <>", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeGreaterThan(String value) {
            addCriterion("enroll_code >", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeGreaterThanOrEqualTo(String value) {
            addCriterion("enroll_code >=", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeLessThan(String value) {
            addCriterion("enroll_code <", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeLessThanOrEqualTo(String value) {
            addCriterion("enroll_code <=", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeLike(String value) {
            addCriterion("enroll_code like", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotLike(String value) {
            addCriterion("enroll_code not like", value, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeIn(List<String> values) {
            addCriterion("enroll_code in", values, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotIn(List<String> values) {
            addCriterion("enroll_code not in", values, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeBetween(String value1, String value2) {
            addCriterion("enroll_code between", value1, value2, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andEnrollCodeNotBetween(String value1, String value2) {
            addCriterion("enroll_code not between", value1, value2, "enrollCode");
            return (Criteria) this;
        }

        public Criteria andCheckinIsNull() {
            addCriterion("checkin is null");
            return (Criteria) this;
        }

        public Criteria andCheckinIsNotNull() {
            addCriterion("checkin is not null");
            return (Criteria) this;
        }

        public Criteria andCheckinEqualTo(String value) {
            addCriterion("checkin =", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinNotEqualTo(String value) {
            addCriterion("checkin <>", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinGreaterThan(String value) {
            addCriterion("checkin >", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinGreaterThanOrEqualTo(String value) {
            addCriterion("checkin >=", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinLessThan(String value) {
            addCriterion("checkin <", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinLessThanOrEqualTo(String value) {
            addCriterion("checkin <=", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinLike(String value) {
            addCriterion("checkin like", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinNotLike(String value) {
            addCriterion("checkin not like", value, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinIn(List<String> values) {
            addCriterion("checkin in", values, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinNotIn(List<String> values) {
            addCriterion("checkin not in", values, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinBetween(String value1, String value2) {
            addCriterion("checkin between", value1, value2, "checkin");
            return (Criteria) this;
        }

        public Criteria andCheckinNotBetween(String value1, String value2) {
            addCriterion("checkin not between", value1, value2, "checkin");
            return (Criteria) this;
        }

        public Criteria andTermsIsNull() {
            addCriterion("terms is null");
            return (Criteria) this;
        }

        public Criteria andTermsIsNotNull() {
            addCriterion("terms is not null");
            return (Criteria) this;
        }

        public Criteria andTermsEqualTo(Integer value) {
            addCriterion("terms =", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsNotEqualTo(Integer value) {
            addCriterion("terms <>", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsGreaterThan(Integer value) {
            addCriterion("terms >", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsGreaterThanOrEqualTo(Integer value) {
            addCriterion("terms >=", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsLessThan(Integer value) {
            addCriterion("terms <", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsLessThanOrEqualTo(Integer value) {
            addCriterion("terms <=", value, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsIn(List<Integer> values) {
            addCriterion("terms in", values, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsNotIn(List<Integer> values) {
            addCriterion("terms not in", values, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsBetween(Integer value1, Integer value2) {
            addCriterion("terms between", value1, value2, "terms");
            return (Criteria) this;
        }

        public Criteria andTermsNotBetween(Integer value1, Integer value2) {
            addCriterion("terms not between", value1, value2, "terms");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}