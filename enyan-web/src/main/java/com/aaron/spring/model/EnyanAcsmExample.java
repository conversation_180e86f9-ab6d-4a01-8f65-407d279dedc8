package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EnyanAcsmExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanAcsmExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andAcsmIdIsNull() {
            addCriterion("acsm_id is null");
            return (Criteria) this;
        }

        public Criteria andAcsmIdIsNotNull() {
            addCriterion("acsm_id is not null");
            return (Criteria) this;
        }

        public Criteria andAcsmIdEqualTo(Long value) {
            addCriterion("acsm_id =", value, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdNotEqualTo(Long value) {
            addCriterion("acsm_id <>", value, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdGreaterThan(Long value) {
            addCriterion("acsm_id >", value, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdGreaterThanOrEqualTo(Long value) {
            addCriterion("acsm_id >=", value, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdLessThan(Long value) {
            addCriterion("acsm_id <", value, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdLessThanOrEqualTo(Long value) {
            addCriterion("acsm_id <=", value, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdIn(List<Long> values) {
            addCriterion("acsm_id in", values, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdNotIn(List<Long> values) {
            addCriterion("acsm_id not in", values, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdBetween(Long value1, Long value2) {
            addCriterion("acsm_id between", value1, value2, "acsmId");
            return (Criteria) this;
        }

        public Criteria andAcsmIdNotBetween(Long value1, Long value2) {
            addCriterion("acsm_id not between", value1, value2, "acsmId");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("order_num is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("order_num is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(String value) {
            addCriterion("order_num =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(String value) {
            addCriterion("order_num <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(String value) {
            addCriterion("order_num >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("order_num >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(String value) {
            addCriterion("order_num <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(String value) {
            addCriterion("order_num <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLike(String value) {
            addCriterion("order_num like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotLike(String value) {
            addCriterion("order_num not like", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<String> values) {
            addCriterion("order_num in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<String> values) {
            addCriterion("order_num not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(String value1, String value2) {
            addCriterion("order_num between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(String value1, String value2) {
            addCriterion("order_num not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNull() {
            addCriterion("book_id is null");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNotNull() {
            addCriterion("book_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookIdEqualTo(Long value) {
            addCriterion("book_id =", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotEqualTo(Long value) {
            addCriterion("book_id <>", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThan(Long value) {
            addCriterion("book_id >", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThanOrEqualTo(Long value) {
            addCriterion("book_id >=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThan(Long value) {
            addCriterion("book_id <", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThanOrEqualTo(Long value) {
            addCriterion("book_id <=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdIn(List<Long> values) {
            addCriterion("book_id in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotIn(List<Long> values) {
            addCriterion("book_id not in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdBetween(Long value1, Long value2) {
            addCriterion("book_id between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotBetween(Long value1, Long value2) {
            addCriterion("book_id not between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdIsNull() {
            addCriterion("transaction_id is null");
            return (Criteria) this;
        }

        public Criteria andTransactionIdIsNotNull() {
            addCriterion("transaction_id is not null");
            return (Criteria) this;
        }

        public Criteria andTransactionIdEqualTo(String value) {
            addCriterion("transaction_id =", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdNotEqualTo(String value) {
            addCriterion("transaction_id <>", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdGreaterThan(String value) {
            addCriterion("transaction_id >", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdGreaterThanOrEqualTo(String value) {
            addCriterion("transaction_id >=", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdLessThan(String value) {
            addCriterion("transaction_id <", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdLessThanOrEqualTo(String value) {
            addCriterion("transaction_id <=", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdLike(String value) {
            addCriterion("transaction_id like", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdNotLike(String value) {
            addCriterion("transaction_id not like", value, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdIn(List<String> values) {
            addCriterion("transaction_id in", values, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdNotIn(List<String> values) {
            addCriterion("transaction_id not in", values, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdBetween(String value1, String value2) {
            addCriterion("transaction_id between", value1, value2, "transactionId");
            return (Criteria) this;
        }

        public Criteria andTransactionIdNotBetween(String value1, String value2) {
            addCriterion("transaction_id not between", value1, value2, "transactionId");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledIsNull() {
            addCriterion("is_fulfilled is null");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledIsNotNull() {
            addCriterion("is_fulfilled is not null");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledEqualTo(Byte value) {
            addCriterion("is_fulfilled =", value, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledNotEqualTo(Byte value) {
            addCriterion("is_fulfilled <>", value, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledGreaterThan(Byte value) {
            addCriterion("is_fulfilled >", value, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_fulfilled >=", value, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledLessThan(Byte value) {
            addCriterion("is_fulfilled <", value, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledLessThanOrEqualTo(Byte value) {
            addCriterion("is_fulfilled <=", value, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledIn(List<Byte> values) {
            addCriterion("is_fulfilled in", values, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledNotIn(List<Byte> values) {
            addCriterion("is_fulfilled not in", values, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledBetween(Byte value1, Byte value2) {
            addCriterion("is_fulfilled between", value1, value2, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andIsFulfilledNotBetween(Byte value1, Byte value2) {
            addCriterion("is_fulfilled not between", value1, value2, "isFulfilled");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoIsNull() {
            addCriterion("acsm_info is null");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoIsNotNull() {
            addCriterion("acsm_info is not null");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoEqualTo(String value) {
            addCriterion("acsm_info =", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoNotEqualTo(String value) {
            addCriterion("acsm_info <>", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoGreaterThan(String value) {
            addCriterion("acsm_info >", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoGreaterThanOrEqualTo(String value) {
            addCriterion("acsm_info >=", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoLessThan(String value) {
            addCriterion("acsm_info <", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoLessThanOrEqualTo(String value) {
            addCriterion("acsm_info <=", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoLike(String value) {
            addCriterion("acsm_info like", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoNotLike(String value) {
            addCriterion("acsm_info not like", value, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoIn(List<String> values) {
            addCriterion("acsm_info in", values, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoNotIn(List<String> values) {
            addCriterion("acsm_info not in", values, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoBetween(String value1, String value2) {
            addCriterion("acsm_info between", value1, value2, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andAcsmInfoNotBetween(String value1, String value2) {
            addCriterion("acsm_info not between", value1, value2, "acsmInfo");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}