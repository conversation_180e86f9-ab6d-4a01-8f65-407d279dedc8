package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class EnyanRedeemCode extends BaseDTO{
    private static final long serialVersionUID = 8288259964231196865L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long redeemCodeId;

    private String userEmail;

    private String code;

    private Integer type;

    private Integer status;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endAt;

    private String createTime;

    private String note;

    private RedeemCodeNoteInfo redeemCodeNoteInfo;

    private String[] bookIDs;

    private boolean isExpired;

    public Long getRedeemCodeId() {
        return redeemCodeId;
    }

    public void setRedeemCodeId(Long redeemCodeId) {
        this.redeemCodeId = redeemCodeId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail == null ? null : userEmail.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getEndAt() {
        return endAt;
    }

    public void setEndAt(Date endAt) {
        this.endAt = endAt;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime == null ? null : createTime.trim();
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note == null ? null : note.trim();
    }

    public RedeemCodeNoteInfo getRedeemCodeNoteInfo() {
        return redeemCodeNoteInfo;
    }

    public void setRedeemCodeNoteInfo(RedeemCodeNoteInfo redeemCodeNoteInfo) {
        this.redeemCodeNoteInfo = redeemCodeNoteInfo;
    }

    public String[] getBookIDs() {
        return bookIDs;
    }

    public void setBookIDs(String[] bookIDs) {
        this.bookIDs = bookIDs;
    }

    public boolean isExpired() {
        return isExpired;
    }

    public void setExpired(boolean expired) {
        isExpired = expired;
    }
}