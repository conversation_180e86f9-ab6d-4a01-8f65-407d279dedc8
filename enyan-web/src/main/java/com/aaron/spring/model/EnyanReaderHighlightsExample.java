package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.List;

public class EnyanReaderHighlightsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanReaderHighlightsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHighlightIdIsNull() {
            addCriterion("highlight_id is null");
            return (Criteria) this;
        }

        public Criteria andHighlightIdIsNotNull() {
            addCriterion("highlight_id is not null");
            return (Criteria) this;
        }

        public Criteria andHighlightIdEqualTo(String value) {
            addCriterion("highlight_id =", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdNotEqualTo(String value) {
            addCriterion("highlight_id <>", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdGreaterThan(String value) {
            addCriterion("highlight_id >", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdGreaterThanOrEqualTo(String value) {
            addCriterion("highlight_id >=", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdLessThan(String value) {
            addCriterion("highlight_id <", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdLessThanOrEqualTo(String value) {
            addCriterion("highlight_id <=", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdLike(String value) {
            addCriterion("highlight_id like", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdNotLike(String value) {
            addCriterion("highlight_id not like", value, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdIn(List<String> values) {
            addCriterion("highlight_id in", values, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdNotIn(List<String> values) {
            addCriterion("highlight_id not in", values, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdBetween(String value1, String value2) {
            addCriterion("highlight_id between", value1, value2, "highlightId");
            return (Criteria) this;
        }

        public Criteria andHighlightIdNotBetween(String value1, String value2) {
            addCriterion("highlight_id not between", value1, value2, "highlightId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNull() {
            addCriterion("user_email is null");
            return (Criteria) this;
        }

        public Criteria andUserEmailIsNotNull() {
            addCriterion("user_email is not null");
            return (Criteria) this;
        }

        public Criteria andUserEmailEqualTo(String value) {
            addCriterion("user_email =", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotEqualTo(String value) {
            addCriterion("user_email <>", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThan(String value) {
            addCriterion("user_email >", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailGreaterThanOrEqualTo(String value) {
            addCriterion("user_email >=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThan(String value) {
            addCriterion("user_email <", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLessThanOrEqualTo(String value) {
            addCriterion("user_email <=", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailLike(String value) {
            addCriterion("user_email like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotLike(String value) {
            addCriterion("user_email not like", value, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailIn(List<String> values) {
            addCriterion("user_email in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotIn(List<String> values) {
            addCriterion("user_email not in", values, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailBetween(String value1, String value2) {
            addCriterion("user_email between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andUserEmailNotBetween(String value1, String value2) {
            addCriterion("user_email not between", value1, value2, "userEmail");
            return (Criteria) this;
        }

        public Criteria andPublicationIdIsNull() {
            addCriterion("publication_id is null");
            return (Criteria) this;
        }

        public Criteria andPublicationIdIsNotNull() {
            addCriterion("publication_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublicationIdEqualTo(String value) {
            addCriterion("publication_id =", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdNotEqualTo(String value) {
            addCriterion("publication_id <>", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdGreaterThan(String value) {
            addCriterion("publication_id >", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdGreaterThanOrEqualTo(String value) {
            addCriterion("publication_id >=", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdLessThan(String value) {
            addCriterion("publication_id <", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdLessThanOrEqualTo(String value) {
            addCriterion("publication_id <=", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdLike(String value) {
            addCriterion("publication_id like", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdNotLike(String value) {
            addCriterion("publication_id not like", value, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdIn(List<String> values) {
            addCriterion("publication_id in", values, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdNotIn(List<String> values) {
            addCriterion("publication_id not in", values, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdBetween(String value1, String value2) {
            addCriterion("publication_id between", value1, value2, "publicationId");
            return (Criteria) this;
        }

        public Criteria andPublicationIdNotBetween(String value1, String value2) {
            addCriterion("publication_id not between", value1, value2, "publicationId");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNull() {
            addCriterion("book_id is null");
            return (Criteria) this;
        }

        public Criteria andBookIdIsNotNull() {
            addCriterion("book_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookIdEqualTo(Long value) {
            addCriterion("book_id =", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotEqualTo(Long value) {
            addCriterion("book_id <>", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThan(Long value) {
            addCriterion("book_id >", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdGreaterThanOrEqualTo(Long value) {
            addCriterion("book_id >=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThan(Long value) {
            addCriterion("book_id <", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdLessThanOrEqualTo(Long value) {
            addCriterion("book_id <=", value, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdIn(List<Long> values) {
            addCriterion("book_id in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotIn(List<Long> values) {
            addCriterion("book_id not in", values, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdBetween(Long value1, Long value2) {
            addCriterion("book_id between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andBookIdNotBetween(Long value1, Long value2) {
            addCriterion("book_id not between", value1, value2, "bookId");
            return (Criteria) this;
        }

        public Criteria andResourceIndexIsNull() {
            addCriterion("resource_index is null");
            return (Criteria) this;
        }

        public Criteria andResourceIndexIsNotNull() {
            addCriterion("resource_index is not null");
            return (Criteria) this;
        }

        public Criteria andResourceIndexEqualTo(Integer value) {
            addCriterion("resource_index =", value, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexNotEqualTo(Integer value) {
            addCriterion("resource_index <>", value, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexGreaterThan(Integer value) {
            addCriterion("resource_index >", value, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexGreaterThanOrEqualTo(Integer value) {
            addCriterion("resource_index >=", value, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexLessThan(Integer value) {
            addCriterion("resource_index <", value, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexLessThanOrEqualTo(Integer value) {
            addCriterion("resource_index <=", value, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexIn(List<Integer> values) {
            addCriterion("resource_index in", values, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexNotIn(List<Integer> values) {
            addCriterion("resource_index not in", values, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexBetween(Integer value1, Integer value2) {
            addCriterion("resource_index between", value1, value2, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceIndexNotBetween(Integer value1, Integer value2) {
            addCriterion("resource_index not between", value1, value2, "resourceIndex");
            return (Criteria) this;
        }

        public Criteria andResourceHrefIsNull() {
            addCriterion("resource_href is null");
            return (Criteria) this;
        }

        public Criteria andResourceHrefIsNotNull() {
            addCriterion("resource_href is not null");
            return (Criteria) this;
        }

        public Criteria andResourceHrefEqualTo(String value) {
            addCriterion("resource_href =", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefNotEqualTo(String value) {
            addCriterion("resource_href <>", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefGreaterThan(String value) {
            addCriterion("resource_href >", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefGreaterThanOrEqualTo(String value) {
            addCriterion("resource_href >=", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefLessThan(String value) {
            addCriterion("resource_href <", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefLessThanOrEqualTo(String value) {
            addCriterion("resource_href <=", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefLike(String value) {
            addCriterion("resource_href like", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefNotLike(String value) {
            addCriterion("resource_href not like", value, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefIn(List<String> values) {
            addCriterion("resource_href in", values, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefNotIn(List<String> values) {
            addCriterion("resource_href not in", values, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefBetween(String value1, String value2) {
            addCriterion("resource_href between", value1, value2, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceHrefNotBetween(String value1, String value2) {
            addCriterion("resource_href not between", value1, value2, "resourceHref");
            return (Criteria) this;
        }

        public Criteria andResourceTypeIsNull() {
            addCriterion("resource_type is null");
            return (Criteria) this;
        }

        public Criteria andResourceTypeIsNotNull() {
            addCriterion("resource_type is not null");
            return (Criteria) this;
        }

        public Criteria andResourceTypeEqualTo(String value) {
            addCriterion("resource_type =", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeNotEqualTo(String value) {
            addCriterion("resource_type <>", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeGreaterThan(String value) {
            addCriterion("resource_type >", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("resource_type >=", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeLessThan(String value) {
            addCriterion("resource_type <", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeLessThanOrEqualTo(String value) {
            addCriterion("resource_type <=", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeLike(String value) {
            addCriterion("resource_type like", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeNotLike(String value) {
            addCriterion("resource_type not like", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeIn(List<String> values) {
            addCriterion("resource_type in", values, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeNotIn(List<String> values) {
            addCriterion("resource_type not in", values, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeBetween(String value1, String value2) {
            addCriterion("resource_type between", value1, value2, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeNotBetween(String value1, String value2) {
            addCriterion("resource_type not between", value1, value2, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTitleIsNull() {
            addCriterion("resource_title is null");
            return (Criteria) this;
        }

        public Criteria andResourceTitleIsNotNull() {
            addCriterion("resource_title is not null");
            return (Criteria) this;
        }

        public Criteria andResourceTitleEqualTo(String value) {
            addCriterion("resource_title =", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleNotEqualTo(String value) {
            addCriterion("resource_title <>", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleGreaterThan(String value) {
            addCriterion("resource_title >", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleGreaterThanOrEqualTo(String value) {
            addCriterion("resource_title >=", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleLessThan(String value) {
            addCriterion("resource_title <", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleLessThanOrEqualTo(String value) {
            addCriterion("resource_title <=", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleLike(String value) {
            addCriterion("resource_title like", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleNotLike(String value) {
            addCriterion("resource_title not like", value, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleIn(List<String> values) {
            addCriterion("resource_title in", values, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleNotIn(List<String> values) {
            addCriterion("resource_title not in", values, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleBetween(String value1, String value2) {
            addCriterion("resource_title between", value1, value2, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andResourceTitleNotBetween(String value1, String value2) {
            addCriterion("resource_title not between", value1, value2, "resourceTitle");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("location is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("location is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("location =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("location <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("location >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("location >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("location <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("location <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("location like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("location not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("location in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("location not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("location between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("location not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocatorTextIsNull() {
            addCriterion("locator_text is null");
            return (Criteria) this;
        }

        public Criteria andLocatorTextIsNotNull() {
            addCriterion("locator_text is not null");
            return (Criteria) this;
        }

        public Criteria andLocatorTextEqualTo(String value) {
            addCriterion("locator_text =", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextNotEqualTo(String value) {
            addCriterion("locator_text <>", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextGreaterThan(String value) {
            addCriterion("locator_text >", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextGreaterThanOrEqualTo(String value) {
            addCriterion("locator_text >=", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextLessThan(String value) {
            addCriterion("locator_text <", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextLessThanOrEqualTo(String value) {
            addCriterion("locator_text <=", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextLike(String value) {
            addCriterion("locator_text like", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextNotLike(String value) {
            addCriterion("locator_text not like", value, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextIn(List<String> values) {
            addCriterion("locator_text in", values, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextNotIn(List<String> values) {
            addCriterion("locator_text not in", values, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextBetween(String value1, String value2) {
            addCriterion("locator_text between", value1, value2, "locatorText");
            return (Criteria) this;
        }

        public Criteria andLocatorTextNotBetween(String value1, String value2) {
            addCriterion("locator_text not between", value1, value2, "locatorText");
            return (Criteria) this;
        }

        public Criteria andColorIsNull() {
            addCriterion("color is null");
            return (Criteria) this;
        }

        public Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (Criteria) this;
        }

        public Criteria andColorEqualTo(Integer value) {
            addCriterion("color =", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotEqualTo(Integer value) {
            addCriterion("color <>", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThan(Integer value) {
            addCriterion("color >", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualTo(Integer value) {
            addCriterion("color >=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThan(Integer value) {
            addCriterion("color <", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualTo(Integer value) {
            addCriterion("color <=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorIn(List<Integer> values) {
            addCriterion("color in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotIn(List<Integer> values) {
            addCriterion("color not in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorBetween(Integer value1, Integer value2) {
            addCriterion("color between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotBetween(Integer value1, Integer value2) {
            addCriterion("color not between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andAnnotationIsNull() {
            addCriterion("annotation is null");
            return (Criteria) this;
        }

        public Criteria andAnnotationIsNotNull() {
            addCriterion("annotation is not null");
            return (Criteria) this;
        }

        public Criteria andAnnotationEqualTo(String value) {
            addCriterion("annotation =", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationNotEqualTo(String value) {
            addCriterion("annotation <>", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationGreaterThan(String value) {
            addCriterion("annotation >", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationGreaterThanOrEqualTo(String value) {
            addCriterion("annotation >=", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationLessThan(String value) {
            addCriterion("annotation <", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationLessThanOrEqualTo(String value) {
            addCriterion("annotation <=", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationLike(String value) {
            addCriterion("annotation like", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationNotLike(String value) {
            addCriterion("annotation not like", value, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationIn(List<String> values) {
            addCriterion("annotation in", values, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationNotIn(List<String> values) {
            addCriterion("annotation not in", values, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationBetween(String value1, String value2) {
            addCriterion("annotation between", value1, value2, "annotation");
            return (Criteria) this;
        }

        public Criteria andAnnotationNotBetween(String value1, String value2) {
            addCriterion("annotation not between", value1, value2, "annotation");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNull() {
            addCriterion("creation_date is null");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNotNull() {
            addCriterion("creation_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreationDateEqualTo(Long value) {
            addCriterion("creation_date =", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotEqualTo(Long value) {
            addCriterion("creation_date <>", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThan(Long value) {
            addCriterion("creation_date >", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThanOrEqualTo(Long value) {
            addCriterion("creation_date >=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThan(Long value) {
            addCriterion("creation_date <", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThanOrEqualTo(Long value) {
            addCriterion("creation_date <=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateIn(List<Long> values) {
            addCriterion("creation_date in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotIn(List<Long> values) {
            addCriterion("creation_date not in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateBetween(Long value1, Long value2) {
            addCriterion("creation_date between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotBetween(Long value1, Long value2) {
            addCriterion("creation_date not between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}