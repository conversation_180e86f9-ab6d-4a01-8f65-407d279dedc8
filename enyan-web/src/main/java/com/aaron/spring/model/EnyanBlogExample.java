package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EnyanBlogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanBlogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBlogIdIsNull() {
            addCriterion("blog_id is null");
            return (Criteria) this;
        }

        public Criteria andBlogIdIsNotNull() {
            addCriterion("blog_id is not null");
            return (Criteria) this;
        }

        public Criteria andBlogIdEqualTo(Long value) {
            addCriterion("blog_id =", value, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdNotEqualTo(Long value) {
            addCriterion("blog_id <>", value, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdGreaterThan(Long value) {
            addCriterion("blog_id >", value, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdGreaterThanOrEqualTo(Long value) {
            addCriterion("blog_id >=", value, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdLessThan(Long value) {
            addCriterion("blog_id <", value, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdLessThanOrEqualTo(Long value) {
            addCriterion("blog_id <=", value, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdIn(List<Long> values) {
            addCriterion("blog_id in", values, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdNotIn(List<Long> values) {
            addCriterion("blog_id not in", values, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdBetween(Long value1, Long value2) {
            addCriterion("blog_id between", value1, value2, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogIdNotBetween(Long value1, Long value2) {
            addCriterion("blog_id not between", value1, value2, "blogId");
            return (Criteria) this;
        }

        public Criteria andBlogTitleIsNull() {
            addCriterion("blog_title is null");
            return (Criteria) this;
        }

        public Criteria andBlogTitleIsNotNull() {
            addCriterion("blog_title is not null");
            return (Criteria) this;
        }

        public Criteria andBlogTitleEqualTo(String value) {
            addCriterion("blog_title =", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleNotEqualTo(String value) {
            addCriterion("blog_title <>", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleGreaterThan(String value) {
            addCriterion("blog_title >", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleGreaterThanOrEqualTo(String value) {
            addCriterion("blog_title >=", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleLessThan(String value) {
            addCriterion("blog_title <", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleLessThanOrEqualTo(String value) {
            addCriterion("blog_title <=", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleLike(String value) {
            addCriterion("blog_title like", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleNotLike(String value) {
            addCriterion("blog_title not like", value, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleIn(List<String> values) {
            addCriterion("blog_title in", values, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleNotIn(List<String> values) {
            addCriterion("blog_title not in", values, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleBetween(String value1, String value2) {
            addCriterion("blog_title between", value1, value2, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andBlogTitleNotBetween(String value1, String value2) {
            addCriterion("blog_title not between", value1, value2, "blogTitle");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNull() {
            addCriterion("author is null");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNotNull() {
            addCriterion("author is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorEqualTo(String value) {
            addCriterion("author =", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotEqualTo(String value) {
            addCriterion("author <>", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThan(String value) {
            addCriterion("author >", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThanOrEqualTo(String value) {
            addCriterion("author >=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThan(String value) {
            addCriterion("author <", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThanOrEqualTo(String value) {
            addCriterion("author <=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLike(String value) {
            addCriterion("author like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotLike(String value) {
            addCriterion("author not like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorIn(List<String> values) {
            addCriterion("author in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotIn(List<String> values) {
            addCriterion("author not in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorBetween(String value1, String value2) {
            addCriterion("author between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotBetween(String value1, String value2) {
            addCriterion("author not between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNull() {
            addCriterion("publisher_id is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNotNull() {
            addCriterion("publisher_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdEqualTo(Long value) {
            addCriterion("publisher_id =", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotEqualTo(Long value) {
            addCriterion("publisher_id <>", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThan(Long value) {
            addCriterion("publisher_id >", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThanOrEqualTo(Long value) {
            addCriterion("publisher_id >=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThan(Long value) {
            addCriterion("publisher_id <", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThanOrEqualTo(Long value) {
            addCriterion("publisher_id <=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIn(List<Long> values) {
            addCriterion("publisher_id in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotIn(List<Long> values) {
            addCriterion("publisher_id not in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdBetween(Long value1, Long value2) {
            addCriterion("publisher_id between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotBetween(Long value1, Long value2) {
            addCriterion("publisher_id not between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andBlogCoverIsNull() {
            addCriterion("blog_cover is null");
            return (Criteria) this;
        }

        public Criteria andBlogCoverIsNotNull() {
            addCriterion("blog_cover is not null");
            return (Criteria) this;
        }

        public Criteria andBlogCoverEqualTo(String value) {
            addCriterion("blog_cover =", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverNotEqualTo(String value) {
            addCriterion("blog_cover <>", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverGreaterThan(String value) {
            addCriterion("blog_cover >", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverGreaterThanOrEqualTo(String value) {
            addCriterion("blog_cover >=", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverLessThan(String value) {
            addCriterion("blog_cover <", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverLessThanOrEqualTo(String value) {
            addCriterion("blog_cover <=", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverLike(String value) {
            addCriterion("blog_cover like", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverNotLike(String value) {
            addCriterion("blog_cover not like", value, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverIn(List<String> values) {
            addCriterion("blog_cover in", values, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverNotIn(List<String> values) {
            addCriterion("blog_cover not in", values, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverBetween(String value1, String value2) {
            addCriterion("blog_cover between", value1, value2, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverNotBetween(String value1, String value2) {
            addCriterion("blog_cover not between", value1, value2, "blogCover");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppIsNull() {
            addCriterion("blog_cover_app is null");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppIsNotNull() {
            addCriterion("blog_cover_app is not null");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppEqualTo(String value) {
            addCriterion("blog_cover_app =", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppNotEqualTo(String value) {
            addCriterion("blog_cover_app <>", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppGreaterThan(String value) {
            addCriterion("blog_cover_app >", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppGreaterThanOrEqualTo(String value) {
            addCriterion("blog_cover_app >=", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppLessThan(String value) {
            addCriterion("blog_cover_app <", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppLessThanOrEqualTo(String value) {
            addCriterion("blog_cover_app <=", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppLike(String value) {
            addCriterion("blog_cover_app like", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppNotLike(String value) {
            addCriterion("blog_cover_app not like", value, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppIn(List<String> values) {
            addCriterion("blog_cover_app in", values, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppNotIn(List<String> values) {
            addCriterion("blog_cover_app not in", values, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppBetween(String value1, String value2) {
            addCriterion("blog_cover_app between", value1, value2, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogCoverAppNotBetween(String value1, String value2) {
            addCriterion("blog_cover_app not between", value1, value2, "blogCoverApp");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractIsNull() {
            addCriterion("blog_abstract is null");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractIsNotNull() {
            addCriterion("blog_abstract is not null");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractEqualTo(String value) {
            addCriterion("blog_abstract =", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractNotEqualTo(String value) {
            addCriterion("blog_abstract <>", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractGreaterThan(String value) {
            addCriterion("blog_abstract >", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractGreaterThanOrEqualTo(String value) {
            addCriterion("blog_abstract >=", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractLessThan(String value) {
            addCriterion("blog_abstract <", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractLessThanOrEqualTo(String value) {
            addCriterion("blog_abstract <=", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractLike(String value) {
            addCriterion("blog_abstract like", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractNotLike(String value) {
            addCriterion("blog_abstract not like", value, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractIn(List<String> values) {
            addCriterion("blog_abstract in", values, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractNotIn(List<String> values) {
            addCriterion("blog_abstract not in", values, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractBetween(String value1, String value2) {
            addCriterion("blog_abstract between", value1, value2, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andBlogAbstractNotBetween(String value1, String value2) {
            addCriterion("blog_abstract not between", value1, value2, "blogAbstract");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIsNull() {
            addCriterion("recommended_order is null");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIsNotNull() {
            addCriterion("recommended_order is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderEqualTo(Integer value) {
            addCriterion("recommended_order =", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotEqualTo(Integer value) {
            addCriterion("recommended_order <>", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderGreaterThan(Integer value) {
            addCriterion("recommended_order >", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("recommended_order >=", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderLessThan(Integer value) {
            addCriterion("recommended_order <", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderLessThanOrEqualTo(Integer value) {
            addCriterion("recommended_order <=", value, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderIn(List<Integer> values) {
            addCriterion("recommended_order in", values, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotIn(List<Integer> values) {
            addCriterion("recommended_order not in", values, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderBetween(Integer value1, Integer value2) {
            addCriterion("recommended_order between", value1, value2, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("recommended_order not between", value1, value2, "recommendedOrder");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionIsNull() {
            addCriterion("recommended_caption is null");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionIsNotNull() {
            addCriterion("recommended_caption is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionEqualTo(String value) {
            addCriterion("recommended_caption =", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotEqualTo(String value) {
            addCriterion("recommended_caption <>", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionGreaterThan(String value) {
            addCriterion("recommended_caption >", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionGreaterThanOrEqualTo(String value) {
            addCriterion("recommended_caption >=", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionLessThan(String value) {
            addCriterion("recommended_caption <", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionLessThanOrEqualTo(String value) {
            addCriterion("recommended_caption <=", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionLike(String value) {
            addCriterion("recommended_caption like", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotLike(String value) {
            addCriterion("recommended_caption not like", value, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionIn(List<String> values) {
            addCriterion("recommended_caption in", values, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotIn(List<String> values) {
            addCriterion("recommended_caption not in", values, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionBetween(String value1, String value2) {
            addCriterion("recommended_caption between", value1, value2, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andRecommendedCaptionNotBetween(String value1, String value2) {
            addCriterion("recommended_caption not between", value1, value2, "recommendedCaption");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andReadCountIsNull() {
            addCriterion("read_count is null");
            return (Criteria) this;
        }

        public Criteria andReadCountIsNotNull() {
            addCriterion("read_count is not null");
            return (Criteria) this;
        }

        public Criteria andReadCountEqualTo(Integer value) {
            addCriterion("read_count =", value, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountNotEqualTo(Integer value) {
            addCriterion("read_count <>", value, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountGreaterThan(Integer value) {
            addCriterion("read_count >", value, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("read_count >=", value, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountLessThan(Integer value) {
            addCriterion("read_count <", value, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountLessThanOrEqualTo(Integer value) {
            addCriterion("read_count <=", value, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountIn(List<Integer> values) {
            addCriterion("read_count in", values, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountNotIn(List<Integer> values) {
            addCriterion("read_count not in", values, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountBetween(Integer value1, Integer value2) {
            addCriterion("read_count between", value1, value2, "readCount");
            return (Criteria) this;
        }

        public Criteria andReadCountNotBetween(Integer value1, Integer value2) {
            addCriterion("read_count not between", value1, value2, "readCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountIsNull() {
            addCriterion("like_count is null");
            return (Criteria) this;
        }

        public Criteria andLikeCountIsNotNull() {
            addCriterion("like_count is not null");
            return (Criteria) this;
        }

        public Criteria andLikeCountEqualTo(Integer value) {
            addCriterion("like_count =", value, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountNotEqualTo(Integer value) {
            addCriterion("like_count <>", value, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountGreaterThan(Integer value) {
            addCriterion("like_count >", value, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("like_count >=", value, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountLessThan(Integer value) {
            addCriterion("like_count <", value, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountLessThanOrEqualTo(Integer value) {
            addCriterion("like_count <=", value, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountIn(List<Integer> values) {
            addCriterion("like_count in", values, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountNotIn(List<Integer> values) {
            addCriterion("like_count not in", values, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountBetween(Integer value1, Integer value2) {
            addCriterion("like_count between", value1, value2, "likeCount");
            return (Criteria) this;
        }

        public Criteria andLikeCountNotBetween(Integer value1, Integer value2) {
            addCriterion("like_count not between", value1, value2, "likeCount");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}