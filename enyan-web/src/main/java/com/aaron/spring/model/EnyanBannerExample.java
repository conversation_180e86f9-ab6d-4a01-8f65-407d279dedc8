package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EnyanBannerExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanBannerExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDataIdIsNull() {
            addCriterion("data_id is null");
            return (Criteria) this;
        }

        public Criteria andDataIdIsNotNull() {
            addCriterion("data_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataIdEqualTo(Long value) {
            addCriterion("data_id =", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotEqualTo(Long value) {
            addCriterion("data_id <>", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThan(Long value) {
            addCriterion("data_id >", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_id >=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThan(Long value) {
            addCriterion("data_id <", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThanOrEqualTo(Long value) {
            addCriterion("data_id <=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdIn(List<Long> values) {
            addCriterion("data_id in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotIn(List<Long> values) {
            addCriterion("data_id not in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdBetween(Long value1, Long value2) {
            addCriterion("data_id between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotBetween(Long value1, Long value2) {
            addCriterion("data_id not between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataNameIsNull() {
            addCriterion("data_name is null");
            return (Criteria) this;
        }

        public Criteria andDataNameIsNotNull() {
            addCriterion("data_name is not null");
            return (Criteria) this;
        }

        public Criteria andDataNameEqualTo(String value) {
            addCriterion("data_name =", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameNotEqualTo(String value) {
            addCriterion("data_name <>", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameGreaterThan(String value) {
            addCriterion("data_name >", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameGreaterThanOrEqualTo(String value) {
            addCriterion("data_name >=", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameLessThan(String value) {
            addCriterion("data_name <", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameLessThanOrEqualTo(String value) {
            addCriterion("data_name <=", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameLike(String value) {
            addCriterion("data_name like", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameNotLike(String value) {
            addCriterion("data_name not like", value, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameIn(List<String> values) {
            addCriterion("data_name in", values, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameNotIn(List<String> values) {
            addCriterion("data_name not in", values, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameBetween(String value1, String value2) {
            addCriterion("data_name between", value1, value2, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataNameNotBetween(String value1, String value2) {
            addCriterion("data_name not between", value1, value2, "dataName");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlIsNull() {
            addCriterion("data_img_url is null");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlIsNotNull() {
            addCriterion("data_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlEqualTo(String value) {
            addCriterion("data_img_url =", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlNotEqualTo(String value) {
            addCriterion("data_img_url <>", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlGreaterThan(String value) {
            addCriterion("data_img_url >", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("data_img_url >=", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlLessThan(String value) {
            addCriterion("data_img_url <", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlLessThanOrEqualTo(String value) {
            addCriterion("data_img_url <=", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlLike(String value) {
            addCriterion("data_img_url like", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlNotLike(String value) {
            addCriterion("data_img_url not like", value, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlIn(List<String> values) {
            addCriterion("data_img_url in", values, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlNotIn(List<String> values) {
            addCriterion("data_img_url not in", values, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlBetween(String value1, String value2) {
            addCriterion("data_img_url between", value1, value2, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataImgUrlNotBetween(String value1, String value2) {
            addCriterion("data_img_url not between", value1, value2, "dataImgUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlIsNull() {
            addCriterion("data_to_url is null");
            return (Criteria) this;
        }

        public Criteria andDataToUrlIsNotNull() {
            addCriterion("data_to_url is not null");
            return (Criteria) this;
        }

        public Criteria andDataToUrlEqualTo(String value) {
            addCriterion("data_to_url =", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlNotEqualTo(String value) {
            addCriterion("data_to_url <>", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlGreaterThan(String value) {
            addCriterion("data_to_url >", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlGreaterThanOrEqualTo(String value) {
            addCriterion("data_to_url >=", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlLessThan(String value) {
            addCriterion("data_to_url <", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlLessThanOrEqualTo(String value) {
            addCriterion("data_to_url <=", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlLike(String value) {
            addCriterion("data_to_url like", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlNotLike(String value) {
            addCriterion("data_to_url not like", value, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlIn(List<String> values) {
            addCriterion("data_to_url in", values, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlNotIn(List<String> values) {
            addCriterion("data_to_url not in", values, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlBetween(String value1, String value2) {
            addCriterion("data_to_url between", value1, value2, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataToUrlNotBetween(String value1, String value2) {
            addCriterion("data_to_url not between", value1, value2, "dataToUrl");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowIsNull() {
            addCriterion("data_index_show is null");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowIsNotNull() {
            addCriterion("data_index_show is not null");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowEqualTo(Integer value) {
            addCriterion("data_index_show =", value, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowNotEqualTo(Integer value) {
            addCriterion("data_index_show <>", value, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowGreaterThan(Integer value) {
            addCriterion("data_index_show >", value, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_index_show >=", value, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowLessThan(Integer value) {
            addCriterion("data_index_show <", value, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowLessThanOrEqualTo(Integer value) {
            addCriterion("data_index_show <=", value, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowIn(List<Integer> values) {
            addCriterion("data_index_show in", values, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowNotIn(List<Integer> values) {
            addCriterion("data_index_show not in", values, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowBetween(Integer value1, Integer value2) {
            addCriterion("data_index_show between", value1, value2, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataIndexShowNotBetween(Integer value1, Integer value2) {
            addCriterion("data_index_show not between", value1, value2, "dataIndexShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowIsNull() {
            addCriterion("data_middle_show is null");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowIsNotNull() {
            addCriterion("data_middle_show is not null");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowEqualTo(Integer value) {
            addCriterion("data_middle_show =", value, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowNotEqualTo(Integer value) {
            addCriterion("data_middle_show <>", value, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowGreaterThan(Integer value) {
            addCriterion("data_middle_show >", value, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_middle_show >=", value, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowLessThan(Integer value) {
            addCriterion("data_middle_show <", value, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowLessThanOrEqualTo(Integer value) {
            addCriterion("data_middle_show <=", value, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowIn(List<Integer> values) {
            addCriterion("data_middle_show in", values, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowNotIn(List<Integer> values) {
            addCriterion("data_middle_show not in", values, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowBetween(Integer value1, Integer value2) {
            addCriterion("data_middle_show between", value1, value2, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataMiddleShowNotBetween(Integer value1, Integer value2) {
            addCriterion("data_middle_show not between", value1, value2, "dataMiddleShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowIsNull() {
            addCriterion("data_read_show is null");
            return (Criteria) this;
        }

        public Criteria andDataReadShowIsNotNull() {
            addCriterion("data_read_show is not null");
            return (Criteria) this;
        }

        public Criteria andDataReadShowEqualTo(Integer value) {
            addCriterion("data_read_show =", value, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowNotEqualTo(Integer value) {
            addCriterion("data_read_show <>", value, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowGreaterThan(Integer value) {
            addCriterion("data_read_show >", value, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_read_show >=", value, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowLessThan(Integer value) {
            addCriterion("data_read_show <", value, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowLessThanOrEqualTo(Integer value) {
            addCriterion("data_read_show <=", value, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowIn(List<Integer> values) {
            addCriterion("data_read_show in", values, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowNotIn(List<Integer> values) {
            addCriterion("data_read_show not in", values, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowBetween(Integer value1, Integer value2) {
            addCriterion("data_read_show between", value1, value2, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataReadShowNotBetween(Integer value1, Integer value2) {
            addCriterion("data_read_show not between", value1, value2, "dataReadShow");
            return (Criteria) this;
        }

        public Criteria andDataPriorityIsNull() {
            addCriterion("data_priority is null");
            return (Criteria) this;
        }

        public Criteria andDataPriorityIsNotNull() {
            addCriterion("data_priority is not null");
            return (Criteria) this;
        }

        public Criteria andDataPriorityEqualTo(Integer value) {
            addCriterion("data_priority =", value, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityNotEqualTo(Integer value) {
            addCriterion("data_priority <>", value, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityGreaterThan(Integer value) {
            addCriterion("data_priority >", value, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_priority >=", value, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityLessThan(Integer value) {
            addCriterion("data_priority <", value, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityLessThanOrEqualTo(Integer value) {
            addCriterion("data_priority <=", value, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityIn(List<Integer> values) {
            addCriterion("data_priority in", values, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityNotIn(List<Integer> values) {
            addCriterion("data_priority not in", values, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityBetween(Integer value1, Integer value2) {
            addCriterion("data_priority between", value1, value2, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataPriorityNotBetween(Integer value1, Integer value2) {
            addCriterion("data_priority not between", value1, value2, "dataPriority");
            return (Criteria) this;
        }

        public Criteria andDataStatusIsNull() {
            addCriterion("data_status is null");
            return (Criteria) this;
        }

        public Criteria andDataStatusIsNotNull() {
            addCriterion("data_status is not null");
            return (Criteria) this;
        }

        public Criteria andDataStatusEqualTo(Integer value) {
            addCriterion("data_status =", value, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusNotEqualTo(Integer value) {
            addCriterion("data_status <>", value, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusGreaterThan(Integer value) {
            addCriterion("data_status >", value, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("data_status >=", value, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusLessThan(Integer value) {
            addCriterion("data_status <", value, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusLessThanOrEqualTo(Integer value) {
            addCriterion("data_status <=", value, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusIn(List<Integer> values) {
            addCriterion("data_status in", values, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusNotIn(List<Integer> values) {
            addCriterion("data_status not in", values, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusBetween(Integer value1, Integer value2) {
            addCriterion("data_status between", value1, value2, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andDataStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("data_status not between", value1, value2, "dataStatus");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andBeginAtIsNull() {
            addCriterion("begin_at is null");
            return (Criteria) this;
        }

        public Criteria andBeginAtIsNotNull() {
            addCriterion("begin_at is not null");
            return (Criteria) this;
        }

        public Criteria andBeginAtEqualTo(Date value) {
            addCriterion("begin_at =", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtNotEqualTo(Date value) {
            addCriterion("begin_at <>", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtGreaterThan(Date value) {
            addCriterion("begin_at >", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtGreaterThanOrEqualTo(Date value) {
            addCriterion("begin_at >=", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtLessThan(Date value) {
            addCriterion("begin_at <", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtLessThanOrEqualTo(Date value) {
            addCriterion("begin_at <=", value, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtIn(List<Date> values) {
            addCriterion("begin_at in", values, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtNotIn(List<Date> values) {
            addCriterion("begin_at not in", values, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtBetween(Date value1, Date value2) {
            addCriterion("begin_at between", value1, value2, "beginAt");
            return (Criteria) this;
        }

        public Criteria andBeginAtNotBetween(Date value1, Date value2) {
            addCriterion("begin_at not between", value1, value2, "beginAt");
            return (Criteria) this;
        }

        public Criteria andEndAtIsNull() {
            addCriterion("end_at is null");
            return (Criteria) this;
        }

        public Criteria andEndAtIsNotNull() {
            addCriterion("end_at is not null");
            return (Criteria) this;
        }

        public Criteria andEndAtEqualTo(Date value) {
            addCriterion("end_at =", value, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtNotEqualTo(Date value) {
            addCriterion("end_at <>", value, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtGreaterThan(Date value) {
            addCriterion("end_at >", value, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtGreaterThanOrEqualTo(Date value) {
            addCriterion("end_at >=", value, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtLessThan(Date value) {
            addCriterion("end_at <", value, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtLessThanOrEqualTo(Date value) {
            addCriterion("end_at <=", value, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtIn(List<Date> values) {
            addCriterion("end_at in", values, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtNotIn(List<Date> values) {
            addCriterion("end_at not in", values, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtBetween(Date value1, Date value2) {
            addCriterion("end_at between", value1, value2, "endAt");
            return (Criteria) this;
        }

        public Criteria andEndAtNotBetween(Date value1, Date value2) {
            addCriterion("end_at not between", value1, value2, "endAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}