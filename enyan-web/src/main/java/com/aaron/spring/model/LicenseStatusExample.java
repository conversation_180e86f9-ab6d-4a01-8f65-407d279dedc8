package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LicenseStatusExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public LicenseStatusExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedIsNull() {
            addCriterion("license_updated is null");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedIsNotNull() {
            addCriterion("license_updated is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedEqualTo(Date value) {
            addCriterion("license_updated =", value, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedNotEqualTo(Date value) {
            addCriterion("license_updated <>", value, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedGreaterThan(Date value) {
            addCriterion("license_updated >", value, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedGreaterThanOrEqualTo(Date value) {
            addCriterion("license_updated >=", value, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedLessThan(Date value) {
            addCriterion("license_updated <", value, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedLessThanOrEqualTo(Date value) {
            addCriterion("license_updated <=", value, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedIn(List<Date> values) {
            addCriterion("license_updated in", values, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedNotIn(List<Date> values) {
            addCriterion("license_updated not in", values, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedBetween(Date value1, Date value2) {
            addCriterion("license_updated between", value1, value2, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andLicenseUpdatedNotBetween(Date value1, Date value2) {
            addCriterion("license_updated not between", value1, value2, "licenseUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedIsNull() {
            addCriterion("status_updated is null");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedIsNotNull() {
            addCriterion("status_updated is not null");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedEqualTo(Date value) {
            addCriterion("status_updated =", value, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedNotEqualTo(Date value) {
            addCriterion("status_updated <>", value, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedGreaterThan(Date value) {
            addCriterion("status_updated >", value, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedGreaterThanOrEqualTo(Date value) {
            addCriterion("status_updated >=", value, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedLessThan(Date value) {
            addCriterion("status_updated <", value, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedLessThanOrEqualTo(Date value) {
            addCriterion("status_updated <=", value, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedIn(List<Date> values) {
            addCriterion("status_updated in", values, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedNotIn(List<Date> values) {
            addCriterion("status_updated not in", values, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedBetween(Date value1, Date value2) {
            addCriterion("status_updated between", value1, value2, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andStatusUpdatedNotBetween(Date value1, Date value2) {
            addCriterion("status_updated not between", value1, value2, "statusUpdated");
            return (Criteria) this;
        }

        public Criteria andDeviceCountIsNull() {
            addCriterion("device_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceCountIsNotNull() {
            addCriterion("device_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceCountEqualTo(Integer value) {
            addCriterion("device_count =", value, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountNotEqualTo(Integer value) {
            addCriterion("device_count <>", value, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountGreaterThan(Integer value) {
            addCriterion("device_count >", value, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("device_count >=", value, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountLessThan(Integer value) {
            addCriterion("device_count <", value, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountLessThanOrEqualTo(Integer value) {
            addCriterion("device_count <=", value, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountIn(List<Integer> values) {
            addCriterion("device_count in", values, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountNotIn(List<Integer> values) {
            addCriterion("device_count not in", values, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountBetween(Integer value1, Integer value2) {
            addCriterion("device_count between", value1, value2, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andDeviceCountNotBetween(Integer value1, Integer value2) {
            addCriterion("device_count not between", value1, value2, "deviceCount");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndIsNull() {
            addCriterion("potential_rights_end is null");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndIsNotNull() {
            addCriterion("potential_rights_end is not null");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndEqualTo(Date value) {
            addCriterion("potential_rights_end =", value, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndNotEqualTo(Date value) {
            addCriterion("potential_rights_end <>", value, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndGreaterThan(Date value) {
            addCriterion("potential_rights_end >", value, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndGreaterThanOrEqualTo(Date value) {
            addCriterion("potential_rights_end >=", value, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndLessThan(Date value) {
            addCriterion("potential_rights_end <", value, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndLessThanOrEqualTo(Date value) {
            addCriterion("potential_rights_end <=", value, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndIn(List<Date> values) {
            addCriterion("potential_rights_end in", values, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndNotIn(List<Date> values) {
            addCriterion("potential_rights_end not in", values, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndBetween(Date value1, Date value2) {
            addCriterion("potential_rights_end between", value1, value2, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andPotentialRightsEndNotBetween(Date value1, Date value2) {
            addCriterion("potential_rights_end not between", value1, value2, "potentialRightsEnd");
            return (Criteria) this;
        }

        public Criteria andLicenseRefIsNull() {
            addCriterion("license_ref is null");
            return (Criteria) this;
        }

        public Criteria andLicenseRefIsNotNull() {
            addCriterion("license_ref is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseRefEqualTo(String value) {
            addCriterion("license_ref =", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefNotEqualTo(String value) {
            addCriterion("license_ref <>", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefGreaterThan(String value) {
            addCriterion("license_ref >", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefGreaterThanOrEqualTo(String value) {
            addCriterion("license_ref >=", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefLessThan(String value) {
            addCriterion("license_ref <", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefLessThanOrEqualTo(String value) {
            addCriterion("license_ref <=", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefLike(String value) {
            addCriterion("license_ref like", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefNotLike(String value) {
            addCriterion("license_ref not like", value, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefIn(List<String> values) {
            addCriterion("license_ref in", values, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefNotIn(List<String> values) {
            addCriterion("license_ref not in", values, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefBetween(String value1, String value2) {
            addCriterion("license_ref between", value1, value2, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andLicenseRefNotBetween(String value1, String value2) {
            addCriterion("license_ref not between", value1, value2, "licenseRef");
            return (Criteria) this;
        }

        public Criteria andRightsEndIsNull() {
            addCriterion("rights_end is null");
            return (Criteria) this;
        }

        public Criteria andRightsEndIsNotNull() {
            addCriterion("rights_end is not null");
            return (Criteria) this;
        }

        public Criteria andRightsEndEqualTo(Date value) {
            addCriterion("rights_end =", value, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndNotEqualTo(Date value) {
            addCriterion("rights_end <>", value, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndGreaterThan(Date value) {
            addCriterion("rights_end >", value, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndGreaterThanOrEqualTo(Date value) {
            addCriterion("rights_end >=", value, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndLessThan(Date value) {
            addCriterion("rights_end <", value, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndLessThanOrEqualTo(Date value) {
            addCriterion("rights_end <=", value, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndIn(List<Date> values) {
            addCriterion("rights_end in", values, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndNotIn(List<Date> values) {
            addCriterion("rights_end not in", values, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndBetween(Date value1, Date value2) {
            addCriterion("rights_end between", value1, value2, "rightsEnd");
            return (Criteria) this;
        }

        public Criteria andRightsEndNotBetween(Date value1, Date value2) {
            addCriterion("rights_end not between", value1, value2, "rightsEnd");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}