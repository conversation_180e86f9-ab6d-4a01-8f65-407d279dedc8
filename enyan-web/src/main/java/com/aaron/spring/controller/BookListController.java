package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.common.NameAndValueDTO;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.api.v4.model.RestBookSet;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.BookWebInfo;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookList;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanBookListService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/17
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/bookListAdmin")
public class BookListController extends BaseController{
    @Resource
    private EnyanBookListService enyanBookListService;

    @Resource
    private EnyanBookService enyanBookService;

    @RequestMapping(value = "/list")
    public String discountPage(HttpServletRequest req, EnyanBookList record, ModelMap modelMap){
        log.debug("list");
        try {
            if (null == record){
                record = new EnyanBookList();
            }
            if (null == record.getPage()){
                record.setPage(new Page());
            }
            Map<String, Object> queryParams = new HashMap<>();

            // 获取分页参数
            String total = req.getParameter("total");
            String currentPage = req.getParameter("pageNo");

            if (StringUtils.hasLength(total)) {
                record.getPage().setTotalRecord(Integer.parseInt(total));
            }
            if (StringUtils.hasLength(currentPage)) {
                record.getPage().setCurrentPage(Integer.parseInt(currentPage));
            }

            // 高级查询条件：
            String searchText = req.getParameter("searchText");
            String searchType = req.getParameter("searchType");
            if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
                switch (searchType){
                    case "0":
                        record.setSetName(searchText);
                        break;
                }
                queryParams.put("searchText",searchText);
                queryParams.put("searchType",searchType);
            }
            record.addOrder(new OrderObj("show_order", InterfaceContant.OrderBy.DESC));
            Page<EnyanBookList> page = enyanBookListService.queryRecords(record.getPage(), record);

            record.setPage(page);
            record.excutePageLand(queryParams);

            modelMap.addAttribute("list",page.getRecords());
            modelMap.addAttribute("pageLand", record.getPageLand());

            modelMap.addAttribute("record", record);
            modelMap.addAttribute("explan","书单管理");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "admin/bookListList";
    }
    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("getById");
        EnyanBookList bookList = enyanBookListService.queryRecordByPrimaryKey(id).getResult();
        if (null != bookList.getBookWebInfo()){
            bookList.setBookWebSalePapers(bookList.getBookWebInfo().getSalePapersJson());
            bookList.setBookWebVersions(bookList.getBookWebInfo().getVersionsJson());
        }
        modelMap.addAttribute("enyanBookList",bookList);
        return "admin/bookListAdd";
    }

    @RequestMapping(value = "/set-{id}", method = RequestMethod.GET)
    public String setById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("setById");
        EnyanBookList record = enyanBookListService.queryRecordByPrimaryKey(id).getResult();
        modelMap.addAttribute("bookIDsList", Constant.booksList);
        modelMap.addAttribute("record",record);

        String bookIdText = record.getBookIdText();
        if (StringUtils.hasLength(bookIdText) == false){
            bookIdText = "";
        }
        String[] ids = bookIdText.split(",");

        record.setBookIDs(ids);
        record.setBookIDsOld(ids);
        return "admin/bookListEdit";
    }
    /*
    @RequestMapping(value = "/associate-{id}", method = RequestMethod.GET)
    public String associateById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("associate");
        EnyanBookList EnyanBookList = enyanBookListService.queryRecordByPrimaryKey(id).getResult();
        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_1);
        enyanBook.setDiscountSingleId(id);
        enyanBook.setDiscountSingleStartTime(EnyanBookList.getStartTime());
        enyanBook.setDiscountSingleEndTime(EnyanBookList.getEndTime());
        enyanBook.setDiscountSingleValue(EnyanBookList.getDiscountSingleValue());
        enyanBook.setDiscountSingleType(Constant.BYTE_VALUE_0);
        enyanBook.setDiscountSingleDescription(enyanBook.getBookDiscountDescription());
        enyanBookService.updateBookSingleDiscountAssociateNDiscount(enyanBook);
        return "redirect:/closePage";
    }*/

    /**
     * <p>保存书单与信息的关联</p>
     * @param request
     * @param enyanBookList
     * @param modelMap
     * @return java.lang.String
     * @since : 2023/5/23
     **/
    @RequestMapping(value = "/saveBookList", method = RequestMethod.POST)
    public String saveBookList(HttpServletRequest request, EnyanBookList enyanBookList, ModelMap modelMap){
        log.debug("method saveBookList："+ enyanBookList);
        /*
        //EnyanBookList record = enyanBookListService.queryRecordByPrimaryKey(enyanBookList.getSetId()).getResult();

        String[] ids = enyanBookList.getBookIDs();
        String[] idsOld = enyanBookList.getBookIDsOld();
        if (idsOld == null){
            idsOld = new String[]{};
        }
        String bookIdText = org.apache.commons.lang3.StringUtils.join(ids,",");
        EnyanBookList update = new EnyanBookList();
        update.setSetId(enyanBookList.getSetId());
        update.setBookIdText(bookIdText);
        enyanBookListService.updateRecord(update);*/
        enyanBookListService.updateRecord(enyanBookList);
        return "redirect:/closePage";
    }

    @RequestMapping("/del-{id}")
    public String delById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("method delById");
        enyanBookListService.deleteRecordByPrimaryKey(id);
        return "redirect:/bookListAdmin/list";
    }

    @ResponseBody
    @RequestMapping(value = "/reset")
    public ExecuteResult<String> reset(@RequestBody RestBookSet restObj, ModelMap modelMap,
                                       HttpServletRequest request, HttpServletResponse response){
        Long id = restObj.getSetId();

        ExecuteResult<String> result = new ExecuteResult<>();
        log.debug("SetId:{}",id);
        if (null == id ){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return result;
        }
        enyanBookListService.resetCache(id);
        return result;
    }

    @RequestMapping("/addUI")
    public String addUI(EnyanBookList enyanBookList){
        log.debug("method addUI");
        enyanBookList.setIsValid(1);
        enyanBookList.setCanAllBuy(1);
        return "admin/bookListAdd";
    }

    /**
     * <p>添加书单信息</p>
     * @param request
     * @param enyanBookList
     * @param modelMap
     * @return java.lang.String
     * @since : 2023/5/23
     **/
    @RequestMapping(value = "/saveSet", method = RequestMethod.POST)
    public String saveSet(HttpServletRequest request, EnyanBookList enyanBookList, ModelMap modelMap){
        log.debug("method saveSet："+ enyanBookList);

        if (StringUtils.hasLength(enyanBookList.getSetName()) == false){
            this.setErrorMsg(modelMap,"请填写书单名");
            return "admin/bookListAdd";
        }
        if (null == enyanBookList.getDiscountValue()){
            enyanBookList.setDiscountValue(100);
        }
        if (enyanBookList.getDiscountValue() < 100){
            enyanBookList.setIsDiscountValid(1);
        }else{
            enyanBookList.setIsDiscountValid(0);
        }
        if (StringUtils.hasLength(enyanBookList.getBookWebSalePapers()) || StringUtils.hasLength(enyanBookList.getBookWebVersions())){
            BookWebInfo bookWebInfo = new BookWebInfo();
            if (StringUtils.hasLength(enyanBookList.getBookWebSalePapers())){
                NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBookList.getBookWebSalePapers(),NameAndValueDTO.class);
                if (null != nameAndValueDTO){
                    bookWebInfo.setSalePapers(nameAndValueDTO.getValues());
                }
            }
            if (StringUtils.hasLength(enyanBookList.getBookWebVersions())){
                NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBookList.getBookWebVersions(),NameAndValueDTO.class);
                if (null != nameAndValueDTO){
                    bookWebInfo.setVersions(nameAndValueDTO.getValues());
                }
            }
            enyanBookList.setBookWeb(JSON.toJSONString(bookWebInfo));
        }
        if (null == enyanBookList.getSetId()){
            this.setSuccessMsg(modelMap,"添加书单成功");
            enyanBookListService.addRecord(enyanBookList);
        }else {
            this.setSuccessMsg(modelMap,"修改书单成功");
            enyanBookListService.updateRecord(enyanBookList);
        }
        return "redirect:/bookListAdmin/list";
    }

}
