package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.security.function.domain.IUser;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.common.WebUtil;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.AuthUser;
import com.aaron.spring.model.EmailHistory;
import com.aaron.spring.model.EmailStatusDTO;
import com.aaron.spring.service.AuthUserService;
import com.aaron.spring.service.EmailHistoryService;
import com.aaron.spring.service.EmailService;
import com.aaron.util.EmailUtils;
import com.aaron.util.EmailValidatorUtil;
import com.aaron.util.UserUtils;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.LocaleContextResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/29
 * @Modified By:
 */
@Controller
public class HomeController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String EMAIL_TITLE_REG_ACTIVE = "感谢您注册恩道电子书账号";//邮件点击,注册激活,title
    public static final String EMAIL_URL_REG_ACTIVE = "ra?emailCode=";//邮件点击,注册激活
    public static final String EMAIL_FTL_REG_ACTIVE = "email-active.ftl";//邮件点击,注册激活,ftl
    public static final String EMAIL_TITLE_REG_ACTIVE_EN = "Thank you for signing up for Inspirata eBooks";//邮件点击,注册激活,title
    public static final String EMAIL_FTL_REG_ACTIVE_EN = "email-active-en.ftl";//邮件点击,注册激活,ftl

    public static final String EMAIL_TITLE_PWD_FORGET = "恩道电子书-找回密码";//邮件点击,邮件密码找回,title
    public static final String EMAIL_URL_PWD_FORGET = "pf?emailCode=";//邮件点击,邮件密码找回
    public static final String EMAIL_FTL_PWD_FORGET = "pwd-forget.ftl";//邮件点击,邮件密码找回,ftl
    public static final String EMAIL_TITLE_PWD_FORGET_EN = "Inspirata eBooks-Retrieve Password";//邮件点击,邮件密码找回,title
    public static final String EMAIL_FTL_PWD_FORGET_EN = "pwd-forget-en.ftl";//邮件点击,邮件密码找回,ftl

    public static final String EMAIL_CODE = "emailCode";
    public static final String EMAIL_OPERATION = "emailOperation";
    public static final String EMAIL_EXPLAN = "emailExplan";
    public static final String EMAIL_STEP = "emailStep";
    public static final String EMAIL_ERROR = "emailError";

    public static final String SESSION_EMAIL_STATUS = "emailStatus";

    public static final int EMAIL_DONE_TIME_BETWEEN = 1000 * 60 * 60;//Email链接有效时间：1 小时
    public static final int EMAIL_TOO_MUCH_TIME_BETWEEN = 1000 * 60 * 10;//Email频繁时间：10 分钟

    @Resource
    private AuthUserService authUserService;

    @Resource
    private EmailHistoryService emailHistoryService;

    @Resource
    private EmailService emailService;

    @RequestMapping("/login")
    public String login(ModelMap modelMap, HttpServletRequest request) {
        if (UserUtils.isAnonymous() == false){
            String toUrl = request.getParameter("targetUrl");
            request.getSession().removeAttribute(InterfaceContant.WebConstant.LOGIN_BACK_URL);
            if (StringUtils.isNotBlank(toUrl)){
                return "redirect:"+toUrl;
            }
            //modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "redirect:/index";
        }
        //String requestURI = request.getRequestURI();
        String referer = request.getHeader("referer");
        if (StringUtils.indexOf(referer, "emailSuccess") != -1){
            referer = "/index";
        }
        /*
        if (StringUtils.isNotBlank(referer)){
            if (!referer.contains(WebUtil.getBasePath())){
                referer = "/index";
            }
        }*/
        request.getSession().setAttribute(InterfaceContant.WebConstant.LOGIN_BACK_URL,referer);
        return "login";
    }

    @RequestMapping("/reg")
    public String reg(ModelMap modelMap) {
        return "reg";
    }

    @RequestMapping("/404")
    public String page404(ModelMap modelMap) {
        return "404";
    }
    @RequestMapping("/500")
    public String page500(ModelMap modelMap) {
        return "500";
    }

    @RequestMapping("/regSuccess")
    public String regSuccess(ModelMap modelMap) {
        return "regSuccess";
    }

    @RequestMapping("/emailSuccess")
    public String emailSuccess(ModelMap modelMap) {
        return "emailSuccess";
    }

    @RequestMapping("/pwdForget")
    public String pwdForget(ModelMap modelMap) {
        return "pwdForget";
    }

    @RequestMapping("/success")
    public String success(ModelMap modelMap) {
        return "success";
    }

    @RequestMapping("/fail")
    public String fail(ModelMap modelMap) {
        return "fail";
    }

    @RequestMapping(value = "/regAction", method = RequestMethod.POST)
    public String regAction(AuthUser authUser, ModelMap modelMap, HttpServletRequest request) {
        //logger.info("method regAction："+authUser);
        modelMap.addAttribute("authUser", authUser);
        if (StringUtils.isBlank(authUser.getUserName())) {
            this.setErrorMsg(modelMap, this.getMessage("error.nickname.null", request));
            return "reg";
        }
        if (StringUtils.isBlank(authUser.getEmail())) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.null", request));
            return "reg";
        }
        if (!EmailValidatorUtil.validate(authUser.getEmail())) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.invalid", request));
            return "reg";
        }
        if (StringUtils.isBlank(authUser.getUserPassword()) || StringUtils.isBlank(authUser.getUserPasswordAgain())) {
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.null", request));
            return "reg";
        }
        if (authUser.getUserName().length()>14){
            this.setErrorMsg(modelMap, this.getMessage("error.nickname.toolong", request));
            return "reg";
        }
        if (authUser.getUserPassword().length() < 6 || authUser.getUserPassword().length() > 20) {
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.format", request));
            return "reg";
        }
        if (!authUser.getUserPassword().equals(authUser.getUserPasswordAgain())) {
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.again", request));
            return "reg";
        }
        /*if (null != authUserService.getUserByName(authUser.getUserName()).getResult()){
            this.setErrorMsg(modelMap, "该昵称已被占用！请重新输入！");
            return "reg";
        }*/
        if (null != authUserService.getUserByEmail(authUser.getEmail()).getResult()) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.exist", request));
            return "reg";
        }
        authUserService.reg(authUser);

        int yearMonthDay = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMMdd"));
        EmailHistory history = new EmailHistory();
        history.setEmail(authUser.getEmail());
        history.setEmailType(EBookConstant.EmailType.REG_ACTIVE);
        history.setSendAt(System.currentTimeMillis());
        history.setRepeatTimes(0);
        history.setYearMonthDay(yearMonthDay);
        emailHistoryService.addRecord(history);

        String json = JSONObject.toJSONString(history);
        EmailStatusDTO dto = new EmailStatusDTO();
        dto.setStep(dto.STEP_ACTIVE);
        dto.setHead(this.getMessage("email.verify",request));

        try {
            String emailCode = EmailUtils.encriptEmailCode(json, Constant.EMAIL_SEND_REG_ACTIVE_AES_KEY);
            //logger.info("json:"+json);
            //logger.info("emailCode:"+emailCode);

            emailCode = URLEncoder.encode(emailCode, "UTF-8");

            request.getSession().setAttribute("emailCode", emailCode);

            dto.setEmailCode(emailCode);
            this.sendActiveEmail(history, request, authUserService);
        } catch (Exception e) {
            e.printStackTrace();
        }
        dto.setEmailOperation("emailActive");
        dto.setExplan(this.getMessage("success.email.send",new String[]{history.getEmail()},request));
        dto.setSuccess(true);
        request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
        return "redirect:/emailSuccess";
    }

    @RequestMapping(value = {"/", "/home"}, method = RequestMethod.GET)
    public String homePage(ModelMap model) {
        model.addAttribute("user", getPrincipal());
        return "redirect:/shop/index";
    }

    @RequestMapping(value = "/logout", method = RequestMethod.GET)
    public String logoutPage(HttpServletRequest request, HttpServletResponse response) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            new SecurityContextLogoutHandler().logout(request, response, auth);
        }
        //LogoutConfigurer()

        return "redirect:/login?logout";
    }

    @RequestMapping(value = "/accessDenied")
    public String accessDeniedPage(ModelMap model) {
        if (null != getPrincipal()){
            model.addAttribute("user", getPrincipal());
        }
        return "accessDenied";
    }

    @RequestMapping(value = "/admin", method = RequestMethod.GET)
    public String adminPage(ModelMap model) {
        model.addAttribute("user", getPrincipal());
        return "admin/adminMain";
    }

    @RequestMapping(value = "/vendor", method = RequestMethod.GET)
    public String vendorPage(ModelMap model) {
        model.addAttribute("user", getPrincipal());
        return "vendor/adminMain";
    }

    @RequestMapping(value = "/operation", method = RequestMethod.GET)
    public String operationPage(ModelMap model) {
        model.addAttribute("user", getPrincipal());
        return "operation/adminMain";
    }

    @RequestMapping(value = "/finance", method = RequestMethod.GET)
    public String financePage(ModelMap model) {
        model.addAttribute("user", getPrincipal());
        return "finance/adminMain";
    }

    @RequestMapping(value = "/db", method = RequestMethod.GET)
    public String dbaPage(ModelMap model) {
        model.addAttribute("user", getPrincipal());
        return "dba";
    }

    @RequestMapping(value = "/managerIndex", method = RequestMethod.GET)
    public String managerIndex(ModelMap model) {
        model.addAttribute("user", getPrincipal());
        return "admin/adminMain";
    }

    @RequestMapping(value = "/closePage")
    public String closePage(ModelMap model) {
        if (null != getPrincipal()){
            model.addAttribute("user", getPrincipal());
        }
        return "closePage";
    }
    /**
     *
     *  通过email重新发送激活email
     * @param emailCode
     * @param modelMap
     * @param request
     * @Date: 2018/4/5
     */
    @RequestMapping(value = "/earbe")
    public String emailActiveResendByEmail(@RequestParam(value = "emailCode", required = false) String emailCode,
                              ModelMap modelMap, HttpServletRequest request) {
        EmailStatusDTO dto = new EmailStatusDTO();
        dto.setStep(dto.STEP_ACTIVE);
        dto.setHead(this.getMessage("email.verify",request));
        try {
            if (StringUtils.isBlank(emailCode)){
                dto.setExplan(this.getMessage("error.email.activefail", request));
                request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
                return "redirect:/emailSuccess";
            }
            int yearMonthDay = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            EmailHistory historyNew = emailHistoryService.getEmailHistoryByName(emailCode, EBookConstant.EmailType.REG_ACTIVE,yearMonthDay).getResult();
            Boolean shouldSendEmail = false;
            if (null == historyNew) {//没有email发送记录
                historyNew = new EmailHistory();
                historyNew.setSendAt(System.currentTimeMillis());
                historyNew.setYearMonthDay(yearMonthDay);
                historyNew.setRepeatTimes(0);
                historyNew.setEmail(emailCode);
                historyNew.setEmailType(EBookConstant.EmailType.REG_ACTIVE);
                shouldSendEmail = true;

                emailHistoryService.addRecord(historyNew);
            }else {
                long between = System.currentTimeMillis() - historyNew.getSendAt();
                if (between < EMAIL_TOO_MUCH_TIME_BETWEEN || historyNew.getRepeatTimes() > 10) {//小于10min 或者 多余10次
                    dto.setSuccess(true);
                    dto.setExplan(this.getMessage("error.email.toomuch", request));
                } else {
                    historyNew.setSendAt(System.currentTimeMillis());
                    emailHistoryService.refreshEmailHistory(historyNew.getEmailId());
                    shouldSendEmail = true;
                }
            }
            if (shouldSendEmail){
                this.sendActiveEmail(historyNew, request, authUserService);

                dto.setExplan(this.getMessage("success.email.send",new String[]{historyNew.getEmail()},request));
                dto.setSuccess(true);

                String json = JSONObject.toJSONString(historyNew);

                emailCode = EmailUtils.encriptEmailCode(json, Constant.EMAIL_SEND_REG_ACTIVE_AES_KEY);

                emailCode = URLEncoder.encode(emailCode, "UTF-8");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        dto.setEmailCode(emailCode);
        dto.setEmailOperation("emailActive");
        request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);

        return "redirect:/emailSuccess";
    }
    /**
     *
     * 加密的history
     * @param emailCode
     * @param modelMap
     * @param request
     * @Date: 2018/4/5
     */
    @RequestMapping(value = "/emailActive")
    public String emailActive(@RequestParam(value = "emailCode", required = false) String emailCode,
                              ModelMap modelMap, HttpServletRequest request) {
        EmailStatusDTO dto = new EmailStatusDTO();
        dto.setStep(dto.STEP_ACTIVE);
        dto.setHead(this.getMessage("email.verify",request));
        try {
            if (StringUtils.isBlank(emailCode)){
                dto.setExplan(this.getMessage("error.email.activefail", request));
                request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
                return "redirect:/emailSuccess";
            }
            String json = EmailUtils.decryptEmail(emailCode, Constant.EMAIL_SEND_REG_ACTIVE_AES_KEY);
            EmailHistory history = JSONObject.parseObject(json, EmailHistory.class);
            EmailHistory historyNew = null;
            if (null != history) {
                historyNew = emailHistoryService.queryRecordByPrimaryKey(history.getEmailId()).getResult();
            }

            if (null == historyNew) {//没有email发送记录
                dto.setExplan(this.getMessage("error.email.activefail", request));
            } else {
                long between = System.currentTimeMillis() - historyNew.getSendAt();
                if (between < EMAIL_TOO_MUCH_TIME_BETWEEN || historyNew.getRepeatTimes() > 10) {//小于10min 或者 多余10次

                    dto.setExplan(this.getMessage("error.email.toomuch", request));
                    dto.setSuccess(true);
                } else {
                    historyNew.setSendAt(System.currentTimeMillis());

                    emailHistoryService.refreshEmailHistory(historyNew.getEmailId());
                    this.sendActiveEmail(historyNew, request,authUserService);

                    json = JSONObject.toJSONString(historyNew);

                    emailCode = EmailUtils.encriptEmailCode(json, Constant.EMAIL_SEND_REG_ACTIVE_AES_KEY);


                    dto.setExplan(this.getMessage("success.email.send",new String[]{history.getEmail()},request));
                    dto.setSuccess(true);

                    //request.getSession().removeAttribute("SPRING_SECURITY_LAST_EXCEPTION");
                }
                emailCode = URLEncoder.encode(emailCode, "UTF-8");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        dto.setEmailCode(emailCode);
        dto.setEmailOperation("emailActive");
        request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);

        return "redirect:/emailSuccess";
    }

    /**
     * @param emailCode
     * @param modelMap
     * @param request
     *
     *  实施用户激活(regActive)
     * @Date: 2018/3/2
     */
    @RequestMapping(value = "/ra")
    public String ra(@RequestParam(value = "emailCode", required = false) String emailCode,
                     ModelMap modelMap, HttpServletRequest request) {
        EmailStatusDTO dto = new EmailStatusDTO();
        dto.setStep(dto.STEP_ACTIVE);
        dto.setHead(this.getMessage("email.verify",request));

        try {

            String json = EmailUtils.decryptEmail(emailCode, Constant.EMAIL_REG_ACTIVE_AES_KEY);
            EmailHistory history = JSONObject.parseObject(json, EmailHistory.class);
            EmailHistory historyNew = null;
            if (null != history) {
                historyNew = emailHistoryService.queryRecordByPrimaryKey(history.getEmailId()).getResult();
            }

            if (null == historyNew) {
                dto.setExplan(this.getMessage("error.email.activefail", request));
            } else {
                long between = System.currentTimeMillis() - history.getSendAt();
                if (between < EMAIL_DONE_TIME_BETWEEN) {//小于1小时
                    authUserService.activeUser(historyNew.getEmail());
                    dto.setEnd(true);
                    dto.setSuccess(true);
                    dto.setExplan(this.getMessage("success.done", request));
                } else {
                    dto.setExplan(this.getMessage("error.done.timeout", request));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
        return "redirect:/emailSuccess";
    }

    private String getPrincipal() {
        if (null == SecurityContextHolder.getContext().getAuthentication()){
            return "";
        }
        String userName = null;
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (null != principal){
            if (principal instanceof IUser) {
                userName = ((IUser) principal).getUsername();
            } else {
                userName = principal.toString();
            }
        }
        return userName;
    }

    @RequestMapping(value = "/pwdForgetAction", method = RequestMethod.POST)
    public String pwdForgetAction(String email, ModelMap modelMap, HttpServletRequest request) {
        //logger.info("method regAction："+authUser);
        EmailStatusDTO dto = new EmailStatusDTO();
        dto.setStep(dto.STEP_PASSWD);
        dto.setHead(this.getMessage("passwd.get",request));

        if (StringUtils.isBlank(email)) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.null", request));
            return "pwdForget";
        }
        if (!EmailValidatorUtil.validate(email)) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.invalid", request));
            return "pwdForget";
        }
        AuthUser authUser = authUserService.getUserByEmail(email).getResult();
        if (null == authUser) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.notexist", request));
            return "pwdForget";
        }
        boolean success = false;
        try {
            int yearMonthDay = Integer.parseInt(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            EmailHistory history = emailHistoryService.getEmailHistoryByName(email, EBookConstant.EmailType.PASSWORD_FORGET,yearMonthDay).getResult();
            if (null == history) {
                history = new EmailHistory();
                history.setEmail(email);
                history.setEmailType(EBookConstant.EmailType.PASSWORD_FORGET);
                history.setSendAt(System.currentTimeMillis());
                history.setRepeatTimes(0);
                history.setYearMonthDay(yearMonthDay);
                emailHistoryService.addRecord(history);
                success = true;
            } else {
                long between = System.currentTimeMillis() - history.getSendAt();
                if (between < EMAIL_TOO_MUCH_TIME_BETWEEN || history.getRepeatTimes() >= 10) {//小于10min 或者 多余10次
                    dto.setExplan(this.getMessage("error.email.toomuch", request));
                } else {
                    history.setSendAt(System.currentTimeMillis());
                    emailHistoryService.refreshEmailHistory(history.getEmailId());
                    success = true;
                }
            }
            if (success) {
                this.sendActiveEmail(history, request,authUserService);
                dto.setExplan(this.getMessage("success.email.send",new String[]{history.getEmail()}, request));
            }
            String json = JSONObject.toJSONString(history);

            String emailCode = EmailUtils.encriptEmailCode(json, Constant.EMAIL_SEND_PWD_FORGET_AES_KEY);
            emailCode = URLEncoder.encode(emailCode, "UTF-8");

            dto.setEmailCode(emailCode);
            dto.setEmailOperation("emailPasswdGet");
            dto.setSuccess(true);

            request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "redirect:/emailSuccess";
    }
    @RequestMapping(value = "/emailPasswdGet")
    public String emailPasswdGet(@RequestParam(value = "emailCode", required = false) String emailCode,
                              ModelMap modelMap, HttpServletRequest request) {
        EmailStatusDTO dto = new EmailStatusDTO();
        dto.setStep(dto.STEP_PASSWD);
        dto.setHead(this.getMessage("passwd.get",request));
        try {
            if (StringUtils.isBlank(emailCode)){
                dto.setExplan(this.getMessage("error.update", request));
                request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
                return "redirect:/emailSuccess";
            }
            String json = EmailUtils.decryptEmail(emailCode, Constant.EMAIL_SEND_PWD_FORGET_AES_KEY);
            EmailHistory history = JSONObject.parseObject(json, EmailHistory.class);
            EmailHistory historyNew = null;
            if (null != history) {
                historyNew = emailHistoryService.queryRecordByPrimaryKey(history.getEmailId()).getResult();
            }

            if (null == historyNew) {//没有email发送记录
                dto.setExplan(this.getMessage("error.update", request));
            } else {
                long between = System.currentTimeMillis() - historyNew.getSendAt();
                if (between < EMAIL_TOO_MUCH_TIME_BETWEEN || historyNew.getRepeatTimes() > 10) {//小于10min 或者 多余10次

                    dto.setExplan(this.getMessage("error.email.toomuch", request));
                    dto.setSuccess(true);
                } else {
                    historyNew.setSendAt(System.currentTimeMillis());

                    emailHistoryService.refreshEmailHistory(historyNew.getEmailId());
                    this.sendActiveEmail(historyNew, request, authUserService);

                    json = JSONObject.toJSONString(historyNew);

                    emailCode = EmailUtils.encriptEmailCode(json, Constant.EMAIL_SEND_PWD_FORGET_AES_KEY);


                    dto.setExplan(this.getMessage("success.email.send",new String[]{history.getEmail()},request));
                    dto.setSuccess(true);

                    //request.getSession().removeAttribute("SPRING_SECURITY_LAST_EXCEPTION");
                }
                emailCode = URLEncoder.encode(emailCode, "UTF-8");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        dto.setEmailCode(emailCode);
        dto.setEmailOperation("emailPasswdGet");
        request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);

        return "redirect:/emailSuccess";
    }
    /**
     * @param emailCode
     * @param modelMap
     * @param request
     *
     *  实施密码找回激活(pwdForget)
     * @Date: 2018/3/2
     */
    @RequestMapping(value = "/pf")
    public String pf(@RequestParam(value = "emailCode", required = false) String emailCode,
                     ModelMap modelMap, HttpServletRequest request) {
        try {
//            request.getSession().removeAttribute("emailSuccess");
            if (StringUtils.isBlank(emailCode)){
                EmailStatusDTO dto = new EmailStatusDTO();
                dto.setStep(dto.STEP_PASSWD);
                dto.setHead(this.getMessage("passwd.get",request));
                dto.setExplan(this.getMessage("error.update", request));
                request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
                return "redirect:/emailSuccess";
            }
            String json = EmailUtils.decryptEmail(emailCode, Constant.EMAIL_PWD_FORGET_AES_KEY);
            EmailHistory history = JSONObject.parseObject(json, EmailHistory.class);
            if (null == history || null == history.getSendAt()){
                EmailStatusDTO dto = new EmailStatusDTO();
                dto.setStep(dto.STEP_PASSWD);
                dto.setHead(this.getMessage("passwd.get",request));
                dto.setExplan(this.getMessage("error.update", request));
                request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
                return "redirect:/emailSuccess";
            }
            long between = System.currentTimeMillis() - history.getSendAt();
            if (between < EMAIL_DONE_TIME_BETWEEN) {//小于1小时

            } else {
                EmailStatusDTO dto = new EmailStatusDTO();
                dto.setStep(dto.STEP_PASSWD);
                dto.setHead(this.getMessage("passwd.get",request));
                dto.setExplan(this.getMessage("error.done.timeout", request));
                request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
                return "redirect:/emailSuccess";
            }

            request.getSession().setAttribute(EMAIL_CODE, emailCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "pwdForgetConfirm";
    }

    @RequestMapping(value = "/pwdForgetUpdateAction", method = RequestMethod.POST)
    public String pwdForgetUpdateAction(AuthUser authUser, ModelMap modelMap, HttpServletRequest request) {
        EmailStatusDTO dto = new EmailStatusDTO();
        dto.setStep(dto.STEP_PASSWD);
        dto.setHead(this.getMessage("passwd.get",request));

        modelMap.addAttribute("authUser", authUser);

        if (StringUtils.isBlank(authUser.getUserPassword()) || StringUtils.isBlank(authUser.getUserPasswordAgain())) {
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.null", request));
            return "pwdForgetConfirm";
        }
        if (authUser.getUserPassword().length() < 6 || authUser.getUserPassword().length() > 20) {
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.format", request));
            return "pwdForgetConfirm";
        }
        if (!authUser.getUserPassword().equals(authUser.getUserPasswordAgain())) {
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.again", request));
            return "pwdForgetConfirm";
        }

        if (StringUtils.isBlank(authUser.getSalt())) {//
            this.setErrorMsg(modelMap, this.getMessage("error.update", request));
            return "pwdForgetConfirm";
        }

        String emailCode = authUser.getSalt();
        try {

            String json = EmailUtils.decryptEmail(emailCode, Constant.EMAIL_PWD_FORGET_AES_KEY);
            EmailHistory history = JSONObject.parseObject(json, EmailHistory.class);
            EmailHistory historyNew = null;
            if (null != history) {
                historyNew = emailHistoryService.queryRecordByPrimaryKey(history.getEmailId()).getResult();
            }

            if (null == historyNew) {
                //this.setErrorMsg(modelMap, this.getMessage("error.update", request));
                dto.setExplan(this.getMessage("error.update", request));
            } else {
                long between = System.currentTimeMillis() - history.getSendAt();
                if (between < EMAIL_DONE_TIME_BETWEEN) {//小于1小时
                    authUser.setEmail(history.getEmail());
                    authUserService.updatePasswd(authUser);

                    dto.setEnd(true);
                    dto.setSuccess(true);
                    dto.setExplan(this.getMessage("success.done", request));

                } else {
                    //this.setErrorMsg(modelMap, this.getMessage("error.done.timeout", request));
                    dto.setExplan(this.getMessage("error.done.timeout", request));
                }
            }
            request.getSession().setAttribute(SESSION_EMAIL_STATUS,dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "redirect:/emailSuccess";
    }

    /**
     * @param history
     *
     * 发送注册激活Email
     * @Date: 2018/3/2
     */
    private void sendActiveEmail(EmailHistory history, HttpServletRequest request, AuthUserService authUserService) {
        try {
            String json = JSONObject.toJSONString(history);
            String text = EmailUtils.encriptEmailCode(json, this.getEmailClickAesKey(history));

            text = URLEncoder.encode(text, "UTF-8");

            String basePath = WebUtil.getBasePath();

            String url = basePath + this.getEmailUrlAction(history) + text;

            Mail mail = new Mail();
            mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
            mail.setSubject(getEmailObject(history,request,localeResolver));
            mail.setTo(history.getEmail());//
            //mail.setTo("<EMAIL>");
            mail.setFtl(getEmailFtl(history,request,localeResolver));
            Map<String, Object> model = new HashMap<>();
            model.put("emailToName", authUserService.getUserNameByEmail(history.getEmail()));
            model.put("signature", this.getMessage("shop.copyright",request));
            model.put("location", DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
            model.put("emailUrl", url);
            model.put("home",basePath);

            mail.setModel(model);

            emailService.sendSimpleMessage(mail);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param history
     *
     * email subject
     * @Date: 2018/3/5
     */
    public static String getEmailObject(EmailHistory history, HttpServletRequest request, LocaleContextResolver localeResolver) {
        return getEmailObjectByLang(history,localeResolver.resolveLocale(request).toString());
    }
    /**
     * <p>英文需要设置为 en_US</p>
     *  * @param history
      * @param lang
     * @return: java.lang.String
     * @since : 2021/3/5
     */
    public static String getEmailObjectByLang(EmailHistory history, String lang){
        if ("en_US".equals(lang)){
            switch (history.getEmailType()) {
                case EBookConstant.EmailType.REG_ACTIVE:
                    return EMAIL_TITLE_REG_ACTIVE_EN;
                case EBookConstant.EmailType.PASSWORD_FORGET:
                    return EMAIL_TITLE_PWD_FORGET_EN;
            }
        }
        switch (history.getEmailType()) {
            case EBookConstant.EmailType.REG_ACTIVE:
                return EMAIL_TITLE_REG_ACTIVE;
            case EBookConstant.EmailType.PASSWORD_FORGET:
                return EMAIL_TITLE_PWD_FORGET;
        }
        return null;
    }

    /**
     * @param history
     *
     *  email ftl
     * @Date: 2018/3/5
     */
    public static String getEmailFtl(EmailHistory history, HttpServletRequest request, LocaleContextResolver localeResolver) {
        //return getEmailObjectByLang(history,localeResolver.resolveLocale(request).toString());
        return getEmailFtlByLang(history,localeResolver.resolveLocale(request).toString());
    }
    /**
     * <p>英文需要设置为 en_US</p>
     *  * @param history
      * @param lang
     * @return: java.lang.String
     * @since : 2021/3/5
     */
    public static String getEmailFtlByLang(EmailHistory history, String lang){
        if ("en_US".equals(lang)){
            switch (history.getEmailType()) {
                case EBookConstant.EmailType.REG_ACTIVE:
                    return EMAIL_FTL_REG_ACTIVE_EN;
                case EBookConstant.EmailType.PASSWORD_FORGET:
                    return EMAIL_FTL_PWD_FORGET_EN;
            }
        }
        switch (history.getEmailType()) {
            case EBookConstant.EmailType.REG_ACTIVE:
                return EMAIL_FTL_REG_ACTIVE;
            case EBookConstant.EmailType.PASSWORD_FORGET:
                return EMAIL_FTL_PWD_FORGET;
        }
        return null;
    }

    /**
     * @param history
     *
     *  email send Aes Key
     * @Date: 2018/3/5
     */
    public static String getEmailSendAesKey(EmailHistory history) {
        switch (history.getEmailType()) {
            case EBookConstant.EmailType.REG_ACTIVE:
                return Constant.EMAIL_SEND_REG_ACTIVE_AES_KEY;
            case EBookConstant.EmailType.PASSWORD_FORGET:
                return Constant.EMAIL_SEND_PWD_FORGET_AES_KEY;
        }
        return Constant.EMAIL_SEND_REG_ACTIVE_AES_KEY;
    }

    /**
     * @param history
     *
     *  email click Aes Key
     * @Date: 2018/3/5
     */
    public static String getEmailClickAesKey(EmailHistory history) {
        switch (history.getEmailType()) {
            case EBookConstant.EmailType.REG_ACTIVE:
                return Constant.EMAIL_REG_ACTIVE_AES_KEY;
            case EBookConstant.EmailType.PASSWORD_FORGET:
                return Constant.EMAIL_PWD_FORGET_AES_KEY;
        }
        return Constant.EMAIL_REG_ACTIVE_AES_KEY;
    }

    /**
     * @param history
     *
     *  email click Aes Key
     * @Date: 2018/3/5
     */
    public static String getEmailUrlAction(EmailHistory history) {
        switch (history.getEmailType()) {
            case EBookConstant.EmailType.REG_ACTIVE:
                return EMAIL_URL_REG_ACTIVE;
            case EBookConstant.EmailType.PASSWORD_FORGET:
                return EMAIL_URL_PWD_FORGET;
        }
        return null;
    }
}
