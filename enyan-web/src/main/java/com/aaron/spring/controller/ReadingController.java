package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanReading;
import com.aaron.spring.service.EnyanReadingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/6/23
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/reading")
public class ReadingController extends BaseController{
	@Resource
	private EnyanReadingService enyanReadingService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanReading record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		if (null == record){
			record = new EnyanReading();
		}
		if (null == record.getPage()){
			record.setPage(new Page());
		}
		Map<String, Object> queryParams = new HashMap<>();

		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			record.getPage().setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			record.getPage().setCurrentPage(Integer.parseInt(currentPage));
		}

		// 高级查询条件：
		String searchText = req.getParameter("searchText");
		String searchType = req.getParameter("searchType");
		if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
			switch (searchType){
				case "0":
					record.setDataName(searchText);
					break;
				case "1":
					break;
			}
			queryParams.put("searchText",searchText);
			queryParams.put("searchType",searchType);
		}

        record.addOrder(new OrderObj("data_priority", InterfaceContant.OrderBy.DESC));
//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		Page<EnyanReading> page = enyanReadingService.queryRecords(record.getPage(),record);
		record.setPage(page);
		record.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",record.getPageLand());
		modelMap.addAttribute("dto",record);
		modelMap.addAttribute("explan","banner管理");

		return "admin/readingList";
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanReading record = enyanReadingService.queryRecordByPrimaryKey(id).getResult();
		String rangeDate = DateFormatUtils.format(record.getBeginAt(),"yyyy/MM/dd HH:mm:ss")+" - "
				                   + DateFormatUtils.format(record.getEndAt(),"yyyy/MM/dd HH:mm:ss");
		record.setRangeDate(rangeDate);

		modelMap.addAttribute("record",record);
		return "admin/readingAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		enyanReadingService.deleteRecordByPrimaryKey(id);
		return "redirect:/reading/list";
	}

	@RequestMapping("/reset")
	public String reset(ModelMap modelMap){
		log.debug("method reset");
		enyanReadingService.resetReadings();
		return "redirect:/reading/list";
	}

	@RequestMapping("/addUI")
	public String addUI(EnyanReading record, ModelMap modelMap){
		log.debug("method addUI");
		record.setDataReadShow(1);
		record.setDataStatus(1);
		modelMap.addAttribute("record",record);
		return "admin/readingAdd";
	}

	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public String save(HttpServletRequest request, EnyanReading record, ModelMap modelMap){
		log.debug("method save："+record);
		if (StringUtils.hasLength(record.getDataName()) == false){
			this.setErrorMsg(modelMap,"请填写读书会名称");
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}
		if (StringUtils.hasLength(record.getDataImgUrl()) == false){
			this.setErrorMsg(modelMap,"请填写图片的Url");
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}
		if (StringUtils.hasLength(record.getDataToUrl()) == false){
			this.setErrorMsg(modelMap,"请填写转向的页面");
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}
		if (StringUtils.hasLength(record.getDataBuyUrl()) == false){
			this.setErrorMsg(modelMap,"请填写购买书籍的url");
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}
		if (null == record.getDataPriority()){
			this.setErrorMsg(modelMap,"请填写推荐次序");
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}

		// 高级查询条件：
		String startDate, endDate ;
		String rangeDate = request.getParameter("rangeDate");
		if (StringUtils.hasLength(rangeDate) == false){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}
		String[] rangeDateArray = rangeDate.split("-");
		if (rangeDateArray.length != 2){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}
		startDate = rangeDateArray[0].trim();
		endDate = rangeDateArray[1].trim();
		if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			modelMap.addAttribute("record",record);
			return "admin/readingAdd";
		}
		try {
			record.setBeginAt(DateUtils.parseDate(startDate,"yyyy/MM/dd HH:mm:ss"));
			record.setEndAt(DateUtils.parseDate(endDate,"yyyy/MM/dd HH:mm:ss"));
		} catch (ParseException e) {
			e.printStackTrace();
		}

		if (null == record.getDataId()){
			this.setSuccessMsg(modelMap,"添加活动成功");
			record.setIsDeleted(0);
			record.setCreateAt(new Date());
			enyanReadingService.addRecord(record);
		}else {
			this.setSuccessMsg(modelMap,"修改活动成功");
			enyanReadingService.updateRecord(record);
		}
		return "redirect:/reading/list";
	}
}
