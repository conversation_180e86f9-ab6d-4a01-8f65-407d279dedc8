package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.ListCompareUtil;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.LicenseStatus;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanCategoryService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/14
 * @Modified By:
 */
@Controller
@RequestMapping("/category")
public class CategoryController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(CategoryController.class);
    @Resource
    private EnyanCategoryService enyanCategoryService;

    @Resource
    private EnyanBookService enyanBookService;

    @RequestMapping(value = "/categories")
    public String categoriesPage(HttpServletRequest req, EnyanCategory category, ModelMap modelMap){
        logger.info("categoriesPage");
        try {
            if (null == category){
                category = new EnyanCategory();
            }
            if (null == category.getPage()){
                category.setPage(new Page());
                //category.getPage().setPageSize(1);
            }
            Map<String, Object> queryParams = new HashMap<String, Object>();

            // 获取分页参数
            String total = req.getParameter("total");
            String currentPage = req.getParameter("pageNo");

            if (StringUtils.hasLength(total)) {
                category.getPage().setTotalRecord(Integer.parseInt(total));
            }
            if (StringUtils.hasLength(currentPage)) {
                category.getPage().setCurrentPage(Integer.parseInt(currentPage));
            }

            // 高级查询条件：
            String searchText = req.getParameter("searchText");
            String searchType = req.getParameter("searchType");
            if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
                switch (searchType){
                    case "0":
                        category.setCategoryName(searchText);
                        break;
                }
                queryParams.put("searchText",searchText);
                queryParams.put("searchType",searchType);
            }
            category.addOrder(new OrderObj("category_order", InterfaceContant.OrderBy.DESC));

            Page<EnyanCategory> page = enyanCategoryService.queryRecords(category.getPage(),category);



            category.setPage(page);
            category.excutePageLand(queryParams);

            modelMap.addAttribute("list",page.getRecords());
            modelMap.addAttribute("pageLand",category.getPageLand());

            modelMap.addAttribute("enyanCategory",category);
            modelMap.addAttribute("explan","类别管理");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "admin/categories";
    }

    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getCategoryById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.info("getCategoryById");
        EnyanCategory enyanCategory = enyanCategoryService.queryRecordByPrimaryKey(id).getResult();
        modelMap.addAttribute("enyanCategory",enyanCategory);
        return "admin/categoryAdd";
    }


    @RequestMapping(value = "/associate-{id}", method = RequestMethod.GET)
    public String associateCategoryById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.info("associateCategoryById");
        EnyanCategory enyanCategory = enyanCategoryService.queryRecordByPrimaryKey(id).getResult();
        if (-2 == enyanCategory.getCategoryId()){//如果是特价商品，直接从书籍表获取相应ID
//            String.join(",", new ArrayList<String>());
            List<EnyanBook> specialList = enyanBookService.findBookBasicInfoSpecialOffer();
            /*
            List<Long> idList = specialList.stream().map(book -> book.getBookId()).collect(Collectors.toList());
            String ebookRecommend = String.join(",", (CharSequence) idList);*/
            String ebookRecommend = specialList.stream().map(enyanBook -> enyanBook.getBookId().toString()).collect(Collectors.joining(","));
            enyanCategory.setBookRecommended(ebookRecommend);
        }
        enyanCategory.setBookRecommendedOld(enyanCategory.getBookRecommended());
        modelMap.addAttribute("enyanCategory",enyanCategory);
        modelMap.addAttribute("bookIDsList", Constant.booksList);
        return "admin/categoryAssociate";
    }

    @RequestMapping("/del-{id}")
    public String delCategoryById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.info("method delCategoryById");
        enyanCategoryService.deleteRecordByPrimaryKey(id);
        return "redirect:/category/categories";
    }

    @RequestMapping("/addCategoryUI")
    public String addCategoryUI(EnyanCategory enyanCategory){
        logger.info("method addProductUI");

        return "admin/categoryAdd";
    }

    @RequestMapping(value = "/saveCategory", method = RequestMethod.POST)
    public String saveCategory(EnyanCategory enyanCategory, ModelMap modelMap){
        logger.info("method addCategory："+enyanCategory);

        if (StringUtils.hasLength(enyanCategory.getCategoryName()) == false){
            this.setErrorMsg(modelMap,"请填写分类名称");
            return "admin/categoryAdd";
        }
        if (StringUtils.hasLength(enyanCategory.getCategoryNameTc()) == false){
            this.setErrorMsg(modelMap,"请填写分类繁体名称");
            return "admin/categoryAdd";
        }
        if (null == enyanCategory.getCategoryOrder()){
            this.setErrorMsg(modelMap,"请填写分类优先级");
            return "admin/categoryAdd";
        }
        if (null == enyanCategory.getCategoryId()){
            this.setSuccessMsg(modelMap,"添加分类成功");
            enyanCategoryService.addRecord(enyanCategory);
        }else {
            this.setSuccessMsg(modelMap,"修改分类成功");
            enyanCategoryService.updateRecord(enyanCategory);

            enyanBookService.updateBookCategoryName(enyanCategory.getCategoryName(),enyanCategory.getCategoryId());
        }
        return "redirect:/category/categories";
    }

    @RequestMapping(value = "/setCategory", method = RequestMethod.POST)
    public String setCategory(EnyanCategory enyanCategory, ModelMap modelMap){
        logger.info("method setCategory："+enyanCategory);
        if (StringUtils.hasLength(enyanCategory.getBookRecommended()) == false){
            this.setErrorMsg(modelMap,"请填写关联推荐书籍");
            return "admin/categoryAssociate";
        }
        logger.debug("getBookRecommended:{}",enyanCategory.getBookRecommended());
        if (-2 == enyanCategory.getCategoryId()){//如果是特价商品
            List<String> oldList = StringUtils.hasLength(enyanCategory.getBookRecommendedOld())?Arrays.asList(enyanCategory.getBookRecommendedOld().split(",")):new ArrayList<>();
            List<String> newList = StringUtils.hasLength(enyanCategory.getBookRecommended())?Arrays.asList(enyanCategory.getBookRecommended().split(",")):new ArrayList<>();
            Map<String,List<String>> diffMap = ListCompareUtil.getDiffListAll(oldList, newList);
            enyanBookService.updateBooksToSpecialOffer(diffMap.get("-1").stream().toArray(String[]::new), 0);
            enyanBookService.updateBooksToSpecialOffer(diffMap.get("1").stream().toArray(String[]::new), 1);
            return "redirect:/category/categories";
        }
        EnyanCategory category = new EnyanCategory();
        category.setCategoryId(enyanCategory.getCategoryId());
        category.setBookRecommended(enyanCategory.getBookRecommended());
        enyanCategoryService.updateRecord(category);
        enyanBookService.initIndexCategoryBooks(category.getCategoryId());
        return "redirect:/category/categories";
    }
}
