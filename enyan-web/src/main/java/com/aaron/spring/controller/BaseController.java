package com.aaron.spring.controller;

import com.aaron.spring.common.Constant;
import com.aaron.spring.service.CallbackAction;
import com.aaron.util.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleContextResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Locale;
import java.util.Map;

/**
 *
 * @Author: Aaron <PERSON>
 * @Date: Created in  2017/11/15
 * @Modified By:
 */
public class BaseController implements CallbackAction {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String Tip_Error = "isSaveError";

    private static final String Tip_Success = "isSaveSuccess";

    public static final String Tip_Msg = "msg";

    public static final String Error_Msg = "errorMsg";

    public static final String ERROR_CODE = "errorCode";

    @Resource
    protected MessageSource messageSource;

    @Resource
    protected LocaleContextResolver localeResolver;

    public void setErrorMsg(Model modelMap, String msg){
        modelMap.addAttribute(Tip_Error, true);
        modelMap.addAttribute(Tip_Msg, msg);
    }
    public void setErrorMsg(ModelMap modelMap, String msg){
        modelMap.addAttribute(Tip_Error, true);
        modelMap.addAttribute(Tip_Msg, msg);
    }
    public void setSuccessMsg(ModelMap modelMap,String msg){
        modelMap.addAttribute(Tip_Success, true);
        modelMap.addAttribute(Tip_Msg, msg);
    }

    public String getMessage(String code, HttpServletRequest request){
        return messageSource.getMessage(code,null,localeResolver.resolveLocale(request));
    }

    public String getMessage(String code, HttpServletRequest request, String defaultMessage){
        return messageSource.getMessage(code,null,localeResolver.resolveLocale(request));
    }

    public String getMessage(String code, Object[] args,HttpServletRequest request){
        return messageSource.getMessage(code,args,localeResolver.resolveLocale(request));
    }

    public static String getMessage(String code, String lang, MessageSource messageSource){
        if (StringUtils.hasLength(lang) == false){
            return messageSource.getMessage(code,null, Locale.SIMPLIFIED_CHINESE);
        }
        return messageSource.getMessage(code, null, StringUtils.parseLocale(lang));
    }

    /**
     * <p></p>
     * @param code
     * @param lang zh_CN zh_HK en_US
     * @return java.lang.String
     * @since : 2021/5/26
     **/
    public String getMessage(String code, String lang){
        if (StringUtils.hasLength(lang) == false){
            return messageSource.getMessage(code,null, Locale.SIMPLIFIED_CHINESE);
        }
        return messageSource.getMessage(code,null, StringUtils.parseLocale(lang));
    }

    @Override
    public void emailCallback(Map<String, Object> toReturn, Map<String, Object> result) {
        logger.debug("toReturn:{},result:{}",toReturn,result);
    }

    public MessageSource getMessageSource() {
        return messageSource;
    }

    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }
}
