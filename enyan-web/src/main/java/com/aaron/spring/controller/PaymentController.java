package com.aaron.spring.controller;

import com.aaron.spring.model.BizContent;
import com.aaron.spring.model.SignInfo;
import com.alipay.util.AlipayNotify;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/2
 * @Modified By:
 */
@Controller
@RequestMapping("/pay")
public class PaymentController {
    // 支付宝重要参数
    private static final String APP_ID = "";
    private static final String APP_PRIVATE_KEY = "";
    private static final String CHARSET = "utf-8";
    private static final String ALIPAY_PUBLIC_KEY = "";

    /**
     * 对支付宝支付信息进行签名
     * https://docs.open.alipay.com/200/105311/
     *http://blog.csdn.net/mixi9760/article/details/66473368
     * http://zzc1684.iteye.com/blog/2239959
     * http://www.cnblogs.com/suruozhong/p/6632196.html
     *
     *
     *
     * @param info
     *            数据类
     * @return
     * @throws Exception
     * @throws UnsupportedEncodingException
     */
    @RequestMapping("/sign")
    public Object sign(@RequestBody SignInfo info) throws Exception, UnsupportedEncodingException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String appID = APP_ID;
        String bizContent = toJson(info.Content);
        String charset = CHARSET;
        String method = "alipay.trade.app.pay";
        String signType = "RSA";
        String timestamp = sdf.format(new Date());
        String version = "1.0";
        String notify_url = "https://pay.ytbapp.com/payment/notify";// 增加支付异步通知回调,记住上下notify_url的位置,全在sign_type之前,很重要,同样放在最后都不行
        String content = "app_id=" + appID + "&biz_content=" + bizContent + "&charset=" + charset + "&method=" + method
                + "¬ify_url=" + (notify_url) + "&sign_type=" + signType + "×tamp=" + timestamp + "&version="
                + version;

        String sign = "" ;//AlipaySignature.rsaSign(content, APP_PRIVATE_KEY, charset);

        return "{\"Result\": \"app_id=" + encode(appID) + "&biz_content=" + encode(bizContent) + "&charset="
                + encode(charset) + "&method=" + encode(method) + "¬ify_url=" + encode(notify_url) + "&sign_type="
                + encode(signType) + "×tamp=" + encode(timestamp) + "&version=" + encode(version) + "&sign="
                + encode(sign) + "\"}";
    }

    private String encode(String sign) throws UnsupportedEncodingException {
        return URLEncoder.encode(sign, "utf-8").replace("+", "%20");
    }

    private String toJson(BizContent content) {
        String context = "";
        context += "{" + "\"timeout_express\":\"" + content.timeout_express + "\"," + "\"seller_id\":\""
                + content.seller_id + "\"," + "\"product_code\":\"" + content.product_code + "\","
                + "\"total_amount\":\"" + content.total_amount + "\"," + "\"subject\":\"" + content.subject + "\","
                + "\"body\":\"" + content.body + "\"," + "\"out_trade_no\":\"" + content.out_trade_no + "\"}";

        return context;
    }

    /**
     * 支付宝支付成功后.会回调该接口
     *
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    @RequestMapping("/notify")
    public String notify(HttpServletRequest request) throws UnsupportedEncodingException {
        //获取支付宝POST过来反馈信息
        Map<String,String> params = new HashMap<String,String>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "gbk");
            params.put(name, valueStr);
        }

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以下仅供参考)//
        //商户订单号

        String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");

        //支付宝交易号

        String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");

        //交易状态
        String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以上仅供参考)//

        if(AlipayNotify.verify(params)){//验证成功
            //////////////////////////////////////////////////////////////////////////////////////////
            //请在这里加上商户的业务逻辑程序代码

            //——请根据您的业务逻辑来编写程序（以下代码仅作参考）——

            if(trade_status.equals("TRADE_FINISHED")){
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //请务必判断请求时的total_fee、seller_id与通知时获取的total_fee、seller_id为一致的
                //如果有做过处理，不执行商户的业务程序

                //注意：
                //退款日期超过可退款期限后（如三个月可退款），支付宝系统发送该交易状态通知
            } else if (trade_status.equals("TRADE_SUCCESS")){
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //请务必判断请求时的total_fee、seller_id与通知时获取的total_fee、seller_id为一致的
                //如果有做过处理，不执行商户的业务程序

                //注意：
                //付款完成后，支付宝系统发送该交易状态通知
            }

            //——请根据您的业务逻辑来编写程序（以上代码仅作参考）——

            System.out.println("success");	//请不要修改或删除

            //////////////////////////////////////////////////////////////////////////////////////////
        }else{//验证失败
            System.out.println("fail");
        }
        return null;
    }
    /**
     * 支付宝支付成功后.会回调该接口
     *
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    @RequestMapping("/notifyOld")
    public String notifyOld(HttpServletRequest request) throws UnsupportedEncodingException {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = iter.next();
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            // 乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
            // valueStr = new String(valueStr.getBytes("ISO-8859-1"), "gbk");
            params.put(name, valueStr);
        }
        String out_trade_no = request.getParameter("out_trade_no");// 商户订单号
        boolean signVerified = false;
        try {
            //signVerified = AlipaySignature.rsaCheckV1(params, ALIPAY_PUBLIC_KEY, CHARSET);
        } catch (Exception e) {
            e.printStackTrace();
            return ("fail");// 验签发生异常,则直接返回失败
        }
        // 调用SDK验证签名
        if (signVerified) {
            // TODO 验签成功后
            // 按照支付结果异步通知中的描述，对支付结果中的业务内容进行1\2\3\4二次校验，校验成功后在response中返回success，校验失败返回failure
            /*
            String result = updateALiPayOrderStatus(out_trade_no);
            System.out.println("验证成功,去更新状态 \t订单号:" + out_trade_no + "来自支付宝支付,更新结果:" + result);
            BaseResponse baseResponse = GsonUtils.getGson().fromJson(result, BaseResponse.class);
            if (null != baseResponse && baseResponse.isSucceeded) {
                return ("success");
            } else {
                return ("fail");// 更新状态失败
            }*/
            if("TRADE_SUCCESS".equals(params.get("trade_status"))){
                //付款金额
                String amount = params.get("buyer_pay_amount");
                //商户订单号
                //String out_trade_no = params.get("out_trade_no");
                //支付宝交易号
                String trade_no = params.get("trade_no");
                //附加数据
                String passback_params = URLDecoder.decode(params.get("passback_params"),"UTF-8");
                //URLDecoder.decode(params.get("passback_params"),"");
            }
            return null;
        } else {
            // TODO 验签失败则记录异常日志，并在response中返回failure.
            System.out.println("验证失败,不去更新状态");
            return ("fail");
        }
    }
}
