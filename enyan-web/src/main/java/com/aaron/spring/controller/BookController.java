package com.aaron.spring.controller;

import com.aaron.a4j.util.ResultUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.Money;
import com.aaron.common.NameAndValue;
import com.aaron.common.NameAndValueDTO;
import com.aaron.common.OrderObj;
import com.aaron.excel.ExcelImportUtil;
import com.aaron.excel.FileUtil;
import com.aaron.excel.util.ExcelReaderMultiFromBook;
import com.aaron.excel.util.ExcelReaderMutiFromJW;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.api.v4.model.RestComment;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.common.SystemUtil;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.*;
import com.aaron.spring.service.*;
import com.aaron.util.ExecuteResult;
import com.aaron.util.FilePropertiesUtil;
import com.aaron.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/13
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/book")
public class BookController extends BaseController{

    private static final String epubBaseDir = FilePropertiesUtil.props.getProperty("epubBaseDir");//

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private BaseDataService baseDataService;

    @Resource
    private PublicationService publicationService;

    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EmailService emailService;

    @ResponseBody
    @RequestMapping(value = "/booksJson",method = RequestMethod.POST)
    public Page<EnyanBook> loginAction(@RequestBody EnyanBook book){
        ExecuteResult<EnyanBook> result = new ExecuteResult<>();
        //Page page = new Page();
        Page<EnyanBook> page = enyanBookService.queryRecords(book.getPage(),book);
        if (page == null){
            page = new Page<>();
        }
        return page;
    }

    @RequestMapping(value = "/books")
    public String booksPage(HttpServletRequest req, EnyanBook book, ModelMap modelMap){
        //logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        if (null == book){
            book = new EnyanBook();
        }
        if (null == book.getPage()){
            book.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            book.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            book.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件：
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        String searchOption = req.getParameter("searchOption");
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType){
                case "0":
                    book.setBookTitle(searchText);
                    break;
                case "1":
                    book.setAuthor(searchText);
                    break;
                case "2":
                    book.setPublisherName(searchText);
                    break;
                case "3":
                    book.setCategoryName(searchText);
                    break;
            }
            queryParams.put("searchText",searchText);
            queryParams.put("searchType",searchType);
        }

        if (StringUtils.hasLength(searchOption)){
            switch (searchOption){
                case "0":
//                    book.setShelfStatus(Constant.BYTE_VALUE_1);
                    break;
                case "1":
                    book.setShelfStatus(Constant.BYTE_VALUE_1);
                    queryParams.put("searchOption",searchOption);
                    break;
                case "2":
                    book.setShelfStatus(Constant.BYTE_VALUE_0);
                    queryParams.put("searchOption",searchOption);
                    break;
            }
        }

        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
        Page<EnyanBook> page = enyanBookService.queryRecords(book.getPage(),book);
        book.setPage(page);
        book.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",book.getPageLand());
        modelMap.addAttribute("book",book);
        modelMap.addAttribute("explan","书籍管理");

        return "admin/books";
    }

    @RequestMapping(value = "/initIndexCategory")
    public String initIndexCategory(HttpServletRequest req, EnyanBook book, ModelMap modelMap){
        //logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        enyanBookService.initIndexAllInfo();
        return "redirect:/closePage";
    }

    @RequestMapping(value = "/initBookNameList")
    public String initBookNameList(HttpServletRequest req, EnyanBook book, ModelMap modelMap){
        //logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        enyanBookService.initBookNameAndValue(true);
        return "redirect:/closePage";
    }

    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getBookById(@PathVariable("id")Long id , ModelMap modelMap){
        //logger.debug("getBookById");
        List<NameAndValue> publicationList = publicationService.findAllPublicationNameAndValues();
        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(id).getResult();

        modelMap.addAttribute("enyanBook",enyanBook);
        modelMap.addAttribute("categoryList", Constant.categoriesAllList);
        modelMap.addAttribute("publisherList",Constant.publishersList);
        modelMap.addAttribute("discountList",Constant.discountsList);
        modelMap.addAttribute("bookDrmRefList",publicationList);
        modelMap.addAttribute("bookIDsList", enyanBookService.getBookIDsList());

        if (null == enyanBook){
            return "admin/bookUpdate";
        }
        /*
        if (StringUtils.hasLength(enyanBook.getSetInfo())){
            BookSetInfo bookSetInfo = JSONObject.parseObject(enyanBook.getSetInfo(), BookSetInfo.class);
            if (null != bookSetInfo.getBooksToSetList() && !bookSetInfo.getBooksToSetList().isEmpty()){
                String bookIDs[] = new String[bookSetInfo.getBooksToSetList().size()];
                for (int i = 0; i < bookSetInfo.getBooksToSetList().size(); i++) {
                    EnyanBook tmp = bookSetInfo.getBooksToSetList().get(i);
                    bookIDs[i] = tmp.getBookId().toString();
                }
                enyanBook.setBookIDs(bookIDs);
            }
        }*/
        if (null != enyanBook.getBookWebInfo()){
            enyanBook.setBookWebSalePapers(enyanBook.getBookWebInfo().getSalePapersJson());
            enyanBook.setBookWebVersions(enyanBook.getBookWebInfo().getVersionsJson());
        }

        return "admin/bookUpdate";
    }

    @RequestMapping("/del-{id}")
    public String delBookById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("method delBookById");
        /*
        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(id).getResult();

        String picPath = SystemUtil.getProductDir() + enyanBook.getBookCover();
        String appPicPath = SystemUtil.getProductDir() + enyanBook.getBookId()+".png";
        String samplePath = SystemUtil.getProductSampleDir() + enyanBook.getBookSample();
        String epubPath = epubBaseDir + File.separator + "book" + File.separator + id + ".epub";

        File filePic = new File(picPath);
        File fileAppPic = new File(appPicPath);
        File fileSample = new File(samplePath);
        File fileEpub = new File(epubPath);

        try {
            if (filePic.exists()) {
                FileUtils.forceDelete(filePic);
            }
            if (fileAppPic.exists()) {
                FileUtils.forceDelete(fileAppPic);
            }
            if (fileSample.exists()){
                FileUtils.forceDelete(fileSample);
            }
            if (fileEpub.exists()){
                FileUtils.forceDelete(fileEpub);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }*/

        enyanBookService.deleteRecordByPrimaryKey(id);
        return "redirect:/book/books";
    }

    @RequestMapping("/shelfStatus-{id}-{status}")
    public String shelfStatusBookById(@PathVariable("id")Long id , @PathVariable("status")String status, ModelMap modelMap){
        log.debug("method shelfStatusBookById");

        EnyanBook enyanBookNew = enyanBookService.queryRecordByPrimaryKey(id).getResult();
        if (enyanBookNew == null){
            return "redirect:/book/books";
        }

        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setBookId(id);
        if ("0".equals(status)){
            enyanBook.setShelfStatus(Constant.BYTE_VALUE_0);

        }else if("1".equals(status)){
            enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);
            enyanBook.setOpensaleAt(new Date());
            enyanBookService.resetBookElasticSearch(enyanBook.getBookId());
        }
        enyanBookService.updateByPrimaryKeySelective(enyanBook);

        return "redirect:/book/books";
    }

    @ResponseBody
    @RequestMapping(value = "/shelfStatus")
    public ExecuteResult<String> shelfStatus(@RequestBody RestBook restObj, ModelMap modelMap,
                                     HttpServletRequest request, HttpServletResponse response){
        Long id = restObj.getBookId();
        String status = restObj.getShelfStatus() + "";

        ExecuteResult<String> result = new ExecuteResult<>();
        log.debug("bookId:{},status:{}",id,status);
        if (null == id || StringUtils.hasLength(status) == false){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return result;
        }

        EnyanBook enyanBookNew = enyanBookService.queryRecordByPrimaryKey(id).getResult();
        if (enyanBookNew == null){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E2);
            return result;
        }

        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setBookId(id);
        if ("0".equals(status)){
            enyanBook.setShelfStatus(Constant.BYTE_VALUE_0);

        }else if("1".equals(status)){
            enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);
            enyanBook.setOpensaleAt(new Date());
            enyanBookService.resetBookElasticSearch(enyanBook.getBookId());
        }
        enyanBookService.updateByPrimaryKeySelective(enyanBook);
        return result;
    }

    @RequestMapping("/presaleEmail-{id}")
    public String presaleEmail(@PathVariable("id")Long id , HttpServletRequest request){
        log.debug("method presaleEmail");
        EnyanBook enyanBookNew = enyanBookService.queryRecordByPrimaryKey(id).getResult();
        if (null == enyanBookNew){
            return "redirect:/book/books";
        }
        List<EnyanOrderDetail> list = enyanOrderDetailService.findBookIDAndNameAndEmailByBookID(id);
        List<String> emailList = new ArrayList<>();
        String bookName = enyanBookNew.getBookTitle();
        for (EnyanOrderDetail orderDetail: list){
            emailList.add(orderDetail.getUserEmail());
        }
        //emailList.add("<EMAIL>");
        this.sendPresaleEmail(enyanBookNew,emailList,request);

        return "redirect:/book/books";
    }

    /**
     * JSON 返回
     * */
    @ResponseBody
    @RequestMapping(value = "/delSample-{id}", method = RequestMethod.GET)
    public Map<String,Object> delBookSample(@PathVariable("id")Long id){
        log.debug("delBookSample");
        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(id).getResult();

        String samplePath = SystemUtil.getProductSampleDir() + enyanBook.getBookSample();

        File fileSample = new File(samplePath);

        try {
            if (fileSample.exists()){
                FileUtils.forceDelete(fileSample);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        EnyanBook enyanBookNew = new EnyanBook();
        enyanBookNew.setBookId(id);
        enyanBookNew.setBookSample("");
        enyanBookService.updateByPrimaryKeySelective(enyanBookNew);

        boolean success = true;



        Map<String,Object> result ;
        if (success){
            Map<String,String> data = new HashMap<>();
            //data.put("amountCny",String.valueOf(cartInfo.getAmountCny()));
            //data.put("amountUsd",String.valueOf(cartInfo.getAmountUsd()));
            data.put("result","成功删除");
            result = ResultUtils.getSuccessResultData(data);
        }else {
            result = ResultUtils.getFailedResultData("未删除");
        }
        return result;
    }
    @RequestMapping("/addBookUI")
    public String addCategoryUI(EnyanBook enyanBook, ModelMap modelMap){
        log.debug("method addBookUI");
        enyanBook.setDiscountSingleValue(100);
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);
        this.initBookValue(modelMap);
        return "admin/bookAdd";
    }
    @RequestMapping(value = "/saveBook", method = RequestMethod.POST)
    public String saveBook(EnyanBook enyanBook , ModelMap modelMap) {


        if (!StringUtils.hasLength(enyanBook.getBookTitle())) {
            this.setErrorMsg(modelMap, "请添加标题！");
            this.initBookValue(modelMap);
            return "admin/bookAdd";
        }
        if (!StringUtils.hasLength(enyanBook.getRecommendedCaption())) {
            this.setErrorMsg(modelMap, "请添加推荐语！");
            this.initBookValue(modelMap);
            return "admin/bookAdd";
        }
        /*
        if (filedataPic == null || filedataPic.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择图片！");
            return "admin/bookAdd";
        }
        if (filedataAppPic == null || filedataAppPic.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择App图片！");
            return "admin/bookAdd";
        }
        if (filedataBook == null || filedataBook.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择书籍！");
            return "admin/bookAdd";
        }
        if (filedataSample == null || filedataSample.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择样章！");
            return "admin/bookAdd";
        }*/
        if (null == enyanBook.getPriceHkd()){
            this.setErrorMsg(modelMap, "请设置价格！");
            this.initBookValue(modelMap);
            return "admin/bookAdd";
        }
        String checkDiscountInfo = this.checkDiscountInfo(enyanBook);
        if (StringUtils.hasLength(checkDiscountInfo)){
            this.setErrorMsg(modelMap, checkDiscountInfo);
            this.initBookValue(modelMap);
            return "admin/bookAdd";
        }

        try {
            enyanBook.setOpensaleAt(new Date());
            if (null != enyanBook.getPublisherId()){
                String value = baseDataService.getPublisherNameById(String.valueOf(enyanBook.getPublisherId()));
                enyanBook.setPublisherName(value);
            }
            if (null != enyanBook.getCategoryId()){
                String value = baseDataService.getCategoryNameById(String.valueOf(enyanBook.getCategoryId()));
                enyanBook.setCategoryName(value);
            }
            enyanBook.setSalesVolume(0L);
            enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_0);
            enyanBook.setDiscountSingleType(Constant.BYTE_VALUE_0);
            enyanBook.setDiscountSingleDescription(this.getDiscountDescription(enyanBook));
            enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_0);
            enyanBook.setBookCost(0);
            enyanBook.setBookHash("");
            enyanBook.setStarCount(0);
            enyanBook.setSetId(0L);

            this.initInputBookSetInfo(enyanBook);

            if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                    &&enyanBook.getDiscountSingleValue()>0){
                Money money = Money.hkds(enyanBook.getPriceHkd());
                enyanBook.setPrice(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
            }else {
                enyanBook.setPrice(enyanBook.getPriceHkd());
            }

            enyanBook.setBookEsin(IdUtil.getEDBookId());
            if (StringUtils.hasLength(enyanBook.getBookWebSalePapers()) || StringUtils.hasLength(enyanBook.getBookWebVersions())){
                BookWebInfo bookWebInfo = new BookWebInfo();
                if (StringUtils.hasLength(enyanBook.getBookWebSalePapers())){
                    NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBook.getBookWebSalePapers(),NameAndValueDTO.class);
                    if (null != nameAndValueDTO){
                        bookWebInfo.setSalePapers(nameAndValueDTO.getValues());
                    }
                }
                if (StringUtils.hasLength(enyanBook.getBookWebVersions())){
                    NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBook.getBookWebVersions(),NameAndValueDTO.class);
                    if (null != nameAndValueDTO){
                        bookWebInfo.setVersions(nameAndValueDTO.getValues());
                    }
                }
                enyanBook.setBookWeb(JSON.toJSONString(bookWebInfo));
            }

            enyanBookService.addRecord(enyanBook);

        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            this.initBookValue(modelMap);
            return "admin/bookAdd";
        }

        this.setSuccessMsg(modelMap, "添加书籍成功！");
        return "redirect:/book/books";
    }

    @RequestMapping(value = "/updateBook", method = RequestMethod.POST)
    public String updateBook(EnyanBook enyanBook , ModelMap modelMap) {

        List<NameAndValue> publicationList = publicationService.findAllPublicationNameAndValues();
        modelMap.addAttribute("categoryList", Constant.categoriesAllList);
        modelMap.addAttribute("publisherList",Constant.publishersList);
        modelMap.addAttribute("discountList",Constant.discountsList);
        modelMap.addAttribute("bookDrmRefList",publicationList);
        modelMap.addAttribute("bookIDsList", enyanBookService.getBookIDsList());

        if (!StringUtils.hasText(enyanBook.getBookTitle())) {
            this.setErrorMsg(modelMap, "请添加标题！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }
        if (!StringUtils.hasText(enyanBook.getRecommendedCaption())) {
            this.setErrorMsg(modelMap, "请添加推荐语！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }
        if (null == enyanBook || enyanBook.getBookId()==0){
            this.setErrorMsg(modelMap, "修改失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }
        if (null == enyanBook.getPriceHkd()){
            this.setErrorMsg(modelMap, "请设置价格！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }
        if (null == enyanBook.getVendorPercent()){
            this.setErrorMsg(modelMap, "请设置版税金额！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }
        String checkDiscountInfo = this.checkDiscountInfo(enyanBook);
        if (!StringUtils.hasLength(checkDiscountInfo) == false){
            this.setErrorMsg(modelMap, checkDiscountInfo);
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }
        try {
            if (null != enyanBook.getPublisherId()){
                String value = baseDataService.getPublisherNameById(String.valueOf(enyanBook.getPublisherId()));
                enyanBook.setPublisherName(value);
            }
            if (null != enyanBook.getCategoryId()){
                String value = baseDataService.getCategoryNameById(String.valueOf(enyanBook.getCategoryId()));
                enyanBook.setCategoryName(value);
            }
            //enyanBook.setDiscountSingleType(new Byte("0"));

            //enyanBook.setDiscountSingleDescription(this.getDiscountDescription(enyanBook));

    /*
            if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                    &&enyanBook.getDiscountSingleValue()>0){
                Money money = Money.hkds(enyanBook.getPriceHkd());
                enyanBook.setPrice(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
            }else {
                enyanBook.setPrice(enyanBook.getPriceHkd());
            }*/

            //enyanBook.setPrice(enyanBook.getPriceHkd());
            this.initInputBookSetInfo(enyanBook);


            /*NameAndValue valueAndName1 = new NameAndValue();
            valueAndName1.setName("大陆");
            valueAndName1.setValue("https://book.endao.co/cn/index.php?route=product/product&product_id=113");

            NameAndValue valueAndName2 = new NameAndValue();
            valueAndName2.setName("海外");
            valueAndName2.setValue("https://book.endao.co/cn/index.php?route=product/product&product_id=113");

            NameAndValueDTO nameAndValueDTO = new NameAndValueDTO();
            nameAndValueDTO.addNameAndValue(valueAndName1);
            nameAndValueDTO.addNameAndValue(valueAndName2);

            String bookWeb = nameAndValueDTO.jsonString();
            enyanBook.setBookWeb(bookWeb);*/

            enyanBookService.updateRecord(enyanBook);

        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        this.setSuccessMsg(modelMap, "修改书籍成功！");
        return "redirect:/closePage";
    }

    @RequestMapping(value = "/updateBook1", method = RequestMethod.POST)
    public String updateBook1(EnyanBook enyanBook , ModelMap modelMap) {

        List<NameAndValue> publicationList = publicationService.findAllPublicationNameAndValues();
        modelMap.addAttribute("categoryList", Constant.categoriesAllList);
        modelMap.addAttribute("publisherList",Constant.publishersList);
        modelMap.addAttribute("discountList",Constant.discountsList);
        modelMap.addAttribute("bookDrmRefList",publicationList);
        modelMap.addAttribute("bookIDsList", enyanBookService.getBookIDsList());

        if (StringUtils.hasLength(enyanBook.getBookTitle()) == false) {
            this.setErrorMsg(modelMap, "请添加标题！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }
        if (null == enyanBook || enyanBook.getBookId()==0){
            this.setErrorMsg(modelMap, "修改失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        try {
            if (null != enyanBook.getPublisherId()){
                String value = baseDataService.getPublisherNameById(String.valueOf(enyanBook.getPublisherId()));
                enyanBook.setPublisherName(value);
            }

            enyanBookService.updateRecord(enyanBook);
        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        this.setSuccessMsg(modelMap, "修改书籍成功！");
        return "redirect:/closePage";
    }

    @RequestMapping(value = "/updateBook2", method = RequestMethod.POST)
    public String updateBook2(EnyanBook enyanBook , ModelMap modelMap) {

        List<NameAndValue> publicationList = publicationService.findAllPublicationNameAndValues();
        modelMap.addAttribute("ca2tegoryList", Constant.categoriesAllList);
        modelMap.addAttribute("publisherList",Constant.publishersList);
        modelMap.addAttribute("discountList",Constant.discountsList);
        modelMap.addAttribute("bookDrmRefList",publicationList);
        modelMap.addAttribute("bookIDsList", enyanBookService.getBookIDsList());

        if (null == enyanBook || enyanBook.getBookId()==0){
            this.setErrorMsg(modelMap, "修改失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        try {
            if (null != enyanBook.getCategoryId()){
                String value = baseDataService.getCategoryNameById(String.valueOf(enyanBook.getCategoryId()));
                enyanBook.setCategoryName(value);
            }

            if (StringUtils.hasLength(enyanBook.getBookWebSalePapers()) || StringUtils.hasLength(enyanBook.getBookWebVersions())){
                BookWebInfo bookWebInfo = new BookWebInfo();
                if (StringUtils.hasLength(enyanBook.getBookWebSalePapers())){
                    NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBook.getBookWebSalePapers(),NameAndValueDTO.class);
                    if (null != nameAndValueDTO){
                        bookWebInfo.setSalePapers(nameAndValueDTO.getValues());
                    }
                }
                if (StringUtils.hasLength(enyanBook.getBookWebVersions())){
                    NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBook.getBookWebVersions(),NameAndValueDTO.class);
                    if (null != nameAndValueDTO){
                        bookWebInfo.setVersions(nameAndValueDTO.getValues());
                    }
                }
                enyanBook.setBookWeb(JSON.toJSONString(bookWebInfo));
            }

            enyanBookService.updateRecord(enyanBook);

        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        this.setSuccessMsg(modelMap, "修改书籍成功！");
        return "redirect:/closePage";
    }

    @RequestMapping(value = "/updateBook3", method = RequestMethod.POST)
    public String updateBook3(EnyanBook enyanBook , ModelMap modelMap) {

        List<NameAndValue> publicationList = publicationService.findAllPublicationNameAndValues();
        modelMap.addAttribute("categoryList", Constant.categoriesAllList);
        modelMap.addAttribute("publisherList",Constant.publishersList);
        modelMap.addAttribute("discountList",Constant.discountsList);
        modelMap.addAttribute("bookDrmRefList",publicationList);
        modelMap.addAttribute("bookIDsList", enyanBookService.getBookIDsList());

        if (null == enyanBook || enyanBook.getBookId()==0){
            this.setErrorMsg(modelMap, "修改失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        try {
            EnyanBook tmpBook = enyanBookService.queryRecordByPrimaryKey(enyanBook.getBookId()).getResult();
            if (Constant.BYTE_VALUE_1.equals(tmpBook.getDiscountSingleIsValid())
                    &&tmpBook.getDiscountSingleValue()>0){
                Money money = Money.hkds(enyanBook.getPriceHkd());
                enyanBook.setPrice(money.getHKDDiscountAmount(tmpBook.getDiscountSingleValue()));
            }else {
                enyanBook.setPrice(enyanBook.getPriceHkd());
            }
            enyanBookService.updateRecord(enyanBook);

        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        this.setSuccessMsg(modelMap, "修改书籍成功！");
        return "redirect:/closePage";
    }

    @RequestMapping(value = "/updateBook4", method = RequestMethod.POST)
    public String updateBook4(EnyanBook enyanBook , ModelMap modelMap) {

        List<NameAndValue> publicationList = publicationService.findAllPublicationNameAndValues();
        modelMap.addAttribute("categoryList", Constant.categoriesAllList);
        modelMap.addAttribute("publisherList",Constant.publishersList);
        modelMap.addAttribute("discountList",Constant.discountsList);
        modelMap.addAttribute("bookDrmRefList",publicationList);
        modelMap.addAttribute("bookIDsList", enyanBookService.getBookIDsList());


        if (null == enyanBook || enyanBook.getBookId()==0){
            this.setErrorMsg(modelMap, "修改失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        try {

            this.initInputBookSetInfo(enyanBook);

            enyanBookService.updateRecord(enyanBook);

        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            this.initBookValue(modelMap);
            return "admin/bookUpdate";
        }

        this.setSuccessMsg(modelMap, "修改书籍成功！");
        return "redirect:/closePage";
    }

    @RequestMapping("/addMultiUI")
    public String addMultiUI(EnyanRedeemCode enyanRedeemCode, ModelMap modelMap){
        log.debug("method addImgUI");

        return "admin/bookAddMulti";
    }

    @RequestMapping(value = "/saveMuti", method = RequestMethod.POST)
    public String saveMuti(@RequestParam(value = "uploadExcel", required = false) MultipartFile filedataPic,
                             EnyanRedeemCode enyanRedeemCode , ModelMap modelMap) {
        if (filedataPic == null || filedataPic.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择Excel！");
            return "admin/bookAddMulti";
        }

        File picFile ;
        try {
            // 获取图片的文件名
            String fileName = filedataPic.getOriginalFilename();
            // 获取图片的扩展名
            String extensionName = fileName.substring(fileName.lastIndexOf(".") + 1);

            // 新的图片文件名 = 获取时间戳+"."图片扩展名
            String newFileName = fileName+"_"+ System.currentTimeMillis() + "." + extensionName;


            //enyanImgService.addRecord(enyanImg);

            String filePath = SystemUtil.getExcelDir()+newFileName;
            picFile = new File(filePath);
            filedataPic.transferTo(picFile);
            //picFile.setWritable(true);//设置可写权限
            //picFile.setExecutable(true);//设置可执行权限
            //picFile.setReadable(true);//设置可读权限

            Runtime.getRuntime().exec("chmod 777 -R " + filePath);

            ExcelReaderMultiFromBook excelReaderMuti = new ExcelReaderMultiFromBook();
            excelReaderMuti.setBatchReadBeginRow(1);//从1开始
            excelReaderMuti.setEnyanBookService(enyanBookService);
            ExcelImportUtil.readExcel(filePath, excelReaderMuti, excelReaderMuti, excelReaderMuti.getBatchReadBeginRow());

            log.error("game over...");

        } catch (Exception e) {
            log.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传Excel失败！");
            this.setErrorMsg(modelMap, "上传Excel失败！");

            return "admin/bookAddMulti";
        }

        this.setSuccessMsg(modelMap, "添加书籍成功！");
        //return "redirect:/adminRedeemCode/codes";
        return "admin/bookAddMulti";
    }

    @RequestMapping(value = "/template-book", method = RequestMethod.GET)
    public ResponseEntity<byte[]> excelDownload(HttpServletRequest request, ModelMap modelMap){
        log.debug("excelDownload");
        ResponseEntity<byte[]> entity = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型

            String templateName = "bookImport.xlsx";
            String fileName = "书籍导入Excel.xlsx";

            File file = new File(FileUtil.getTemplatePath(templateName));
            String downloadFileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");;

            headers.setContentDispositionFormData("attachment", downloadFileName);//告知浏览器以下载方式打开
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            entity = new ResponseEntity<>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
            return entity;
            //ExcelExportUtil.createExcel();
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return entity;
    }


    /**
     *
     * 展示折扣信息
     * @param enyanBook 折扣类型 0：打折；1：直接减
     * @Date: 2017/12/27
     */
    private String getDiscountDescription(EnyanBook enyanBook){
        return enyanBook.getBookDiscountDescription();
    }
    /**
     *
     *  校验折扣信息是否合法
     * @param enyanBook
     * @Date: 2017/12/27
     */
    private String checkDiscountInfo(EnyanBook enyanBook){
        if (!Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())){
            return null;
        }
        if ((Constant.BYTE_VALUE_0).equals(enyanBook.getDiscountSingleType())){
            if (enyanBook.getDiscountSingleValue() == 100){
                return "打折优惠的折扣值不应为100";
            }
            if (enyanBook.getDiscountSingleValue() == 0){
                return "打折优惠的折扣值不应为0";
            }
            if (enyanBook.getDiscountSingleValue()<0){
                return "打折优惠的折扣值不应小于0";
            }

        }
        return null;
    }
    /**
     * <p>设置 选择套装书信息的功能</p>
     * @param enyanBook
     * @return: void
     * @since : 2020-07-20
     */
    private void initInputBookSetInfo(EnyanBook enyanBook){
        if (null != enyanBook.getBookIDs() && enyanBook.getBookIDs().length > 0){
            String[] ids = enyanBook.getBookIDs();
            List<Long> bookIds = new ArrayList<>();
            for (String id:ids){
                Long bookId = Long.parseLong(id);
                bookIds.add(bookId);
            }
            List<EnyanBook> bookList = this.enyanBookService.findBookByIds(bookIds);
            List<EnyanBook> newList = new ArrayList<>();
            for (EnyanBook tmpBook : bookList){
                EnyanBook newBook = new EnyanBook();
                newBook.setBookId(tmpBook.getBookId());
                newBook.setBookTitle(tmpBook.getBookTitle());

                newList.add(newBook);
            }
            BookSetInfo bookSetInfo = new BookSetInfo();
            bookSetInfo.setBooksToSetList(newList);

            //enyanBook.setSetInfo(JSON.toJSONString(bookSetInfo));
            enyanBook.setBookType(EBookConstant.BookType.EBOOK_SET);
        }else {
            enyanBook.setBookType(EBookConstant.BookType.EBOOK_SINGLE);
            //enyanBook.setSetInfo("");
        }
    }

    /**
     * <p>初始化书籍页面的参数</p>
     * @param modelMap
     * @return: void
     * @since : 2020-07-24
     */
    private void initBookValue(ModelMap modelMap){
        List<NameAndValue> publicationList = publicationService.findAllPublicationNameAndValues();
        modelMap.addAttribute("categoryList", Constant.categoriesAllList);
        modelMap.addAttribute("publisherList",Constant.publishersList);
        modelMap.addAttribute("discountList",Constant.discountsList);
        modelMap.addAttribute("bookDrmRefList",publicationList);
        modelMap.addAttribute("bookIDsList", Constant.booksList);
    }

    private void sendPresaleEmail(EnyanBook book, List<String> bccList,HttpServletRequest request) {
        try {

            Mail mail = new Mail();
            mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
            if (Constant.BYTE_VALUE_1.equals(book.getIsInCn())){//书籍语言(1 简体sc;2 繁体tc;3 英文eng)
                mail.setSubject("【恩道电子书】您预购的电子书《"+book.getBookTitle()+"》已正式上架，快來阅读吧！");
                mail.setFtl("email-presale-sc.ftl");
            } else if (Constant.BYTE_VALUE_3.equals(book.getIsInCn())) {//英文eng
                mail.setSubject("【Inspirata eBooks】The pre-order eBook of ["+book.getBookTitle()+"] that you purchased is now officially available！");
                mail.setFtl("email-presale-en.ftl");
            }else {
                mail.setSubject("【恩道電子書】您預購的電子書《"+book.getBookTitle()+"》已正式上架，快來閱讀吧！");
                mail.setFtl("email-presale-tc.ftl");
            }
            //mail.setSubject("【恩道電子書】您預購的電子書《"+book.getBookTitle()+"》已正式上架，快來閱讀吧！");
//            mail.setTo(history.getEmail());//
            //mail.setTo("<EMAIL>");
            mail.setBccList(bccList);

            Map<String, Object> model = new HashMap<>();
            model.put("signature", this.getMessage("shop.copyright",request));
            model.put("location", DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
            model.put("bookName",book.getBookTitle());

            mail.setModel(model);

            emailService.sendSimpleMessage(mail);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
