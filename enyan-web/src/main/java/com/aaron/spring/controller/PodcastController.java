package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.model.PodEpisode;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.spring.service.PodEpisodeService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description: 播客后台管理控制器
 */
@Slf4j
@Controller
@RequestMapping("/admin/podcast")
public class PodcastController extends BaseController {

    @Resource
    private PodPodcastService podPodcastService;

    @Resource
    private PodEpisodeService podEpisodeService;
    
    @RequestMapping(value = "/list")
    public String list(HttpServletRequest req, PodPodcast record, ModelMap modelMap) {
        if (null == record) {
            record = new PodPodcast();
        }
        if (null == record.getPage()) {
            record.setPage(new Page());
        }
        
        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            record.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            record.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件
        Map<String, Object> queryParams = new HashMap<>();
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType) {
                case "0": // 按名称搜索
                    record.setTitle(searchText);
                    break;
                case "1": // 按ID搜索
                    record.setId(Long.parseLong(searchText));
                    break;
            }
        }

        Page<PodPodcast> page = podPodcastService.queryRecords(record.getPage(), record);
        record.setPage(page);
        record.excutePageLand(queryParams);

        modelMap.put("page", page);
        modelMap.put("record", record);
        
        return "admin/podcast/list";
    }

    @RequestMapping("/addUI")
    public String addPodcastUI(PodPodcast record, ModelMap modelMap) {
        log.debug("method addPodcastUI");
        record.setIsDeleted(0);
        modelMap.put("record", record);
        return "admin/podcast/form";
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public String savePodcast(HttpServletRequest request, PodPodcast record, ModelMap modelMap) {
        log.debug("method savePodcast：" + record);
        
        // 参数验证
        if (StringUtils.hasLength(record.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写播客名称");
            modelMap.addAttribute("record", record);
            return "admin/podcast/form";
        }

        if (StringUtils.hasLength(record.getAuthorName()) == false) {
            this.setErrorMsg(modelMap, "请填写作者名称");
            modelMap.addAttribute("record", record);
            return "admin/podcast/form";
        }

        // 新增逻辑
        if (null == record.getPodcastId()) {
            this.setSuccessMsg(modelMap, "添加播客成功");
            record.setEpisodeCount(0);
            record.setIsPublished(0);
            record.setIsDeleted(0);
            record.setCreatedAt(new Date());
            podPodcastService.addRecord(record);
        } 
        // 修改逻辑
        else {
            this.setSuccessMsg(modelMap, "修改播客成功");
            podPodcastService.updateRecord(record);
        }
        
        return "redirect:/admin/podcast/list";
    }

    @RequestMapping("/del-{id}")
    public String deletePodcast(@PathVariable Long id) {
        podPodcastService.deleteRecordByPrimaryKey(id);
        return "redirect:/admin/podcast/list";
    }

    @RequestMapping(value = "/get-{id}")
    public String get(@PathVariable Long id, ModelMap modelMap) {
        ExecuteResult<PodPodcast> result = podPodcastService.queryRecordByPrimaryKey(id);
        modelMap.put("record", result.getResult());
        return "admin/podcast/form";
    }

    /**
     * 批量删除播客
     */
    @RequestMapping(value = "/batchDelete", method = RequestMethod.POST)
    public String batchDelete(@RequestParam("ids") Long[] ids) {
        log.debug("批量删除播客，ids: " + java.util.Arrays.toString(ids));
        for (Long id : ids) {
            podPodcastService.deleteRecordByPrimaryKey(id);
        }
        return "redirect:/admin/podcast/list";
    }

    // ==================== 单集管理相关方法 ====================

    /**
     * 单集列表页面
     */
    @RequestMapping(value = "/episode/list")
    public String episodeList(HttpServletRequest req, PodEpisode record, ModelMap modelMap) {
        if (null == record) {
            record = new PodEpisode();
        }
        if (null == record.getPage()) {
            record.setPage(new Page());
        }

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");
        String podcastId = req.getParameter("podcastId");

        if (StringUtils.hasLength(total)) {
            record.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            record.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }
        if (StringUtils.hasLength(podcastId)) {
            record.setPodcastId(Long.parseLong(podcastId));
        }

        // 高级查询条件
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");

        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType) {
                case "0": // 按标题搜索
                    record.setTitle(searchText);
                    break;
                case "1": // 按ID搜索
                    record.setEpisodeId(Long.parseLong(searchText));
                    break;
            }
        }

        Page<PodEpisode> page = podEpisodeService.queryRecords(record.getPage(), record);
        modelMap.put("page", page);
        modelMap.put("record", record);

        // 获取所有播客列表用于筛选下拉框
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        return "admin/podcast/episodeList";
    }

    /**
     * 单集添加页面
     */
    @RequestMapping("/episode/addUI")
    public String addEpisodeUI(@RequestParam(required = false) Long podcastId, PodEpisode record, ModelMap modelMap) {
        log.debug("method addEpisodeUI");
        record.setIsDeleted(0);
        record.setIsPublished(0);
        if (podcastId != null) {
            record.setPodcastId(podcastId);
        }
        modelMap.put("record", record);

        // 获取所有播客列表用于下拉选择
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        return "admin/podcast/episodeForm";
    }

    /**
     * 保存单集
     */
    @RequestMapping(value = "/episode/save", method = RequestMethod.POST)
    public String saveEpisode(HttpServletRequest request, PodEpisode record, ModelMap modelMap) {
        log.debug("method saveEpisode：" + record);

        // 参数验证
        if (StringUtils.hasLength(record.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写单集标题");
            modelMap.addAttribute("record", record);
            return "admin/podcast/episodeForm";
        }

        if (record.getPodcastId() == null) {
            this.setErrorMsg(modelMap, "请选择所属播客");
            modelMap.addAttribute("record", record);
            return "admin/podcast/episodeForm";
        }

        // 新增逻辑
        if (null == record.getEpisodeId()) {
            this.setSuccessMsg(modelMap, "添加单集成功");
            record.setIsDeleted(0);
            record.setCreatedAt(new Date());
            record.setListenCount(0);
            record.setLikeCount(0);
            podEpisodeService.addRecord(record);
        }
        // 修改逻辑
        else {
            this.setSuccessMsg(modelMap, "修改单集成功");
            podEpisodeService.updateRecord(record);
        }

        return "redirect:/admin/podcast/episode/list?podcastId=" + record.getPodcastId();
    }

    /**
     * 单集编辑页面
     */
    @RequestMapping(value = "/episode/get-{id}")
    public String getEpisode(@PathVariable Long id, ModelMap modelMap) {
        ExecuteResult<PodEpisode> result = podEpisodeService.queryRecordByPrimaryKey(id);
        modelMap.put("record", result.getResult());

        // 获取所有播客列表用于下拉选择
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        return "admin/podcast/episodeForm";
    }

    /**
     * 删除单集
     */
    @RequestMapping("/episode/del-{id}")
    public String deleteEpisode(@PathVariable Long id, @RequestParam(required = false) Long podcastId) {
        podEpisodeService.deleteRecordByPrimaryKey(id);
        if (podcastId != null) {
            return "redirect:/admin/podcast/episode/list?podcastId=" + podcastId;
        }
        return "redirect:/admin/podcast/episode/list";
    }

    /**
     * 批量删除单集
     */
    @RequestMapping(value = "/episode/batchDelete", method = RequestMethod.POST)
    public String batchDeleteEpisodes(@RequestParam("ids") Long[] ids, @RequestParam(required = false) Long podcastId) {
        log.debug("批量删除单集，ids: " + java.util.Arrays.toString(ids));
        for (Long id : ids) {
            podEpisodeService.deleteRecordByPrimaryKey(id);
        }
        if (podcastId != null) {
            return "redirect:/admin/podcast/episode/list?podcastId=" + podcastId;
        }
        return "redirect:/admin/podcast/episode/list";
    }

}
