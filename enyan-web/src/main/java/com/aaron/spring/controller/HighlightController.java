package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.model.EnyanReaderHighlights;
import com.aaron.spring.service.EnyanReaderHighlightService;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Aaron <PERSON>
 * @Description:
 * @Date: Created in  2023/2/13
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/highlightsAdmin")
public class HighlightController extends BaseController{
	@Resource
	private EnyanReaderHighlightService enyanReaderHighlightService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanReaderHighlights record, ModelMap modelMap){

		String bookIdStr = req.getParameter("searchText");
		if (StringUtils.hasLength(bookIdStr) == false){
			return "admin/userHighlights";
		}
		Long bookId = Long.parseLong(bookIdStr);
		Page<EnyanReaderHighlights> page = new Page<>();

		Map<String, Object> queryParams = new HashMap<>();
		queryParams.put("searchText",bookIdStr);
		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			page.setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			page.setCurrentPage(Integer.parseInt(currentPage));
		}

//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		page = enyanReaderHighlightService.findReaderHighlights(page, bookId, 0);//0:划线；1：书签

		for (EnyanReaderHighlights tmp : page.getRecords()) {
			if (StringUtils.hasLength(tmp.getLocatorText())){
				JSONObject object = JSONObject.parseObject(tmp.getLocatorText(),JSONObject.class);
				tmp.setResourceType(object.getString("highlight"));
			}
		}

//		page.setTotalRecord(100*page.getPageSize());
		
		EnyanPublisher onlyToShow = new EnyanPublisher();

		onlyToShow.setPage(page);
		onlyToShow.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",onlyToShow.getPageLand());
		modelMap.addAttribute("dto",record);
		modelMap.addAttribute("explan","用户划线");

		return "admin/userHighlights";
	}

	/*
	@ResponseBody
	@RequestMapping(value = "/solve")
	public ExecuteResult<String> solve(@RequestBody RestFeedback restObj, ModelMap modelMap,
	                                   HttpServletRequest request, HttpServletResponse response){
		ExecuteResult<String> result = new ExecuteResult<>();
		log.debug("dataId:{}",restObj.getDataId());
		if (null == restObj.getDataId()){
			result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanReaderHighlights update = new EnyanReaderHighlights();
		update.setDataId(restObj.getDataId());
		update.setDoneType(1);
		EnyanReaderHighlightsService.updateRecord(update);
		return result;
	}*/

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanReaderHighlights record = enyanReaderHighlightService.queryRecordByPrimaryKey(id).getResult();

		modelMap.addAttribute("record",record);
		modelMap.addAttribute("publisherList", Constant.publishersList);
		return "admin/blogAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		enyanReaderHighlightService.deleteRecordByPrimaryKey(id);
		return "redirect:/blog/list";
	}

	@RequestMapping("/addUI")
	public String addCompanyUI(EnyanReaderHighlights record, ModelMap modelMap){
		log.debug("method addCompanyUI");
		record.setIsDeleted(0);
		modelMap.addAttribute("publisherList",Constant.publishersList);
		modelMap.addAttribute("record",record);
		return "admin/blogAdd";
	}

}
