package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.PinYinUtil;
import com.aaron.spring.common.SystemUtil;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanImg;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanImgService;
import com.aaron.util.FilePropertiesUtil;
import com.aaron.util.ImageUtils;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2018/1/19
 * @Modified By:
 */
@Controller
@RequestMapping("/adminImg")
public class ImgController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(ImgController.class);

    @Resource
    private EnyanImgService enyanImgService;

    @Resource
    private EnyanBookService enyanBookService;

    @RequestMapping(value = "/imgs")
    public String imgsPage(HttpServletRequest req, EnyanImg enyanImg, ModelMap modelMap){
        //logger.info("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        if (null == enyanImg){
            enyanImg = new EnyanImg();
        }
        if (null == enyanImg.getPage()){
            enyanImg.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            enyanImg.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            enyanImg.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件：
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        String imageType = req.getParameter("imageType");
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType){
                case "0":
                    enyanImg.setImgDescription(searchText);
                    break;
            }
            queryParams.put("searchText",searchText);
            queryParams.put("searchType",searchType);
        }
        if (StringUtils.hasLength(imageType)){
            queryParams.put("imageType",imageType);
            enyanImg.setImageType(Integer.parseInt(imageType));
        }


        enyanImg.addOrder(new OrderObj("img_id", InterfaceContant.OrderBy.DESC));

        Page<EnyanImg> page = enyanImgService.queryRecords(enyanImg.getPage(),enyanImg);
        enyanImg.setPage(page);
        enyanImg.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",enyanImg.getPageLand());
        modelMap.addAttribute("enyanImg",enyanImg);
        modelMap.addAttribute("explan","图片管理");

        return "admin/imgs";
    }
    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getImgById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.debug("getImgById");
        EnyanImg enyanImg = enyanImgService.queryRecordByPrimaryKey(id).getResult();
        modelMap.addAttribute("enyanImg",enyanImg);
        return "admin/imgUpdate";
    }

    @RequestMapping("/del-{id}")
    public String delImgById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.debug("method delImgById");
        EnyanImg enyanImg = enyanImgService.queryRecordByPrimaryKey(id).getResult();
        try {
            String picPath = this.getPicPath(enyanImg.getImageType(), enyanImg.getImgName());
            File imgPic = new File(picPath);
            if (imgPic.exists()) {
                FileUtils.forceDelete(imgPic);
            }
            File imgPicSmall = new File(picPath+Constant.IMG_APP_THUMBNAIL);
            if (imgPicSmall.exists()) {
                FileUtils.forceDelete(imgPicSmall);
            }
            enyanImgService.deleteRecordByPrimaryKey(id);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "redirect:/adminImg/imgs";
    }

    @RequestMapping("/addImgUI")
    public String addImgUI(HttpServletRequest req, EnyanImg enyanImg, ModelMap modelMap){
        //logger.debug("method addImgUI");
        String imageType = req.getParameter("imageType");
        if (StringUtils.hasLength(imageType) == false){
            imageType = "0";
        }
        modelMap.addAttribute("bookIDsList", Constant.booksList);
        return "admin/imgAdd";
    }

    @RequestMapping("/addEpubUI")
    public String addEpubUI(HttpServletRequest req, EnyanImg enyanImg, ModelMap modelMap){
        //logger.debug("method addImgUI");
        String imageType = req.getParameter("imageType");
        if (StringUtils.hasLength(imageType) == false){
            imageType = "0";
        }
        modelMap.addAttribute("bookIDsList", Constant.booksList);
        return "admin/imgAddEpub";
    }

    @RequestMapping("/addBookCoverUI")
    public String addBookCoverUI(HttpServletRequest req, EnyanImg enyanImg, ModelMap modelMap){
        //logger.debug("method addImgUI");
        modelMap.addAttribute("bookIDsList", Constant.booksList);
        return "admin/imgAddBookCover";
    }

    @RequestMapping(value = "/saveImg", method = RequestMethod.POST)
    public String saveImg(
            @RequestParam(value = "uploadPic", required = false) MultipartFile filedataPic,
            EnyanImg enyanImg , ModelMap modelMap) {
        String returnJsp = "admin/imgAdd";
        if (StringUtils.hasLength(enyanImg.getImgDescription()) == false) {
            this.setErrorMsg(modelMap, "请添加描述！");
            return returnJsp;
        }
        if (filedataPic == null || filedataPic.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择文件！");
            return returnJsp;
        }

        try {
            long bookId = -1L;
            if (null != enyanImg.getBookIDs() && enyanImg.getBookIDs().length > 0){
                bookId = Long.parseLong(enyanImg.getBookIDs()[0]);
            }
            enyanImg.setBookId(bookId);
            // 获取图片的文件名
            String fileName = filedataPic.getOriginalFilename();
            // 获取图片的扩展名
            String extensionName = fileName .substring(fileName.lastIndexOf(".") + 1);

            // 新的图片文件名 = 获取时间戳+"."图片扩展名
            String newFileName = System.currentTimeMillis() + "." + extensionName;

            enyanImg.setImgName(newFileName);

            enyanImgService.addRecord(enyanImg);

            String picPath = this.getPicPath(enyanImg.getImageType(), newFileName);

            if (0 == enyanImg.getImageType()){//封面图
                BufferedImage originalImage = ImageIO.read(filedataPic.getInputStream());

                BufferedImage outputImage = ImageUtils.resizeImageFitWidth(originalImage, Constant.IMG_WEB_WIDTH);
                ImageIO.write(outputImage, "jpg", new File(picPath));

                String picPathSmall = picPath + Constant.IMG_APP_THUMBNAIL;
                BufferedImage outputImageSmall = ImageUtils.resizeImageFitWidth(originalImage, Constant.IMG_APP_WIDTH);
                ImageIO.write(outputImageSmall, "jpg", new File(picPathSmall));

                Runtime.getRuntime().exec("chmod 777 -R " + picPathSmall);
                if (bookId > 0){//更新书籍的封面及app URL
                    String picUrl = BookUtil.getUrlInBook(newFileName, "detail");
                    String picUrlSmall = BookUtil.getUrlInBook(newFileName + Constant.IMG_APP_THUMBNAIL, "detail");
                    EnyanBook book = new EnyanBook();
                    book.setBookId(bookId);
                    book.setBookCover(picUrl);
                    book.setBookCoverApp(picUrlSmall);

                    enyanBookService.updateByPrimaryKeySelective(book);
                }
            }else if (5 == enyanImg.getImageType()){//epub
                File picFile = new File(picPath);
                filedataPic.transferTo(picFile);

                if (bookId > 0){
                    String epubPath = BookUtil.getUrlInBook(newFileName, "epub");
                    EnyanBook book = new EnyanBook();
                    book.setBookId(bookId);
                    book.setBookSample(epubPath);

                    enyanBookService.updateByPrimaryKeySelective(book);
                }
            }else {
                File picFile = new File(picPath);
                filedataPic.transferTo(picFile);
            }
            //picFile.setWritable(true);//设置可写权限
            //picFile.setExecutable(true);//设置可执行权限
            //picFile.setReadable(true);//设置可读权限

            Runtime.getRuntime().exec("chmod 777 -R " + picPath);

        } catch (Exception e) {
            logger.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传失败！");
            this.setErrorMsg(modelMap, "上传失败！");

            return returnJsp;
        }

        this.setSuccessMsg(modelMap, "添加成功！");
        return "redirect:/adminImg/imgs";
    }
    @RequestMapping(value = "/updateImg", method = RequestMethod.POST)
    public String updateImg(
            @RequestParam(value = "uploadPic", required = false) MultipartFile filedataPic,
            EnyanImg enyanImg , ModelMap modelMap) {

        String returnJsp = "admin/imgUpdate";

        if (StringUtils.hasLength(enyanImg.getImgDescription()) == false) {
            this.setErrorMsg(modelMap, "请添加描述！");
            return returnJsp;
        }

        try {

            if (filedataPic != null && !filedataPic.isEmpty()){

                EnyanImg img = enyanImgService.queryRecordByPrimaryKey(enyanImg.getImgId()).getResult();
                if (null == img || img.getImgId()==0){
                    this.setErrorMsg(modelMap, "修改失败！");
                    return returnJsp;
                }
                // 获取图片的文件名
                //String fileName = filedataPic.getOriginalFilename();
                // 获取图片的扩展名
                //String extensionName = fileName .substring(fileName.lastIndexOf(".") + 1);

                // 新的图片文件名 = 获取时间戳+"."图片扩展名
                //String newFileName = enyanImg.getImgName();

               /* if (!newFileName.equals(enyanImg.getImgName())){//删除老File数据
                    File imgPic = new File(enyanImg.getImgName());
                    if (imgPic.exists()){
                        FileUtils.forceDelete(imgPic);
                    }
                }*/

                //enyanImg.setImgName(newFileName);

                String picPath = this.getPicPath(img.getImageType(), img.getImgName());

                if (0 == img.getImageType()){//封面图
                    BufferedImage originalImage = ImageIO.read(filedataPic.getInputStream());

                    BufferedImage outputImage = ImageUtils.resizeImageFitWidth(originalImage, Constant.IMG_WEB_WIDTH);
                    ImageIO.write(outputImage, "jpg", new File(picPath));

                    String picPathSmall = picPath + Constant.IMG_APP_THUMBNAIL;
                    BufferedImage outputImageSmall = ImageUtils.resizeImageFitWidth(originalImage, Constant.IMG_APP_WIDTH);
                    ImageIO.write(outputImageSmall, "jpg", new File(picPathSmall));


                }else if (5 == img.getImageType()){//epub
                    File picFile = new File(picPath);
                    filedataPic.transferTo(picFile);
                }else {
                    File picFile = new File(picPath);
                    filedataPic.transferTo(picFile);
                }


                //picFile.setWritable(true);//设置可写权限
                //picFile.setExecutable(true);//设置可执行权限
                //picFile.setReadable(true);//设置可读权限

                Runtime.getRuntime().exec("chmod 777 -R " + picPath);

            }
            enyanImgService.updateRecord(enyanImg);

        } catch (Exception e) {
            logger.error("上传图片失败.", e);
            modelMap.addAttribute("msg", "上传图片失败！");
            this.setErrorMsg(modelMap, "上传图片失败！");
            return returnJsp;
        }

        this.setSuccessMsg(modelMap, "修改成功！");
        return "redirect:/adminImg/imgs";
    }
    /**
     * <p>获取资源的路径</p>
     * @param imageType
     * @param name
     * @return java.lang.String
     * @since : 2021/7/23
     **/
    private String getPicPath(int imageType, String name){
        String picPath ;
        if (5 == imageType){//上传epub文件，单独处理
            picPath = SystemUtil.getProductEpubDir()+name;
        }else {
            picPath = SystemUtil.getProductDetailDir()+name;
        }
        return picPath;
    }
}
