package com.aaron.spring.controller;

import com.aaron.common.NameAndValue;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.EnyanDiscount;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanDiscountService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/17
 * @Modified By:
 */
@Controller
@RequestMapping("/discount")
public class DiscountController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(CategoryController.class);
    @Resource
    private EnyanDiscountService enyanDiscountService;

    @Resource
    private EnyanBookService enyanBookService;

    @RequestMapping(value = "/discounts")
    public String discountPage(HttpServletRequest req, EnyanDiscount discount, ModelMap modelMap){
        logger.info("discountPage");
        try {
            if (null == discount){
                discount = new EnyanDiscount();
            }
            if (null == discount.getPage()){
                discount.setPage(new Page());
            }
            Map<String, Object> queryParams = new HashMap<String, Object>();

            // 获取分页参数
            String total = req.getParameter("total");
            String currentPage = req.getParameter("pageNo");

            if (StringUtils.hasLength(total)) {
                discount.getPage().setTotalRecord(Integer.parseInt(total));
            }
            if (StringUtils.hasLength(currentPage)) {
                discount.getPage().setCurrentPage(Integer.parseInt(currentPage));
            }

            // 高级查询条件：
            String searchText = req.getParameter("searchText");
            String searchType = req.getParameter("searchType");
            if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
                switch (searchType){
                    case "0":
                        discount.setDiscountTitle(searchText);
                        break;
                }
                queryParams.put("searchText",searchText);
                queryParams.put("searchType",searchType);
            }

            Page<EnyanCategory> page = enyanDiscountService.queryRecords(discount.getPage(),discount);



            discount.setPage(page);
            discount.excutePageLand(queryParams);

            modelMap.addAttribute("list",page.getRecords());
            modelMap.addAttribute("pageLand",discount.getPageLand());

            modelMap.addAttribute("discount",discount);
            modelMap.addAttribute("explan","折扣管理");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "admin/discounts";
    }
    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getDiscountById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.debug("getDiscountById");
        EnyanDiscount enyanDiscount = enyanDiscountService.queryRecordByPrimaryKey(id).getResult();
        /*if (null != enyanDiscount.getStartTime()){
            enyanDiscount.setStartTimeStr(DateUtil.getDateStr(enyanDiscount.getStartTime()));
        }
        if (null != enyanDiscount.getEndTime()){
            enyanDiscount.setEndTimeStr(DateUtil.getDateStr(enyanDiscount.getEndTime()));
        }*/
        //20200701 - 20200831
        String rangeDate = DateFormatUtils.format(enyanDiscount.getStartTime(),"yyyyMMdd")+" - "
                + DateFormatUtils.format(enyanDiscount.getEndTime(),"yyyyMMdd");
        enyanDiscount.setRangeDate(rangeDate);
        modelMap.addAttribute("enyanDiscount",enyanDiscount);
        return "admin/discountEdit";
    }

    @RequestMapping(value = "/set-{id}", method = RequestMethod.GET)
    public String setDiscountById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.debug("setDiscountById");
        EnyanDiscount enyanDiscount = enyanDiscountService.queryRecordByPrimaryKey(id).getResult();
        modelMap.addAttribute("bookIDsList", Constant.booksList);
        modelMap.addAttribute("enyanDiscount",enyanDiscount);

        List<NameAndValue> selectedList;
        if (enyanDiscount.getDiscountType().equals(Constant.BYTE_VALUE_2)){//单个折扣
            selectedList = enyanBookService.findBookNameAndValueByDiscountSingleId(id);
        }else {
            selectedList = enyanBookService.findBookNameAndValueByDiscountId(id);
        }
        String[] ids = new String[selectedList.size()];
        for (int i = 0; i < selectedList.size(); i++) {
            ids[i] = selectedList.get(i).getValue();
        }
        enyanDiscount.setBookIDs(ids);
        enyanDiscount.setBookIDsOld(ids);
        return "admin/discountSet";
    }

    @RequestMapping(value = "/associate-{id}", method = RequestMethod.GET)
    public String associateDiscountById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.debug("associate");
        EnyanDiscount enyanDiscount = enyanDiscountService.queryRecordByPrimaryKey(id).getResult();
        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_1);
        enyanBook.setDiscountSingleId(id);
        enyanBook.setDiscountSingleStartTime(enyanDiscount.getStartTime());
        enyanBook.setDiscountSingleEndTime(enyanDiscount.getEndTime());
        enyanBook.setDiscountSingleValue(enyanDiscount.getDiscountSingleValue());
        enyanBook.setDiscountSingleType(Constant.BYTE_VALUE_0);
        enyanBook.setDiscountSingleDescription(enyanBook.getBookDiscountDescription());
        enyanBookService.updateBookSingleDiscountAssociateNDiscount(enyanBook);
        return "redirect:/closePage";
    }

    @RequestMapping(value = "/saveBookDiscount", method = RequestMethod.POST)
    public String saveBookDiscount(HttpServletRequest request, EnyanDiscount enyanDiscount, ModelMap modelMap){
        logger.debug("method saveBookDiscount："+enyanDiscount);
        EnyanDiscount discount = enyanDiscountService.queryRecordByPrimaryKey(enyanDiscount.getDiscountId()).getResult();

        String[] ids = enyanDiscount.getBookIDs();
        String[] idsOld = enyanDiscount.getBookIDsOld();
        if (idsOld == null){
            idsOld = new String[]{};
        }

        EnyanBook enyanBook = new EnyanBook();
        if (discount.getDiscountType().equals(EBookConstant.DiscountType.DISCOUNT_SINGLE)){//单个折扣
            enyanBook.setDiscountSingleId(discount.getDiscountId());
            enyanBook.setDiscountSingleType(Constant.BYTE_VALUE_0);
            enyanBook.setDiscountSingleIsValid(discount.getIsValid());
            enyanBook.setDiscountSingleValue(discount.getDiscountSingleValue());
            enyanBook.setDiscountSingleEndTime(discount.getEndTime());
            enyanBook.setDiscountSingleStartTime(discount.getStartTime());
            enyanBook.setDiscountSingleDescription(enyanBook.getBookDiscountDescription());
        }else {//
            enyanBook.setDiscountId(discount.getDiscountId());
            enyanBook.setDiscountDescription(discount.getDiscountTitle());
            enyanBook.setDiscountIsValid(discount.getIsValid());
        }

        EnyanDiscount discountUpdate = new EnyanDiscount();
        discountUpdate.setDiscountId(discount.getDiscountId());
        discountUpdate.setBookCount(ids.length);
        enyanDiscountService.updateRecordOnly(discountUpdate);

        enyanBookService.updateBookDiscount(enyanBook,ids,idsOld,discount.getDiscountType().equals(EBookConstant.DiscountType.DISCOUNT_SINGLE));
        return "redirect:/closePage";
    }

    @RequestMapping("/del-{id}")
    public String delDiscountById(@PathVariable("id")Long id , ModelMap modelMap){
        logger.debug("method delDiscountById");
        enyanDiscountService.deleteRecordByPrimaryKey(id);
        return "redirect:/discount/discounts";
    }

    @RequestMapping("/addDiscountUI")
    public String addDiscountUI(EnyanDiscount enyanDiscount){
        logger.debug("method addDiscountUI");
        enyanDiscount.setIsValid(Constant.BYTE_VALUE_1);
        enyanDiscount.setDiscountType(Constant.BYTE_VALUE_2);
        return "admin/discountAdd";
    }

    @RequestMapping(value = "/saveDiscount", method = RequestMethod.POST)
    public String saveDiscount(HttpServletRequest request, EnyanDiscount enyanDiscount, ModelMap modelMap){
        logger.debug("method saveDiscount："+enyanDiscount);

        if (StringUtils.hasLength(enyanDiscount.getDiscountTitle()) == false){
            this.setErrorMsg(modelMap,"请填写折扣名称");
            return "admin/discountAdd";
        }
        if (null == enyanDiscount.getDiscountType()){
            this.setErrorMsg(modelMap,"请选择折扣类型");
            return "admin/discountAdd";
        }
        if (enyanDiscount.getDiscountType() == 0){
            if (null == enyanDiscount.getCumulateDiscount() || null == enyanDiscount.getCumulatePackage()){
                this.setErrorMsg(modelMap,"请完善累计折旧");
                return "admin/discountAdd";
            }
        }else if (enyanDiscount.getDiscountType() == 2){
            if (null == enyanDiscount.getDiscountSingleValue()){
                this.setErrorMsg(modelMap,"请完善单个折旧");
                return "admin/discountAdd";
            }
        }else{
            if (null == enyanDiscount.getFullBase() || null == enyanDiscount.getFullMinus()){
                this.setErrorMsg(modelMap,"请完善满减折旧");
                return "admin/discountAdd";
            }
        }
        // 高级查询条件：
        String startDate, endDate ;
        String rangeDate = request.getParameter("rangeDate");
        if (StringUtils.hasLength(rangeDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            return "admin/discountAdd";
        }
        String[] rangeDateArray = rangeDate.split("-");
        if (rangeDateArray.length != 2){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            return "admin/discountAdd";
        }
        startDate = rangeDateArray[0].trim();
        endDate = rangeDateArray[1].trim();
        if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            return "admin/discountAdd";
        }
        try {
            enyanDiscount.setStartTime(DateUtils.parseDate(startDate,"yyyyMMdd"));
            enyanDiscount.setEndTime(DateUtils.parseDate(endDate,"yyyyMMdd"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        /*if (StringUtils.isEmpty(enyanDiscount.getStartTimeStr())||StringUtils.isEmpty(enyanDiscount.getEndTimeStr())){
            this.setErrorMsg(modelMap,"请完善折扣的起止时间");
            return "admin/discountAdd";
        }
        enyanDiscount.setStartTime(DateUtil.getDate(enyanDiscount.getStartTimeStr()));
        enyanDiscount.setEndTime(DateUtil.getDate(enyanDiscount.getEndTimeStr()));*/
        if (null == enyanDiscount.getDiscountId()){
            this.setSuccessMsg(modelMap,"添加折扣成功");
            enyanDiscountService.addRecord(enyanDiscount);
        }else {
            this.setSuccessMsg(modelMap,"修改折扣成功");
            enyanDiscountService.updateRecord(enyanDiscount);
        }
        return "redirect:/discount/discounts";
    }

}
