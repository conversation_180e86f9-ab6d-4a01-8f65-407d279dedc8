package com.aaron.spring.controller;

import com.aaron.a4j.util.ResultUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.CreditInfo;
import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.exception.BusinessException;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.security.function.domain.IUser;
import com.aaron.security.function.service.UserService;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.api.v4.model.RestComment;
import com.aaron.spring.common.*;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.*;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.*;
import com.aaron.spring.common.OrderUtil;
import com.aaron.util.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.alipay.config.AlipayConfig;
import com.alipay.util.AlipayNotify;
import com.alipay.util.AlipaySubmit;
import com.stripe.config.StripeConfig;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.net.RequestOptions;
import com.stripe.util.StripeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mobile.device.Device;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.LongPredicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.aaron.spring.common.OrderUtil.PRE_RENT;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/12/2
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping(value = {"/shop",""})
public class ShopController extends BaseController{
//    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EnyanBookService enyanBookService;

    //@Resource
    //private BaseDataService baseDataService;

    @Resource
    private EnyanOrderService enyanOrderService;

    @Resource
    private EmailService emailService;

    @Resource
    private EnyanWishService enyanWishService;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private UserService userService;

    @Resource
    private SendEmailService sendEmailService;

//    @Resource
//    private EnyanAcsmService enyanAcsmService;
//
//    @Resource
//    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EnyanRedeemCodeService enyanRedeemCodeService;

    @Resource
    private EnyanBookBuyService enyanBookBuyService;

    @Resource
    private EnyanBookSetService enyanBookSetService;

    @Resource
    private EnyanBookListService enyanBookListService;

    @Resource
    private LogService  logService;

    @Resource
    private EnyanCartService enyanCartService;

    @Resource
    private EnyanCouponService enyanCouponService;

    @Resource
    private GeoIPLocationService geoIPLocationService;

    @Resource
    private EnyanBlogService enyanBlogService;

    @Resource
    private EnyanSubscriptionService enyanSubscriptionService;

    @Resource
    private EnyanRentService enyanRentService;

    @Resource
    private EnyanRentDetailService enyanRentDetailService;

    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EnyanPlanNoteService enyanPlanNoteService;

    @Resource
    private EnyanPlanService enyanPlanService;

    @Resource
    private EnyanReaderHighlightService enyanReaderHighlightService;

    @Resource
    private EnyanCommentService enyanCommentService;

    @Resource
    private PdfGenaratorUtil pdfGenaratorUtil;

    /*
    @Autowired
    private MessageSource messageSource;

    @Autowired
    private LocaleResolver localeResolver;
    */
    @RequestMapping(value = "/pdfView-{pdf}")
    public String pdfView(@PathVariable("pdf")String pdf, ModelMap modelMap, HttpServletRequest request){
        modelMap.addAttribute("pdf",pdf);
        return "/shop/pdfView";
    }

    @RequestMapping(value = "/preDownload-{orderNum}-{bookId}")
    public String preDownload(@PathVariable("orderNum")String orderNum,@PathVariable("bookId")Long bookId,
                              ModelMap modelMap, HttpServletRequest request){
        modelMap.addAttribute("orderNum",orderNum);
        modelMap.addAttribute("bookId",bookId);
        return "/shop/download";
    }

    @RequestMapping(value = "/bookRead")
    public String bookRead(ModelMap modelMap, HttpServletRequest request){
        return "/shop/bookRead";
    }

    @RequestMapping(value = {"/rent","/renttoown"})
    public String rent(ModelMap modelMap, HttpServletRequest request){
        return "redirect:/rent-to-own";
    }

    @RequestMapping(value = "/checkoutReturnShow")
    public String checkoutReturnShow(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response){
        //response.addHeader("Content-Security-Policy", "default-src 'self'; frame-ancestors 'self'; frame-src 'self'; style-src 'self'; connect-src 'self'; script-src 'self' 'unsafe-eval'; media-src 'self'; img-src 'self'; base-uri 'self';");
        response.addHeader("X-Frame-Options", "SAMEORIGIN");
        response.addHeader("Content-Security-Policy", "default-src 'self'; frame-ancestors 'self'; frame-src 'self'; worker-src 'self'; child-src 'self'; script-src 'self'; sandbox allow-same-origin; ");
        response.addHeader("X-XSS-Protection", "0");
        response.addHeader("P3P", "CP=CAO PSA OUR");
        response.addHeader("Access-Control-Allow-Methods", "POST, GET");
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Credentials", "true");
        return "/shop/checkoutReturn";
    }

    @RequestMapping(value = "/gratiscopy")
    public String gratiscopy(ModelMap modelMap, HttpServletRequest request){
        return "/shop/gratiscopy";
    }

    @RequestMapping(value = "/gratiscopyInfo")
    public String gratiscopyInfo(ModelMap modelMap, HttpServletRequest request){
        return "/shop/gratiscopyInfo";
    }

    /**
     * <p>初始化Api调用时要用到的初始值</p>
     * @param modelMap
     * @param request
     * @return void
     * @since : 2022/11/29
     **/
    private void initApiValue(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response){
        String email = "";
        String name = "";
        String isLogin = "0";
        if (UserUtils.isAnonymous() == false){
            email = UserUtils.getCurrentLoginUser().getEmail();
            isLogin = "1";
            name = UserUtils.getCurrentLoginUser().getNickName();
        }
        Object sessionData = request.getSession().getAttribute(EBookConstant.Cart.DATA);
        String cartNum = "0";
        if (null != sessionData){
            Map<String,String> data = (Map<String, String>) sessionData;
            if (null != data.get("quantity")){
                cartNum = data.get("quantity");
            }
        }
        String currency = CookieUtil.getCookieValue(InterfaceContant.CookieName.CURRENCY, String.class, request);
        if (StringUtils.isEmpty(currency)){
            currency = CurrencyType.CNY.getHeaderName();
        }
        JSONObject user = new JSONObject();
        user.put("isLogin", isLogin);//是否已经登陆
        user.put("name", name);
        user.put("email", email);//email
        user.put("cart", cartNum);//购物车个数
        modelMap.addAttribute("user", JSONObject.toJSONString(user));

        Locale locale = null;
        String localeString = "zh_CN";
        //localeString = (String)request.getServletContext().getAttribute("locale");
        locale = RequestContextUtils.getLocaleResolver(request).resolveLocale(request);
        if (null != locale){
            localeString = locale.toString();
        }
        JSONObject config = new JSONObject();
        config.put("currency", currency);//货币类型
        config.put("locale", localeString);//语言

        modelMap.addAttribute("config", JSONObject.toJSONString(config));
    }

    @RequestMapping(value = "/myRent")
    public String myRent(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response){
        this.initApiValue(modelMap, request,response);
        return "/shop/myRent";
    }

    @RequestMapping(value = "/frontInfo")
    public String frontInfo(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response){
        //this.initApiValue(modelMap, request,response);
        return "/shop/frontInfo";
    }

    @RequestMapping(value = "/cbcs")
    public String cbcs(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response){
        //this.initApiValue(modelMap, request,response);
        Map<String,List<EnyanBook>> map = new HashMap<>();
        map.put("otSc",BookUtil.getBooksInRentType(EBookConstant.RentTypeCode.CBCS_OT, EBookConstant.RentLang.Hans).stream().map(id->enyanBookService.queryRecordByPrimaryKey(id).getResult()).collect(Collectors.toList()));
        map.put("otTc",BookUtil.getBooksInRentType(EBookConstant.RentTypeCode.CBCS_OT, EBookConstant.RentLang.Hant).stream().map(id->enyanBookService.queryRecordByPrimaryKey(id).getResult()).collect(Collectors.toList()));
        map.put("ntSc",BookUtil.getBooksInRentType(EBookConstant.RentTypeCode.CBCS_NT, EBookConstant.RentLang.Hans).stream().map(id->enyanBookService.queryRecordByPrimaryKey(id).getResult()).collect(Collectors.toList()));
        map.put("ntTc",BookUtil.getBooksInRentType(EBookConstant.RentTypeCode.CBCS_NT, EBookConstant.RentLang.Hant).stream().map(id->enyanBookService.queryRecordByPrimaryKey(id).getResult()).collect(Collectors.toList()));
        modelMap.addAttribute("bookList",map);
        return "/shop/cbcs";
    }

    @RequestMapping(value = "/index")
    public String index(HttpServletRequest request, EnyanPublisher publisher, ModelMap modelMap) throws CloneNotSupportedException {

        //this.initCart(modelMap,request);
//        System.out.println(messageSource.getMessage("shop.indexEditor",null,localeResolver.resolveLocale(request)));
//        System.out.println(localeResolver.resolveLocale(request));
//        System.out.println(LocaleContextHolder.getLocale());
        String area = this.getArea(request);
        modelMap.addAttribute("categoryId","0");

        List<EnyanBook> recommendedList = new ArrayList<>();
        List<EnyanCategory> categoryList = new ArrayList<>();

        for (EnyanBook book : Constant.indexRecommendedList){
            if (book.getAreaDiscount() > 0){
                EnyanBook newBook = (EnyanBook) book.clone();
                newBook.resetByArea(area);
                recommendedList.add(newBook);
            }else {
                recommendedList.add(book);
            }
        }

        for (EnyanCategory category:Constant.indexCategoryList){
            if (null == category.getPage() || null == category.getPage().getRecords()){
                continue;
            }
            EnyanCategory newCategory = new EnyanCategory();
            newCategory.setCategoryId(category.getCategoryId());
            newCategory.setCategoryName(category.getCategoryName());
            newCategory.setCategoryNameEn(category.getCategoryNameEn());
            newCategory.setCategoryNameTc(category.getCategoryNameTc());
            Page<EnyanBook> page = new Page<>();
            page.setRecords(new ArrayList<>());
            newCategory.setPage(page);

            for (Object object : category.getPage().getRecords()){
                EnyanBook book = (EnyanBook) object;
                if (book.getAreaDiscount() > 0){
                    EnyanBook newBook = (EnyanBook) book.clone();
                    newBook.resetByArea(area);
                    newCategory.getPage().getRecords().add(newBook);
                }else {
                    newCategory.getPage().getRecords().add(book);
                }
            }
            categoryList.add(newCategory);
        }

        modelMap.addAttribute("recommendedList",recommendedList);
        modelMap.addAttribute("indexCategoryList",categoryList);
        modelMap.addAttribute("config",Constant.DEFAULT_REST_CONFIG);

        if (!UserUtils.isAnonymous()){
            CartInfoGerenal cartInfoGerenal = this.initCartInfoGeneral(modelMap,request);
            Map<String,String> data = new HashMap<>();
            if (null != cartInfoGerenal){
                data.put("quantity",String.valueOf(cartInfoGerenal.getQuantity()));
                //data.put("amountCny",String.valueOf(cartInfo.getAmountCny()));
                //data.put("amountUsd",String.valueOf(cartInfo.getAmountUsd()));
                request.getSession().setAttribute(EBookConstant.Cart.DATA,data);
            }
        }

        return "shop/index";
    }

    @RequestMapping(value = "/index-{path}")
    public String indexFaq(@PathVariable("path")String path, ModelMap modelMap){
        if (StringUtils.hasLength(path) == false){
            return "500";
        }
        path = AaronUtils.toUpperCaseFirst(path.toLowerCase());
        return "shop/index"+path;
    }
    @RequestMapping(value = "/category-{categoryId}-{orderBy}-{isInCn}-{publisherId}------")
    public String category2(@PathVariable("categoryId")Long categoryId, @PathVariable("orderBy")String orderBy,
                           @PathVariable("isInCn")String isInCn, @PathVariable("publisherId")Long publisherId){
        return  "redirect:/category-"+categoryId+"-"+orderBy+"-"+isInCn+"-"+publisherId+"-0-0-0-0-0-0";
    }
    /**
     * 增加参数的时候，还需要同步修改 searchBook方法的
     * */
    @RequestMapping(value = "/category-{categoryId}-{orderBy}-{isInCn}-{publisherId}-{isFreeType}-{isPresaleType}-{isDiscountType}-{isSpecialOffer}-0-0")
    public String bookCategory(@PathVariable("categoryId")Long categoryId, @PathVariable("orderBy")String orderBy,
                           @PathVariable("isInCn")String isInCn, @PathVariable("publisherId")Long publisherId,
                           @PathVariable("isFreeType")String isFreeType, @PathVariable("isPresaleType")String isPresaleType,
                           @PathVariable("isDiscountType")String isDiscountType, @PathVariable("isSpecialOffer")String isSpecialOffer,
                           HttpServletRequest request, HttpServletResponse response, ModelMap modelMap){
        //this.initCart(modelMap,request);
        //isFreeType: 0 默认；1：免费；2：付费
        if (null == orderBy){
            orderBy = "grid";
        }
        if (StringUtils.hasLength(isFreeType) == false || "1".equals(isFreeType) == false){
            isFreeType = "0";
        }
        if (StringUtils.hasLength(isPresaleType) == false || "1".equals(isPresaleType) == false){
            isPresaleType = "0";
        }
        if (StringUtils.hasLength(isDiscountType) == false || "1".equals(isDiscountType) == false){
            isDiscountType = "0";
        }
        if (StringUtils.hasLength(isSpecialOffer) == false || "1".equals(isSpecialOffer) == false){
            isSpecialOffer = "0";
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        String order = request.getParameter("order");//排序
        if(StringUtils.hasLength(order) == false){
            if ("1".equals(isFreeType)) {//是免费的书籍
                order = "0";
            }else{
                order = "5";
            }
        }

        queryParams.put("order",order);

        //String explan = this.getCategoryName(String.valueOf(categoryId));

        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);
        if (null != categoryId && categoryId>0){
            enyanBook.setCategoryId(categoryId);
        }
        if (null != publisherId && publisherId>0){
            enyanBook.setPublisherId(publisherId);
        }
        if (StringUtils.hasLength(isInCn)){
            switch (isInCn){
                case "1":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_1);
                    break;
                case "2":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_2);
                    break;
                case "3":
                    enyanBook.setIsInCn(Constant.BYTE_VALUE_3);
                    break;
            }
        }
        if ("0".equals(order)){
            OrderObj orderObj = new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("1".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("2".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("3".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("4".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("5".equals(order)){//默认，按照优先级次序
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }
        OrderObj orderObjId = new OrderObj("book_id",InterfaceContant.OrderBy.ASC);
        enyanBook.addOrder(orderObjId);
        if ("1".equals(isFreeType)){//是免费的书籍
            enyanBook.setPrice(Constant.VALUE_0);
        }
        if ("1".equals(isPresaleType)){
            enyanBook.setSalesModel(1);
        }
        if ("1".equals(isDiscountType)){//N件折商品
            enyanBook.setDiscountIsValid(Constant.BYTE_VALUE_1);
        }
        if ("1".equals(isSpecialOffer)){
            enyanBook.setSpecialOffer(1);
        }
        Page<EnyanBook> page = new Page();
        page.setPageSize(12);
        enyanBook.setPage(page);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }
        /*
        if ("list".equals(orderBy)){
            page = enyanBookService.queryRecordsUseBlob(enyanBook.getPage(),enyanBook);
            for (EnyanBook book:page.getRecords()){
                if (StringUtils.hasLength(book.getBookDescription())){
                    book.setBookDescription(Jsoup.clean(book.getBookDescription(), Whitelist.none()));
                }
            }

        }else {
            page = enyanBookService.queryRecords(enyanBook.getPage(),enyanBook);
        }*/
        page = enyanBookService.queryRecords(enyanBook.getPage(),enyanBook);
        enyanBook.excuteFrontPageLand(queryParams);

        String area = this.getArea(request);
        for (Object object:page.getRecords()){
            ((EnyanBook) object).resetByArea(area);
        }

        modelMap.addAttribute("bookList",page.getRecords());
        modelMap.addAttribute("pageLand",enyanBook.getPageLand());
        modelMap.addAttribute("explan",this.getCategoryName(String.valueOf(categoryId)));
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));


        if ("list".equals(orderBy)){
            return "shop/categoryList";
        }

        return "shop/categoryGrid";
    }

    @RequestMapping(value = "/searchBook")
    public String searchBookOrAuthor(HttpServletRequest request, HttpServletResponse response, EnyanBook enyanBook, ModelMap modelMap){
        //this.initCart(modelMap,request);

        //@RequestParam Map<String,String> allRequestParams
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        String order = request.getParameter("order");//排序
        if(StringUtils.hasLength(order) == false){
            order = "3";
        }

        queryParams.put("order",order);
        //String explan = this.getCategoryName(String.valueOf(categoryId));


        if (StringUtils.hasLength(enyanBook.getSearchText())){
            queryParams.put("searchText",enyanBook.getSearchText());
        }

        Page page = new Page();
        page.setPageSize(12);
        enyanBook.setPage(page);
        //book.setShelfStatus(Constant.BYTE_VALUE_1);
        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }
        if ("0".equals(order)){
            OrderObj orderObj = new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("1".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("2".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("3".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("4".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("5".equals(order)){//默认，按照优先级次序
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }

        page = enyanBookService.searchBookByTitleOrAuthor(enyanBook);

        enyanBook.excuteFrontPageLand(queryParams);

        String area = this.getArea(request);
        for (Object object:page.getRecords()){
            ((EnyanBook) object).resetByArea(area);
        }

        modelMap.addAttribute("book",enyanBook);
        modelMap.addAttribute("bookList",page.getRecords());
        modelMap.addAttribute("pageLand",enyanBook.getPageLand());
        modelMap.addAttribute("explan",enyanBook.getSearchText());
        modelMap.addAttribute("isSearch","1");
        modelMap.addAttribute("isInCn","0");
        modelMap.addAttribute("publisherId","0");
        modelMap.addAttribute("categoryId","0");
        modelMap.addAttribute("isFreeType","0");
        modelMap.addAttribute("isPresaleType","0");
        modelMap.addAttribute("isDiscountType","0");
        modelMap.addAttribute("isSpecialOffer","0");
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "shop/categoryGrid";
    }

    @RequestMapping(value = "/searchAuthor")
    public String searchBookByAuthor(HttpServletRequest request, HttpServletResponse response, EnyanBook enyanBook, ModelMap modelMap){
        //this.initCart(modelMap,request);

        //@RequestParam Map<String,String> allRequestParams
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        String order = request.getParameter("order");//排序
        if(StringUtils.hasLength(order) == false){
            order = "3";
        }

        queryParams.put("order",order);
        //String explan = this.getCategoryName(String.valueOf(categoryId));


        if (StringUtils.hasLength(enyanBook.getSearchText())){
            queryParams.put("searchText",enyanBook.getSearchText());
        }

        Page page = new Page();
        page.setPageSize(12);
        enyanBook.setPage(page);
        //book.setShelfStatus(Constant.BYTE_VALUE_1);
        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }
        if ("0".equals(order)){
            OrderObj orderObj = new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("1".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("2".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("3".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("4".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("5".equals(order)){//默认，按照优先级次序
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }

        page = enyanBookService.searchBookByAuthor(enyanBook);

        enyanBook.excuteFrontPageLand(queryParams);

        String area = this.getArea(request);
        for (Object object:page.getRecords()){
            ((EnyanBook) object).resetByArea(area);
        }

        modelMap.addAttribute("book",enyanBook);
        modelMap.addAttribute("bookList",page.getRecords());
        modelMap.addAttribute("pageLand",enyanBook.getPageLand());
        modelMap.addAttribute("explan",enyanBook.getSearchText());
        modelMap.addAttribute("isSearch","1");
        modelMap.addAttribute("isInCn","0");
        modelMap.addAttribute("publisherId","0");
        modelMap.addAttribute("categoryId","0");
        modelMap.addAttribute("isFreeType","0");
        modelMap.addAttribute("isPresaleType","0");
        modelMap.addAttribute("isDiscountType","0");
        modelMap.addAttribute("isSpecialOffer","0");
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "shop/categoryGrid";
    }

    @RequestMapping(value = "/revokeAction", method = RequestMethod.POST)
    public String revokeAction(AuthUser authUser, ModelMap modelMap, HttpServletRequest request) {
        log.debug("user email={},password={}",authUser.getEmail(),authUser.getUserPassword());
        if (org.apache.commons.lang3.StringUtils.isBlank(authUser.getEmail())) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.null", request));
            return "shop/indexRevoke";
        }
        if (!EmailValidatorUtil.validate(authUser.getEmail())) {
            this.setErrorMsg(modelMap, this.getMessage("error.email.invalid", request));
            return "shop/indexRevoke";
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(authUser.getUserPassword())) {
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.null", request));
            return "shop/indexRevoke";
        }
        AuthUser retUser = authUserService.getUserByEmail(authUser.getEmail()).getResult();
        if (null == retUser){
            this.setErrorMsg(modelMap, this.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", request));
            return "shop/indexRevoke";
        }
        if (!authUserService.isPasswordValid(retUser.getUserPassword(),authUser.getUserPassword(),retUser.getSalt())){
            this.setErrorMsg(modelMap, this.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", request));
            return "shop/indexRevoke";
        }
        if (Constant.TEST_ACCOUNT.contains(authUser.getEmail())) {//是测试账户，不可以注销
            this.setErrorMsg(modelMap, this.getMessage("error.email.invalid", request));
            //return "shop/indexRevoke";
        }

        String newEmail = authUser.getEmail();
        if (newEmail.length() > 70){//数据最多可以存储96，为避免溢出，需要提前处理一下长度
            newEmail = newEmail.substring(0, 70);
        }
        Float random = RandomUtils.nextFloat(0, 1f)*10000;
        newEmail = newEmail + "_revoked_"+random.intValue();
        authUserService.revokeLcpUser(authUser.getEmail(), newEmail);
        authUserService.revokeUser(authUser.getEmail(), newEmail);
        enyanBookBuyService.revokeUser(authUser.getEmail(), newEmail);
        enyanOrderDetailService.revokeUser(authUser.getEmail(), newEmail);
        enyanOrderService.revokeUser(authUser.getEmail(), newEmail);
        enyanWishService.revokeUser(authUser.getEmail(), newEmail);
        enyanCartService.revokeUser(authUser.getEmail(), newEmail);
        enyanPlanNoteService.revokeUser(authUser.getEmail(), newEmail);
        enyanPlanService.revokeUser(authUser.getEmail(), newEmail);
        enyanRedeemCodeService.revokeUser(authUser.getEmail(), newEmail);
        enyanReaderHighlightService.revokeUser(authUser.getEmail(), newEmail);
        this.setSuccessMsg(modelMap, this.getMessage("success.revoke", request));
        return "successResult";
    }

    @RequestMapping(value = "/currency/{currency}/{toUrl}")
    public String changeCurrency(@PathVariable("currency")String currency, @PathVariable("toUrl")String toUrl,HttpServletResponse response){
        if (StringUtils.hasLength(toUrl) == false || "/".equals(toUrl)){
            toUrl = "index";
        }
        if (null != currency){
            //CurrencyType currencyType = CurrencyType.getPriceNameByHeaderName(currency);
            CookieUtil.addCookie(InterfaceContant.CookieName.CURRENCY, currency, response,false,false);
        }
        return "redirect:/"+toUrl;
    }
    @ResponseBody
    @RequestMapping(value = "/currencyjson-{currency}")
    public String changeCurrencyJson(@PathVariable("currency")String currency, HttpServletResponse response){

        if (null != currency){
            if (Locale.SIMPLIFIED_CHINESE.getCountry().equals(currency)){
                currency = Locale.TRADITIONAL_CHINESE.getCountry();
            }else {
                currency = Locale.SIMPLIFIED_CHINESE.getCountry();
            }
            //CookieUtil.addCookie(InterfaceContant.CookieName.CURRENCY, currency, response);
            CookieUtil.addCookie(InterfaceContant.CookieName.CURRENCY, currency, response,false,false);
        }
        return null;
    }

    @RequestMapping(value = "/book-{bookId}")
    public String bookInfo(@PathVariable("bookId")String bookIdString, ModelMap modelMap,HttpServletRequest request){
        if (StringUtils.hasLength(bookIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        String[] bookIdSpilit = bookIdString.split("#");
        if (bookIdSpilit.length == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        bookIdString = bookIdSpilit[0];
        if (NumberUtils.isDigits(bookIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        Long bookId = Long.parseLong(bookIdString);
        if (null == bookId || bookId<0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        String area = this.getArea(request);
        //log.error("area:{}",area);
        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
        if (null == enyanBook){

            //enyanBook = new EnyanBook();
            //throw new BusinessException(Integer.valueOf("106"),getMessage("error.404.book",request),"找不到 bookId:"+bookId);
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        if (!enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1)){
            modelMap.addAttribute(Error_Msg,getMessage("error.book.shelf.down",request));
            return "/fail";
        }
        if (0 > enyanBook.getPublisherId()){
            enyanBook.setPublisherId(0L);
        }
        modelMap.addAttribute("isLogin","0");
        modelMap.addAttribute("sex","0");
        if (!UserUtils.isAnonymous()){
            if (EBookConstant.BookType.EBOOK_SET == enyanBook.getBookType()){//套装书
                List<EnyanOrder> list = enyanOrderService.selectByBookId(enyanBook.getBookId(),UserUtils.getCurrentLoginUser().getEmail());
                if (list.isEmpty()){
                    modelMap.addAttribute("isBought",false);
                }else{
                    modelMap.addAttribute("isBought",true);
                }
            }else{//单本书
                EnyanOrderDetail orderDetail = new EnyanOrderDetail();
                orderDetail.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
                orderDetail.setBookId(enyanBook.getBookId());
                //List<EnyanOrderDetail> orderDetailList = enyanOrderDetailService.findAllOrderDetailList(orderDetail);
                List<EnyanBookBuy>  buyList = enyanBookBuyService.getBookIDAndNameByEmailAndBookId(UserUtils.getCurrentLoginUser().getEmail(), enyanBook.getBookId());
                if (null == buyList || buyList.isEmpty()){
                    modelMap.addAttribute("isBought",false);
                }else{
                    modelMap.addAttribute("isBought",true);
                }
            }
            IUser user = UserUtils.getCurrentLoginUser();
            modelMap.addAttribute("isLogin","1");
            modelMap.addAttribute("nickName",UserUtils.getCurrentLoginUser().getNickName());
            if (user.getUserInfo() instanceof AuthUser){
                AuthUser authUser = (AuthUser)user.getUserInfo();
                modelMap.addAttribute("sex",authUser.getSex());
            }
        }
        enyanBook.resetByArea(area);
        modelMap.addAttribute("enyanBook",enyanBook);

        return "shop/bookInfo";
    }

    @RequestMapping(value = "/bookset-{dataId}")
    public String bookSet(@PathVariable("dataId")String dataIdString, ModelMap modelMap, HttpServletRequest request){
        if (StringUtils.hasLength(dataIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        String[] dataIdSpilit = dataIdString.split("#");
        if (dataIdSpilit.length == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        dataIdString = dataIdSpilit[0];
        if (NumberUtils.isDigits(dataIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        Long dataId = Long.parseLong(dataIdString);
        if (null == dataId || dataId<0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }

        EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(dataId).getResult();
        if (null == bookSet || null == bookSet.getSetId()){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        String order = request.getParameter("order");//排序


        queryParams.put("order",order);

        //String explan = this.getCategoryName(String.valueOf(categoryId));

        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);
        enyanBook.setSalesModel(0);
        enyanBook.setSetId(dataId);

        if ("0".equals(order)){
            OrderObj orderObj = new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("1".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("2".equals(order)){
            OrderObj orderObj = new OrderObj("price",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("3".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }else if ("4".equals(order)){
            OrderObj orderObj = new OrderObj("sales_volume",InterfaceContant.OrderBy.ASC);
            enyanBook.addOrder(orderObj);
        }else if ("5".equals(order)){//默认，按照优先级次序
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            enyanBook.addOrder(orderObj);
        }

        Page<EnyanBook> page = new Page();
        page.setPageSize(12);
        enyanBook.setPage(page);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }

        page = enyanBookService.queryRecords(enyanBook.getPage(),enyanBook);
        enyanBook.excuteFrontPageLand(queryParams);

        String area = this.getArea(request);
        for (Object object:page.getRecords()){
            ((EnyanBook) object).resetByArea(area);
        }

        modelMap.addAttribute("obj",bookSet);
        modelMap.addAttribute("bookList",page.getRecords());
        modelMap.addAttribute("pageLand",enyanBook.getPageLand());
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "shop/bookSetInfo";
    }

    @RequestMapping(value = "/booklist-{dataId}")
    public String bookList(@PathVariable("dataId")String dataIdString, ModelMap modelMap, HttpServletRequest request){
        if (StringUtils.hasLength(dataIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        String[] dataIdSpilit = dataIdString.split("#");
        if (dataIdSpilit.length == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        dataIdString = dataIdSpilit[0];
        if (NumberUtils.isDigits(dataIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }
        Long dataId = Long.parseLong(dataIdString);
        if (null == dataId || dataId<0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }

        EnyanBookList bookList = enyanBookListService.queryRecordByPrimaryKey(dataId).getResult();
        if (null == bookList || null == bookList.getSetId()){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.book",request));
            return "/fail";
        }

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        //String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        //String order = request.getParameter("order");//排序


        //queryParams.put("order",order);

        //String explan = this.getCategoryName(String.valueOf(categoryId));

        EnyanBook enyanBook = new EnyanBook();

        Page<EnyanBook> page = new Page();
        page.setPageSize(12);
        enyanBook.setPage(page);
        /*
        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }*/
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }

        page = enyanBookListService.queryBookRecords(enyanBook.getPage(),enyanBook,bookList);
        enyanBook.excuteFrontPageLand(queryParams);

        String area = this.getArea(request);
        for (Object object:page.getRecords()){
            ((EnyanBook) object).resetByArea(area);
        }

        modelMap.addAttribute("obj",bookList);
        modelMap.addAttribute("bookList",page.getRecords());
        modelMap.addAttribute("pageLand",enyanBook.getPageLand());
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "shop/bookSetInfo";
    }

    @RequestMapping(value = "/cart-{todo}-{bookId}")
        public String cartAdd(@PathVariable("todo")String todo, @PathVariable("bookId")Long bookId, ModelMap modelMap,
                          HttpServletRequest request, HttpServletResponse response){
        if (null == bookId || bookId<0){
            return "shop/myCart";
        }
        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
        if (null == enyanBook || !enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1)){
            //enyanBook = new EnyanBook();
            return "shop/myCart";
        }
        if (enyanBook.getBookType() == EBookConstant.BookType.EBOOK_SET){//套装书及免费书不能加入购物车
            return "shop/myCart";
        }
        //ProductInfo productInfo = new ProductInfo(enyanBook);

        String area = this.getArea(request);
        enyanBook.resetByArea(area);

        //CartInfoGerenal cartInfoGerenal = this.initCartInfoGeneral(modelMap,request);
        List<Long> bookIdList = new ArrayList<>();
        bookIdList.add(bookId);
        if ("add".equals(todo)){
//            cartInfoGerenal.addProduct(productInfo,1);
            this.syncCart(bookIdList,true,request);
        }else {
            this.syncCart(bookIdList,false,request);
        }
        //modelMap.addAttribute(InterfaceContant.CookieName.BUYER_CART,cartMap);
        //CookieUtil.addCookie(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal,response);
        //this.syncCart(cartInfoGerenal,request);

        return "shop/myCart";
    }
    /**
     * JSON 返回
     * */
    @ResponseBody
    @RequestMapping(value = "/addCart-{bookId}")
    public Map<String,Object> cartAdd(@PathVariable("bookId")Long bookId, ModelMap modelMap,
                                      HttpServletRequest request, HttpServletResponse response){
        log.debug("method addCart：{}",bookId);

        if (null == bookId || bookId<0){
            return ResultUtils.getFailedResultData(this.getMessage("error.book.select",request));
        }
        if (UserUtils.isAnonymous()){
            return ResultUtils.getFailedResultData(InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
        }
        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
        if (null == enyanBook || !enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1)){
            return ResultUtils.getFailedResultData(this.getMessage("error.book.select",request));
        }
        String area = this.getArea(request);
        enyanBook.resetByArea(area);

        if (enyanBook.getBookType() == EBookConstant.BookType.EBOOK_SET){//套装书及免费书不能加入购物车
            return ResultUtils.getFailedResultData(this.getMessage("error.book.select",request));
        }
        List<Long> bookIdList = new ArrayList<>();
        bookIdList.add(bookId);

        //CookieUtil.addCookie(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal,response);
//        this.syncCart(cartInfoGerenal,request);
        long cartCount = this.syncCart(bookIdList, true, request);

        Map<String,String> data = new HashMap<>();
        data.put("quantity",String.valueOf(cartCount));
        //data.put("amountCny",String.valueOf(cartInfo.getAmountCny()));
        //data.put("amountUsd",String.valueOf(cartInfo.getAmountUsd()));
        //request.getSession().setAttribute(EBookContant.Cart.DATA,data);

        Map<String,Object> result = ResultUtils.getSuccessResultData(data);


        return result;
    }

    @RequestMapping(value = "/editCart-{bookId}-{num}")
    public String cartEdit(@PathVariable("bookId")Long bookId, @PathVariable("num")int num, ModelMap modelMap,
                          HttpServletRequest request, HttpServletResponse response){
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "500";
        }
//        CartInfoGerenal cartInfoGerenal = this.initCartInfoGeneral(modelMap,request);
//        if (num>0){
//            cartInfoGerenal.updateProduct(bookId,num);
//        }
        List<Long> bookIdList = new ArrayList<>();
        bookIdList.add(bookId);
        //CookieUtil.addCookie(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal,response);
        //this.syncCart(cartInfoGerenal,request);
        this.syncCart(bookIdList, true, request);
        return "shop/myCart";
    }

    @RequestMapping(value = "/delCart-{bookId}")
    public String cartDel(@PathVariable("bookId")Long bookId, ModelMap modelMap,
                           HttpServletRequest request, HttpServletResponse response){
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "500";
        }
//        CartInfoGerenal cartInfoGerenal = this.initCartInfoGeneral(modelMap,request);
//        cartInfoGerenal.removeProduct(bookId);
        List<Long> bookIdList = new ArrayList<>();
        bookIdList.add(bookId);
        //CookieUtil.addCookie(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal,response);
//        this.syncCart(cartInfoGerenal,request);
        this.syncCart(bookIdList,false, request);
        /*
        Map<String,String> data = new HashMap<>();
        data.put("quantity",String.valueOf(cartInfoGerenal.getQuantity()));
//        data.put("amountCny",String.valueOf(cartInfo.getAmountCny()));
//        data.put("amountUsd",String.valueOf(cartInfo.getAmountUsd()));
        request.getSession().setAttribute(EBookContant.Cart.DATA,data);
        */

        return "redirect:/myCart";
    }

    @RequestMapping(value = "/clearCart")
    public String cartClear(ModelMap modelMap, HttpServletRequest request, HttpServletResponse response){
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "500";
        }
        CartInfoGerenal cartInfoGerenal = new CartInfoGerenal();
        modelMap.addAttribute(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal);
        //CookieUtil.addCookie(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal,response);
//        this.syncCart(cartInfoGerenal,request);
        enyanCartService.deleteCartsAllByEmail(UserUtils.getCurrentLoginUser().getEmail());
        Map<String,String> data = new HashMap<>();
        data.put("quantity","0");
        request.getSession().setAttribute(EBookConstant.Cart.DATA,data);
        return "shop/myCart";
    }
    /**
     *
     * 购物车触发的订单确认
     * @Date: 2018/2/28
     */
    @RequestMapping(value = "/orderConfirm")
    public String orderConfirm(ModelMap modelMap,HttpServletRequest request, HttpServletResponse response){

        String[] products = request.getParameterValues("cartProductId");
        if (products == null ||products.length == 0){
            products = (String[]) request.getSession().getAttribute("cartProductId");
        }

        if (products == null || products.length == 0){
            modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
            return "500";
        }

        request.getSession().setAttribute("cartProductId",products);

        List<Long> productList = new ArrayList<>(products.length);
        for (String p:products){
            productList.add(Long.parseLong(p));
        }
        List<EnyanBook> list = enyanBookService.findBookByIds(productList);

        String area = this.getArea(request);
        for (Object object:list){
            ((EnyanBook) object).resetByArea(area);
        }

        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
        Map<Long,EnyanBook> map = new HashMap<>();
        int bookType = EBookConstant.BookType.EBOOK_SINGLE;
        for (EnyanBook enyanBook:list){
            map.put(enyanBook.getBookId(),enyanBook);
            bookType = enyanBook.getBookType();
        }
        for (Long productId:productList){
            EnyanBook enyanBook = map.get(productId);
            if (!enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1)){
                continue;
            }
            ProductInfo productInfo = new ProductInfo(enyanBook);
            cartInfo.addProduct(productInfo,1);
        }

        EnyanOrder order ;
        try {
            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

            if (titleInfo.isEmpty()){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }
            if (UserUtils.isAnonymous()){
                modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
                return "500";
            }

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderTitleInfo(titleInfo);
            order.setOrderDetailInfo(detailInfo);

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_0);
            order.setIsCounted(Constant.BYTE_VALUE_0);

            if (bookType == EBookConstant.BookType.EBOOK_SET){
                order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SET_BUY);
            }else{
                order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);
            }

            order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
            order.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
            order.setPurchasedAt(new Date());

            /*String currency = CookieUtil.getCookieValue(InterfaceContant.CookieName.CURRENCY,String.class,request);
            if (Locale.TRADITIONAL_CHINESE.getCountry().equals(currency)){//美元
                order.setOrderCurrency(Constant.USD_BYTE_VALUE);
            }else {
                order.setOrderCurrency(Constant.CNY_BYTE_VALUE);
            }

            if (order.getOrderCurrency().equals(Constant.USD_BYTE_VALUE)){//美元
                order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountUsd())));
            }else {
                order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountCny())));
            }*/
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(detailInfo.getAmountHkd());
            order.setOrderFrom(EBookConstant.OrderFrom.WEB);

            modelMap.addAttribute("orderMain",order);
            modelMap.addAttribute("order",detailInfo);
            modelMap.addAttribute("clearCart","1");

            request.getSession().setAttribute(EBookConstant.Cart.ORDER_CONFIRM,order);
        } catch (Exception e) {
            e.printStackTrace();
            modelMap.addAttribute(Error_Msg,"生成订单失败！");
            return "500";
        }
        return "shop/orderConfirm";
    }

    /**
     *
     * 书籍详细页 的"立即购买"
     * @Date: 2018/2/28
     */
    @RequestMapping(value = "/orderConfirmBook-{bookId}")
    public String orderConfirmBook(@PathVariable("bookId")Long bookId, ModelMap modelMap,HttpServletRequest request, HttpServletResponse response){
        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
        EnyanOrder order ;
        try {
            if (null == bookId || bookId<0){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }
            if (UserUtils.isAnonymous()){
                modelMap.addAttribute(ERROR_CODE,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_CODE);
                //return "redirect:login";
                return "redirect:/login?targetUrl=/shop/book-"+bookId;
            }
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
            if (null == enyanBook || !enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1)){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }

            String area = this.getArea(request);//获取区域信息
            enyanBook.resetByArea(area);

            List<EnyanBookBuy>  buyList = enyanBookBuyService.getBookIDAndNameByEmailAndBookId(UserUtils.getCurrentLoginUser().getEmail(), enyanBook.getBookId());
            if (null != buyList && !buyList.isEmpty()){
                modelMap.addAttribute(Error_Msg,this.getMessage("book.hasBought",request));
                return "500";
            }
            //logger.debug("book:"+enyanBook.getBookId()+","+enyanBook.getBookTitle());
            ProductInfo productInfo = new ProductInfo(enyanBook);
            //logger.debug("p:"+productInfo);
            cartInfo.addProduct(productInfo,1);

            //logger.debug("SIZE:"+cartInfo.findProducts().size());

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

            /*if (titleInfo.isEmpty()){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }*/

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderTitleInfo(titleInfo);
            order.setOrderDetailInfo(detailInfo);

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_0);
            order.setIsCounted(Constant.BYTE_VALUE_0);

            if (enyanBook.getBookType() == EBookConstant.BookType.EBOOK_SET){
                order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SET_BUY);
            }else{
                order.setOrderType(EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY);
            }

            order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
            order.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
            order.setPurchasedAt(new Date());
            /*
            String currency = CookieUtil.getCookieValue(InterfaceContant.CookieName.CURRENCY,String.class,request);
            if (Locale.TRADITIONAL_CHINESE.getCountry().equals(currency)){//美元
                order.setOrderCurrency(Constant.USD_BYTE_VALUE);
            }else {
                order.setOrderCurrency(Constant.CNY_BYTE_VALUE);
            }

            if (order.getOrderCurrency().equals(Constant.USD_BYTE_VALUE)){//美元
                order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountUsd())));
            }else {
                order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountCny())));
            }*/
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountHkd())));
            order.setOrderFrom(EBookConstant.OrderFrom.WEB);

            modelMap.addAttribute("orderMain",order);
            modelMap.addAttribute("order",detailInfo);
            modelMap.addAttribute("clearCart","0");

            request.getSession().setAttribute(EBookConstant.Cart.ORDER_CONFIRM,order);
        } catch (Exception e) {
            e.printStackTrace();
            modelMap.addAttribute(Error_Msg,"生成订单失败！");
            return "500";
        }
        return "shop/orderConfirm";
    }
    /**
     *
     *  赠送功能
     * @param bookId
     * @param count
     * @param modelMap
     * @param request
     * @param response
     * @Date: 2020-06-22
     */
    @RequestMapping(value = "/orderConfirmGift-{bookId}-{count}")
    public String orderConfirmGift(@PathVariable("bookId")Long bookId, @PathVariable("count")Integer count,
                                   ModelMap modelMap,
                                   HttpServletRequest request, HttpServletResponse response){
        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
        EnyanOrder order ;
        try {
            if (null == bookId || bookId<0){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }
            if (null == count || count<0 || count > Constant.MAX_REDEEM_BUY_LIMIT){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.count.select",request));
                return "500";
            }
            if (UserUtils.isAnonymous()){
                modelMap.addAttribute(ERROR_CODE,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_CODE);
                //return "redirect:login";
                return "redirect:/login?targetUrl=/shop/book-"+bookId;
            }
            String productDescription = null;
            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
            if (null == enyanBook || !Constant.BYTE_VALUE_1.equals(enyanBook.getShelfStatus())){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }
            productDescription = enyanBook.getBookTitle();
            String area = this.getArea(request);//获取区域信息
            enyanBook.resetByArea(area);

            //logger.debug("book:"+enyanBook.getBookId()+","+enyanBook.getBookTitle());
            ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_BUY);
            //logger.debug("p:"+productInfo);
            cartInfo.addProduct(productInfo,count);

            //logger.debug("SIZE:"+cartInfo.findProducts().size());

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo,productDescription);

            /*if (titleInfo.isEmpty()){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }*/

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderTitleInfo(titleInfo);
            order.setOrderDetailInfo(detailInfo);

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_0);
            order.setIsCounted(Constant.BYTE_VALUE_1);//兑换码默认直接统计

            order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_BUY);

            order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
            order.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
            order.setPurchasedAt(new Date());

            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(new BigDecimal(String.valueOf(cartInfo.getAmountHkd())));
            order.setOrderFrom(EBookConstant.OrderFrom.WEB);

            modelMap.addAttribute("orderMain",order);
            modelMap.addAttribute("order",detailInfo);
            modelMap.addAttribute("clearCart","0");

            request.getSession().setAttribute(EBookConstant.Cart.ORDER_CONFIRM,order);
        } catch (Exception e) {
            e.printStackTrace();
            modelMap.addAttribute(Error_Msg,"生成订单失败！");
            return "500";
        }


        return "shop/orderConfirm";
    }

    @ResponseBody
    @RequestMapping(value = "/sendCouponCode")
    public Map<String,Object> sendCouponCode(@RequestBody Coupon coupon, HttpServletRequest request){
        log.debug("method sendCouponCode：{}",coupon);
        if (StringUtils.hasLength(coupon.getCode()) == false){
            return ResultUtils.getFailedResultData(this.getMessage("error.coupon.null",request));
        }
        if (UserUtils.isAnonymous()){
            return ResultUtils.getFailedResultData(InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
        }
        EnyanCoupon enyanCoupon = enyanCouponService.getCouponByCode(coupon.getCode().toUpperCase());
        if (null == enyanCoupon){
            return ResultUtils.getFailedResultData(this.getMessage("error.coupon.invalid",request));
        }
        if (0 == enyanCoupon.getCouponStatus()){
            return ResultUtils.getFailedResultData(this.getMessage("error.coupon.invalid",request));
        }
        if (enyanCoupon.getBuyCount() >= enyanCoupon.getBuyMax()){//已经购买的数量大于可购买数
            return ResultUtils.getFailedResultData(this.getMessage("error.coupon.out",request));
        }
        if (enyanCoupon.getBeginTime().getTime()>System.currentTimeMillis()){//早于开始的时间
            return ResultUtils.getFailedResultData(this.getMessage("error.coupon.invalid",request));
        }
        if (enyanCoupon.getEndTime().getTime()<System.currentTimeMillis()){
            return ResultUtils.getFailedResultData(this.getMessage("error.coupon.expired",request));
        }
        EnyanOrder order = (EnyanOrder) request.getSession().getAttribute(EBookConstant.Cart.ORDER_CONFIRM);

        String email = UserUtils.getCurrentLoginUser().getEmail();
        long count = enyanOrderService.selectCountByCouponCodeAndEmail(enyanCoupon.getCouponCode(),email);
        if (count >= enyanCoupon.getUseMax()){//超过使用次数
            return ResultUtils.getFailedResultData(this.getMessage("error.coupon.only.times", new String[]{enyanCoupon.getUseMax()+""},request));
        }
        if (enyanCoupon.getCouponValue() != order.getOrderDetailInfo().getAmountCoupon().intValue()){
            order.getOrderDetailInfo().resetProductListByCoupon(enyanCoupon);
            if (order.getOrderDetailInfo().getTotalFeeBeforeCoupon().compareTo(new BigDecimal(enyanCoupon.getMinLimitValue())) == -1){//小于最小满减限额
                return ResultUtils.getFailedResultData(this.getMessage("error.coupon.limit", new String[]{"HK$"+enyanCoupon.getMinLimitValue()},request));
            }
            order.getOrderDetailInfo().setCouponCode(enyanCoupon.getCouponCode().toUpperCase());//设置优惠码，用于记录一个人只能使用一次
            order.getOrderDetailInfo().setAmountCoupon(new BigDecimal(enyanCoupon.getCouponValue()+""));//设置优惠金额
            order.setOrderTotal(new BigDecimal(String.valueOf(order.getOrderDetailInfo().getAmountHkd())));
        }

        Map<String,Object> result = ResultUtils.getSuccessResultData(this.getMessage("success.coupon.msg",new String[]{"HK$"+enyanCoupon.getCouponValue()},request));

        return result;
    }

    /**
     *
     * 提交订单
     * @Date: 2018/2/28
     */
    @RequestMapping(value = "/toCheckout-{clearCart}")
    public String orderToCheckout(@PathVariable("clearCart")String clearCart,
                           ModelMap modelMap,HttpServletRequest request, HttpServletResponse response){
        EnyanOrder order = (EnyanOrder) request.getSession().getAttribute(EBookConstant.Cart.ORDER_CONFIRM);
        request.getSession().removeAttribute(EBookConstant.Cart.ORDER_CONFIRM);
            if (null == order){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }
            if (UserUtils.isAnonymous()){
                modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
                return "500";
            }
            if (!UserUtils.getCurrentLoginUser().isActived()){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.user.inactive",request));
                return "500";
            }
            order.setExpiredAt(DateUtils.addHours(order.getPurchasedAt(),2));
            if (order.getOrderTotal().doubleValue() > 0){

            }else{
                OrderPayInfo orderPayInfo = new OrderPayInfo();
                orderPayInfo.addFree();
                order.setOrderPayInfo(orderPayInfo);
                order.setIsPaid(Constant.BYTE_VALUE_1);
            }
            EnyanOrder queryOrder = new EnyanOrder();
            queryOrder.setOrderNum(order.getOrderNum());
            queryOrder.setUserEmail(order.getUserEmail());
            List<EnyanOrder> orderList = enyanOrderService.findRecordsByOrder(queryOrder);
            if (orderList == null || orderList.isEmpty()){//有时候会出现订单重复的问题，如果订单已经存在，则不需要保存
                ExecuteResult<EnyanOrder> result = enyanOrderService.addRecord(order);
                if (!result.isSuccess()){
                    log.error(result.getErrorMessageString());
                }
            }else {
                EnyanOrder orderDb = orderList.get(0);
                if (Constant.BYTE_VALUE_1.equals(orderDb.getIsPaid())){//有可能有人会重复刷订单，对已经购买的订单，后退刷新订单信息
                    String toUrl = "/shop/checkoutReturn";
                    if (order.getOrderType() == EBookConstant.OrderType.ORDER_REDEEM_BUY){
                        toUrl = "/shop/checkoutReturnGift";
                    }
                    return toUrl;
                }
            }

            List<Long> bookIdList = new ArrayList<>();
            for (CartDiscountInfo cartDiscountInfo:order.getOrderDetailInfo().getCartDiscountInfoList()){
                for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                    bookIdList.add(productInfo.getCode());
                }
            }
            enyanBookService.updateBookSaleVolumeAdd(bookIdList);

            modelMap.addAttribute("orderMain",order);
            modelMap.addAttribute("order",order.getOrderDetailInfo());

            if ("1".equals(clearCart)){
                CartInfoGerenal cartInfoGerenal = this.initCartInfoGeneral(modelMap,request);
                for (CartDiscountInfo cartDiscountInfo:order.getOrderDetailInfo().getCartDiscountInfoList()){
                    for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                        cartInfoGerenal.removeProduct(productInfo.getCode());
                    }
                }

                //CookieUtil.addCookie(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal,response);
                //this.syncCart(cartInfoGerenal,request);
                this.syncCart(bookIdList, false, request);


                Map<String,String> data = new HashMap<>();
                data.put("quantity",String.valueOf(cartInfoGerenal.getQuantity()));
                data.put("amountCny","0");
                data.put("amountUsd","0");
                data.put("amountHkd","0");
                request.getSession().setAttribute(EBookConstant.Cart.DATA,data);
            }

            if (order.getOrderTotal().doubleValue() > 0){
                return "redirect:/orderDetail-"+order.getOrderId();//费用大于0，则直接转向到付款页面
            }
            String toUrl = "/shop/checkoutReturn";
            if (order.getOrderType() == EBookConstant.OrderType.ORDER_REDEEM_BUY){
                toUrl = "/shop/checkoutReturnGift";
            }
            /*
            if (order.getOrderType() == EBookContant.OrderType.ORDER_EBOOK_SINGLE_BUY
                    || order.getOrderType() == EBookContant.OrderType.ORDER_EBOOK_SET_BUY){
                //免费的电子书订单，直接生成订单明细，并且发送email
                this.enyanOrderDetailService.splitOrder(order);
                this.enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());
            }else if (order.getOrderType() == EBookContant.OrderType.ORDER_REDEEM_BUY){
                this.enyanRedeemCodeService.splitOrder(order);
                toUrl = "/shop/checkoutReturnGift";
            }

            this.sendMailOfOrder(order,request);
            this.sendLogOfOrder(order);*/
            this.paySuccess(order,order.getOrderPayInfo(),request);
            return toUrl;
            //this.sendMailOfOrder(order);
        //return "redirect:/orderDetail-"+order.getOrderId();
    }

    @RequestMapping(value = "/checkout-{orderId}")
    public String checkoutAlipay(@PathVariable("orderId")Long orderId, ModelMap modelMap,
                          HttpServletRequest request, HttpServletResponse response){
        //this.initCart(modelMap,request);
        //response.addHeader("Content-Security-Policy", "default-src 'self' *.alipay.com; frame-ancestors 'self' *.alipay.com; frame-src 'self' *.alipay.com; style-src 'self' *.alipay.com; connect-src 'self' *.alipay.com; script-src 'self' 'unsafe-eval' *.alipay.com; media-src 'self' *.alipay.com; img-src 'self' *.alipay.com; base-uri 'self' *.alipay.com;");
        ExecuteResult<EnyanOrder> result = enyanOrderService.queryRecordByPrimaryKey(orderId);
        boolean isMobile = false;
        EnyanOrder order = result.getResult();

        if (null == order){
            return null;
        }
        if (Constant.BYTE_VALUE_1.equals(order.getIsPaid())){
            modelMap.addAttribute("showTop","1");
            if (order.getOrderType() == EBookConstant.OrderType.ORDER_REDEEM_BUY){
                return "/shop/checkoutReturnGift";
            }
            return "/shop/checkoutReturn";
        }

        String type = request.getParameter("type");//是否是香港支付宝
        //String mac = request.getParameter("mac");
        //boolean isMac = "mac".equals(mac);
        /*EnyanOrder orderNew = new EnyanOrder();
        orderNew.setOrderId(order.getOrderId());
        enyanOrderService.updateRecord(orderNew);*/

        Device currentDevice = AaronDeviceUtils.getCurrentDevice(request);
        if (null!=currentDevice && currentDevice.isMobile()){
            isMobile = true;
        }
        /*if(currentDevice.isMobile()) { *//* Mobile *//* }
        if(currentDevice.isTablet()) { *//* Tablet *//* }
        if(currentDevice.isNormal()) { *//* Desktop *//* }*/

        //商户订单号，商户网站订单系统中唯一订单号，必填
        String out_trade_no = order.getOrderNum();

        //订单名称，必填
        String subject = out_trade_no;//"恩道電子書 -  # "+out_trade_no;

        //付款金额，必填
        String total_fee = String.valueOf(order.getOrderTotal());

        //商品描述，可空 2019.2.14 添加json body
        //String body = this.getAlipayGoodsBody(order);

        //商品描述，可空 2019.5.17 添加json body
        String tradeInformation = getAlipayGoodsTradeInformation(order);

        //币种，不可空
        //String currency = "USD";

        //把请求参数打包成数组
        Map<String, String> sParaTemp = new HashMap<>();
        sParaTemp.put("service", AlipayConfig.service);
        //注意：必传，PC端是NEW_OVERSEAS_SELLER，移动端是NEW_WAP_OVERSEAS_SELLER
        //		Remarks:Mandatory.For PC: NEW_OVERSEAS_SELLER ;FOR WAP and APP: NEW_WAP_OVERSEAS_SELLER
        sParaTemp.put("product_code", AlipayConfig.product_code);//PC与WAP一起使用
        if (isMobile == true){
            sParaTemp.put("service", AlipayConfig.service_mobile);
            //sParaTemp.put("product_code", "NEW_WAP_OVERSEAS_SELLER"); //WAP
        }
        sParaTemp.put("partner", AlipayConfig.partner);
        sParaTemp.put("_input_charset", AlipayConfig.input_charset);
        sParaTemp.put("notify_url", AlipayConfig.notify_url);
        sParaTemp.put("return_url", AlipayConfig.return_url);
        sParaTemp.put("refer_url", AlipayConfig.refer_url);
        sParaTemp.put("out_trade_no", out_trade_no);
        sParaTemp.put("subject", subject);
        //sParaTemp.put("total_fee", total_fee);
        //sParaTemp.put("rmb_fee", total_fee);

        /*
        if (order.getOrderCurrency().equals(Constant.BYTE_VALUE_1){//美元
            sParaTemp.put("total_fee", total_fee);
        }else {
            //sParaTemp.put("rmb_fee", total_fee);
            //sParaTemp.put("rmb_fee", "0.1");//RMB
            sParaTemp.put("total_fee", "0.1");
        }*/
        sParaTemp.put("total_fee", total_fee);
        //sParaTemp.put("total_fee", "0.1");//HKD

        //sParaTemp.put("body", body);
        sParaTemp.put("trade_information", tradeInformation);
        sParaTemp.put("currency", AlipayConfig.currency);
        //其他业务参数根据在线开发文档，添加参数
        //如sParaTemp.put("参数名","参数值");

        //https://global.alipay.com/docs/ac/hkapi/create_forex_trade_wap
        //https://global.alipay.com/docs/ac/website_hk/ux
        if ("HK".equals(type)){
            sParaTemp.put("payment_inst", AlipayConfig.payment_inst_HK);//ALIPAYHK ALIPAYCN
        }else {
            //sParaTemp.put("payment_inst", AlipayConfig.payment_inst_CN);//ALIPAYHK ALIPAYCN
        }

        sParaTemp.put("qr_pay_mode", "4");
        sParaTemp.put("qrcode_width", "200");
        //sParaTemp.put("split_fund_info", "[{\"transIn\":\"2088441611946675\",\"amount\":\""+total_fee+"\",\"currency\":\"HKD\",\"desc\":\"电子书\"}]");
        //sParaTemp.put("app_pay","Y");//启用此参数可唤起钱包APP支付。

        //建立请求

        if (isMobile == true){
            String sHtmlText = AlipaySubmit.buildRequest(sParaTemp,"get","确认");
            modelMap.addAttribute("result",sHtmlText);
            return "/showResult";
        }

        String toUrl = AlipaySubmit.buildRequestMethod(sParaTemp);
        log.debug("toUrl:{}",toUrl);
        modelMap.addAttribute("result",toUrl);
        //modelMap.addAttribute("result","/checkoutReturnShow");
        OrderDetailInfo detailInfo = order.getOrderDetailInfo();

        modelMap.addAttribute("orderMain",order);
        modelMap.addAttribute("order",detailInfo);
        modelMap.addAttribute("payType",type);
        return "/shop/alipayConfirm";
    }

    @ResponseBody
    @RequestMapping(value = "/checkoutUsd-{orderId}")
    public ExecuteResult<String> checkoutStripe(@RequestBody CreditInfo creditInfo, @PathVariable("orderId")Long orderId, ModelMap modelMap,
                              HttpServletRequest request, HttpServletResponse response){
        ExecuteResult<String> result = new ExecuteResult<>();
        //this.initCart(modelMap,request);

        creditInfo.checkCredit();
        if (!creditInfo.isValid()){
            result.setErrorMessages(creditInfo.getErrorMessages());
            return result;
        }

        ExecuteResult<EnyanOrder> resultOrder = enyanOrderService.queryRecordByPrimaryKey(orderId);

        EnyanOrder order = resultOrder.getResult();

        if (null == order){
            result.addErrorMessage(this.getMessage("pay.fail",request)+"001");
            return result;
        }
        String token = StripeUtil.getToken(creditInfo, order.getUserEmail());
        if (StringUtils.hasLength(token) == false){
            result.addErrorMessage(this.getMessage("pay.fail",request)+"002");
            return result;
        }
        RequestOptions requestOptions = (new RequestOptions.RequestOptionsBuilder()).setApiKey(StripeConfig.SECRET_KEY).build();
        Map<String, Object> chargeMap = new HashMap<>();
        //付款金额，必填

        BigDecimal fee = order.getOrderTotal().multiply(new BigDecimal("100"));
        int total_fee = fee.intValue();

        //chargeMap.put("amount", 400);//100分
        chargeMap.put("amount", total_fee);//100分
        chargeMap.put("currency", "hkd");//cny usd
        chargeMap.put("source", token); // obtained via Stripe.js
        chargeMap.put("description",order.getOrderNum());
        try {
            Charge charge = Charge.create(chargeMap, requestOptions);
            //Card card = (Card) charge.getSource();
            //System.out.println("country:"+card.getAccount()+card.getCustomer()+card.getCountry());
            result.setResult("/shop/scr");
            result.setSuccessMessage(this.getMessage("pay.success",request));
            OrderPayInfo payInfo = new OrderPayInfo();
            payInfo.addStripe(charge,true);
            this.paySuccess(order,payInfo,request);
            return result;
        } catch (StripeException e) {
            e.printStackTrace();
            logService.sendTimeLog("信用卡支付失败:{"+order.getOrderNum()+"},报错信息：{"+e.getMessage()+"}，信用卡："+creditInfo);
            log.error("信用卡支付失败：{}{}",order.getOrderNum(),e.getMessage());
            //result.addErrorMessage("支付失败");
            //return result;
        }
        result.addErrorMessage(this.getMessage("pay.fail",request)+"003");
        return result;
    }

    /**
     * <p>老版本的Alipay</p>
     * @return
     * @since : 2022/9/16
     **/
    @RequestMapping(value = "/enyanMyNotify", method = RequestMethod.POST)
    @ResponseBody
    public String notify(HttpServletRequest request, ModelMap modelMap) throws UnsupportedEncodingException {
        //获取支付宝POST过来反馈信息
        Map<String,String> params = new HashMap<>();
        Map requestParams = request.getParameterMap();
        log.error("---enyanMyNotify---");
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "gbk");
            log.error("enyanMyNotify name:{},value:{}",name,valueStr);
            params.put(name, valueStr);
        }

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以下仅供参考)//
        //商户订单号

        String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");
        //支付宝交易号
        String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");
        //交易状态
        String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");

        /*String rmb_fee = new String(request.getParameter("rmb_fee").getBytes("ISO-8859-1"),"UTF-8");
        String total_fee = new String(request.getParameter("total_fee").getBytes("ISO-8859-1"),"UTF-8");

        logger.info("enyanMyNotify rmb_fee:"+rmb_fee);
        logger.info("enyanMyNotify total_fee:"+total_fee);*/

        //异步通知ID
        String notify_id = request.getParameter("notify_id");

        //sign
        String sign = request.getParameter("sign");

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以上仅供参考)//

        if(this.notifyVerifyAlipay(params, sign)){//验证成功
            //////////////////////////////////////////////////////////////////////////////////////////
            //请在这里加上商户的业务逻辑程序代码

            //——请根据您的业务逻辑来编写程序（以下代码仅作参考）——

            if(trade_status.equals("TRADE_FINISHED") || trade_status.equals("TRADE_SUCCESS")){
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //请务必判断请求时的total_fee、seller_id与通知时获取的total_fee、seller_id为一致的
                //如果有做过处理，不执行商户的业务程序
                //orderPayInfo.setTradeStatus("交易成功");
                //logger.info("enyanMyNotify trade_status:"+trade_status);

                /*EnyanOrder enyanOrder = new EnyanOrder();
                enyanOrder.setOrderNum(out_trade_no);
                List<EnyanOrder> list = enyanOrderService.selectByExampleWithBLOBs(enyanOrder);
                //logger.debug("list size:"+list.size());
                if (!list.isEmpty()){
                    enyanOrder = list.get(0);
                    OrderPayInfo orderPayInfo = new OrderPayInfo();
                    //logger.info("shop orderPayInfo.addAlipay()");
                    orderPayInfo.addAlipay(params,true);

                    logger.error("email:{}",enyanOrder.getUserEmail());
                    this.paySuccess(enyanOrder,orderPayInfo,request);
                    logger.error("支付验证成功");
                }*/
                this.alipayPaySuccess(params,out_trade_no, EBookConstant.PayType.ALI_PAY_HK,request);
                return "success";
                //注意：
                //退款日期超过可退款期限后（如三个月可退款），支付宝系统发送该交易状态通知
            } else if (trade_status.equals("TRADE_SUCCESS")){
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //请务必判断请求时的total_fee、seller_id与通知时获取的total_fee、seller_id为一致的
                //如果有做过处理，不执行商户的业务程序

                //注意：
                //付款完成后，支付宝系统发送该交易状态通知
            }

            //——请根据您的业务逻辑来编写程序（以上代码仅作参考）——

            log.error("支付验证success");	//请不要修改或删除

            //////////////////////////////////////////////////////////////////////////////////////////
        }else{//验证失败
            //orderPayInfo.setTradeStatus("交易失败");

            log.error("支付验证失败");
            //orderPayInfo.setTradeStatus("验证失败");
            modelMap.addAttribute(Error_Msg,"支付失败！");
            return "fail";
        }
        return "fail";
    }

    /**
     * <p>AlipayHK的回馈数据，手续费不同</p>
     * <p>从现在开始两种计量单位并存</p>
     * @return
     * @since : 2022/9/16
     **/
    @RequestMapping(value = "/enyanMyNotifyHK", method = RequestMethod.POST)
    @ResponseBody
    public String notifyHK(HttpServletRequest request, ModelMap modelMap) throws UnsupportedEncodingException {
        //获取支付宝POST过来反馈信息
        Map<String,String> params = new HashMap<>();
        Map requestParams = request.getParameterMap();
        log.error("---enyanMyNotify---");
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                                   : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "gbk");
            log.error("enyanMyNotify name:{},value:{}",name,valueStr);
            params.put(name, valueStr);
        }

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以下仅供参考)//
        //商户订单号

        String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");
        //支付宝交易号
        String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");
        //交易状态
        String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");

        /*String rmb_fee = new String(request.getParameter("rmb_fee").getBytes("ISO-8859-1"),"UTF-8");
        String total_fee = new String(request.getParameter("total_fee").getBytes("ISO-8859-1"),"UTF-8");

        logger.info("enyanMyNotify rmb_fee:"+rmb_fee);
        logger.info("enyanMyNotify total_fee:"+total_fee);*/

        //异步通知ID
        String notify_id = request.getParameter("notify_id");

        //sign
        String sign = request.getParameter("sign");

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以上仅供参考)//

        if(this.notifyVerifyAlipay(params, sign)){//验证成功
            //////////////////////////////////////////////////////////////////////////////////////////
            //请在这里加上商户的业务逻辑程序代码

            //——请根据您的业务逻辑来编写程序（以下代码仅作参考）——
            log.error("支付验证success");
            if(trade_status.equals("TRADE_FINISHED") || trade_status.equals("TRADE_SUCCESS")){
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //请务必判断请求时的total_fee、seller_id与通知时获取的total_fee、seller_id为一致的
                //如果有做过处理，不执行商户的业务程序
                //orderPayInfo.setTradeStatus("交易成功");
                //logger.info("enyanMyNotify trade_status:"+trade_status);

                /*EnyanOrder enyanOrder = new EnyanOrder();
                enyanOrder.setOrderNum(out_trade_no);
                List<EnyanOrder> list = enyanOrderService.selectByExampleWithBLOBs(enyanOrder);
                //logger.debug("list size:"+list.size());
                if (!list.isEmpty()){
                    enyanOrder = list.get(0);
                    OrderPayInfo orderPayInfo = new OrderPayInfo();
                    //logger.info("shop orderPayInfo.addAlipay()");
                    orderPayInfo.addAlipay(params,true);

                    logger.error("email:{}",enyanOrder.getUserEmail());
                    this.paySuccess(enyanOrder,orderPayInfo,request);
                    logger.error("支付验证成功");
                }*/
                this.alipayPaySuccess(params,out_trade_no, EBookConstant.PayType.ALI_PAY_HK,request);
                return "success";
                //注意：
                //退款日期超过可退款期限后（如三个月可退款），支付宝系统发送该交易状态通知
            } else if (trade_status.equals("TRADE_SUCCESS")){
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //请务必判断请求时的total_fee、seller_id与通知时获取的total_fee、seller_id为一致的
                //如果有做过处理，不执行商户的业务程序

                //注意：
                //付款完成后，支付宝系统发送该交易状态通知
            }

            //——请根据您的业务逻辑来编写程序（以上代码仅作参考）——

            //log.error("支付验证success");	//请不要修改或删除

            //////////////////////////////////////////////////////////////////////////////////////////
        }else{//验证失败
            //orderPayInfo.setTradeStatus("交易失败");

            log.error("支付验证失败");
            //orderPayInfo.setTradeStatus("验证失败");
            modelMap.addAttribute(Error_Msg,"支付失败！");
            return "fail";
        }
        return "fail";
    }

    /**
     * <p>
     *     https://ebook.endao.co/manualPay?out_trade_no=E1083067826
     *     http://localhost:8080/manualPay?out_trade_no=E11506128168
     * </p>
     * @param request
     * @param modelMap
     * @return java.lang.String
     * @since : 2022/2/7
     **/
    @RequestMapping(value = "/manualPay", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> manualPay(HttpServletRequest request, ModelMap modelMap) throws UnsupportedEncodingException {
        //手动支付
        Map<String,String> params = new HashMap<>();
        Map requestParams = request.getParameterMap();
        log.error("---manualPay---");

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以下仅供参考)//
        //商户订单号
        String email = UserUtils.getCurrentLoginUser().getEmail();
        if (Constant.TEST_ACCOUNT.contains(email) == false){
            return ResultUtils.getFailedResultData("manualPay email:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        }
        String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");
        if (StringUtils.hasLength(out_trade_no) == false){
            return ResultUtils.getFailedResultData("manualPay error:"+DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        }
        /*
notify_id,value:2022020700222091544017761436302626
notify_type,value:trade_status_sync
sign,value:f66ff2a64c59474e0410154fd6aab04a
trade_no,value:2022020722001317761419508105
total_fee,value:48.75
out_trade_no,value:E10042814975
notify_time,value:2022-02-07 09:15:44
currency,value:HKD
trade_status,value:TRADE_FINISHED
sign_type,value:MD5
        * */
        String timeString = DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss");

        params.put("notify_id", "手动处理");
        params.put("notify_type", "trade_status_sync");
        params.put("sign", "sign");
        params.put("trade_no", "");
        params.put("total_fee", "");
        params.put("out_trade_no", out_trade_no);
        params.put("notify_time", timeString);
        params.put("currency", "HKD");
        params.put("trade_status", "TRADE_FINISHED");
        params.put("sign_type", "MD5");
        params.put("", "");
        this.alipayPaySuccess(params,out_trade_no, EBookConstant.PayType.ALI_PAY_HK,request);
        Map<String,Object> resultMap = ResultUtils.getSuccessResultData("manualPay:"+timeString);

        return resultMap;
    }

    @RequestMapping(value = "/readingIndex")
    public String readIndex(ModelMap modelMap, HttpServletRequest request){
        modelMap.addAttribute("config",Constant.DEFAULT_REST_CONFIG);
        return "/shop/readingIndex";
    }

    @RequestMapping(value = "/reading-{categoryId}")
    public String read(@PathVariable("categoryId")String categoryIdString, ModelMap modelMap,HttpServletRequest request){
        if (StringUtils.hasLength(categoryIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.reading",request));
            return "/fail";
        }
        String[] bookIdSpilit = categoryIdString.split("#");
        if (bookIdSpilit.length == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.reading",request));
            return "/fail";
        }
        categoryIdString = bookIdSpilit[0];
        if (NumberUtils.isDigits(categoryIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.reading",request));
            return "/fail";
        }
        Integer categoryId = Integer.parseInt(categoryIdString);
        if (null == categoryId || categoryId<0){
            categoryId = 0;
        }

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        String order = request.getParameter("order");//排序
        if (StringUtils.hasLength(order) == false){
            order = "5";
        }
        queryParams.put("order",order);

        //String explan = this.getCategoryName(String.valueOf(categoryId));

        EnyanBlog record = new EnyanBlog();
        if (categoryId > 0){
            record.setCategoryId(categoryId);
        }

        if ("0".equals(order)){//阅读量
            OrderObj orderObj = new OrderObj("read_count",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else if ("1".equals(order)){//点赞量
            OrderObj orderObj = new OrderObj("like_count",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else if ("2".equals(order)){//最新
            OrderObj orderObj = new OrderObj("create_at",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else {//默认，按照优先级次序
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }
        record.setIsDeleted(0);

        Page<EnyanBlog> page = new Page();
        page.setPageSize(12);
        record.setPage(page);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }
        page = enyanBlogService.queryRecords(record.getPage(),record);
        record.excuteFrontPageLand(queryParams);

        modelMap.addAttribute("blogList",page.getRecords());
        modelMap.addAttribute("pageLand",record.getPageLand());
        modelMap.addAttribute("explan","");
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "shop/reading";
    }

    /**
     * 全部主题阅读
     * */
    @RequestMapping(value = "/readingThemes")
    public String readingThemes(HttpServletRequest request, HttpServletResponse response, ModelMap modelMap){
        //this.initCart(modelMap,request);
        //isFreeType: 0 默认；1：免费；2：付费

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        String order = request.getParameter("order");//排序
        if (StringUtils.hasLength(order) == false){
            order = "5";
        }
        queryParams.put("order",order);

        //String explan = this.getCategoryName(String.valueOf(categoryId));

        EnyanBlog record = new EnyanBlog();
        record.setCategoryId(7);

        if ("0".equals(order)){//阅读量
            OrderObj orderObj = new OrderObj("read_count",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else if ("1".equals(order)){//点赞量
            OrderObj orderObj = new OrderObj("like_count",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else if ("2".equals(order)){//最新
            OrderObj orderObj = new OrderObj("create_at",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else {//默认，按照优先级次序
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }
        record.setIsDeleted(0);

        Page<EnyanBlog> page = new Page();
        page.setPageSize(12);
        record.setPage(page);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }
        page = enyanBlogService.queryRecords(record.getPage(),record);
        record.excuteFrontPageLand(queryParams);

        modelMap.addAttribute("blogList",page.getRecords());
        modelMap.addAttribute("pageLand",record.getPageLand());
        modelMap.addAttribute("explan","");
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));
        return "shop/blogList";
    }

    /**
     * blogs
     * */
    @RequestMapping(value = "/blogs")
    public String blogs(HttpServletRequest request, HttpServletResponse response, ModelMap modelMap){
        //this.initCart(modelMap,request);
        //isFreeType: 0 默认；1：免费；2：付费

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");
        String order = request.getParameter("order");//排序
        if (StringUtils.hasLength(order) == false){
            order = "5";
        }
        queryParams.put("order",order);

        //String explan = this.getCategoryName(String.valueOf(categoryId));

        EnyanBlog record = new EnyanBlog();
        record.setCategoryId(1);

        if ("0".equals(order)){//阅读量
            OrderObj orderObj = new OrderObj("read_count",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else if ("1".equals(order)){//点赞量
            OrderObj orderObj = new OrderObj("like_count",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else if ("2".equals(order)){//最新
            OrderObj orderObj = new OrderObj("create_at",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }else {//默认，按照优先级次序
            OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
            record.addOrder(orderObj);
        }
        record.setIsDeleted(0);

        Page<EnyanBlog> page = new Page();
        page.setPageSize(12);
        record.setPage(page);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }
        page = enyanBlogService.queryRecords(record.getPage(),record);
        record.excuteFrontPageLand(queryParams);

        modelMap.addAttribute("blogList",page.getRecords());
        modelMap.addAttribute("pageLand",record.getPageLand());
        modelMap.addAttribute("explan","");
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));
        return "shop/blogList";
    }

    @RequestMapping(value = "/blog-{blogId}")
    public String blogInfo(@PathVariable("blogId")String blogIdString, ModelMap modelMap,HttpServletRequest request){
        if (StringUtils.hasLength(blogIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        String[] blogIdSpilit = blogIdString.split("#");
        if (blogIdSpilit.length == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        blogIdString = blogIdSpilit[0];
        if (NumberUtils.isDigits(blogIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        Long blogId = Long.parseLong(blogIdString);
        if (null == blogId || blogId<0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        int flag = enyanBlogService.updateBlogReadCountById(blogId);
        if (flag == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        EnyanBlog enyanBlog = enyanBlogService.queryRecordByPrimaryKey(blogId).getResult();
        if (null == enyanBlog){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        if (enyanBlog.getIsDeleted() == 1){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }

        modelMap.addAttribute("obj",enyanBlog);
        return "shop/blogInfo";
    }

    @RequestMapping(value = "/blogA-{blogId}")
    public String blogApp(@PathVariable("blogId")String blogIdString, ModelMap modelMap,HttpServletRequest request){
        if (StringUtils.hasLength(blogIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        String[] blogIdSpilit = blogIdString.split("#");
        if (blogIdSpilit.length == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        blogIdString = blogIdSpilit[0];
        if (NumberUtils.isDigits(blogIdString) == false){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        Long blogId = Long.parseLong(blogIdString);
        if (null == blogId || blogId<0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        int flag = enyanBlogService.updateBlogReadCountById(blogId);
        if (flag == 0){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        EnyanBlog enyanBlog = enyanBlogService.queryRecordByPrimaryKey(blogId).getResult();
        if (null == enyanBlog){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }
        if (enyanBlog.getIsDeleted() == 1){
            modelMap.addAttribute(Error_Msg,getMessage("error.404.blog",request));
            return "/fail";
        }

        modelMap.addAttribute("obj",enyanBlog);
        return "shop/blogApp";
    }

    @ResponseBody
    @RequestMapping(value = "/blogLike",method = RequestMethod.POST)
    public ExecuteResult<String> blogLike(@RequestBody EnyanBlog enyanBlog, ModelMap modelMap,HttpServletRequest request){
        ExecuteResult<String> result = new ExecuteResult<>();
        String blogIdString = enyanBlog.getBlogTitle();
        if (StringUtils.hasLength(blogIdString) == false){
            result.addErrorMessage(this.getMessage("blog.like.fail",request));
            return result;
        }
        String[] blogIdSpilit = blogIdString.split("#");
        if (blogIdSpilit.length == 0){
            result.addErrorMessage(this.getMessage("blog.like.fail",request));
            return result;
        }
        blogIdString = blogIdSpilit[0];
        if (NumberUtils.isDigits(blogIdString) == false){
            result.addErrorMessage(this.getMessage("blog.like.fail",request));
            return result;
        }
        Long blogId = Long.parseLong(blogIdString);
        if (null == blogId || blogId<0){
            result.addErrorMessage(this.getMessage("blog.like.fail",request));
            return result;
        }
        int flag = enyanBlogService.updateBlogLikeCountById(blogId);
        if (flag == 0){
            result.addErrorMessage(this.getMessage("blog.like.fail",request));
            return result;
        }
        result.setSuccessMessage(this.getMessage("blog.like.success",request));
        return result;
    }

    @RequestMapping(value = "/subscribeAction", method = RequestMethod.POST)
    public String subscribeAction(HttpServletRequest request, EnyanSubscription record, ModelMap modelMap){
        log.debug("method saveBlog："+record);
        if (StringUtils.hasLength(record.getEmail()) == false || record.getEmail().length() > 100
                    || EmailValidatorUtil.validate(record.getEmail()) == false){
//            modelMap.addAttribute("record",record);
            this.setErrorMsg(modelMap, this.getMessage("error.email.invalid",request));
            return "/shop/indexSubscription";
        }

        record.setCreateAt(new Date());
        enyanSubscriptionService.addRecord(record);
        request.getSession().setAttribute("updateExplan",this.getMessage("subscription.success",request));
        return "redirect:/updateFullSuccess";//updateFullSuccess
    }

    /**
     * <p>从web发起的支付 sign是md5，app发起的支付 sign 是 RSA2 ；所以需要分别区分</p>
     * @param params
     * @return boolean
     * @since : 2021/7/7
     **/
    private boolean notifyVerifyAlipay(Map<String,String> params, String sign){
        if ("MD5".equals(params.get("sign_type"))){
            if(AlipayNotify.verify(params)){
                return true;
            }
        }else if ("RSA2".equals(params.get("sign_type"))){
            if (com.alipay.mobile.util.AlipayNotify.getSignVeryfy(params,sign)){
                return true;
            }
        }
        return false;
    }

    @RequestMapping("/testAliPay")
    public String testAlipayReturn(HttpServletRequest request, ModelMap modelMap){
        String out_trade_no = request.getParameter("orderNum");
        if (StringUtils.hasLength(out_trade_no) == false){
            modelMap.addAttribute(Error_Msg,"非法请求！");
            return "fail";
        }
        if (Constant.IS_PRODUCT){
            modelMap.addAttribute(Error_Msg,"非法请求，支付失败！");
            return "fail";
        }

        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,"请登录，支付失败！");
            return "fail";
        }
        String email = UserUtils.getCurrentLoginUser().getEmail();
        if (!Constant.TEST_ACCOUNT.contains(email) && !Constant.TEST_ACCOUNT_PAY.contains(email)){
            modelMap.addAttribute(Error_Msg,"非法账户，支付失败！");
            return "fail";
        }
        Map<String,String> params = new HashMap<>();
        params.put("total_fee","测试支付金额");
        params.put("currency","HKD");
        params.put("trade_no","tradeNO测试的");
        params.put("trade_status","TRADE_FINISHED");
        this.alipayPaySuccess(params,out_trade_no, EBookConstant.PayType.ALI_PAY_HK,request);
        modelMap.addAttribute(Error_Msg,"支付成功！");
        return "success";
    }

    @RequestMapping("/testStripPay")
    public String testStripePayReturn(HttpServletRequest request, ModelMap modelMap){
        String out_trade_no = request.getParameter("orderNum");
        if (StringUtils.hasLength(out_trade_no) == false){
            modelMap.addAttribute(Error_Msg,"非法请求！");
            return "fail";
        }
        if (Constant.IS_PRODUCT){
            modelMap.addAttribute(Error_Msg,"非法请求，支付失败！");
            return "fail";
        }

        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,"请登录，支付失败！");
            return "fail";
        }
        String email = UserUtils.getCurrentLoginUser().getEmail();
        if (!Constant.TEST_ACCOUNT.contains(email) && !Constant.TEST_ACCOUNT_PAY.contains(email)){
            modelMap.addAttribute(Error_Msg,"非法账户，支付失败！");
            return "fail";
        }
        EnyanOrder enyanOrder = new EnyanOrder();
        enyanOrder.setOrderNum(out_trade_no);
        List<EnyanOrder> list = enyanOrderService.findRecordsWithBLOBsByOrder(enyanOrder);
        //logger.debug("list size:"+list.size());
        if (!list.isEmpty()){
            enyanOrder = list.get(0);
            OrderPayInfo orderPayInfo = new OrderPayInfo();
            //logger.info("shop orderPayInfo.addAlipay()");
            Charge charge = new Charge();
            charge.setId("chargeTestID");
            charge.setAmount(enyanOrder.getOrderTotal().longValue()*100);
            charge.setCurrency("HK");
            Charge.PaymentMethodDetails.Card card = new Charge.PaymentMethodDetails.Card();
            card.setCountry("Test");

            Charge.PaymentMethodDetails paymentMethodDetails = new Charge.PaymentMethodDetails();
            paymentMethodDetails.setCard(card);

            charge.setPaymentMethodDetails(paymentMethodDetails);

            charge.setStatus("succeeded");
            orderPayInfo.addStripe(charge, true);
            this.paySuccess(enyanOrder,orderPayInfo,request);
            if (Constant.IS_PRODUCT){
                log.error("email:{}",enyanOrder.getUserEmail());
                log.error("支付验证成功");
            }
        }

        modelMap.addAttribute(Error_Msg,"支付成功！");
        return "success";
    }

    /**
     * <p>单独处理Alipay的成功支付</p>
     * @param params
     * @param out_trade_no
     * @param request
     * @return: void
     * @since : 2020-07-21
     */
    private void alipayPaySuccess(Map<String,String> params,String out_trade_no,int payType,HttpServletRequest request){
        if (out_trade_no.startsWith(PRE_RENT)){//如果是先租后买的
            //this.payRentSuccess();
            EnyanRent query = new EnyanRent();
            query.setOrderNum(BookUtil.getRentRealOrderByAlipay(out_trade_no));
            List<EnyanRent>  list = enyanRentService.findRecords(query);
            if (list.isEmpty() == true){
                return;
            }
            EnyanRent rent = list.get(0);
            OrderPayInfo orderPayInfo = new OrderPayInfo();
            //logger.info("shop orderPayInfo.addAlipay()");
            orderPayInfo.addAlipay(params,payType, true);
            if (rent.getIsPaid() == 1){//有时候，会多次获取支付成功记录，所以需要判断是否已经支付成功
                //校验detail里的tradeNo和orderNum，看是否已经写入这个交易，避免重复
                EnyanRentDetail queryDetail = new EnyanRentDetail();
                queryDetail.setOrderNum(rent.getOrderNum());
                queryDetail.setTradeNo(orderPayInfo.getCharge().getTradeNO());
                List<EnyanRentDetail> detailList = enyanRentDetailService.findRecords(queryDetail);
                if (detailList.isEmpty() == false){//这笔交易已经记录
                    return;
                }
            }
            int rentMonths = BookUtil.getRentMonthsByFee(rent.getRentPrice(),params.get("total_fee"));
            //this.paySuccess(enyanOrder,orderPayInfo,request);
            this.payRentSuccess(rent, orderPayInfo, "zh_CN", rentMonths);
            if (Constant.IS_PRODUCT){
                log.error("rent email:{}",rent.getUserEmail());
                log.error("支付验证成功");
            }
            return;
        }

        EnyanOrder enyanOrder = new EnyanOrder();
        enyanOrder.setOrderNum(out_trade_no);
        List<EnyanOrder> list = enyanOrderService.findRecordsWithBLOBsByOrder(enyanOrder);
        //logger.debug("list size:"+list.size());
        if (!list.isEmpty()){
            enyanOrder = list.get(0);
            if (Constant.BYTE_VALUE_1.equals(enyanOrder.getIsPaid())){//有时候，会多次获取支付成功记录，所以需要判断是否已经支付成功
                return;
            }
            OrderPayInfo orderPayInfo = new OrderPayInfo();
            //logger.info("shop orderPayInfo.addAlipay()");
            orderPayInfo.addAlipay(params,payType, true);
            this.paySuccess(enyanOrder,orderPayInfo,request);
            if (Constant.IS_PRODUCT){
                log.error("处理支付验证成功 email:{}",enyanOrder.getUserEmail());
            }
        }
    }

    @RequestMapping("/checkoutReturn")
    public String checkoutAlipayReturn(HttpServletRequest request, HttpServletResponse response, ModelMap modelMap) throws UnsupportedEncodingException {
        //response.addHeader("Content-Security-Policy", "default-src 'self' https://endao.co; connect-src 'none';");
        //response.addHeader("Content-Security-Policy", "default-src 'self'; frame-ancestors 'self'; frame-src 'self'; X-Frame-Options 'SAMEORIGIN'; style-src 'self'; connect-src 'self'; script-src 'self' 'unsafe-eval'; media-src 'self'; img-src 'self'; base-uri 'self';");
        //response.addHeader("X-XSS-Protection", "0");
        response.addHeader("X-Frame-Options", "SAMEORIGIN");
        response.addHeader("Content-Security-Policy", "default-src 'self'; frame-ancestors 'self'; frame-src 'self'; worker-src 'self'; child-src 'self'; script-src 'self'; sandbox allow-same-origin; ");
        response.addHeader("X-XSS-Protection", "0");
        response.addHeader("P3P", "CP=CAO PSA OUR");
        response.addHeader("Access-Control-Allow-Methods", "POST, GET");
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Credentials", "true");
        //获取支付宝GET过来反馈信息
        Map<String,String> params = new HashMap<>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
            valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            //logger.info("checkoutReturn name:"+name+",value:"+valueStr);
            params.put(name, valueStr);
        }

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以下仅供参考)//
        //商户订单号
        String outTradeNO = request.getParameter("out_trade_no");
        String tradeNO = request.getParameter("trade_no");
        String tradeStatus = request.getParameter("trade_status");
        if (StringUtils.hasLength(outTradeNO) == false|| StringUtils.hasLength(tradeNO) == false
                    || StringUtils.hasLength(tradeStatus) == false){
            log.info("checkoutReturn 验证失败");
            modelMap.addAttribute(Error_Msg,this.getMessage("error.pay.fail",request));
            return "500";
        }

        String out_trade_no = new String(outTradeNO.getBytes("ISO-8859-1"),"UTF-8");

        //支付宝交易号

        String trade_no = new String(tradeNO.getBytes("ISO-8859-1"),"UTF-8");

        //交易状态
        String trade_status = new String(tradeStatus.getBytes("ISO-8859-1"),"UTF-8");

        //获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以上仅供参考)//

        //计算得出通知验证结果
        boolean verify_result = AlipayNotify.verifyReturn(params);

        if(verify_result){//验证成功
            //////////////////////////////////////////////////////////////////////////////////////////
            //请在这里加上商户的业务逻辑程序代码

            //——请根据您的业务逻辑来编写程序（以下代码仅作参考）——
            if(trade_status.equals("TRADE_FINISHED") || trade_status.equals("TRADE_SUCCESS")){
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //如果有做过处理，不执行商户的业务程序

                /*EnyanOrder enyanOrder = new EnyanOrder();
                enyanOrder.setOrderNum(out_trade_no);
                List<EnyanOrder> list = enyanOrderService.selectByExampleWithBLOBs(enyanOrder);
                if (!list.isEmpty()){
                    enyanOrder = list.get(0);
                    if (!(Constant.BYTE_VALUE_1).equals(enyanOrder.getIsPaid())){
                        OrderPayInfo orderPayInfo = new OrderPayInfo();
                        orderPayInfo.addAlipay(params,true);
                        this.paySuccess(enyanOrder,orderPayInfo);
                    }
                }*/
            }

            //该页面可做页面美工编辑
            //logger.info("checkoutReturn 验证成功<br />");
            modelMap.addAttribute("tradeNO",out_trade_no);
            //——请根据您的业务逻辑来编写程序（以上代码仅作参考）——
            EnyanOrder order = (EnyanOrder) request.getSession().getAttribute(EBookConstant.Cart.ORDER_CONFIRM);
            if (null == order){
                return "/shop/checkoutReturn";
            }
            if (order.getOrderType() == EBookConstant.OrderType.ORDER_REDEEM_BUY){
                return "/shop/checkoutReturnGift";
            }
            return "/shop/checkoutReturn";
            //////////////////////////////////////////////////////////////////////////////////////////
        }else{
            //该页面可做页面美工编辑
            log.info("checkoutReturn 验证失败");
            modelMap.addAttribute(Error_Msg,this.getMessage("error.pay.fail",request));
            return "500";
        }
    }
    @RequestMapping("/scr")
    public String creditCheckoutReturn(HttpServletRequest request, ModelMap modelMap){
        EnyanOrder order = (EnyanOrder) request.getSession().getAttribute(EBookConstant.Cart.ORDER_CONFIRM);
        if (null == order){
            return "/shop/checkoutReturn";
        }
        if (order.getOrderType() == EBookConstant.OrderType.ORDER_REDEEM_BUY){
            return "/shop/checkoutReturnGift";
        }
        return "/shop/checkoutReturn";
    }
    @RequestMapping(value = "/myCenter")
    public String myCenter(HttpServletRequest request, ModelMap modelMap){
        String email = UserUtils.getCurrentLoginUser().getEmail();
        if (StringUtils.hasLength(email)){
            Pattern p = Pattern.compile("(\\w{1})(\\w+)(\\w{1})(@\\w+)");
            Matcher m = p.matcher(email);
            String showEmail = m.replaceAll("$1***$3$4");

            modelMap.addAttribute("showEmail",showEmail);
        }
        return "/shop/myCenter";
    }
    @RequestMapping(value = "/myOrders")
    public String orders(HttpServletRequest request, HttpServletResponse response, ModelMap modelMap){
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        EnyanOrder order = new EnyanOrder();
        //order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
        order.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
        Page page = new Page();
        page.setPageSize(12);
        order.setPage(page);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }

        page = enyanOrderService.queryRecords(order.getPage(),order);

        order.excuteFrontPageLand(queryParams);

        modelMap.addAttribute("orders",page.getRecords());
        modelMap.addAttribute("pageLand",order.getPageLand());
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "/shop/myOrders";
    }

    @RequestMapping(value = "/cancelOrder-{orderId}")
    public String cancelOrder(@PathVariable("orderId")Long orderId, ModelMap modelMap,
                           HttpServletRequest request, HttpServletResponse response){
        EnyanOrder order = enyanOrderService.queryRecordByPrimaryKey(orderId).getResult();

        if (null == order || UserUtils.isInvalid(order.getUserId())){
            modelMap.addAttribute(Error_Msg, InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        EnyanOrder newOrder = new EnyanOrder();
        newOrder.setOrderId(orderId);
        newOrder.setIsValid(Constant.BYTE_VALUE_0);
        enyanOrderService.updateRecord(newOrder);

        String orderDetailInfo = order.getOrderDetail();
        OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);

        //订单取消，销量-1
        List<Long> bookIdList = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:detailInfo.getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                bookIdList.add(productInfo.getCode());
            }
        }
        if (bookIdList.size() > 0){
            enyanBookService.updateBookSaleVolumeMinus(bookIdList);
        }

        return "redirect:/myOrders";
    }

    @RequestMapping(value = "/delOrder-{orderId}")
    public String delOrder(@PathVariable("orderId")Long orderId, ModelMap modelMap,
                              HttpServletRequest request, HttpServletResponse response){
        EnyanOrder order = enyanOrderService.queryRecordByPrimaryKey(orderId).getResult();

        if (null == order || UserUtils.isInvalid(order.getUserId())){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        if (!order.getIsValid().equals(Constant.BYTE_VALUE_0)){
            modelMap.addAttribute(Error_Msg,"只能删除取消的订单");
            return "/500";
        }
        if (!order.getIsPaid().equals(Constant.BYTE_VALUE_0)){
            modelMap.addAttribute(Error_Msg,"删除失败");
            return "/500";
        }
        enyanOrderService.deleteOrder(order);

        String orderDetailInfo = order.getOrderDetail();
        OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);

        //订单取消，销量-1
        List<Long> bookIdList = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:detailInfo.getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                bookIdList.add(productInfo.getCode());
            }
        }
        if (bookIdList.size()>0){
            enyanBookService.updateBookSaleVolumeMinus(bookIdList);
        }
        return "redirect:/myOrders";
    }

    @RequestMapping(value = "/orderDetail-{orderId}")
    public String orderDetail(@PathVariable("orderId")Long orderId, ModelMap modelMap,
                              HttpServletRequest request, HttpServletResponse response){
        //this.initCart(modelMap,request);

        ExecuteResult<EnyanOrder> result = enyanOrderService.queryRecordByPrimaryKey(orderId);

        EnyanOrder order = result.getResult();
        if (null == order){
            throw new BusinessException(Integer.valueOf(104),this.getMessage("error.404.order",request),"无法找到订单 orderId:"+orderId);
        }
        if (!Constant.BYTE_VALUE_1.equals(order.getIsValid()) || 1 == order.getIsDeleted()){
            throw new BusinessException(Integer.valueOf(105),this.getMessage("error.invalid.order",request),"订单不合法 orderId:"+orderId);
        }
        if (UserUtils.isInvalid(order.getUserId())){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }

        //String orderDetailInfo = result.getResult().getOrderDetail();

        OrderDetailInfo detailInfo = order.getOrderDetailInfo();

        modelMap.addAttribute("orderMain",order);
        modelMap.addAttribute("order",detailInfo);

        return "/shop/orderDetail";
    }

    @RequestMapping(value = "/showInvoice-{orderId}")
    public String showInvoice(@PathVariable("orderId")Long orderId, ModelMap modelMap,
                              HttpServletRequest request, HttpServletResponse response){

        ExecuteResult<EnyanOrder> result = enyanOrderService.queryRecordByPrimaryKey(orderId);

        EnyanOrder order = result.getResult();
        if (null == order){
            throw new BusinessException(Integer.valueOf(104),this.getMessage("error.404.order",request),"无法找到订单 orderId:"+orderId);
        }
        if (!Constant.BYTE_VALUE_1.equals(order.getIsValid()) || 1 == order.getIsDeleted()){
            throw new BusinessException(Integer.valueOf(105),this.getMessage("error.invalid.order",request),"订单不合法 orderId:"+orderId);
        }
        if (UserUtils.isInvalid(order.getUserId())){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }

        OrderDetailInfo detailInfo = order.getOrderDetailInfo();

        List<ProductInfo> list = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:detailInfo.getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                if (productInfo.isDiscountAnyIsValid() == false){
                    productInfo.setPriceHKDDiscount(productInfo.getPriceHkd());
                }
                list.add(productInfo);
            }
        }
        modelMap.addAttribute("user",UserUtils.getCurrentLoginUser());
        modelMap.addAttribute("order",order);
        modelMap.addAttribute("list",list);

        return "shop/invoice.html";
    }

    @RequestMapping(value = "/downloadInvoice-{orderId}")
    public ResponseEntity<byte[]> downloadInvoice(@PathVariable("orderId")Long orderId,
                              HttpServletRequest request, HttpServletResponse response){
        ResponseEntity<byte[]> entity = null;
        //System.out.println("downloadEpub");
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
        headers.add("Accept-Ranges","bytes");

        ExecuteResult<EnyanOrder> result = enyanOrderService.queryRecordByPrimaryKey(orderId);

        EnyanOrder order = result.getResult();
        if (null == order){
            entity = new ResponseEntity<>(this.getMessage("error.404.order",request).getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);
            return entity;
        }
        if (!Constant.BYTE_VALUE_1.equals(order.getIsValid()) || 1 == order.getIsDeleted()){
            entity = new ResponseEntity<>(this.getMessage("error.invalid.order",request).getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);
            return entity;
        }
        if (UserUtils.isInvalid(order.getUserId())){
            //modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            entity = new ResponseEntity<>("error004".getBytes(), headers, HttpStatus.INTERNAL_SERVER_ERROR);
            return entity;
        }

        OrderDetailInfo detailInfo = order.getOrderDetailInfo();

        List<ProductInfo> list = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:detailInfo.getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                if (productInfo.isDiscountAnyIsValid() == false){
                    productInfo.setPriceHKDDiscount(productInfo.getPriceHkd());
                }
                list.add(productInfo);
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("user",UserUtils.getCurrentLoginUser());
        map.put("order",order);
        map.put("list",list);
        map.put("locale", localeResolver.resolveLocale(request));

        byte[] dataBytes = pdfGenaratorUtil.exportToByte(map,"shop/invoice.html");
        String fileName = order.getOrderNum() + ".pdf";
        headers.setContentDispositionFormData("attachment", fileName);//告知浏览器以下载方式打开
        entity = new ResponseEntity<>(dataBytes, headers, HttpStatus.OK);
        return entity;
    }

    @RequestMapping(value = "/orderNumDetail-{orderNum}")
    public String orderNumDetail(@PathVariable("orderNum")String orderNum, ModelMap modelMap,
                              HttpServletRequest request, HttpServletResponse response){
        //this.initCart(modelMap,request);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }

        EnyanOrder order = new EnyanOrder();
        order.setOrderNum(orderNum);

        order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());

        List<EnyanOrder> list = enyanOrderService.findRecordsWithBLOBsByOrder(order);

        if (null != list && !list.isEmpty()){
            order = list.get(0);
            String orderDetailInfo = order.getOrderDetail();

            OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);

            modelMap.addAttribute("orderMain",order);
            modelMap.addAttribute("order",detailInfo);
        }

        return "/shop/orderDetail";
    }

    @RequestMapping(value = "/myCart")
    public String myCart(HttpServletRequest request,  ModelMap modelMap){
        CartInfoGerenal cartInfoGerenal = this.initCartInfoGeneral(modelMap,request,true);
        Map<String,String> data = new HashMap<>();
        data.put("quantity",String.valueOf(cartInfoGerenal.getQuantity()));
        //data.put("amountCny",String.valueOf(cartInfo.getAmountCny()));
        //data.put("amountUsd",String.valueOf(cartInfo.getAmountUsd()));
        request.getSession().setAttribute(EBookConstant.Cart.DATA,data);
        return "/shop/myCart";
    }

    /**
     * JSON 返回
     * */
    @ResponseBody
    @RequestMapping(value = "/wish-add-{bookId}")
    public Map<String,Object> wishAdd(@PathVariable("bookId")Long bookId, ModelMap modelMap,
                                      HttpServletRequest request, HttpServletResponse response){
        log.debug("method addWish："+bookId);

        if (null == bookId || bookId<0){
            return ResultUtils.getFailedResultData(this.getMessage("error.book.select",request));
        }
        if (UserUtils.isAnonymous()){
            return ResultUtils.getFailedResultData(InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
        }

        EnyanWish enyanWish = new EnyanWish();
        enyanWish.setBookId(bookId);
        enyanWish.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
//        enyanWish.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
        enyanWish.setWishedAt(new Date());

        enyanWishService.addRecord(enyanWish);

        Map<String,String> data = new HashMap<>();

        Map<String,Object> result = ResultUtils.getSuccessResultData(data);

        return result;
    }
    @RequestMapping(value = "/wish-del-{bookId}")
    public String wishDel(@PathVariable("bookId")Long bookId, ModelMap modelMap,
                                      HttpServletRequest request, HttpServletResponse response){
        log.debug("method delWish："+bookId);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }

        EnyanWish enyanWish = new EnyanWish();
        enyanWish.setBookId(bookId);
        enyanWish.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
//        enyanWish.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());

        enyanWishService.deleteByExample(enyanWish);

        return "redirect:/myWishes";
    }
    @RequestMapping(value = "/myWishes")
    public String wishesMy(HttpServletRequest request, HttpServletResponse response, ModelMap modelMap){
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        EnyanWish enyanWish = new EnyanWish();
        enyanWish.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
        Page page = new Page();
        page.setPageSize(12);
        enyanWish.setPage(page);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }

        page = enyanWishService.queryWishes(enyanWish.getPage(),enyanWish);

        enyanWish.excuteFrontPageLand(queryParams);

        modelMap.addAttribute("books",page.getRecords());
        modelMap.addAttribute("pageLand",enyanWish.getPageLand());
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "/shop/myWishes";
    }

    @RequestMapping(value = "/device-del-{index}")
    public String delDevice(@PathVariable("index")int index, ModelMap modelMap,
                          HttpServletRequest request, HttpServletResponse response){
        log.debug("method delDevice："+index);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }

        String email = UserUtils.getCurrentLoginUser().getEmail().toLowerCase();

        UserInfo userInfo = authUserService.loadUserInfoByEmail(email);
        List<DeviceLimit> deviceLimitList = userInfo.getCustomUserDetail().getDeviceLimitList();
        if (null != deviceLimitList && index >=0){
//            deviceLimitList.remove(deviceLimitList.get(index));
            if (index < deviceLimitList.size()){
                deviceLimitList.remove(index);
            }
        }
        userInfo.getCustomUserDetail().setDeviceLimitList(deviceLimitList);
        authUserService.updateUserInfo(userInfo);

        return "redirect:/myDevices";
    }

    @RequestMapping(value = "/myDevices")
    public String myDevices(HttpServletRequest request, HttpServletResponse response, ModelMap modelMap){
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        String email = UserUtils.getCurrentLoginUser().getEmail();

        UserInfo userInfo = authUserService.loadUserInfoByEmail(email);
        if (null == userInfo){
            userInfo = new UserInfo();
            userInfo.setUsername(email);
            CustomUserDetail customUserDetail = new CustomUserDetail();
            customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
            customUserDetail.setDeviceLimitList(new ArrayList<>());
            userInfo.setCustomUserDetail(customUserDetail);
            authUserService.addUserInfo(userInfo);
        }
        if (null == userInfo.getCustomUserDetail().getDeviceLimitList()){
            userInfo.getCustomUserDetail().setDeviceLimitList(new ArrayList<>());
        }

        modelMap.addAttribute("deviceLimitList",userInfo.getCustomUserDetail().getDeviceLimitList());

        return "/shop/myDevices";
    }

    @RequestMapping(value = "/myUpdatePwd")
    public String updatePwd(HttpServletRequest request, ModelMap modelMap){
        return "/shop/myUpdatePwd";
    }
    @RequestMapping(value = "/updateSuccess")
    public String updateSuccess(HttpServletRequest request, ModelMap modelMap){
        return "/shop/updateSuccess";
    }
    @RequestMapping(value = "/updateFullSuccess")
    public String updateFullSuccess(HttpServletRequest request, ModelMap modelMap){
        return "/shop/updateFullSuccess";
    }
    @RequestMapping(value = "/myUpdatePwdAction", method = RequestMethod.POST)
    public String myUpdatePwdAction(AuthUser authUser, ModelMap modelMap,HttpServletRequest request){
        //logger.info("method regAction："+authUser);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        log.debug("passwd:{},{}",authUser.getUserPassword(),authUser.getUserPassword().toString());
        modelMap.addAttribute("authUser",authUser);
        if (StringUtils.hasLength(authUser.getSalt()) == false){//使用salt替代 原密码
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.oldnull",request));
            return "/shop/myUpdatePwd";
        }

        if (StringUtils.hasLength(authUser.getUserPassword()) == false || StringUtils.hasLength(authUser.getUserPasswordAgain()) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.null",request));
            return "/shop/myUpdatePwd";
        }
        if (authUser.getUserPassword().length()<6 || authUser.getUserPassword().length() > 20){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.format",request));
            return "/shop/myUpdatePwd";
        }
        if (!authUser.getUserPassword().equals(authUser.getUserPasswordAgain())){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.again",request));
            return "/shop/myUpdatePwd";
        }

        String email = UserUtils.getCurrentLoginUser().getEmail();
        AuthUser user = authUserService.getUserByEmail(email).getResult();

        if (null == user){
            this.setErrorMsg(modelMap, this.getMessage("error.email.notexist",request));
            return "/shop/myUpdatePwd";
        }
        if (!authUserService.isPasswordValid(user.getUserPassword(),authUser.getSalt(),user.getSalt())){
            this.setErrorMsg(modelMap, this.getMessage("error.passwd.old.incorect",request));
            return "/shop/myUpdatePwd";
        }
        authUser.setEmail(email);
        authUserService.updatePasswd(authUser);
        //修改密码，则解绑所有设备
        UserInfo userInfo = authUserService.loadUserInfoByEmail(email);
        if (null != userInfo){
            userInfo.getCustomUserDetail().setDeviceLimitList(new ArrayList<>());
            authUserService.updateUserInfo(userInfo);
        }
        request.getSession().setAttribute("updateExplan",this.getMessage("update.passwd",request));
        return "redirect:/updateSuccess";
    }
    @RequestMapping(value = "/myUpdateName")
    public String updateName(HttpServletRequest request, ModelMap modelMap){
        return "/shop/myUpdateName";
    }
    @RequestMapping(value = "/myUpdateNameAction", method = RequestMethod.POST)
    public String myUpdateNameAction(AuthUser authUser, ModelMap modelMap,HttpServletRequest request){
        //logger.info("method regAction："+authUser);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        modelMap.addAttribute("authUser",authUser);

        if (StringUtils.hasLength(authUser.getNickName()) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.nickname.null",request));
            return "/shop/myUpdateName";
        }

        String email = UserUtils.getCurrentLoginUser().getEmail();

        authUser.setEmail(email);
        authUserService.updateName(authUser);
        request.getSession().setAttribute("updateExplan",this.getMessage("update.nickname",request));


        /*Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof IUser) {

        }*/
        IUser user = userService.findByUsername(email);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        //List<GrantedAuthority> updatedAuthorities = new ArrayList<>(auth.getAuthorities());
        //updatedAuthorities.add(...); //add your role here [e.g., new SimpleGrantedAuthority("ROLE_NEW_ROLE")]

        Authentication newAuth = new UsernamePasswordAuthenticationToken(user, auth.getCredentials(), auth.getAuthorities());

        SecurityContextHolder.getContext().setAuthentication(newAuth);
        return "redirect:/updateSuccess";
    }
    @RequestMapping(value = "/myRedeemCode")
    public String myRedeemCode(HttpServletRequest request, ModelMap modelMap){
        return "/shop/myRedeemCode";
    }

    /**
     * <p>兑换码进行兑换</p>
     * @param enyanRedeemCode
     * @param modelMap
     * @param request
     * @return: java.lang.String
     * @since : 2024/12/24
     */
    @RequestMapping(value = "/redeemCodeUpdateInfo", method = RequestMethod.POST)
    public String redeemCodeUpdateInfo(EnyanRedeemCode enyanRedeemCode, ModelMap modelMap,HttpServletRequest request){
        //logger.info("method regAction："+authUser);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }

        if (StringUtils.hasLength(enyanRedeemCode.getCode()) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }

        List<EnyanRedeemCode> list= this.enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);
        if (null == list || list.isEmpty()){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }
        Long bookId = 0L;
        EnyanRedeemCode newRedeemCode = list.get(0);
        RedeemCodeNoteInfo redeemCodeNoteInfo = newRedeemCode.getRedeemCodeNoteInfo();
        if (null == redeemCodeNoteInfo){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode"; //error.redeemCode.has
        }

        if (newRedeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode.has",request));
            return "/shop/myRedeemCode"; //error.redeemCode.has
        }

        if (null != newRedeemCode.getEndAt()){//有截止日期
            Date current = new Date();
            if (current.compareTo(newRedeemCode.getEndAt()) > 0){//超时
                this.setErrorMsg(modelMap, this.getMessage("error.redeemCode.expired",request));
                return "/shop/myRedeemCode"; //error.redeemCode.expired
            }
        }
        List<EnyanBook> allRedeemBookList = redeemCodeNoteInfo.getBooksToRedeemList();
        if (null == allRedeemBookList || allRedeemBookList.isEmpty()){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }
        Long[] bookIds = null;
        String email = UserUtils.getCurrentLoginUser().getEmail();
        if (newRedeemCode.getType() == EBookConstant.RedeemType.SPECIAL){
            bookId = allRedeemBookList.get(0).getBookId();
            bookIds = new Long[]{bookId};
        } else if (newRedeemCode.getType() == EBookConstant.RedeemType.ALL) {
            bookIds = allRedeemBookList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);
        }
        List<EnyanBook> hasBuyList = enyanBookBuyService.findBookListHasBuyByEmailAndIds(email, bookIds);
        redeemCodeNoteInfo.setBooksHasBuyList(hasBuyList);

        List<EnyanBook> canBuyList = allRedeemBookList.stream().filter(o1 -> hasBuyList.stream().noneMatch(o2 -> o2.getBookTitle().equals(o1.getBookTitle()))).collect(Collectors.toList());
        redeemCodeNoteInfo.setBooksCanBuyList(canBuyList);
        if (null == canBuyList || canBuyList.isEmpty()){//已经购买
            return "redirect:/redeemCodeFail";
        }

        if (newRedeemCode.getType() == EBookConstant.RedeemType.ALL) {
            modelMap.addAttribute("redeemCode",newRedeemCode);
            return "/shop/myRedeemCodeInfo";
        }

        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
        EnyanOrder order ;
        try {
            if (null == bookId || bookId<0){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.redeemCode",request));
                return "/shop/myRedeemCode";
            }
            EnyanOrderDetail queryOrderDetail = new EnyanOrderDetail();
            queryOrderDetail.setBookId(bookId);
            queryOrderDetail.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());

            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
            /*
            if (null == enyanBook || !enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1){ // 下架的也可以进行兑换
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }*/
            //logger.debug("book:"+enyanBook.getBookId()+","+enyanBook.getBookTitle());
            ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            //logger.debug("p:"+productInfo);
            cartInfo.addProduct(productInfo,1);

            //logger.debug("SIZE:"+cartInfo.findProducts().size());

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

            /*if (titleInfo.isEmpty()){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }*/

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderDetailInfo(detailInfo);
            order.setOrderDetail(JSON.toJSONString(detailInfo));

            order.setOrderTitleInfo(titleInfo);
            order.setOrderTitle(JSON.toJSONString(titleInfo));

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_1);
            order.setIsCounted(Constant.BYTE_VALUE_1);

            order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
            order.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
            order.setPurchasedAt(new Date());
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(new BigDecimal("0"));
            order.setOrderFrom(EBookConstant.OrderFrom.WEB);

            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addRedeemCode(newRedeemCode);
            order.setOrderPayInfo(orderPayInfo);
            order.setPayInfo(JSON.toJSONString(orderPayInfo));
            //EnyanOrder savedOrder = this.enyanOrderService.addRecord(order).getResult();

            redeemCodeNoteInfo.setDateToRedeem(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            redeemCodeNoteInfo.setEmailToRedeem(UserUtils.getCurrentLoginUser().getEmail());

            EnyanRedeemCode redeemCodeToUpdate = new EnyanRedeemCode();
            redeemCodeToUpdate.setRedeemCodeNoteInfo(newRedeemCode.getRedeemCodeNoteInfo());
            redeemCodeToUpdate.setRedeemCodeId(newRedeemCode.getRedeemCodeId());
            redeemCodeToUpdate.setStatus(EBookConstant.RedeemStatus.USE);
            //this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);
            /*
            this.enyanOrderDetailService.saveOrderFromRedeem(order,redeemCodeToUpdate);
            enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());*/
            this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);//先更改兑换码状态
            new Thread(new Runnable(){
                @Override
                public void run() {
                    enyanOrderService.saveOrderRedeem(order, List.of(enyanBook),redeemCodeToUpdate);
                    sendMailOfOrder(order,request);
                    sendLogOfOrder(order);
                }
            }
            ).start();

        } catch (Exception e) {
            e.printStackTrace();
            modelMap.addAttribute(Error_Msg,"兑换码兑换失败！");
            return "500";
        }
        return "redirect:/redeemCodeSuccess";
    }

    /**
     * <p>兑换码进行兑换</p>
     * @param enyanRedeemCode
     * @param modelMap
     * @param request
     * @return: java.lang.String
     * @since : 2024/12/24
     */
    @RequestMapping(value = "/redeemCodeDoAction", method = RequestMethod.POST)
    public String redeemCodeDoAction(EnyanRedeemCode enyanRedeemCode, ModelMap modelMap,HttpServletRequest request){
        //logger.info("method regAction："+authUser);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }

        if (StringUtils.hasLength(enyanRedeemCode.getCode()) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }

        List<EnyanRedeemCode> list= this.enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);
        if (null == list || list.isEmpty()){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }
        Long bookId = 0L;
        EnyanRedeemCode newRedeemCode = list.get(0);
        RedeemCodeNoteInfo redeemCodeNoteInfo = newRedeemCode.getRedeemCodeNoteInfo();
        if (null == redeemCodeNoteInfo){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode"; //error.redeemCode.has
        }

        if (newRedeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode.has",request));
            return "/shop/myRedeemCode"; //error.redeemCode.has
        }

        if (null != newRedeemCode.getEndAt()){//有截止日期
            Date current = new Date();
            if (current.compareTo(newRedeemCode.getEndAt()) > 0){//超时
                this.setErrorMsg(modelMap, this.getMessage("error.redeemCode.expired",request));
                return "/shop/myRedeemCode"; //error.redeemCode.expired
            }
        }
        List<EnyanBook> allRedeemBookList = redeemCodeNoteInfo.getBooksToRedeemList();
        if (null == allRedeemBookList || allRedeemBookList.isEmpty()){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }
        Long[] bookIds = null;
        String email = UserUtils.getCurrentLoginUser().getEmail();
        if (newRedeemCode.getType() == EBookConstant.RedeemType.SPECIAL){
            bookId = allRedeemBookList.get(0).getBookId();
            bookIds = new Long[]{bookId};
        } else if (newRedeemCode.getType() == EBookConstant.RedeemType.ALL) {
            bookIds = allRedeemBookList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);
        }
        List<EnyanBook> hasBuyList = enyanBookBuyService.findBookListHasBuyByEmailAndIds(email, bookIds);
        redeemCodeNoteInfo.setBooksHasBuyList(hasBuyList);

        List<EnyanBook> canBuyList = allRedeemBookList.stream().filter(o1 -> hasBuyList.stream().noneMatch(o2 -> o2.getBookTitle().equals(o1.getBookTitle()))).collect(Collectors.toList());
        if (null == canBuyList || canBuyList.isEmpty()){//书籍都已经购买
            return "redirect:/redeemCodeFail";
        }

        Long[] bookIdsCanBuy = canBuyList.stream().map(EnyanBook::getBookId).toArray(Long[]::new);

        List<EnyanBook> bookList = enyanBookService.findBookByIdsArray(bookIdsCanBuy);
        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
        EnyanOrder order ;
        try {
            if (null == bookIds || bookIds.length==0){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.redeemCode",request));
                return "/shop/myRedeemCode";
            }
//            EnyanOrderDetail queryOrderDetail = new EnyanOrderDetail();
//            queryOrderDetail.setBookId(bookId);
//            queryOrderDetail.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());

            for (EnyanBook enyanBook : bookList){
                //EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
                /*
                if (null == enyanBook || !enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1){ // 下架的也可以进行兑换
                    modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                    return "500";
                }*/
                //logger.debug("book:"+enyanBook.getBookId()+","+enyanBook.getBookTitle());
                ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);
                cartInfo.addProduct(productInfo,1);
            }

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);
            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderDetailInfo(detailInfo);
            order.setOrderDetail(JSON.toJSONString(detailInfo));

            order.setOrderTitleInfo(titleInfo);
            order.setOrderTitle(JSON.toJSONString(titleInfo));

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_1);
            order.setIsCounted(Constant.BYTE_VALUE_1);

            order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
            order.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
            order.setPurchasedAt(new Date());
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(new BigDecimal("0"));
            order.setOrderFrom(EBookConstant.OrderFrom.WEB);

            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addRedeemCode(newRedeemCode);
            order.setOrderPayInfo(orderPayInfo);
            order.setPayInfo(JSON.toJSONString(orderPayInfo));
            //EnyanOrder savedOrder = this.enyanOrderService.addRecord(order).getResult();

            redeemCodeNoteInfo.setDateToRedeem(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            redeemCodeNoteInfo.setEmailToRedeem(UserUtils.getCurrentLoginUser().getEmail());

            EnyanRedeemCode redeemCodeToUpdate = new EnyanRedeemCode();
            redeemCodeToUpdate.setRedeemCodeNoteInfo(newRedeemCode.getRedeemCodeNoteInfo());
            redeemCodeToUpdate.setRedeemCodeId(newRedeemCode.getRedeemCodeId());
            redeemCodeToUpdate.setStatus(EBookConstant.RedeemStatus.USE);
            //this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);
            /*
            this.enyanOrderDetailService.saveOrderFromRedeem(order,redeemCodeToUpdate);
            enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());*/
            this.enyanOrderService.saveOrderRedeem(order, bookList,redeemCodeToUpdate);
            this.sendMailOfOrder(order,request);
            this.sendLogOfOrder(order);
        } catch (Exception e) {
            e.printStackTrace();
            modelMap.addAttribute(Error_Msg,"兑换码兑换失败！");
            return "500";
        }
        return "redirect:/redeemCodeSuccess";
    }

    /**
     * <p>兑换码进行兑换</p>
     * @param enyanRedeemCode
     * @param modelMap
     * @param request
     * @return: java.lang.String
     * @since : 2021/3/22
     */
    @RequestMapping(value = "/myUpdateRedeemCodeAction", method = RequestMethod.POST)
    public String redeemCodeUpdateAction(EnyanRedeemCode enyanRedeemCode, ModelMap modelMap,HttpServletRequest request){
        //logger.info("method regAction："+authUser);
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        modelMap.addAttribute("enyanRedeemCode",enyanRedeemCode);

        if (StringUtils.hasLength(enyanRedeemCode.getCode()) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }


        /*Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof IUser) {

        }*/
        List<EnyanRedeemCode> list= this.enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);
        if (null == list || list.isEmpty()){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode";
        }
        Long bookId = 0L;
        EnyanRedeemCode newRedeemCode = list.get(0);
        RedeemCodeNoteInfo redeemCodeNoteInfo = newRedeemCode.getRedeemCodeNoteInfo();
        if (null == redeemCodeNoteInfo){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
            return "/shop/myRedeemCode"; //error.redeemCode.has
        }

        if (newRedeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
            this.setErrorMsg(modelMap, this.getMessage("error.redeemCode.has",request));
            return "/shop/myRedeemCode"; //error.redeemCode.has
        }

        if (null != newRedeemCode.getEndAt()){//有截止日期
            Date current = new Date();
            if (current.compareTo(newRedeemCode.getEndAt()) > 0){//超时
                this.setErrorMsg(modelMap, this.getMessage("error.redeemCode.expired",request));
                return "/shop/myRedeemCode"; //error.redeemCode.expired
            }
        }

        if (newRedeemCode.getType() == EBookConstant.RedeemType.SPECIAL){
            if (null == redeemCodeNoteInfo.getBooksToRedeemList() || redeemCodeNoteInfo.getBooksToRedeemList().isEmpty()){
                this.setErrorMsg(modelMap, this.getMessage("error.redeemCode",request));
                return "/shop/myRedeemCode";
            }
            bookId = redeemCodeNoteInfo.getBooksToRedeemList().get(0).getBookId();
        }

        CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
        EnyanOrder order ;
        try {
            if (null == bookId || bookId<0){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.redeemCode",request));
                return "/shop/myRedeemCode";
            }
            if (UserUtils.isAnonymous()){
                modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
                return "redirect:/login";
            }
            EnyanOrderDetail queryOrderDetail = new EnyanOrderDetail();
            queryOrderDetail.setBookId(bookId);
            queryOrderDetail.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
            List<EnyanBookBuy> bookBuyList = enyanBookBuyService.getBookIDAndNameByEmailAndBookId(UserUtils.getCurrentLoginUser().getEmail(), bookId);
            if (null != bookBuyList && bookBuyList.isEmpty() == false){//已经兑换
                return "redirect:/redeemCodeFail";
            }

            EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(bookId).getResult();
            /*
            if (null == enyanBook || !enyanBook.getShelfStatus().equals(Constant.BYTE_VALUE_1){ // 下架的也可以进行兑换
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }*/
            //logger.debug("book:"+enyanBook.getBookId()+","+enyanBook.getBookTitle());
            ProductInfo productInfo = new ProductInfo(enyanBook, EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            //logger.debug("p:"+productInfo);
            cartInfo.addProduct(productInfo,1);

            //logger.debug("SIZE:"+cartInfo.findProducts().size());

            OrderTitleInfo titleInfo = new OrderTitleInfo(cartInfo);

            /*if (titleInfo.isEmpty()){
                modelMap.addAttribute(Error_Msg,this.getMessage("error.book.select",request));
                return "500";
            }*/

            OrderDetailInfo detailInfo = new OrderDetailInfo(cartInfo);
            order = new EnyanOrder();
            order.setOrderNum(OrderUtil.getOrderId());
            order.setOrderDetailInfo(detailInfo);
            order.setOrderDetail(JSON.toJSONString(detailInfo));

            order.setOrderTitleInfo(titleInfo);
            order.setOrderTitle(JSON.toJSONString(titleInfo));

            order.setIsValid(Constant.BYTE_VALUE_1);
            order.setIsPaid(Constant.BYTE_VALUE_1);
            order.setIsCounted(Constant.BYTE_VALUE_1);

            order.setOrderType(EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE);

            order.setUserId((Long)UserUtils.getCurrentLoginUser().getUserId());
//            order.setUserName(UserUtils.getCurrentLoginUser().getUsername());
            order.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
            order.setPurchasedAt(new Date());
            order.setOrderCurrency(Constant.HKD_BYTE_VALUE);
            order.setOrderTotal(new BigDecimal("0"));
            order.setOrderFrom(EBookConstant.OrderFrom.WEB);

            OrderPayInfo orderPayInfo = new OrderPayInfo();
            orderPayInfo.addRedeemCode(newRedeemCode);
            order.setOrderPayInfo(orderPayInfo);
            order.setPayInfo(JSON.toJSONString(orderPayInfo));
            //EnyanOrder savedOrder = this.enyanOrderService.addRecord(order).getResult();

            redeemCodeNoteInfo.setDateToRedeem(DateFormatUtils.format(new Date(),"yyyyMMdd"));
            redeemCodeNoteInfo.setEmailToRedeem(UserUtils.getCurrentLoginUser().getEmail());

            EnyanRedeemCode redeemCodeToUpdate = new EnyanRedeemCode();
            redeemCodeToUpdate.setRedeemCodeNoteInfo(newRedeemCode.getRedeemCodeNoteInfo());
            redeemCodeToUpdate.setRedeemCodeId(newRedeemCode.getRedeemCodeId());
            redeemCodeToUpdate.setStatus(EBookConstant.RedeemStatus.USE);
            //this.enyanRedeemCodeService.updateRecord(redeemCodeToUpdate);
            /*
            this.enyanOrderDetailService.saveOrderFromRedeem(order,redeemCodeToUpdate);
            enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());*/
            this.enyanOrderService.saveOrderRedeem(order, List.of(enyanBook),redeemCodeToUpdate);
            this.sendMailOfOrder(order,request);
            this.sendLogOfOrder(order);
        } catch (Exception e) {
            e.printStackTrace();
            modelMap.addAttribute(Error_Msg,"兑换码兑换失败！");
            return "500";
        }
        return "redirect:/redeemCodeSuccess";
    }

    @RequestMapping(value = "/redeemCodeSuccess")
    public String redeemCodeSuccess(HttpServletRequest request, ModelMap modelMap){
        return "/shop/redeemCodeSuccessReturn";
    }

    @RequestMapping(value = "/redeemCodeFail")
    public String redeemCodeFail(HttpServletRequest request, ModelMap modelMap){
        return "/shop/redeemCodeFailReturn";
    }

    @RequestMapping(value = "/myGiftHistory")
    public String myGiftHistory(HttpServletRequest request, ModelMap modelMap){
        if (UserUtils.isAnonymous()){
            modelMap.addAttribute(Error_Msg,InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
            return "/500";
        }
        String email = UserUtils.getCurrentLoginUser().getEmail();

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        EnyanRedeemCode redeemCode = new EnyanRedeemCode();
        redeemCode.setUserEmail(email);
        Page page = new Page();
        page.setPageSize(12);
        redeemCode.setPage(page);

        OrderObj orderObj = new OrderObj("create_time",InterfaceContant.OrderBy.DESC);
        redeemCode.addOrder(orderObj);

        if (StringUtils.hasLength(total)) {
            page.setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            page.setCurrentPage(Integer.parseInt(currentPage));
        }

        page = enyanRedeemCodeService.queryRecords(redeemCode.getPage(),redeemCode);

        redeemCode.excuteFrontPageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",redeemCode.getPageLand());
        modelMap.addAttribute("pageDescription",this.getPageDescription(page));

        return "/shop/myGiftHistory";
    }

    @ResponseBody
    @RequestMapping(value = "/sendGiftEmail")
    public Map<String,Object> sendGiftEmail(@RequestBody GiftSendDTO giftSendDTO, HttpServletRequest request){
        log.debug("method addCart：{}",giftSendDTO);

        if (StringUtils.hasLength(giftSendDTO.getEmail()) == false ||StringUtils.hasLength(giftSendDTO.getGiftcode()) == false
                ||StringUtils.hasLength(giftSendDTO.getSendText()) == false){
            return ResultUtils.getFailedResultData(this.getMessage("error.gift.send",request));
        }
        if (UserUtils.isAnonymous()){
            return ResultUtils.getFailedResultData(InterfaceContant.ApiErrorConfig.LOGIN_ERROR_DESCRIPTION);
        }
        String email = UserUtils.getCurrentLoginUser().getEmail();
        EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
        enyanRedeemCode.setUserEmail(email);
        enyanRedeemCode.setCode(giftSendDTO.getGiftcode());

        List<EnyanRedeemCode> list =  enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);

        if (null == list || list.isEmpty()){
            return ResultUtils.getFailedResultData(this.getMessage("error.gift.send",request));
        }
        boolean success ;
        try{
            EnyanRedeemCode redeemCode = list.get(0);
            if (redeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
                return ResultUtils.getFailedResultData(this.getMessage("error.gift.send",request));
            }
            RedeemCodeNoteInfo redeemCodeNoteInfo = redeemCode.getRedeemCodeNoteInfo();
            if (StringUtils.hasLength(redeemCodeNoteInfo.getEmailToGift())){//已经发送email
                return ResultUtils.getFailedResultData(this.getMessage("error.gift.send",request));
            }
            redeemCodeNoteInfo.setEmailToGift(giftSendDTO.getEmail());

            EnyanRedeemCode newRedeemCode = new EnyanRedeemCode();
            newRedeemCode.setRedeemCodeId(redeemCode.getRedeemCodeId());
            newRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));

            enyanRedeemCodeService.updateRecord(newRedeemCode);
            this.sendMailOfRedeemCode(redeemCode,giftSendDTO,request);
            //this.sendMailTest(request);
//            EnyanOrder queryOrder = new EnyanOrder();
//            queryOrder.setOrderNum(redeemCode.getRedeemCodeNoteInfo().getSourceOrderId());
//            queryOrder.setIsPaid(Constant.BYTE_VALUE_1);
//            EnyanOrder order = enyanOrderService.selectByExampleWithBLOBs(queryOrder).get(0);
//
//            order.setOrderDetailInfo(JSON.parseObject(order.getOrderDetail(),OrderDetailInfo.class));
//            order.setOrderPayInfo(JSON.parseObject(order.getPayInfo(),OrderPayInfo.class));
//            this.sendMailOfOrder(order,request);
        }catch (Exception e){
            e.printStackTrace();
        }

        Map<String,Object> result = ResultUtils.getSuccessResultData("success");

        return result;
    }

    //http://localhost:8080/shop/sendGiftEmailTest?code=B-3d9e9824-ec7c-498e-a8e9-daa05a46ea38
    //https://ebookstore.endao.co/shop/sendGiftEmailTest?code=B-3d9e9824-ec7c-498e-a8e9-daa05a46ea38
    @RequestMapping(value = "/sendGiftEmailTest")
    public String sendGiftEmailTest(HttpServletRequest request, ModelMap modelMap){
        log.debug("method addCart：{}",request.getParameter("code"));
        GiftSendDTO giftSendDTO = new GiftSendDTO();

        String code = request.getParameter("code");
        String email = UserUtils.getCurrentLoginUser().getEmail();
        EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
        enyanRedeemCode.setUserEmail(email);
        enyanRedeemCode.setCode(code);

        giftSendDTO.setEmail(email);
        giftSendDTO.setGiftcode(code);
        giftSendDTO.setSendText("测试一下吧："+DateFormatUtils.format(new Date(),"yyyyMMdd hh:mm:ss"));

        List<EnyanRedeemCode> list =  enyanRedeemCodeService.findRecordsByRedeemCode(enyanRedeemCode);

        if (null == list || list.isEmpty()){
            modelMap.addAttribute("result",ResultUtils.getFailedResultData(this.getMessage("error.gift.send",request)+"11"));
            return "/showResult";
        }
        try{
            EnyanRedeemCode redeemCode = list.get(0);
            if (redeemCode.getStatus() == EBookConstant.RedeemStatus.USE){
                modelMap.addAttribute("result",ResultUtils.getFailedResultData(this.getMessage("error.gift.send",request)+"22"));
                return "/showResult";
            }
            RedeemCodeNoteInfo redeemCodeNoteInfo = redeemCode.getRedeemCodeNoteInfo();
            redeemCodeNoteInfo.setEmailToGift("<EMAIL>");

            EnyanRedeemCode newRedeemCode = new EnyanRedeemCode();
            newRedeemCode.setRedeemCodeId(redeemCode.getRedeemCodeId());
            newRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));

            enyanRedeemCodeService.updateRecord(newRedeemCode);

            this.sendMailOfRedeemCode(redeemCode,giftSendDTO,request);
            //this.sendMailTest(request);
        }catch (Exception e){
            e.printStackTrace();
        }

        Map<String,Object> result = ResultUtils.getSuccessResultData("success");
        modelMap.addAttribute("result",result);
        return "/showResult";
    }

    @ResponseBody
    @RequestMapping(value = {"/commentAdd"},method = RequestMethod.POST)
    public ExecuteResult<RestComment> commentAdd(@RequestBody RestComment restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);
        ExecuteResult<RestComment> result = new ExecuteResult<>();
        if (UserUtils.isAnonymous()){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return result;
        }
        IUser user = UserUtils.getCurrentLoginUser();
        restObj.setEmail(user.getEmail());
        restObj.setNickName(user.getNickName());

        AuthUser authUser = (AuthUser) user.getUserInfo();
        restObj.setSex(authUser.getSex());

        boolean canContinue = false;
        if (null == restObj.getBookId()){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return result;
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(restObj.getTitle())){
            if (restObj.getTitle().length() > 50){
                result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
                result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E2);
                return result;
            }
            canContinue = true;
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(restObj.getContent())){//评论与星级 二选一
            if (restObj.getContent().length() > 2000){
                result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
                result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E3);
                return result;
            }
            canContinue = true;
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(restObj.getStar()) && "0".equals(restObj.getStar()) == false){
            canContinue = true;
        }else {
            restObj.setStar("0");
        }
        if (canContinue == false){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E4);
            return result;
        }
        EnyanComment obj = new EnyanComment();
        obj.setContent(restObj.getContent());
        obj.setBookId(restObj.getBookId());
        obj.setEmail(restObj.getEmail());
        obj.setLikeCount(0);
        obj.setNickName(restObj.getNickName());
        obj.setSex(restObj.getSex());//0:默认；1：男；2：女
        if (null == restObj.getParentId()){
            restObj.setParentId(0L);//默认parentId=0
        }
        obj.setCanShow(1);
        obj.setParentId(restObj.getParentId());
        obj.setTitle(restObj.getTitle());
        obj.setIsDeleted(0);
        obj.setCreateAt(new Date());
        obj.setCommentCount(0);
        obj.setStar(restObj.getStar());
        enyanCommentService.addRecord(obj);
        if (restObj.getParentId() == 0 && "0".equals(restObj.getStar()) == false){
            enyanBookService.resetBookStar(restObj.getBookId());
        }
        return result;
    }

    @ResponseBody
    @RequestMapping(value = {"commentPage"},method = RequestMethod.POST)
    public PageResult<RestComment> commentPage(@RequestBody RestComment restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        PageResult<RestComment> pageResult = new PageResult<>();
        if (null == restObj.getPage() || null == restObj.getBookId()){
            pageResult.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            pageResult.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return pageResult;
        }
        if (UserUtils.isAnonymous() == false){//单独处理email
            IUser user = UserUtils.getCurrentLoginUser();
            restObj.setEmail(user.getEmail());
        }
        EnyanComment searchObj = new EnyanComment();
        Page<EnyanComment> page = new Page();
        page.setCurrentPage(restObj.getPage());
        page.setPageSize(pageResult.getPageSize());
        searchObj.setPage(page);

        searchObj.setBookId(restObj.getBookId());
        if (null == restObj.getParentId()){
            searchObj.setParentId(0L);
        }else {
            searchObj.setParentId(restObj.getParentId());
        }
        //searchObj.setIsDeleted(0);
        searchObj.setCanShow(1);//指展示可以展示的数据
        OrderObj orderObj = new OrderObj("create_at",InterfaceContant.OrderBy.DESC);
        searchObj.addOrder(orderObj);
        page = enyanCommentService.queryRecords(searchObj.getPage(),searchObj);

        for (EnyanComment obj : page.getRecords()){
            RestComment tmp = new RestComment();
            tmp.initFrom(obj,restObj.getEmail());
            pageResult.getResult().add(tmp);
        }
        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());
        return pageResult;
    }

    @ResponseBody
    @RequestMapping(value = {"/commentLike"},method = RequestMethod.POST)
    public ExecuteResult<RestComment> commentLike(@RequestBody RestComment restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<RestComment> result = new ExecuteResult<>();
        if (null == restObj.getDataId()){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return result;
        }

        enyanCommentService.updateCommentLikeCountById(restObj.getDataId());
        return result;
    }

    @ResponseBody
    @RequestMapping(value = {"commentDel"},method = RequestMethod.POST)
    public ExecuteResult<RestComment> commentDel(@RequestBody RestComment restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);
        if (UserUtils.isAnonymous() == false){//单独处理email
            IUser user = UserUtils.getCurrentLoginUser();
            restObj.setEmail(user.getEmail());
        }
        ExecuteResult<RestComment> result = new ExecuteResult<>();
        if (null == restObj.getDataId() || org.apache.commons.lang3.StringUtils.isBlank(restObj.getEmail())){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return result;
        }

        EnyanComment comment = enyanCommentService.queryRecordByPrimaryKey(restObj.getDataId()).getResult();
        if (null == comment){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E2);
            return result;
        }
        enyanCommentService.updateCommentToDeletedById(restObj.getDataId(), restObj.getEmail(), comment.getParentId());
        if (comment.getParentId() == 0){//父评论单独处理
            if ("0".equals(comment.getStar()) == false){//已经评星过
                enyanBookService.resetBookStar(comment.getBookId());
            }
        }
        return result;
    }

    @ResponseBody
    @RequestMapping(value = {"inBook"},method = RequestMethod.POST)
    public ExecuteResult<List<RestBook>> recommendInBook(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        ExecuteResult<List<RestBook>> result = new ExecuteResult<>();
        if (null == restObj.getBookId() || restObj.getBookId() < 1) {
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        List<RestBook> bookList = new ArrayList<>();
        List<EnyanBook> enyanBookRet = enyanBookService.findBookRecommendByBook(restObj.getBookId());
        if (null == enyanBookRet || enyanBookRet.size() == 0) {
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        for (EnyanBook book:enyanBookRet){
            RestBook restBook = new RestBook();
            restBook.initFrom(book);
            restBook.setShelfStatus(1);
            bookList.add(restBook);
        }
        result.setResult(bookList);
        return result;
    }

    @ResponseBody
    @RequestMapping(value = {"bookListBySet"},method = RequestMethod.POST)
    public PageResult<RestBook> bookListBySet(@RequestBody RestBook restObj, HttpServletRequest request){
        restObj.initHeaderValue(request);

        PageResult<RestBook> pageResult = new PageResult<>();
        if (null == restObj.getPage() || null == restObj.getSetId() || restObj.getSetId() <= 0){
            //pageResult.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }
        EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(restObj.getSetId()).getResult();
        if (null == bookSet ){
            pageResult.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }

        CurrencyType currencyType = CurrencyType.getPriceNameByCurrency(restObj.getCurrency());
        EnyanBook enyanBook = new EnyanBook();
        Page<EnyanBook> page = new Page();
        page.setCurrentPage(restObj.getPage());
        page.setPageSize(pageResult.getPageSize());
        enyanBook.setPage(page);
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);

        enyanBook.setSetId(restObj.getSetId());

        OrderObj orderObj = new OrderObj("recommended_order",InterfaceContant.OrderBy.DESC);
        enyanBook.addOrder(orderObj);
        page = enyanBookService.queryRecords(enyanBook.getPage(),enyanBook);
        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());

        if (null != bookSet.getIsDiscountValid() && bookSet.getIsDiscountValid() == 1 && null != bookSet.getDiscountValue()){//折扣是否有效
            BigDecimal discount = new BigDecimal(bookSet.getDiscountValue());//有折扣数据
            Byte isSingleValid = Constant.BYTE_VALUE_1;
            for (EnyanBook book : page.getRecords()){
                book.setDiscountSingleIsValid(isSingleValid);
                book.setPriceHKDDiscount(book.getPriceHkd().multiply(discount).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP));

                book.setDiscountId(null);
                book.setDiscountIsValid(Constant.BYTE_VALUE_0);
            }
        }

        for (EnyanBook book : page.getRecords()){
            book.resetByArea(restObj.getArea());
            RestBook tmp = new RestBook();
            tmp.initFrom(book,currencyType);
            tmp.setRecommendedCaption(book.getRecommendedCaption());

            if (null == tmp.getPrice()){//如果没有设置折扣信息，则原价为折扣价
                tmp.setPrice(tmp.getPriceDiscount());
            }

            pageResult.getResult().add(tmp);
        }

        return pageResult;
    }


    private NameAndValue getCategoryName(String id){
        //ValueAndName name = new ValueAndName();

        if (null == id){
            return Constant.CATEGORY_DEFAULT;
        }
        for (NameAndValue valueAndName:Constant.categoriesList){
            if (valueAndName.getValue().equals(id)){
                return valueAndName;
            }
        }
        return Constant.CATEGORY_DEFAULT;
    }

    private String getPageDescription(Page page){
        if (page.getTotalRecord() == 0){
            return "";
        }
        return page.getRecordStart()+ " - " + NumberUtils.min(page.getRecordEnd(),page.getTotalRecord());
    }
    /**
     *
     * 初始化购物车
     * @param modelMap
     * @Date: 2017/12/8
     */
    private CartInfo initCart(ModelMap modelMap, HttpServletRequest request){
        CartInfo cartInfo = CookieUtil.getCookieValue(InterfaceContant.CookieName.BUYER_CART, CartInfo.class, request);
        if (null == cartInfo || !cartInfo.isValidInfo()){
            cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
        }
        modelMap.addAttribute(InterfaceContant.CookieName.BUYER_CART,cartInfo);
        return cartInfo;
    }
    /**
     *
     * 初始化购物车概要
     * @param modelMap
     * @param request
     * @Date: 2018/3/12
     */
    private CartInfoGerenal initCartInfoGeneral(ModelMap modelMap, HttpServletRequest request){
        return this.initCartInfoGeneral(modelMap,request,false);
    }
    private CartInfoGerenal initCartInfoGeneral(ModelMap modelMap, HttpServletRequest request, boolean addCartDetail){
        /*
        CartInfoGerenal cartInfoGerenal = CookieUtil.getCookieValue(InterfaceContant.CookieName.BUYER_CART,CartInfoGerenal.class,request);
        if (null == cartInfoGerenal){
            cartInfoGerenal = new CartInfoGerenal();
        }
        modelMap.addAttribute(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal);
        */
        String area = this.getArea(request);

        CartInfoGerenal cartInfoGerenal = new CartInfoGerenal();
        /*
        UserInfo userInfo = authUserService.loadUserInfoByEmail(UserUtils.getCurrentLoginUser().getEmail());
        if (null != userInfo && null != userInfo.getCustomUserDetail() && null != userInfo.getCustomUserDetail().getCartInfoGerenal()){
            cartInfoGerenal = userInfo.getCustomUserDetail().getCartInfoGerenal();
        }
        if (null == cartInfoGerenal){
            cartInfoGerenal = new CartInfoGerenal();
        }*/

        modelMap.addAttribute(InterfaceContant.CookieName.BUYER_CART,cartInfoGerenal);
        String email = UserUtils.getCurrentLoginUser().getEmail();
        if (addCartDetail){
            //List<EnyanBook> list = enyanBookService.findBookByIds(cartInfoGerenal.getProductIdList());
              List<EnyanBook> list = enyanCartService.searchAllByEmail(email);
//            EnyanOrderDetail orderDetail = new EnyanOrderDetail();
//            orderDetail.setUserEmail(UserUtils.getCurrentLoginUser().getEmail());
//            List<EnyanOrderDetail> orderDetailList = enyanOrderDetailService.findAllOrderDetailList(orderDetail);

            List<EnyanBookBuy> bookBuyList = enyanBookBuyService.findBookIDAndNameByEmail(email);

            HashSet<Long> booksHaveBuy = new HashSet<>();
            for (EnyanBookBuy bookBuy:bookBuyList){
                booksHaveBuy.add(bookBuy.getBookId());
            }

            CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
//            Map<Long,EnyanBook> map = new HashMap<>();
//            for (EnyanBook enyanBook:list){
//                map.put(enyanBook.getBookId(),enyanBook);
//            }
            List<ProductInfo> notBuyList = new ArrayList<>();
            List<ProductInfo> hasBuyList = new ArrayList<>();
            for (EnyanBook enyanBook:list){
                enyanBook.resetByArea(area);
                ProductInfo productInfo = new ProductInfo(enyanBook);

                if(booksHaveBuy.contains(enyanBook.getBookId())){
                    productInfo.setHasBought(true);
                    hasBuyList.add(productInfo);
                }else{
                    productInfo.setHasBought(false);
                    notBuyList.add(productInfo);
                }

                //cartInfo.addProduct(productInfo,product.getQuantity());
            }

            for (ProductInfo productInfo:notBuyList){//没有买的商品在上边
                cartInfo.addProduct(productInfo,1);
            }

            for (ProductInfo productInfo:hasBuyList){//已经购买的商品在下边
                cartInfo.addProduct(productInfo,1);
            }

            CartDTO cartDTO = new CartDTO(cartInfo);
            modelMap.addAttribute(EBookConstant.Cart.PRICE_MAP, JSON.toJSONString(cartDTO));
            modelMap.addAttribute(EBookConstant.Cart.DETAIL,cartInfo);
            cartInfoGerenal.setQuantity(list.size());
        }else {
            Long count = enyanCartService.countOfCartByEmail(email);
            cartInfoGerenal.setQuantity(count.intValue());
            //CartInfo cartInfo = new CartInfo(Constant.SYS_UPDATE,localeResolver.resolveLocale(request).toString());
            //cartInfo.setQuantity(new BigDecimal(count));
            //modelMap.addAttribute(EBookContant.Cart.DETAIL,cartInfo);
        }

        return cartInfoGerenal;
    }
    /**
     *
     * 同步购物车
     * @Date: 2018/7/5
     */
    private long syncCart(List<Long> bookIdList,boolean isAdd,HttpServletRequest request){
        String email = UserUtils.getCurrentLoginUser().getEmail();
        /*
        UserInfo userInfo = authUserService.loadUserInfoByEmail(email);
        if (null == userInfo){
            userInfo = new UserInfo();
            userInfo.setUsername(email);
            CustomUserDetail customUserDetail = new CustomUserDetail();
            customUserDetail.setCartInfoGerenal(cartInfoGerenal);
            customUserDetail.setDeviceLimitList(new ArrayList<>());
            userInfo.setCustomUserDetail(customUserDetail);
            authUserService.addUserInfo(userInfo);
        }else {
            userInfo.getCustomUserDetail().setCartInfoGerenal(cartInfoGerenal);
            authUserService.updateUserInfo(userInfo);
        }*/
        if (isAdd){
            Date date = new Date();
            for (Long bookId:bookIdList){
                EnyanCart enyanCart = new EnyanCart();
                enyanCart.setBookId(bookId);
                enyanCart.setQuantity(1);
                enyanCart.setUserEmail(email);
                enyanCart.setAddAt(date);
                enyanCartService.addRecord(enyanCart);
            }
        }else {
            enyanCartService.deleteCarts(email, bookIdList);
        }
        long count = enyanCartService.countOfCartByEmail(email);
        Map<String,String> data = new HashMap<>();
        data.put("quantity",String.valueOf(count));
        //data.put("amountCny",String.valueOf(cartInfo.getAmountCny()));
        //data.put("amountUsd",String.valueOf(cartInfo.getAmountUsd()));
        request.getSession().setAttribute(EBookConstant.Cart.DATA,data);
        return count;
    }

    public void paySuccess(EnyanOrder order, OrderPayInfo orderPayInfo, HttpServletRequest request) {
        log.debug("paySuccess:");
        enyanOrderService.updateOrderToPaid(order);
        order.setOrderPayInfo(orderPayInfo);
        if (order.getOrderDetailInfo()==null && StringUtils.hasLength(order.getOrderDetail())){
            String orderDetailInfo = order.getOrderDetail();

            OrderDetailInfo detailInfo = JSON.parseObject(orderDetailInfo, OrderDetailInfo.class);
            order.setOrderDetailInfo(detailInfo);
        }

        log.debug("paySuccess updateRecord");
        /*
        EnyanOrder newOrder = new EnyanOrder();
        newOrder.setOrderId(order.getOrderId());
        newOrder.setOrderPayInfo(orderPayInfo);
        newOrder.setIsPaid(Constant.BYTE_VALUE_1);

        enyanOrderService.updateRecord(newOrder);
        //this.createAcsmInfo(order);

        if (order.getOrderType() == EBookContant.OrderType.ORDER_EBOOK_SET_BUY
                || order.getOrderType() == EBookContant.OrderType.ORDER_EBOOK_SINGLE_BUY){
            //电子书订单，直接生成订单明细
            this.enyanOrderDetailService.splitOrder(order);
            //"买书后可以直接设置灵修材料已购买"
            enyanPlanService.updatePlanHasBuy(order.getUserEmail(),order.getOrderDetailInfo());
        }else if (order.getOrderType() == EBookContant.OrderType.ORDER_REDEEM_BUY){
            this.enyanRedeemCodeService.splitOrder(order);
        }*/
        enyanOrderService.saveOrderHasPay(order);

        this.sendMailOfOrder(order,request);
        this.sendLogOfOrder(order);
    }

    /**
     * <p>先租后买已经支付成功</p>
     * <p>增加支付明细并延续订单日期</p>
     * @param rent
     * @param orderPayInfo
     * @param lang
     * @param toRentMonths
     * @return void
     * @since : 2022/11/9
     **/
    public void payRentSuccess(EnyanRent rent,OrderPayInfo orderPayInfo, String lang, Integer toRentMonths) {
        log.debug("paySuccess:");
        ShopController.payRentSuccess(rent, orderPayInfo, toRentMonths,enyanRentService,logService,sendEmailService);
    }

    public static void payRentSuccess(EnyanRent rent,OrderPayInfo orderPayInfo, Integer toRentMonths,
                                      EnyanRentService enyanRentService, LogService logService, SendEmailService sendEmailService) {
        Date newExpireDate = DateUtils.addMonths(rent.getBeginAt(), toRentMonths);
        rent.setNewExpiredAt(newExpireDate);//暂时设置新的过期时间

        EnyanRentDetail detail = enyanRentService.saveOrderHasPay(rent, orderPayInfo, toRentMonths, newExpireDate);
        if (null == detail){
            return;
        }
        boolean isFirstRent = false;//是否首次订阅
        if (StringUtils.hasLength(rent.getUserEmail()) == true){
            if (rent.getTotalMonths() == detail.getRentMonths()){//第一次订阅
                logService.sendRentOrderLog(rent);
                isFirstRent = true;
            }
            logService.sendRentOrderDetailLog(detail);
        }
        //将detail里的这两个数据填充到rent里
        rent.setTotalMonths(toRentMonths);
        rent.setTotalFee(new BigDecimal(orderPayInfo.getCharge().getAmount()));
        //rent.setExpiredAt(newExpireDate);//更新最新结束时间
        if (isFirstRent == true){//首次订阅
            if (rent.getIsAuto() == 0){//手动订阅
                sendEmailService.sendMailOfRent(rent,EBookConstant.MailCode.RENT_0401);
            }else{//自动续费
                sendEmailService.sendMailOfRent(rent,EBookConstant.MailCode.RENT_0402);
            }
        } else{//续费
            //newExpireDate = DateUtils.addMonths(rent.getExpiredAt(), toRentMonths);
            //rent.setNewExpiredAt(newExpireDate);//设置新的过期时间
            //rent.setExpiredAt(newExpireDate);//更新最新结束时间
            //时间的更新，已经在service里处理
            if (rent.getIsAuto() == 0){//手动订阅
                sendEmailService.sendMailOfRent(rent,EBookConstant.MailCode.RENT_0501);
            }else{//自动续费
                sendEmailService.sendMailOfRent(rent,EBookConstant.MailCode.RENT_0502);
            }
        }

        if (StringUtils.hasLength(rent.getBaseLicense()) == false){//还没有生成DRM，则生成DRM信息
            enyanRentService.addDRMInfoToRent(rent, newExpireDate);
        }
        enyanRentService.updateDRMLicenseToNewExpiredTime(rent, rent.getExpiredAt());
    }

    public void testPaySuccess(){
        log.debug("testPaySuccess");
    }

    /**
     * <p></p>
     * @param order
     * @param lang
     * @return void
     * @since : 2021/5/26
     **/
    public static void sendMailOfOrder(EnyanOrder order, String lang, MessageSource messageSource, EmailService emailService, AuthUserService authUserService){
        log.debug("sendMailOfOrder:");
        String email = order.getUserEmail();
        if (StringUtils.hasLength(email) == false){
            return;
        }

        String basePath = WebUtil.getBasePath();

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
//        mail.setSubject(this.getMessage("email.order.title",request));
        mail.setSubject(getMessage("email.order.title", lang, messageSource));
        mail.setTo(email);//
        //mail.setTo("<EMAIL>");
        mail.setFtl("email-order.ftl");
        if (InterfaceContant.LocaleLang.ENG.equals(lang)){
            mail.setFtl("email-order-en.ftl");
        }
        String emailToName = authUserService.getUserNameByEmail(email);

        Map<String,Object> model = new HashMap<>();
        model.put("emailToName",emailToName);
//        model.put("signature",this.getMessage("shop.copyright",request));
        model.put("signature",getMessage("shop.copyright", lang, messageSource));
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));

        model.put("orderNum",order.getOrderNum());
//        model.put("payType",this.getPayTypeLocalized(order,request));
        model.put("payType",getPayTypeLocalized(order, lang, messageSource));
        model.put("orderTime",DateFormatUtils.format(order.getPurchasedAt(),"yyyy-MM-dd"));
        model.put("home",basePath);
        model.put("order",order);


        List<ProductInfo> productInfos = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:order.getOrderDetailInfo().getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                productInfos.add(productInfo);
            }
        }
        for (ProductInfo productInfo:productInfos){
            if (productInfo.isDiscountAnyIsValid()){
                productInfo.setPriceOldText("<del>$"+productInfo.getPriceHkd()+"</del>");
                productInfo.setPriceText("HK$"+productInfo.getPriceHKDDiscount());
                productInfo.setPriceTotalText("HK$"+productInfo.getPriceHKDDiscount().multiply(productInfo.getQuantity()));
            }else {
                productInfo.setPriceText("HK$"+productInfo.getPriceHkd());
                productInfo.setPriceOldText("");
                productInfo.setPriceTotalText("HK$"+productInfo.getPriceHkd().multiply(productInfo.getQuantity()));
            }
        }

        String total = "HK$"+order.getOrderDetailInfo().getAmountHkd();

        model.put("products",productInfos);
        model.put("total",total);
        mail.setModel(model);

        emailService.sendSimpleMessage(mail);
    }

    /**
     * <p>发送先租后买的mail</p>
     * @param order
     * @param lang
     * @return void
     * @since : 2022/11/9
     **/
    public void sendMailOfRentOrder(EnyanRentDetail order, String lang){
        log.debug("sendMailOfRentOrder:");
        if (StringUtils.hasLength(order.getUserEmail()) == false){
            return;
        }

        String basePath = WebUtil.getBasePath();

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
//        mail.setSubject(this.getMessage("email.order.title",request));
        mail.setSubject(getMessage("email.order.title", lang, messageSource));
        mail.setTo(order.getUserEmail());//
        //mail.setTo("<EMAIL>");
        mail.setFtl("email-order.ftl");
        if (InterfaceContant.LocaleLang.ENG.equals(lang)){
            mail.setFtl("email-order-en.ftl");
        }
        Map<String,Object> model = new HashMap<>();
        model.put("emailToName",order.getUserEmail());
//        model.put("signature",this.getMessage("shop.copyright",request));
        model.put("signature",getMessage("shop.copyright", lang, messageSource));
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));

        model.put("orderNum",order.getOrderNum());
//        model.put("payType",this.getPayTypeLocalized(order,request));
        model.put("payType",getPayTypeLocalized(order.getPayType(), lang, messageSource));
        model.put("orderTime",DateFormatUtils.format(order.getPurchasedAt(),"yyyy-MM-dd"));
        model.put("home",basePath);
        model.put("order",order);


        List<ProductInfo> productInfos = new ArrayList<>();


        String total = "HK$"+order.getIncomeTotal();

        model.put("products",productInfos);
        model.put("total",total);
        mail.setModel(model);

        ///TODO:后续需要启用
        //emailService.sendSimpleMessage(mail);
    }

    /**
     *
     * 发送邮件
     * @param order
     * @Date: 2018/2/22
     */
    public void sendMailOfOrder(EnyanOrder order, HttpServletRequest request) {
        //https://freemarker.apache.org/docs/ref_directive_if.html
        sendMailOfOrder(order, localeResolver.resolveLocale(request).toString(), this.getMessageSource(), emailService, authUserService);
        /*
        log.debug("sendMailOfOrder:");
        if (StringUtils.hasLength(order.getUserEmail()) == false){
            return;
        }

        String basePath = WebUtil.getBasePath();

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
        mail.setSubject(this.getMessage("email.order.title",request));
        mail.setTo(order.getUserEmail());//
        //mail.setTo("<EMAIL>");
        mail.setFtl("email-order.ftl");
        if (EBookContant.LocaleLang.ENG.equals(localeResolver.resolveLocale(request).toString())){
            mail.setFtl("email-order-en.ftl");
        }
        Map<String,Object> model = new HashMap<>();
        model.put("emailToName",order.getUserName());
        model.put("signature",this.getMessage("shop.copyright",request));
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));

        model.put("orderNum",order.getOrderNum());
        model.put("payType",this.getPayTypeLocalized(order,request));
        model.put("orderTime",DateFormatUtils.format(order.getPurchasedAt(),"yyyy-MM-dd"));
        model.put("home",basePath);
        model.put("order",order);


        List<ProductInfo> productInfos = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:order.getOrderDetailInfo().getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                productInfos.add(productInfo);
            }
        }
        for (ProductInfo productInfo:productInfos){
            if (productInfo.isDiscountAnyIsValid()){
                productInfo.setPriceOldText("<del>$"+productInfo.getPriceHkd()+"</del>");
                productInfo.setPriceText("HK$"+productInfo.getPriceHKDDiscount());
                productInfo.setPriceTotalText("HK$"+productInfo.getPriceHKDDiscount().multiply(productInfo.getQuantity()));
            }else {
                productInfo.setPriceText("HK$"+productInfo.getPriceHkd());
                productInfo.setPriceOldText("");
                productInfo.setPriceTotalText("HK$"+productInfo.getPriceHkd().multiply(productInfo.getQuantity()));
            }
        }

        String total = "HK$"+order.getOrderDetailInfo().getAmountHkd();

        model.put("products",productInfos);
        model.put("total",total);
        mail.setModel(model);

        emailService.sendSimpleMessage(mail);*/
    }
    /**
     * <p>获取支付方式的国际化</p>
     * @param order
     * @param request
     * @return: java.lang.String
     * @since : 2021/2/18
     */
    public String getPayTypeLocalized(EnyanOrder order, HttpServletRequest request){
        /*
        switch (order.getOrderPayInfo().getCharge().getPayType()){
            case EBookContant.PayType.ALI_PAY:
                return this.getMessage("alipay.label",request);
            case EBookContant.PayType.STRIPE_PAY:
            case EBookContant.PayType.STRIPE_PAY_OUTSIDE_HK:
            case EBookContant.PayType.STRIPE_PAY_IN_HK:
                return this.getMessage("credit.label",request);
            case EBookContant.PayType.FREE_PAY:
                return this.getMessage("label.free",request);
            case EBookContant.PayType.REDEEM_CODE_PAY:
                return this.getMessage("label.redeemCode",request);
        }
        return "";*/
        return getPayTypeLocalized(order, localeResolver.resolveLocale(request).toString(), this.getMessageSource());
    }

    /**
     * <p>获取支付方式的国际化</p>
     * @param order
     * @param lang
     * @param messageSource
     * @return java.lang.String
     * @since : 2021/5/26
     **/
    public static String getPayTypeLocalized(EnyanOrder order, String lang, MessageSource messageSource){
        return getPayTypeLocalized(order.getOrderPayInfo().getCharge().getPayType(), lang, messageSource);
    }

    /**
     * <p>获取支付方式的国际化</p>
     * @param payType
     * @param lang
     * @param messageSource
     * @return java.lang.String
     * @since : 2022/11/9
     **/
    public static String getPayTypeLocalized(int payType, String lang, MessageSource messageSource){
        return BookUtil.getPayTypeLocalized(payType, lang, messageSource);
    }

    public static void sendMailOfRedeemCode(EnyanRedeemCode redeemCode, String lang, GiftSendDTO giftSendDTO, MessageSource messageSource, EmailService emailService, AuthUserService authUserService){
        String toEmail = redeemCode.getRedeemCodeNoteInfo().getEmailToGift();
        if (StringUtils.hasLength(toEmail) == false
                    || null == giftSendDTO
                    || StringUtils.hasLength(giftSendDTO.getGiftcode()) == false){
            return;
        }
        //logger.error("sendMailOfRedeemCode:"+toEmail);
        String basePath = WebUtil.getBasePath();

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
        mail.setSubject(getMessage("email.gift.title",lang, messageSource));
        //mail.setSubject("恩道电子书-simple email");
        mail.setTo(toEmail);//
        //mail.setTo("<EMAIL>");
        //mail.setFtl("email-redeem-sc.ftl");
        mail.setFtl("email-redeem-sc.ftl");
        if (InterfaceContant.LocaleLang.ENG.equals(lang)){
            mail.setFtl("email-redeem-en.ftl");
        } else if (InterfaceContant.LocaleLang.TC.equals(lang)){
            mail.setFtl("email-redeem-tc.ftl");
        }
        Map<String,Object> model = new HashMap<>();
        model.put("emailToName",authUserService.getUserNameByEmail(giftSendDTO.getEmail()));
        model.put("emailFromName",authUserService.getUserNameByEmail(redeemCode.getUserEmail()));
        model.put("signature",getMessage("shop.copyright", lang, messageSource));
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));

        model.put("bookName",redeemCode.getRedeemCodeNoteInfo().getBookNameDescription());
        model.put("sendText",giftSendDTO.getSendText());
        model.put("giftcode",giftSendDTO.getGiftcode());
        model.put("home",basePath);


        mail.setModel(model);

        Map<String, Object> toReturn = new HashMap<>();
        toReturn.put("statusCode",InterfaceContant.CallbackStatus.EMAIL_REDEEM);
        toReturn.put("id",redeemCode.getRedeemCodeId());

        emailService.sendSimpleMessage(mail);
    }
    /**
     *
     * @param redeemCode
     * @param request
     * @Date: 2020-07-02
     */
    protected void sendMailOfRedeemCode(EnyanRedeemCode redeemCode,GiftSendDTO giftSendDTO, HttpServletRequest request) {
        //logger.debug("sendMailOfRedeemCode----"+redeemCode.getRedeemCodeNoteInfo());
        sendMailOfRedeemCode(redeemCode, localeResolver.resolveLocale(request).toString(),giftSendDTO, this.getMessageSource(), emailService, authUserService);
        /*
        String toEmail = redeemCode.getRedeemCodeNoteInfo().getEmailToGift();
        if (StringUtils.hasLength(toEmail) == false){
            return;
        }
        //logger.error("sendMailOfRedeemCode:"+toEmail);
        String basePath = WebUtil.getBasePath();

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
        mail.setSubject(this.getMessage("email.gift.title",request));
        //mail.setSubject("恩道电子书-simple email");
        mail.setTo(toEmail);//
        //mail.setTo("<EMAIL>");
        //mail.setFtl("email-redeem-sc.ftl");
        mail.setFtl("email-redeem-sc.ftl");
        if ("en_US".equals(localeResolver.resolveLocale(request).toString())){
            mail.setFtl("email-redeem-en.ftl");
        }
        Map<String,Object> model = new HashMap<>();
        model.put("emailToName",giftSendDTO.getEmail());
        model.put("emailFromName",redeemCode.getUserEmail());
        model.put("signature",this.getMessage("shop.copyright",request));
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));

        model.put("bookName",redeemCode.getRedeemCodeNoteInfo().getBookNameDescription());
        model.put("sendText",giftSendDTO.getSendText());
        model.put("giftcode",giftSendDTO.getGiftcode());
        model.put("home",basePath);


        mail.setModel(model);

        Map<String, Object> toReturn = new HashMap<>();
        toReturn.put("statusCode",InterfaceContant.CallbackStatus.EMAIL_REDEEM);
        toReturn.put("id",redeemCode.getRedeemCodeId());

        emailService.sendSimpleMessage(mail);
        */

        /* 方法一、
        emailService.sendSimpleMessageCallback(mail,this,toReturn);
        */
        /*方法二、异步的方式处理email
        ListenableFuture listenableFuture = emailService.sendSimpleMessageFuture(mail,toReturn);
        SuccessCallback<Map<String,Object>> successCallback = new SuccessCallback<Map<String, Object>>() {
            @Override
            public void onSuccess(Map<String, Object> result) {
                System.out.println("异步回调成功了, return : " + result);
            }
        };
        FailureCallback failureCallback = new FailureCallback() {
            @Override
            public void onFailure(Throwable throwable) {
                System.out.println("异步回调失败了, exception message : " + throwable.getMessage());
            }
        };

        listenableFuture.addCallback(successCallback, failureCallback);*/
    }

    protected void sendMailTest(HttpServletRequest request) {

        Mail mail = new Mail();
        mail.setFrom(Constant.EMAIL_FROM);//<EMAIL>
        mail.setSubject("恩道电子书-simple email");
        mail.setTo("<EMAIL>");//
        //mail.setTo("<EMAIL>");
        mail.setFtl("email-redeem-sc.ftl");
        if ("en_US".equals(request.getLocale().toString())){
            mail.setFtl("email-redeem-en.ftl");
        }
        Map<String,Object> model = new HashMap<>();
        model.put("emailToName","<EMAIL>");
        model.put("signature","恩道电子书");
        model.put("location", DateFormatUtils.format(new Date(),"yyyy-MM-dd"));


        mail.setModel(model);

        emailService.sendSimpleMessage(mail);
    }

    protected void sendLogOfOrder(EnyanOrder order) {
        log.debug("sendLogOfOrder:");
        if (StringUtils.hasLength(order.getUserEmail()) == false){
            return;
        }
        logService.sendOrderLog(order);
    }

    @Override
    public void emailCallback(Map<String, Object> toReturn, Map<String, Object> result) {
        super.emailCallback(toReturn, result);
    }

    protected String getAlipayGoodsBody(EnyanOrder order){
        List<AlipayGoodsBody> list = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:order.getOrderDetailInfo().getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                AlipayGoodsBody goodsBody = new AlipayGoodsBody();
                goodsBody.setGoods_name(productInfo.getBookEsin());
                goodsBody.setQuantity("1");
                list.add(goodsBody);
            }
        }
        return JSON.toJSONString(list);
    }
    public static String getAlipayGoodsTradeInformation(EnyanOrder order){
        AlipayGoodsTradeInformation goodsTradeInformation = new AlipayGoodsTradeInformation(order);
        return JSON.toJSONString(goodsTradeInformation);
    }

    /**
     * <p>获取用户的区域信息</p>
     * @param request
     * @return java.lang.String
     * @since : 2021/11/23
     **/
    private String getArea(HttpServletRequest request){
        Object areaObject = request.getSession().getAttribute(Constant.ACCESS_AREA);
        String area;
        if (null == areaObject){
            String ip = geoIPLocationService.getClientIpAddress(request);
            //log.error("ip:{}",ip);
            area = geoIPLocationService.getLocationAreaByIP(ip);
            request.getSession().setAttribute(Constant.ACCESS_AREA,area);
        }else{
            area = (String)areaObject;
        }

        return area;
    }
}
class AlipayGoodsBody implements Serializable {
    private static final long serialVersionUID = -3487158605536857237L;
    private String goods_name;
    private String quantity;

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public static  void main(String[] args){
        List<AlipayGoodsBody> list = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            AlipayGoodsBody goodsBody = new AlipayGoodsBody();
            goodsBody.setGoods_name("name"+i);
            goodsBody.setQuantity("1");
            list.add(goodsBody);
        }
        String json = JSON.toJSONString(list);
        System.out.println(json);
    }
}
class AlipayGoodsTradeInformation implements Serializable{
    private static final long serialVersionUID = 7747123899264962089L;
    private String business_type = "4";
    private String goods_info;
    private String total_quantity = "1";

    public AlipayGoodsTradeInformation() {
    }

    public AlipayGoodsTradeInformation(EnyanOrder order) {
        this.initGoods(order);
    }

    private void initGoods(EnyanOrder order){

        StringBuffer sb = new StringBuffer();
        List<ProductInfo> productInfoList = new ArrayList<>();
        for (CartDiscountInfo cartDiscountInfo:order.getOrderDetailInfo().getCartDiscountInfoList()){
            for (ProductInfo productInfo:cartDiscountInfo.getProductInfoList()){
                productInfoList.add(productInfo);
            }
        }
        for (int i = 0; i < productInfoList.size(); i++) {
            ProductInfo productInfo = productInfoList.get(i);
            if (i != 0){
                sb.append("|");
            }
            String goodsName = productInfo.getName();
            sb.append(goodsName);
            sb.append("-电子书");
            sb.append("^1");
            if (i>5){
                break;
            }
        }
        String goodsInfo = sb.toString().replaceAll("圣经","").replaceAll("福音","")
                .replaceAll("聖經","");
        this.setGoods_info(goodsInfo);
        this.setTotal_quantity(String.valueOf(productInfoList.size()));
    }

    public String getBusiness_type() {
        return business_type;
    }

    public void setBusiness_type(String business_type) {
        this.business_type = business_type;
    }

    public String getGoods_info() {
        return goods_info.replaceAll("圣经","").replaceAll("福音","")
                .replaceAll("聖經","");
    }

    public void setGoods_info(String goods_info) {
        this.goods_info = goods_info;
    }

    public String getTotal_quantity() {
        return total_quantity;
    }

    public void setTotal_quantity(String total_quantity) {
        this.total_quantity = total_quantity;
    }

    public static  void main(String[] args){
        List<AlipayGoodsTradeInformation> list = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            AlipayGoodsTradeInformation goodsTradeInformation = new AlipayGoodsTradeInformation();
            goodsTradeInformation.setGoods_info("圣经这聖經场戏福音"+i);
            list.add(goodsTradeInformation);
        }
        String json = JSON.toJSONString(list);
        System.out.println(json);
    }
}
//购物车
class CartDTO implements Serializable{
    private static final long serialVersionUID = -2586437712362613213L;
    @JSONField(name = "lineList")
    private List<DiscountLineDTO> lineList = new ArrayList<>();

    public CartDTO(CartInfo cartInfo) {
        List<CartDiscountInfo> cartDiscountInfoList = cartInfo.getCartDiscountInfoList();
        for (CartDiscountInfo discountInfo:cartDiscountInfoList){
            DiscountLineDTO discountLineDTO = new DiscountLineDTO(discountInfo);
            lineList.add(discountLineDTO);
        }
    }

    public List<DiscountLineDTO> getLineList() {
        return lineList;
    }

    public void setLineList(List<DiscountLineDTO> lineList) {
        this.lineList = lineList;
    }
}
//折扣列表
class DiscountLineDTO implements Serializable{
    private static final long serialVersionUID = 202671292001279183L;
    @JSONField(name = "productList")
    private List<ProductDTO> productDTOList = new ArrayList<>();
    /**
     * 0:累计折扣 n件n折;1:满300减30
     * */
    private Byte discountType;//
    /**
     * 累计件数
     * */
    @JSONField(name = "package")
    private Integer cumulatePackage;//

    @JSONField(name = "discount")
    private Integer cumulateDiscount;

    @JSONField(name = "packageMuti")
    private Integer cumulatePackageMuti;//

    @JSONField(name = "discountMuti")
    private Integer cumulateDiscountMuti;

    private Long discountId;

    private boolean valid = false; // 是否有折扣

    public DiscountLineDTO(CartDiscountInfo discountInfo) {
        List<ProductInfo> productInfoList = discountInfo.getProductInfoList();
        for (ProductInfo productInfo:productInfoList){
            ProductDTO productDTO = new ProductDTO(productInfo);
            productDTOList.add(productDTO);
        }
        this.discountId = discountInfo.getDiscountId();
        this.cumulateDiscount = discountInfo.getCumulateDiscount();
        this.cumulatePackage = discountInfo.getCumulatePackage();
        this.cumulateDiscountMuti = discountInfo.getCumulateDiscountMuti();
        this.cumulatePackageMuti = discountInfo.getCumulatePackageMuti();
        this.valid = discountInfo.isValid();
    }

    public List<ProductDTO> getProductDTOList() {
        return productDTOList;
    }

    public void setProductDTOList(List<ProductDTO> productDTOList) {
        this.productDTOList = productDTOList;
    }

    public Byte getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Byte discountType) {
        this.discountType = discountType;
    }

    public Integer getCumulatePackage() {
        return cumulatePackage;
    }

    public void setCumulatePackage(Integer cumulatePackage) {
        this.cumulatePackage = cumulatePackage;
    }

    public Integer getCumulateDiscount() {
        return cumulateDiscount;
    }

    public void setCumulateDiscount(Integer cumulateDiscount) {
        this.cumulateDiscount = cumulateDiscount;
    }

    public Integer getCumulatePackageMuti() {
        return cumulatePackageMuti;
    }

    public void setCumulatePackageMuti(Integer cumulatePackageMuti) {
        this.cumulatePackageMuti = cumulatePackageMuti;
    }

    public Integer getCumulateDiscountMuti() {
        return cumulateDiscountMuti;
    }

    public void setCumulateDiscountMuti(Integer cumulateDiscountMuti) {
        this.cumulateDiscountMuti = cumulateDiscountMuti;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }
}
class ProductDTO implements Serializable{
    private static final long serialVersionUID = 4199462831918588603L;
    private Long code;
    private BigDecimal price;
    private BigDecimal priceDiscount;
    public ProductDTO(ProductInfo productInfo) {
        this.code = productInfo.getCode();
        this.price = productInfo.getPriceHkd();
        this.priceDiscount = productInfo.getPriceHKDDiscount();
    }

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPriceDiscount() {
        return priceDiscount;
    }

    public void setPriceDiscount(BigDecimal priceDiscount) {
        this.priceDiscount = priceDiscount;
    }
}