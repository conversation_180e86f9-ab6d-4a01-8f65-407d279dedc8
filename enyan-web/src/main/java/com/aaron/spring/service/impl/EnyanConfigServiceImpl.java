package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.exception.BusinessException;
import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.http.HttpResult;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.AdModel;
import com.aaron.spring.api.NoticeMsg;
import com.aaron.spring.api.SpiritImgModel;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanConfigMapper;
import com.aaron.spring.mapper.custom.EnyanConfigCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanConfigService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

/**
 *
 *  BusinessException: 101X
 * @Author: Aaron Hao
 * @Date: Created in  2017/12/6
 * @Modified By:
 */
@Service
public class EnyanConfigServiceImpl implements EnyanConfigService{

    public static final String CNY_RATE = "CNY_RATE";
    public static final String SYS_UPDATE = "SYS_UPDATE";
    public static final String SECRET_CODE = "SECRET_CODE";
    public static final String SPIRIT_UPDATE = "SPIRIT_UPDATE";
    public static final String LATEST_NOTICE_UPDATE = "LATEST_NOTICE_UPDATE";

    public static final Long BASE_CURRENCY_CONF_ID = 200L;

    public static final String FIXER_IO_API_KEY = "********************************";
    public static final String FIXER_IO_API_SYMBOLS = "&symbols=USD,AUD,CAD,PLN,MXN,HKD,CNY,JPY,TWD,MYR,IDR,SGD,EUR,GBP,KRW,JPY,THB,NZD,BRL";
    @Resource
    private EnyanConfigMapper enyanConfigMapper;

    @Resource
    private EnyanConfigCustomMapper enyanConfigCustomMapper;

    @Override
    public Page queryRecords(Page<EnyanConfig> page, EnyanConfig record) {
        if (null == record){
            record = new EnyanConfig();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanConfigExample example = new EnyanConfigExample();
            EnyanConfigExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            if (StringUtils.isNotBlank(record.getConfigDescription())){
                criteria.andConfigDescriptionLike("%"+record.getConfigDescription()+"%");
            }

            if (null != record.getIsShow()){
                criteria.andIsShowEqualTo(record.getIsShow());
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanConfigMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanConfig> list = enyanConfigMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setTotalRecord(count);
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanConfig> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanConfig> addRecord(EnyanConfig record) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanConfig> updateRecord(EnyanConfig record) {
        ExecuteResult<EnyanConfig> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanConfigMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                this.initConfigs();
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public String checkSaveRecord(EnyanConfig record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanConfig record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (record.getConfigId()==0){
            sb.append("主键信息不可以为空");
            return sb.toString();
        }
        return null;
    }

    @Override
    public void initConfigs(){
        List<EnyanConfig> list = this.findConfigs(null);
        for (EnyanConfig enyanConfig:list){
            this.setConfig(enyanConfig);
        }
        this.initAd(list);
        this.initCoupon(list);
        this.initSpirit(list);
        this.initNoticeMsg();
    }

    @Override
    public List<EnyanConfig> findConfigs(EnyanConfig record) {
        List<EnyanConfig> list = null;
        if (null == record){
            record = new EnyanConfig();
        }
        try {
            EnyanConfigExample example = new EnyanConfigExample();
            EnyanConfigExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(record.getConfigDescription())){
                criteria.andConfigDescriptionLike("%"+record.getConfigDescription()+"%");
            }
            if (null != record.getIsShow()){
                criteria.andIsShowEqualTo(record.getIsShow());
            }

            list = enyanConfigMapper.selectByExample(example);


        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    private void setConfig(EnyanConfig enyanConfig){
        if (StringUtils.isBlank(enyanConfig.getConfigName())|| StringUtils.isBlank(enyanConfig.getConfigValue())){
            return;
        }
        switch (enyanConfig.getConfigName()){
            case CNY_RATE:
                BigDecimal num = new BigDecimal(enyanConfig.getConfigValue());
                Constant.CNY_RATE = num;
                //CurrencyType.CNY.setValue(new BigDecimal(1));
                return;
            case SYS_UPDATE:
                Constant.SYS_UPDATE = enyanConfig.getConfigValue();
                return;
            case SECRET_CODE:
                Constant.TEST_TOKEN_VALUE = enyanConfig.getConfigValue();
                return;
            case SPIRIT_UPDATE:
                Constant.DEFAULT_REST_CONFIG.setSpiritTime(Long.parseLong(enyanConfig.getConfigValue()));
                return;
            case LATEST_NOTICE_UPDATE:
                Constant.DEFAULT_REST_CONFIG.setLatestNoticeTime(Long.parseLong(enyanConfig.getConfigValue()));
                return;
        }
        for (CurrencyType currencyType:CurrencyType.values()){
            if (enyanConfig.getConfigName().equals(currencyType.getCurrency())){
                currencyType.setValue(new BigDecimal(enyanConfig.getConfigValue()));
                return;
            }
        }
    }
    /**
     *
     *  初始化广告列表
     * @param list
     * @Date: 2020-06-11
     */
    private void initAd(List<EnyanConfig> list){
        AdModel adModel1 = new AdModel();
        AdModel adModel2 = new AdModel();
        AdModel adModel3 = new AdModel();
        AdModel adModel11 = new AdModel();

        for (EnyanConfig enyanConfig:list){
            if (StringUtils.isBlank(enyanConfig.getConfigName())|| StringUtils.isBlank(enyanConfig.getConfigValue())){
                continue;
            }
            switch (enyanConfig.getConfigName()){
                case "AD1_IMAGE_URL":
                    adModel1.setImgUrl(enyanConfig.getConfigValue());
                    break;
                case "AD1_TO_URL":
                    adModel1.setToUrl(enyanConfig.getConfigValue());
                    break;
                case "AD1_DURATION":
                    adModel1.setDuration(Integer.parseInt(enyanConfig.getConfigValue()));
                    break;
                case "AD1_BEGIN_DATE":
                    try {
                        Date beginDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 00:00:01","yyyyMMdd HH:mm:ss");
                        adModel1.setBeginDate(beginDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel1.setBeginDate(-1L);
                    }
                    break;
                case "AD1_END_DATE":
                    try {
                        Date endDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 23:59:59","yyyyMMdd HH:mm:ss");
                        adModel1.setEndDate(endDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel1.setEndDate(-1L);
                    }
                    break;
                case "AD2_IMAGE_URL":
                    adModel2.setImgUrl(enyanConfig.getConfigValue());
                    break;
                case "AD2_TO_URL":
                    adModel2.setToUrl(enyanConfig.getConfigValue());
                    break;
                case "AD2_DURATION":
                    adModel2.setDuration(Integer.parseInt(enyanConfig.getConfigValue()));
                    break;
                case "AD2_BEGIN_DATE":
                    try {
                        Date beginDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 00:00:01","yyyyMMdd HH:mm:ss");
                        adModel2.setBeginDate(beginDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel2.setBeginDate(-1L);
                    }
                    break;
                case "AD2_END_DATE":
                    try {
                        Date endDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 23:59:59","yyyyMMdd HH:mm:ss");
                        adModel2.setEndDate(endDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel2.setEndDate(-1L);
                    }
                    break;
                case "AD3_IMAGE_URL":
                    adModel3.setImgUrl(enyanConfig.getConfigValue());
                    break;
                case "AD3_TO_URL":
                    adModel3.setToUrl(enyanConfig.getConfigValue());
                    break;
                case "AD3_DURATION":
                    adModel3.setDuration(Integer.parseInt(enyanConfig.getConfigValue()));
                    break;
                case "AD3_BEGIN_DATE":
                    try {
                        Date beginDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 00:00:01","yyyyMMdd HH:mm:ss");
                        adModel3.setBeginDate(beginDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel3.setBeginDate(-1L);
                    }
                    break;
                case "AD3_END_DATE":
                    try {
                        Date endDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 23:59:59","yyyyMMdd HH:mm:ss");
                        adModel3.setEndDate(endDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel3.setEndDate(-1L);
                    }
                    break;

                case "AD11_IMAGE_URL":
                    adModel11.setImgUrl(enyanConfig.getConfigValue());
                    break;
                case "AD11_TO_URL":
                    adModel11.setToUrl(enyanConfig.getConfigValue());
                    break;
                case "AD11_DURATION":
                    adModel11.setDuration(Integer.parseInt(enyanConfig.getConfigValue()));
                    break;
                case "AD11_BEGIN_DATE":
                    try {
                        Date beginDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 00:00:01","yyyyMMdd HH:mm:ss");
                        adModel11.setBeginDate(beginDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel3.setBeginDate(-1L);
                    }
                    break;
                case "AD11_END_DATE":
                    try {
                        Date endDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 23:59:59","yyyyMMdd HH:mm:ss");
                        adModel11.setEndDate(endDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        adModel3.setEndDate(-1L);
                    }
                    break;
            }
        }

        List<AdModel> adList = new ArrayList<>();
        if (adModel1.getDuration() > 0){
            adList.add(adModel1);
        }
        if (adModel2.getDuration() > 0){
            adList.add(adModel2);
        }
        if (adModel3.getDuration() > 0){
            adList.add(adModel3);
        }

        List<AdModel> adWebList = new ArrayList<>();
        adWebList.add(adModel11);

        Constant.DEFAULT_REST_CONFIG.setAdList(adList);
        Constant.DEFAULT_REST_CONFIG.setAdWebList(adWebList);
    }

    /**
     * <p>初始化灵修书籍的图片信息</p>
     * @param list
     * @return: void
     * @since : 12/4/20
     */
    private void initSpirit(List<EnyanConfig> list){
        SpiritImgModel spiritImgModel = new SpiritImgModel();


        for (EnyanConfig enyanConfig:list){
            if (StringUtils.isBlank(enyanConfig.getConfigName())|| StringUtils.isBlank(enyanConfig.getConfigValue())){
                continue;
            }
            switch (enyanConfig.getConfigName()){
                case "SPIRIT_BENIFIT_CN_IMG":
                    spiritImgModel.setBenifitImgCn(enyanConfig.getConfigValue());
                    break;
                case "SPIRIT_WHY_CN_IMG":
                    spiritImgModel.setWhyImgCn(enyanConfig.getConfigValue());
                    break;
                case "SPIRIT_BENIFIT_HK_IMG":
                    spiritImgModel.setBenifitImgHk(enyanConfig.getConfigValue());
                    break;
                case "SPIRIT_WHY_HK_IMG":
                    spiritImgModel.setWhyImgHk(enyanConfig.getConfigValue());
                    break;
                case "LECTIO_DIVINA_CN_IMG":
                    spiritImgModel.setLectioDivinaImgCn(enyanConfig.getConfigValue());
                    break;
                case "LECTIO_DIVINA_HK_IMG":
                    spiritImgModel.setLectioDivinaImgHk(enyanConfig.getConfigValue());
                    break;
            }
        }

        Constant.DEFAULT_REST_CONFIG.setSpiritImg(spiritImgModel);
    }

    /**
     * <p>初始化优惠码信息</p>
     * @param list
     * @return: void
     * @since : 2020-07-22
     */
    private void initCoupon(List<EnyanConfig> list){
        Coupon coupon = new Coupon();
        for (EnyanConfig enyanConfig:list) {
            if (StringUtils.isBlank(enyanConfig.getConfigName()) || StringUtils.isBlank(enyanConfig.getConfigValue())) {
                continue;
            }
            switch (enyanConfig.getConfigName()) {
                case "COUPON_CODE":
                    coupon.setCode(enyanConfig.getConfigValue().toUpperCase());
                    break;
                case "COUPON_VALUE":
                    coupon.setValue(new BigDecimal(enyanConfig.getConfigValue()));
                    break;
                case "COUPON_MIN_LIMIT_VALUE":
                    coupon.setMinLimitValue(new BigDecimal(enyanConfig.getConfigValue()));
                    break;
                case "COUPON_BEGIN_DATE":
                    try {
                        Date beginDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 00:00:01","yyyyMMdd HH:mm:ss");
                        coupon.setBeginDate(beginDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        coupon.setEndDate(-1L);
                    }
                    break;
                case "COUPON_END_DATE":
                    try {
                        Date endDate = DateUtils.parseDate(enyanConfig.getConfigValue()+" 23:59:59","yyyyMMdd HH:mm:ss");
                        coupon.setEndDate(endDate.getTime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                        coupon.setEndDate(-1L);
                    }
                    break;
            }
        }
//        Constant.DEFAULT_COUPON = coupon;
    }

    /**
     * <p>初始化需要通知的系统消息</p>
     * @param
     * @return void
     * @since : 2024-02-28
     **/
    private void initNoticeMsg(){
        Date now = new Date();
        Long current = now.getTime();
        List<NoticeMsg> noticeMsgList = new ArrayList<>();
        /*
        NoticeMsg msg1 = new NoticeMsg();
        msg1.setMsgId(1L);
        msg1.setText("系统消息1");
        msg1.setCreateAt(current);
        msg1.setShouldShow(1);
        noticeMsgList.add(msg1);

        NoticeMsg msg2 = new NoticeMsg();
        msg2.setMsgId(2L);
        msg2.setText("恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，\n旨在通过与主内作者和出版机构的合作，\n协力促进华文基督教资源电子化，\n帮助全球华人基督徒，\n更加便捷地获取并阅读基督教图书。");
        msg2.setCreateAt(current);
        msg2.setShouldShow(1);
        noticeMsgList.add(msg2);*/

        /*
        NoticeMsg msg3 = new NoticeMsg();
        msg3.setMsgId(3L);
        msg3.setText("自2025年1月1日起，「靈修」板塊的使用方式有所調整，所有靈修材料（包含已加入靈修計劃的）僅支持購書後使用；我們也將陸續上架新的靈修材料，敬請期待！");
        msg3.setCreateAt(current);
        msg3.setShouldShow(1);
        noticeMsgList.add(msg3);*/

        Constant.DEFAULT_REST_CONFIG.setNoticeMsgList(noticeMsgList);
    }

    public void updateSystemConfigDate(){
        String sysDate = DateFormatUtils.format(new Date(),"yyyyMMddhhmmss");
        EnyanConfig config = new EnyanConfig();
        config.setConfigId(-1L);
        config.setConfigValue(sysDate);

        //enyanConfigMapper.updateByPrimaryKey(config);
    }

    @Override
    public void initPayRateConfigFromFixerIO() {
        try {
            //String FIXER_IO_API_KEY = "********************************";
            //String FIXER_IO_API_SYMBOLS = "&symbols=USD,AUD,CAD,PLN,MXN,HKD,CNY,JPY,TWD,MYR,IDR,SGD,EUR";

            String url = "http://data.fixer.io/api/latest?access_key=" + FIXER_IO_API_KEY + FIXER_IO_API_SYMBOLS;

            HttpResult result = HttpProtocolHandler.execute(null,url, HttpMethod.GET);
            String resultString = result.getStringResult();
            PayRateDTO payRateDTO = JSONObject.parseObject(resultString, PayRateDTO.class);
            String methodName = "";
            for (CurrencyType currencyType:CurrencyType.values()){
                methodName = "get"+currencyType.getCurrency();
                String value = MethodUtils.invokeMethod(payRateDTO.getRates(),methodName).toString();
                currencyType.setValue(new BigDecimal(value));
                try {
                    EnyanConfig enyanConfig = new EnyanConfig();
                    enyanConfig.setConfigDescription(currencyType.getDescription());
                    enyanConfig.setConfigId(BASE_CURRENCY_CONF_ID+currencyType.ordinal());
                    enyanConfig.setIsShow(Constant.BYTE_VALUE_1);
                    enyanConfig.setConfigValue(value);
                    enyanConfig.setConfigName(currencyType.getCurrency());
                    enyanConfigCustomMapper.add(enyanConfig);
                }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

                }
            }
            String value = MethodUtils.invokeMethod(payRateDTO.getRates(),"getHKD").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updatePayRateConfigFromFixerIO() {
        String resultString = "",methodName="";
        try {
            //String FIXER_IO_API_KEY = "********************************";
            //String FIXER_IO_API_SYMBOLS = "&symbols=USD,AUD,CAD,PLN,MXN,HKD,CNY,JPY,TWD,MYR,IDR,SGD,EUR";

            String url = "http://data.fixer.io/api/latest?access_key=" + FIXER_IO_API_KEY + FIXER_IO_API_SYMBOLS;

            HttpResult result = HttpProtocolHandler.execute(null,url, HttpMethod.GET);
            resultString = result.getStringResult();
            if (StringUtils.isBlank(resultString)){
                return;
            }
            PayRateDTO payRateDTO = JSONObject.parseObject(resultString, PayRateDTO.class);
            for (CurrencyType currencyType:CurrencyType.values()){
                methodName = "get"+currencyType.getCurrency();
                Object valueObject =  MethodUtils.invokeMethod(payRateDTO.getRates(),methodName);
                if (null == valueObject){
                    continue;
                }
                String value = valueObject.toString();
                if (null == value){
                    continue;
                }
                currencyType.setValue(new BigDecimal(value));
                try {
                    EnyanConfig enyanConfig = new EnyanConfig();
                    //enyanConfig.setConfigDescription(currencyType.getDescription());
                    enyanConfig.setConfigId(BASE_CURRENCY_CONF_ID+currencyType.ordinal());
                    //enyanConfig.setIsShow(Constant.BYTE_VALUE_1);
                    enyanConfig.setConfigValue(value);
                    //enyanConfig.setConfigName(currencyType.getCurrency());
                    enyanConfigMapper.updateByPrimaryKeySelective(enyanConfig);
                }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

                }
            }
            //String value = MethodUtils.invokeMethod(payRateDTO.getRates(),"getHKD").toString();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(Integer.valueOf(1011), InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "methodName="+methodName+",updatePayRateConfigFromFixerIO result="+resultString);
        }
    }
}
