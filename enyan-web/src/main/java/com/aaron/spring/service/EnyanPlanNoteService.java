package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanPlanNote;
import com.aaron.spring.model.EnyanPlanNoteExample;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/13/20
 * @Modified By:
 */
public interface EnyanPlanNoteService extends IService<EnyanPlanNote, EnyanPlanNoteExample>{
    /**
     * <p>获取大于updateTime的计划note数据,主要用于同步</p>
     * @param page
     * @param enyanPlan
     * @return: com.aaron.mybatis.dao.pojo.Page
     * @since : 12/10/20
     */
    Page findSpiritPlanNotesGTUpdateTime(Page<EnyanPlanNote> page, EnyanPlanNote enyanPlanNote);

    /**
     * <p>app同步的planNote信息存储</p>
     * @param list
     * @return: int
     * @since : 12/14/20
     */
    int updateSyncPlanNote(List<EnyanPlanNote> list);

    /**
     * <p>注销用户</p>
     * @param email
     * @param revokedEmail
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2022/8/26
     **/
    ExecuteResult<String> revokeUser(String email, String revokedEmail);
}
