package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBanner;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanBannerMapper;
import com.aaron.spring.mapper.custom.EnyanBannerCustomMapper;
import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanBannerExample;
import com.aaron.spring.service.EnyanBannerService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/6/23
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanBannerServiceImpl implements EnyanBannerService {
	@Resource
	private EnyanBannerMapper enyanBannerMapper;

	@Resource
	private EnyanBannerCustomMapper enyanBannerCustomMapper;

	@Override
	public Page queryRecords(Page<EnyanBanner> page, EnyanBanner record) {
		if (null == record){
			record = new EnyanBanner();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanBannerExample example = new EnyanBannerExample();
			EnyanBannerExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			*/
			if (StringUtils.isNotBlank(record.getDataName())){
				criteria.andDataNameLike("%"+record.getDataName()+"%");
			}
			if (null != record.getDataIndexShow()){
				criteria.andDataIndexShowEqualTo(record.getDataIndexShow());
			}
			if (null != record.getDataReadShow()){
				criteria.andDataReadShowEqualTo(record.getDataReadShow());
			}
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			criteria.andIsDeletedEqualTo(0);
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanBannerMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanBanner> list;
			if (count > 0){
				list = enyanBannerMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<EnyanBanner> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanBanner> result = new ExecuteResult<>();
		try {
			EnyanBanner record = enyanBannerMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanBanner();
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBanner> addRecord(EnyanBanner record) {
		ExecuteResult<EnyanBanner> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = enyanBannerMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBanner> updateRecord(EnyanBanner record) {
		ExecuteResult<EnyanBanner> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanBannerMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			int deleteFlag = enyanBannerCustomMapper.updateRecordToDeletedById(pkId);
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanBanner record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanBanner record) {
		return null;
	}

	@Override
	public void initBanners() {
		List<EnyanBanner> bannerList = enyanBannerCustomMapper.findRecords();
		Date currentDate = new Date();
		List<RestBanner> indexBanners = new ArrayList<>();
		List<RestBanner> middleBanners = new ArrayList<>();
		List<RestBanner> readBanners = new ArrayList<>();
		for (EnyanBanner banner:bannerList){
			if (null == banner.getEndAt()){
				continue;
			}
			if (banner.getEndAt().compareTo(currentDate) < 0){
				continue;
			}
			RestBanner restBanner = new RestBanner();
			restBanner.initFrom(banner);
			if (banner.getDataReadShow() == 1){//专门给读书会使用的banner
				readBanners.add(restBanner);
			}
			if (banner.getDataIndexShow() == 1){//首页banner数据
				indexBanners.add(restBanner);
			}
			if (banner.getDataMiddleShow() == 1){//首页中部banner
				middleBanners.add(restBanner);
			}
		}
		Constant.DEFAULT_REST_CONFIG.setIndexBanners(indexBanners);
		Constant.DEFAULT_REST_CONFIG.setReadBanners(readBanners);
		Constant.DEFAULT_REST_CONFIG.setMiddleBanners(middleBanners);
	}

	@Override
	public void resetBanners() {
		enyanBannerCustomMapper.updateRecordToExpired();
		this.initBanners();
	}
}
