package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.DataStatMapper;
import com.aaron.spring.mapper.custom.DataStatCustomMapper;
import com.aaron.spring.mapper.custom.EnyanBookBuyCustomMapper;
import com.aaron.spring.mapper.custom.EnyanOrderCustomMapper;
import com.aaron.spring.mapper.custom.EnyanOrderDetailCustomMapper;
import com.aaron.spring.model.DataStat;
import com.aaron.spring.model.DataStatExample;
import com.aaron.spring.model.EnyanOrder;
import com.aaron.spring.service.DataStatService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2021/9/26
 * @Modified By:
 */
@Slf4j
@Service
public class DataStatServiceImpl implements DataStatService {

	private static final String selectCountUserAll = "select count(1) from oc_customer";

	private static final String selectCountUserActiveAll = "select count(1) from oc_customer where approved = 1";

	private static final String selectCountUserYesterday = "select count(1) from oc_customer where TO_DAYS(NOW())-TO_DAYS(date_added) = 1";

	private static final String selectCountUserByDay = "select count(1) from oc_customer where DATE_FORMAT(date_added,'%Y%m%d') = ?";
	@Resource
	private DataStatMapper dataStatMapper;

	@Resource
	private EnyanOrderCustomMapper enyanOrderCustomMapper;

	@Resource
	private EnyanOrderDetailCustomMapper enyanOrderDetailCustomMapper;

//	@Resource
//	private EnyanBookBuyCustomMapper enyanBookBuyCustomMapper;

	@Resource
	private JdbcTemplate userJdbcTemplate;

	@Resource
	private DataStatCustomMapper dataStatCustomMapper;

	@Override
	public Page queryRecords(Page<DataStat> page, DataStat record) {
		if (null == record){
			record = new DataStat();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			DataStatExample example = new DataStatExample();
			DataStatExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}*/

			long count = page.getTotalRecord();
			if (count<=0){
				count = dataStatMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<DataStat> list;
			if (count > 0){
				list = dataStatMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<DataStat> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<DataStat> result = new ExecuteResult<>();
		try {
			DataStat record = dataStatMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new DataStat();
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<DataStat> addRecord(DataStat record) {
		ExecuteResult<DataStat> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = dataStatMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<DataStat> updateRecord(DataStat record) {
		ExecuteResult<DataStat> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = dataStatMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			int deleteFlag = dataStatMapper.deleteByPrimaryKey(pkId);
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(DataStat record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(DataStat record) {
		return null;
	}

	@Override
	public DataStat saveDataYesterday() {
		Date today = new Date();
		Date yesterday = DateUtils.addDays(today, -1);
		String yyyyMMdd = DateFormatUtils.format(yesterday, "yyyyMMdd");
		//昨天的统计信息（订单数，销售额,下单用户数）(不包含兑换码兑换订单)
		List<EnyanOrder> list = enyanOrderCustomMapper.countOfOrderByDay(yyyyMMdd);
		int orderCount = 0;//订单数
		BigDecimal incomeTotal = new BigDecimal("0");//销售额
		int userBuyCount = 0;//购买用户数
		if (list.isEmpty() == false){
			EnyanOrder order = list.get(0);
			orderCount = order.getCount().intValue();
			if (null != order.getOrderTotal()){
				incomeTotal = order.getOrderTotal();
			}
			userBuyCount = order.getCount2().intValue();
		}

		int salesVolume = 0;//销量
		salesVolume = (int) enyanOrderDetailCustomMapper.countOfBooksByDay(yyyyMMdd);
		int orderFeeCount = 0;//付费订单数
		orderFeeCount = (int)enyanOrderCustomMapper.countOfOrderFeeByDay(yyyyMMdd);
		int orderFreeCount = 0;//免费订单数
		orderFreeCount = orderCount - orderFeeCount;
		int userNewCount ;//新注册用户数
		userNewCount = userJdbcTemplate.queryForObject(selectCountUserByDay, Integer.class,yyyyMMdd);

		DataStat dataStat = new DataStat();
		dataStat.setCreateTime(yesterday);
		dataStat.setIncomeTotal(incomeTotal);
		dataStat.setOrderCount(orderCount);
		dataStat.setOrderFeeCount(orderFeeCount);
		dataStat.setOrderFreeCount(orderFreeCount);
		dataStat.setSalesVolume(salesVolume);
		dataStat.setUserBuyCount(userBuyCount);
		dataStat.setUserNewCount(userNewCount);

		dataStatMapper.insert(dataStat);
		return dataStat;
	}

	@Override
	public void updateDataInDay(String yyyyMMdd) {
//昨天的统计信息（订单数，销售额,下单用户数）
		List<EnyanOrder> list = enyanOrderCustomMapper.countOfOrderByDay(yyyyMMdd);
		int orderCount = 0;//订单数
		BigDecimal incomeTotal = new BigDecimal("0");//销售额
		int userBuyCount = 0;//购买用户数
		if (list.isEmpty() == false){
			EnyanOrder order = list.get(0);
			orderCount = order.getCount().intValue();
			if (null != order.getOrderTotal()){
				incomeTotal = order.getOrderTotal();
			}
			userBuyCount = order.getCount2().intValue();
		}

		int salesVolume = 0;//销量
		salesVolume = (int) enyanOrderDetailCustomMapper.countOfBooksByDay(yyyyMMdd);
		int orderFeeCount = 0;//付费订单数
		orderFeeCount = (int)enyanOrderCustomMapper.countOfOrderFeeByDay(yyyyMMdd);
		int orderFreeCount = 0;//免费订单数
		orderFreeCount = orderCount - orderFeeCount;

		DataStat dataStat = new DataStat();
		dataStat.setIncomeTotal(incomeTotal);
		dataStat.setOrderCount(orderCount);
		dataStat.setOrderFeeCount(orderFeeCount);
		dataStat.setOrderFreeCount(orderFreeCount);
		dataStat.setSalesVolume(salesVolume);
		dataStat.setUserBuyCount(userBuyCount);

		dataStatCustomMapper.updateDataStatByDay(dataStat, yyyyMMdd);
	}

	@Override
	public List<DataStat> findDataByDay(String beginTime, String endTime) {
		return dataStatCustomMapper.findDataByDay(beginTime, endTime);
	}

	@Override
	public List<DataStat> findDataByMonth(String beginTime, String endTime) {
		return dataStatCustomMapper.findDataByMonth(beginTime, endTime);
	}

	@Override
	public List<DataStat> findDataByYear(String beginTime, String endTime) {
		return dataStatCustomMapper.findDataByYear(beginTime, endTime);
	}

	@Override
	public DataStat basicInfo() {
		DataStat dataStat = new DataStat();
		int userBuyCount = 0;//总下单人数
		List<EnyanOrder> list = enyanOrderCustomMapper.countOfOrderUser();
		if (list.isEmpty() == false){
			userBuyCount = list.get(0).getCount2().intValue();
		}
		int userCount = 0;//总注册人数
		userCount = userJdbcTemplate.queryForObject(selectCountUserAll, Integer.class);

		int userActiveCount = 0;//总激活人数
		userActiveCount = userJdbcTemplate.queryForObject(selectCountUserActiveAll, Integer.class);

		dataStat.setUserAllCount(userCount);
		dataStat.setUserBuyCount(userBuyCount);
		dataStat.setUserActiveCount(userActiveCount);
		return dataStat;
	}
}
