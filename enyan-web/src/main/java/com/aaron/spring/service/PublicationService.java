package com.aaron.spring.service;

import com.aaron.common.NameAndValue;
import com.aaron.drm.model.PublicationLcp;
import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.model.Publication;
import com.aaron.spring.model.PublicationExample;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-09
 * @Modified By:
 */
public interface PublicationService extends IService<Publication, PublicationExample>{
    /**
     * 获取 Publishers
     * */
    List<Publication> findAllPublicationList(Publication publication);

    List<NameAndValue> findAllPublicationNameAndValues();

    List<Publication> findPublicationReally(Publication publication);
    /**
     *
     *  通过DRM添加publication
     * @param record
     * @Date: 2019-07-03
     */
    ExecuteResult<Publication> addRecordByLcp(PublicationLcp record);
}
