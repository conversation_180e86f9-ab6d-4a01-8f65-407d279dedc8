package com.aaron.spring.service;

import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanOrder;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.EnyanRedeemCodeExample;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-11-11
 * @Modified By:
 */
public interface EnyanRedeemCodeService extends IService<EnyanRedeemCode, EnyanRedeemCodeExample>{
    ExecuteResult<EnyanRedeemCode> addRecord(EnyanRedeemCode record, List<EnyanBook> bookListToRedeem);
    /**
     * 根据record的信息查询数据
     * @param record
     * @since : 2019-11-13
     */
    List<EnyanRedeemCode> findRecordsByRedeemCode(EnyanRedeemCode record);

    /**
     * <p>根据code获取兑换码列表</p>
     * @param code
     * @return java.util.List<com.aaron.spring.model.EnyanRedeemCode>
     * @since : 2021/7/19
     **/
    List<EnyanRedeemCode> getRecordByCode(String code);

    /**
     * 分离兑换码订单
     * @param enyanOrder
     * @since : 2020-06-24
     */
    void splitOrder(EnyanOrder enyanOrder);


    /**
     * <p>注销用户</p>
     * @param email
     * @param revokedEmail
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2022/8/26
     **/
    ExecuteResult<String> revokeUser(String email, String revokedEmail);
}
