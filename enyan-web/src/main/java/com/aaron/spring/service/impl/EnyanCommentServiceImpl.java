package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanCommentMapper;
import com.aaron.spring.mapper.custom.EnyanCommentCustomMapper;
import com.aaron.spring.model.EnyanComment;
import com.aaron.spring.model.EnyanCommentExample;
import com.aaron.spring.service.EnyanCommentService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Aaron Ha<PERSON>
 * @Description:
 * @Date: Created in  2023/4/21
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanCommentServiceImpl implements EnyanCommentService {
	@Resource
	private EnyanCommentMapper enyanCommentMapper;

	@Resource
	private EnyanCommentCustomMapper enyanCommentCustomMapper;

	@Override
	public Page queryRecords(Page<EnyanComment> page, EnyanComment record) {
		if (null == record){
			record = new EnyanComment();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanCommentExample example = new EnyanCommentExample();
			EnyanCommentExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			*/
			if (null != record.getIsDeleted()){
				criteria.andIsDeletedEqualTo(record.getIsDeleted());
			}
			if (null != record.getCanShow()){
				criteria.andCanShowEqualTo(record.getCanShow());
			}
			/*
			if (null != record.getParentId()){
				criteria.andParentIdEqualTo(record.getParentId());
			}else if (record.getParentId() == -1){//如果是-1，则搜索所有数据

			}else{
				criteria.andParentIdGreaterThan(record.getParentId());
			}*/
			if (null != record.getParentId()){
				if (record.getParentId() == -1){//如果是-1，则搜索所有数据

				}else {
					criteria.andParentIdEqualTo(record.getParentId());
				}
			}
			if (null != record.getBookId()){
				criteria.andBookIdEqualTo(record.getBookId());
			}
			if (StringUtils.isNotBlank(record.getEmail())){
				criteria.andEmailEqualTo(record.getEmail());
			}
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanCommentMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanComment> list;
			if (count > 0){
				list = enyanCommentMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<EnyanComment> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanComment> result = new ExecuteResult<>();
		try {
			EnyanComment record = enyanCommentMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanComment();
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanComment> addRecord(EnyanComment record) {
		ExecuteResult<EnyanComment> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = enyanCommentMapper.insert(record);
			if (record.getParentId() > 0){
				enyanCommentCustomMapper.updateCommentToPlusById(record.getParentId());
			}
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanComment> updateRecord(EnyanComment record) {
		ExecuteResult<EnyanComment> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanCommentMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = EnyanCommentCustomMapper.updateRecordToDeletedById(pkId);
			int deleteFlag = 0;
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanComment record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanComment record) {
		return null;
	}

	@Override
	public int updateCommentLikeCountById(Long id) {
		return enyanCommentCustomMapper.updateCommentLikeCountById(id);
	}

	@Override
	public int updateCommentToDeletedById(Long id, String email, Long parentId) {
		int ret = enyanCommentCustomMapper.updateCommentToDeletedById(id,email);
		if (ret == 0){//如果数据不正确，则返回
			return 0;
		}
		if (parentId > 0){//是子评论
			enyanCommentCustomMapper.updateCommentToMinusById(parentId);
		}
		return 1;
	}
}
