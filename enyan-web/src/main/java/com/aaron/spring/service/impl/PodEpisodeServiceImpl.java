package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.PodEpisodeMapper;
import com.aaron.spring.mapper.custom.PodEpisodeCustomMapper;
import com.aaron.spring.model.PodEpisode;
import com.aaron.spring.model.PodEpisodeExample;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.model.PodUserEpisodeInteraction;
import com.aaron.spring.service.PodEpisodeService;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description: 播客单集服务实现类
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Slf4j
@Service
public class PodEpisodeServiceImpl implements PodEpisodeService {

    @Resource
    private PodEpisodeMapper podEpisodeMapper;

    @Resource
    private PodEpisodeCustomMapper podEpisodeCustomMapper;

    @Resource
    private PodPodcastService podPodcastService;


    private String buildOrderClause(List<OrderObj> orderObjList) {
        if (orderObjList == null || orderObjList.isEmpty()) {
            return "episode_number ASC";
        }
        StringBuilder buffer = new StringBuilder();
        orderObjList.forEach(order ->
            buffer.append(order.getOrderBy())
                  .append(" ")
                  .append(order.getAscOrDesc())
                  .append(",")
        );
        return buffer.length() > 0 ?
               buffer.substring(0, buffer.length()-1) :
               "episode_number ASC";
    }

    private void applySorting(Object record, OrderObj orderObj) {
        if (record instanceof PodUserEpisodeInteraction) {
            ((PodUserEpisodeInteraction) record).addOrder(orderObj);
        } else {
            PodUserEpisodeInteraction interaction = new PodUserEpisodeInteraction();
            interaction.setEpisodeId(((PodEpisode)record).getEpisodeId());
            interaction.addOrder(orderObj);
            record = interaction;
        }
    }

    @Override
    public Page<PodEpisode> queryRecords(Page<PodEpisode> page, PodEpisode record) {
        if (null == record){
            record = new PodEpisode();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            PodEpisodeExample example = new PodEpisodeExample();
            PodEpisodeExample.Criteria criteria = example.createCriteria();

            // 设置分页参数
            example.setPage(page);

            // 设置查询条件
            if (null != record.getPodcastId()){
                criteria.andPodcastIdEqualTo(record.getPodcastId());
            }
            if (null != record.getTopicId()){
                criteria.andTopicIdEqualTo(record.getTopicId());
            }
            if (null != record.getIsPublished()){
                criteria.andIsPublishedEqualTo(record.getIsPublished());
            }
            if (null != record.getIsDeleted()){
                criteria.andIsDeletedEqualTo(record.getIsDeleted());
            }
            if (StringUtils.isNotBlank(record.getTitle())){
                criteria.andTitleLike("%"+record.getTitle()+"%");
            }

            // 设置排序
            if (record.getOrderObjList() != null && !record.getOrderObjList().isEmpty()) {
                example.setOrderByClause(buildOrderClause(record.getOrderObjList()));
            }

            // 执行查询
            long count = podEpisodeMapper.countByExample(example);
            List<PodEpisode> list = count > 0 ?
                podEpisodeMapper.selectByExample(example) : new ArrayList<>();

            // 设置分页结果
            page.setRecords(list);
            page.setTotalRecord(count);

        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return page;
    }

    @Override
    public ExecuteResult<PodEpisode> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<PodEpisode> result = new ExecuteResult<>();
        try {
            PodEpisode record = podEpisodeMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new PodEpisode();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<PodEpisode> addRecord(PodEpisode record) {
        ExecuteResult<PodEpisode> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }

            // 设置封面图片URL
            if (record.getPodcastId() != null) {
                String coverImageUrl = this.getPodcastCoverImageUrl(record.getPodcastId());
                record.setCoverImageUrl(coverImageUrl);
                
                // 设置详情页长条图片URL
                String coverImageUrl2 = this.getPodcastCoverImageUrl2(record.getPodcastId());
                record.setCoverImageUrl2(coverImageUrl2);
            }

            int saveFlag = podEpisodeMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<PodEpisode> updateRecord(PodEpisode record) {
        ExecuteResult<PodEpisode> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }

            int saveFlag = podEpisodeMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = podEpisodeCustomMapper.updateEpisodeToDeletedById(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(PodEpisode record) {
        if (null == record) {
            return "播客单集数据不能为空";
        }
        if (null == record.getPodcastId()) {
            return "播客ID不能为空";
        }
        if (StringUtils.isBlank(record.getTitle())) {
            return "单集标题不能为空";
        }
        if (StringUtils.isBlank(record.getDescription())) {
            return "单集描述不能为空";
        }
        if (StringUtils.isBlank(record.getAudioFileUrl())) {
            return "音频文件地址不能为空";
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(PodEpisode record) {
        if (null == record) {
            return "播客单集数据不能为空";
        }
        if (null == record.getEpisodeId()) {
            return "单集ID不能为空";
        }
        return null;
    }

    @Override
    public Page queryEpisodesByRestObj(Page page, PodEpisode record) {
        if (record == null || record.getPodcastId() == null){
            page.setErrorMessage("播客ID不能为空");
            return page;
        }
        record.setIsDeleted(0);
        record.setIsPublished(1); // 默认只查询已发布的单集

        // 兼容排序参数：如果orderObjList为空，才加默认排序，否则用外部透传的排序
        if (record.getOrderObjList() == null || record.getOrderObjList().isEmpty()) {
            OrderObj orderObj = new OrderObj("episode_number", InterfaceContant.OrderBy.ASC);
            applySorting(record, orderObj);
        }
        return this.queryRecords(page, record);
    }


//    /**
//     * 通过RestEpisode对象（继承BaseDTO，带orderObjList），分页查询单集，支持多字段排序透传
//     */
//    @Override
//    public Page<PodEpisode> queryEpisodesByRestObj(Page<PodEpisode> page, RestEpisode restObj) {
//        if (restObj == null || restObj.getPodcastId() == null) {
//            page.setErrorMessage("播客ID不能为空");
//            return page;
//        }
//        PodEpisodeExample example = new PodEpisodeExample();
//        PodEpisodeExample.Criteria criteria = example.createCriteria();
//        example.setPage(page);
//        criteria.andPodcastIdEqualTo(restObj.getPodcastId());
//        criteria.andIsDeletedEqualTo(0);
//        criteria.andIsPublishedEqualTo(1);
//        // 排序透传，参考EnyanOrderDetailServiceImpl
//        if (restObj.getOrderObjList() != null && !restObj.getOrderObjList().isEmpty()) {
//            StringBuilder buffer = new StringBuilder();
//            for (int i = 0; i < restObj.getOrderObjList().size(); i++) {
//                OrderObj orderObj = restObj.getOrderObjList().get(i);
//                if (i != 0) buffer.append(",");
//                buffer.append(orderObj.getOrderBy()).append(" ").append(orderObj.getAscOrDesc());
//            }
//            example.setOrderByClause(buffer.toString());
//        } else {
//            example.setOrderByClause("episode_number desc");
//        }
//        long count = podEpisodeMapper.countByExample(example);
//        List<PodEpisode> list = count > 0 ? podEpisodeMapper.selectByExample(example) : new ArrayList<>();
//        page.setRecords(list);
//        page.setTotalRecord(count);
//        return page;
//    }

    @Override
    public int updateEpisodeListenCountById(Long id) {
        try {
            if (null != id) {
                return podEpisodeCustomMapper.updateEpisodeListenCountById(id);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //log.error("更新单集播放量失败，id={}", id, e);
        }
        return 0;
    }

    @Override
    public int updateEpisodeLikeCountById(Long id) {
        try {
            if (null != id) {
                return podEpisodeCustomMapper.updateEpisodeLikeCountById(id);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //log.error("更新单集点赞量失败，id={}", id, e);
        }
        return 0;
    }

    @Override
    public String getPodcastCoverImageUrl(Long podcastId) {
        if (podcastId == null) {
            return null;
        }
        ExecuteResult<PodPodcast> result = podPodcastService.queryRecordByPrimaryKey(podcastId);
        if (result.isSuccess() && result.getResult() != null) {
            return result.getResult().getCoverImageUrl();
        }
        return null;
    }
    
    @Override
    public String getPodcastCoverImageUrl2(Long podcastId) {
        if (podcastId == null) {
            return null;
        }
        ExecuteResult<PodPodcast> result = podPodcastService.queryRecordByPrimaryKey(podcastId);
        if (result.isSuccess() && result.getResult() != null) {
            return result.getResult().getCoverImageUrl2();
        }
        return null;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordEpisodePlayStart(Long episodeId, String email) {
        if (episodeId == null || (email == null || email.isEmpty())) {
            log.warn("recordEpisodePlayStart failed: episodeId or email is null. episodeId: {}, email: {}", 
                   episodeId, email);
            return false;
        }
        
        try {
            // 1. 记录最后播放时间
            int updated = podEpisodeCustomMapper.recordEpisodePlayStart(episodeId, email);
//            if (updated <= 0) {
//                log.warn("Failed to record episode play start. episodeId: {}, email: {}",
//                        episodeId, email);
//                return false;
//            }
            
            // 2. 增加播放次数（原子操作）
            updated = podEpisodeCustomMapper.incrementEpisodeListenCount(episodeId);
            if (updated <= 0) {
                log.warn("Failed to increment listen count. episodeId: {}", episodeId);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("Error in recordEpisodePlayStart for episodeId: {}, email: {}", 
                     episodeId, email, e);
            throw new RuntimeException("记录播放开始失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordEpisodePlayStop(Long episodeId, String email) {
        if (episodeId == null || (email == null || email.isEmpty())) {
            log.warn("recordEpisodePlayStop failed: episodeId or email is null. episodeId: {}, email: {}", 
                   episodeId, email);
            return false;
        }
        
        try {
            // 0. 检查用户是否已经开始播放
            boolean hasStarted = podEpisodeCustomMapper.hasUserStartedPlaying(episodeId, email);
            if (!hasStarted) {
                log.warn("User has not started playing this episode. Cannot stop. episodeId: {}, email: {}", 
                        episodeId, email);
                return false;
            }
            
            // 1. 标记为已完成
            int updated = podEpisodeCustomMapper.recordEpisodePlayStop(episodeId, email);
            if (updated <= 0) {
                log.warn("No interaction record found to mark as stopped. episodeId: {}, email: {}", 
                        episodeId, email);
                return false;
            }
            
            // 2. 更新累计播放时长
            updated = podEpisodeCustomMapper.updateCumulativePlaybackSeconds(episodeId, email);
            if (updated <= 0) {
                log.warn("Failed to update cumulative playback seconds. episodeId: {}, email: {}", 
                        episodeId, email);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("Error in recordEpisodePlayStop for episodeId: {}, email: {}", 
                     episodeId, email, e);
            throw new RuntimeException("记录播放停止失败", e);
        }
    }
    

}
