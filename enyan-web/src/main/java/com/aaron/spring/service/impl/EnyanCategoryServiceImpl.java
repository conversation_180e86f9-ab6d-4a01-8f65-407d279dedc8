package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanCategoryMapper;
import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.EnyanCategoryExample;
import com.aaron.spring.service.EnyanCategoryService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/2
 * @Modified By:
 */
@Service
public class EnyanCategoryServiceImpl implements EnyanCategoryService {
    @Resource
    private EnyanCategoryMapper enyanCategoryMapper;

    @Override
    public Page queryRecords(Page<EnyanCategory> page, EnyanCategory record) {
        if (null == record){
            record = new EnyanCategory();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanCategoryExample example = new EnyanCategoryExample();
            EnyanCategoryExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            if (StringUtils.isNotBlank(record.getCategoryName())){
                //criteria.andCategoryNameEqualTo(record.getCategoryName());
                criteria.andCategoryNameLike("%"+record.getCategoryName()+"%");
            }
            if (null != record.getIsIndex()){
                criteria.andIsIndexEqualTo(record.getIsIndex());
            }

            //example.setOrderByClause("category_order desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            long count = page.getTotalRecord();
            if (count <= 0){
                count = enyanCategoryMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanCategory> list = enyanCategoryMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
                page.setTotalRecord(count);
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanCategory> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanCategory> result = new ExecuteResult<>();
        try {
            EnyanCategory record= enyanCategoryMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanCategory();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanCategory> addRecord(EnyanCategory record) {
        ExecuteResult<EnyanCategory> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanCategoryMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
                this.initCategories();
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanCategory> updateRecord(EnyanCategory record) {
        ExecuteResult<EnyanCategory> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanCategoryMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
                this.initCategories();
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanCategoryMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
                this.initCategories();
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanCategory record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (StringUtils.isBlank(record.getCategoryName())){
            sb.append("分类不可以为空");
            return sb.toString();
        }

        if (sb.length()>0){
            return sb.toString();
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanCategory record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (record.getCategoryId()==0){
            sb.append("主键信息不可以为空");
            return sb.toString();
        }
        return null;
    }

    @Override
    public void initCategories() {
        EnyanCategory enyanCategory = new EnyanCategory();
        //enyanCategory.setIsHidden(0);
        enyanCategory.addOrder(new OrderObj("category_order",InterfaceContant.OrderBy.DESC));
        List<EnyanCategory> enyanCategoryList = this.findCategories(enyanCategory);

        List<NameAndValue> valueAndNameList = new ArrayList<>();
        List<NameAndValue> valueAndNameAllList = new ArrayList<>();
        /*ValueAndName first = new ValueAndName();
        first.setName("全部图书");
        first.setValue("0");
        valueAndNameList.add(first);*/

        for (EnyanCategory category:enyanCategoryList){
            NameAndValue valueAndName = new NameAndValue();
            valueAndName.setValue(String.valueOf(category.getCategoryId()));
            valueAndName.setName(category.getCategoryName());
            valueAndName.setOther(category.getCategoryNameTc());
            valueAndName.setThird(category.getCategoryNameEn());
            if (1 == category.getIsHidden()){//隐藏的分类只存储在All内
                valueAndNameAllList.add(valueAndName);
                continue;
            }
            valueAndNameAllList.add(valueAndName);
            valueAndNameList.add(valueAndName);
        }
        Constant.categoriesAllList = valueAndNameAllList;
        Constant.categoriesList = valueAndNameList;
    }

    @Override
    public List<EnyanCategory> findCategories(EnyanCategory record) {
        List<EnyanCategory> list = null;
        if (null == record){
            record = new EnyanCategory();
        }
        try {
            EnyanCategoryExample example = new EnyanCategoryExample();
            EnyanCategoryExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(record.getCategoryName())){
                //criteria.andCategoryNameEqualTo(record.getCategoryName());
                criteria.andCategoryNameLike("%"+record.getCategoryName()+"%");
            }
            if (null != record.getIsIndex()){
                criteria.andIsIndexEqualTo(record.getIsIndex());
            }
            if (null != record.getIsHidden()){
                criteria.andIsHiddenEqualTo(record.getIsHidden());
            }
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }
            list = enyanCategoryMapper.selectByExample(example);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

}
