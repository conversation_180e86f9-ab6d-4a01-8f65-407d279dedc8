package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.exception.BusinessException;
import com.aaron.security.data.entity.ITable;
import com.aaron.security.function.domain.IRole;
import com.aaron.security.function.domain.IUser;
import com.aaron.security.function.service.UserService;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.AuthUserMapper;
import com.aaron.spring.mapper.UserInfoMapper;
import com.aaron.spring.model.AuthUser;
import com.aaron.spring.model.AuthUserExample;
import com.aaron.spring.model.CustomUserDetail;
import com.aaron.spring.model.UserInfo;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 *
 * 用于权限控制
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/29
 * @Modified By:
 */
@Service("userService")
public class UserServiceImpl implements UserService{
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String selectUser = "select * from oc_customer where email = ?";

    @Resource
    private AuthUserMapper authUserMapper;

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private JdbcTemplate userJdbcTemplate;

    //@Autowired
    //private PasswordEncoder passwordEncoder;

    @Override
    public IUser findByUsername(String username) {
        if (StringUtils.isBlank(username)){
            return null;
        }
        logger.debug("findByUsername");
        final AuthUser authUser = this.getUserByJdbc(username);
        //final AuthUser authUser = this.getUserByMyBatis(username);
        if (null == authUser){
            //return null;
            //throw new BusinessException(Integer.valueOf("001"),"usernameNotFound","userName:"+username);
            logger.debug("authUser == null");
            throw new UsernameNotFoundException("usernameNotFound");
        }
        final CustomUserDetail customUserDetail = loadCustomUserDetail(authUser);
        UserLogin login = new UserLogin(authUser, customUserDetail);
        return login;
    }

    @Override
    public Iterable<String> getAnonymousPermissionUrlPatterns() {
        return null;
    }

    @Override
    public Collection<ITable> getAnonymousDataPermissionTables() {
        return null;
    }
    /**
     *
     * 获取别的数据库的信息
     * @param userName
     * @Date: 2017/12/28
     */
    private AuthUser getUserByJdbc(String userName){
        final List<AuthUser> listUser = new ArrayList();
        /*
        userJdbcTemplate.query(selectUser,
                new Object[] { userName },
                new RowCallbackHandler() {
                    public void processRow(ResultSet resultSet) throws SQLException {
                        AuthUser u=new AuthUser();
                        u.setUserName(resultSet.getString("username"));
                        u.setUserPassword(resultSet.getString("password"));
                        u.setUserId(new Long(resultSet.getInt("user_id")));

                        int groupId = resultSet.getInt("user_group_id");
                        if (1 == groupId){
                            u.setRoleType(new Byte("1"));
                        }else {
                            u.setRoleType(new Byte("2"));
                        }
                        u.setEmail(resultSet.getString("email"));
                        //u.setSalt(resultSet.getString("salt"));
                        u.setIsActive(resultSet.getByte("status"));
                        listUser.add(u);
                    }
        });*/
        userJdbcTemplate.query(selectUser, ((ResultSet resultSet)->{
            AuthUser u=new AuthUser();
            String email = resultSet.getString("email");
            email = email == null ? null : email.trim().toLowerCase();
            u.setUserName(email);
            u.setUserPassword(resultSet.getString("password"));
            u.setUserId(Long.valueOf(resultSet.getInt("customer_id")));
            u.setSalt(resultSet.getString("salt"));

            int groupId = resultSet.getInt("customer_group_id");
            if (groupId != EBookConstant.RoleDBValue.ROLE_ADMIN_DB_VALUE){
                if (Constant.ROLE_ADMIN_SET.contains(u.getUserName())){
                    u.setRoleType(EBookConstant.RoleDBValue.ROLE_ADMIN_DB_VALUE);
                }else {
                    u.setRoleType(groupId);
                }
            }
            /*if (1 == groupId){
                u.setRoleType(new Byte("1"));
            }else {
                u.setRoleType(new Byte("2"));
            }*/
            u.setEmail(resultSet.getString("email"));
            u.setNickName(resultSet.getString("firstname"));
            u.setSex(resultSet.getInt("sex"));
            //u.setSalt(resultSet.getString("salt"));
            u.setIsActive(resultSet.getByte("approved"));
            listUser.add(u);
        }), userName);

        if (!listUser.isEmpty()){
            return listUser.get(0);
        }
        return null;
    }
    /**
     *
     * 获取本数据库的信息
     * @param userName
     * @Date: 2017/12/28
     */
    private AuthUser getUserByMyBatis(String userName){
        AuthUserExample example = new AuthUserExample();
        AuthUserExample.Criteria criteria = example.createCriteria();

        criteria.andEmailEqualTo(userName);
        List<AuthUser> listUser = authUserMapper.selectByExample(example);

        if (!listUser.isEmpty()){
            return listUser.get(0);
        }
        return null;
    }

    private CustomUserDetail loadCustomUserDetail(AuthUser authUser){
        if (EBookConstant.RoleDBValue.ROLE_VENDOR_DB_VALUE == authUser.getRoleType()){
            UserInfo info = userInfoMapper.selectByPrimaryKey(authUser.getEmail());
            if (info == null){
                return null;
            }
            if (StringUtils.isBlank(info.getInfoText())){
                return null;
            }
            CustomUserDetail customUserDetail = JSON.parseObject(info.getInfoText(),CustomUserDetail.class);
            return customUserDetail;
        }
        return null;
    }
}

class UserLogin implements IUser{
    private static final long serialVersionUID = -7302658871271064611L;
    private AuthUser authUser;
    private CustomUserDetail customUserDetail;

    public UserLogin() {
    }

    public UserLogin(AuthUser authUser, CustomUserDetail customUserDetail) {
        if (null != authUser){
            if (StringUtils.isNotBlank(authUser.getEmail())){//手动调整为全部小写
                authUser.setEmail(authUser.getEmail().toLowerCase());
            }
        }
        this.authUser = authUser;
        this.customUserDetail = customUserDetail;
    }

    @Override
    public Object getUserId() {
        return authUser.getUserId();
    }

    @Override
    public Collection<IRole> getAuthorities() {

        IRole role = new IRole() {
            private static final long serialVersionUID = 1798464644193350207L;

            @Override
            public Object getRoleId() {
                return null;
            }

            @Override
            public String getAuthority() {
                switch (authUser.getRoleType()){
                    case EBookConstant.RoleDBValue.ROLE_ADMIN_DB_VALUE:
                        return InterfaceContant.RoleName.ROLE_ADMIN;
                    case EBookConstant.RoleDBValue.ROLE_VENDOR_DB_VALUE:
                        return InterfaceContant.RoleName.ROLE_VENDOR;
                    case EBookConstant.RoleDBValue.ROLE_OPERATION_DB_VALUE:
                        return InterfaceContant.RoleName.ROLE_OPERATION;
                    case EBookConstant.RoleDBValue.ROLE_FINANCE_DB_VALUE:
                        return InterfaceContant.RoleName.ROLE_FINANCE;
                    case EBookConstant.RoleDBValue.ROLE_USER_DB_VALUE:
                        return InterfaceContant.RoleName.ROLE_USER;
                }
                return null;
            }
        };

        List<IRole> roleList = new ArrayList<>();
        roleList.add(role);

        return roleList;
    }

    @Override
    public void setAuthorities(Iterable<? extends IRole> var1) {

    }

    @Override
    public Set<String> getHasPermissionUrlPatterns() {
        return null;
    }

    @Override
    public Collection<ITable> getPermissionTables() {
        return null;
    }

    @Override
    public Object getSalt() {
        return authUser.getSalt();
    }

    @Override
    public String getEmail() {
        return authUser.getEmail();
    }

    @Override
    public String getNickName() {
        return authUser.getNickName();
    }

    @Override
    public Object getCustomDetail() {
        return customUserDetail;
    }

    @Override
    public boolean isActived() {
        return true;
    }

    @Override
    public Object getUserInfo() {
        return authUser;
    }

    @Override
    public String getPassword() {
        return authUser.getUserPassword();
    }

    @Override
    public String getUsername() {
        return authUser.getUserName();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return (Constant.BYTE_VALUE_1).equals(authUser.getIsActive());
    }

    public AuthUser getAuthUser() {
        return authUser;
    }

    public void setAuthUser(AuthUser authUser) {
        this.authUser = authUser;
    }

    public CustomUserDetail getCustomUserDetail() {
        return customUserDetail;
    }

    public void setCustomUserDetail(CustomUserDetail customUserDetail) {
        this.customUserDetail = customUserDetail;
    }
}