package com.aaron.spring.service;

import com.aaron.spring.model.EnyanRent;
import com.aaron.spring.model.EnyanRentDetail;
import com.aaron.spring.model.EnyanRentExample;
import com.aaron.spring.model.OrderPayInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
public interface EnyanRentService extends IService<EnyanRent, EnyanRentExample>{
	/**
	 * <p>根据条件查询数据列表</p>
	 * @param record
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/3
	 **/
	List<EnyanRent> findRecords(EnyanRent record);

	/**
	 * <p>根据dataId获取基本的信息</p>
	 * @param dataId
	 * @return com.aaron.spring.model.EnyanRent
	 * @since : 2022/12/14
	 **/
	EnyanRent queryRecordBaseInfoByPrimaryKey(Long dataId);

	/**
	 * <p>根据条件查询数据列表</p>
	 * @param record
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/3
	 **/
	List<EnyanRent> findRecordsWithBlob(EnyanRent record);

	/**
	 * <p>直接退订</p>
	 * @param orderNum
	 * @param isAuto
	 * @return void
	 * @since : 2022/11/4
	 **/
	void leaveRent(String orderNum, boolean isAuto);

	/**
	 * <p>根据条件查询数据 数据个数</p>
	 * @param record
	 * @return long
	 * @since : 2022/11/3
	 **/
	long countOfRecords(EnyanRent record);


	/**
	 * <p>初始化可以先租后买数据</p>
	 * @param
	 * @return void
	 * @since : 2022/11/30
	 **/
	void initRentsInfo();

	/**
	 * <p>先租后买订单已经支付</p>
	 * @param record
	 * @return EnyanRentDetail
	 * @since : 2022/11/4
	 **/
	EnyanRentDetail saveOrderHasPay(EnyanRent record, OrderPayInfo orderPayInfo, Integer toRentMonths, Date newExpireDate);

	/**
	 * <p>给租金信息添加DRM数据</p>
	 * @param rent
	 * @return void
	 * @since : 2022/12/12
	 **/
	void addDRMInfoToRent(EnyanRent rent, Date newExpireDate);

	/**
	 * <p>更新相关先租后买的license到期时间</p>
	 * @param rent
	 * @return void
	 * @since : 2022/12/20
	 **/
	void updateDRMLicenseToNewExpiredTime(EnyanRent rent, Date newExpireDate);

	/**
	 * <p>到期宽限时间之后的数据自动过期(非手动退订)</p>
	 * @param graceDays
	 * @return int
	 * @since : 2022/11/10
	 **/
	int updateRentToExpired(Integer graceDays);

	/**
	 * <p>已经订阅满36个月，昨天到期了，直接自动到期</p>
	 * @param
	 * @return int
	 * @since : 2023/1/11
	 **/
	int updateRent36MonthsYesterdayToExpired();

	/**
	 * <p>根据example 里的clause进行修改</p>
	 * <p>id,email和orderNum 不被修改，只能作为where条件</p>
	 * @param record
	 * @return int
	 * @since : 2022/11/10
	 **/
	int updateRecordByExampleClause(EnyanRent record);

	/**
	 * <p>到期宽限时间之后的数据自动过期(手动退订)</p>
	 * @param graceDays
	 * @return int
	 * @since : 2022/11/10
	 **/
	int updateRentManualToExpired(Integer graceDays);

	/**
	 * <p>仅用于初始化数据，将一些时间重置为空</p>
	 * @param
	 * @return int
	 * @since : 2022/12/6
	 **/
	int resetDateToNull();

	/**
	 * <p>处理beforeDays前正常订阅的提醒</p>
	 * @param beforeDays
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/10
	 **/
	List<EnyanRent> findRecordWillExpired(Integer beforeDays);

	/**
	 * <p>处理beforeDays前正常订阅的自动付费提醒</p>
	 * @param beforeDays
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2023/1/11
	 **/
	List<EnyanRent> findRecordAutoWillExpired(Integer beforeDays);

	/**
	 * <p>处理正常阅读状态的结束日期后afterDays后的数据</p>
	 * @param afterDays
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/10
	 **/
	List<EnyanRent> findRecordHasExpired(Integer afterDays);

	/**
	 * <p>今天结束的记录</p>
	 * @param totalMonths
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/24
	 **/
	List<EnyanRent> findRecordExpiredToday(Integer totalMonths);


	/**
	 * <p>处理现在正在阅读状态，但已经在 大于等于 afterDays 之后的所有数据</p>
	 * @param afterDays
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2023/1/11
	 **/
	List<EnyanRent> findRecordHasExpiredGEDaysAndIsValid(Integer afterDays);

	/**
	 * <p>昨天结束的记录</p>
	 * @param totalMonths
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/24
	 **/
	List<EnyanRent> findRecordExpiredYesterday(Integer totalMonths);

	/**
	 * <p>昨天退订的记录</p>
	 * @param totalMonths
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/24
	 **/
	List<EnyanRent> findRecordLeavedYesterday(Integer totalMonths);

	/**
	 * <p>所在时间结束的数据</p>
	 * @param dataAt
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/25
	 **/
	List<EnyanRent> findRecordExpiredInDate(String dataAt);

	/**
	 * <p>处理退订后afterDays后的数据</p>
	 * @param afterDays
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/10
	 **/
	List<EnyanRent> findRecordHasLeaved(Integer afterDays);

	/**
	 * <p>所在时间结束的数据</p>
	 * @param dataAt
	 * @return java.util.List<com.aaron.spring.model.EnyanRent>
	 * @since : 2022/11/25
	 **/
	List<EnyanRent> findRecordLeaveInDate(String dataAt);
}
