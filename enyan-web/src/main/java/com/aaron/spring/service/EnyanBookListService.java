package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookList;
import com.aaron.spring.model.EnyanBookListExample;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
public interface EnyanBookListService extends IService<EnyanBookList, EnyanBookListExample>{

	Page queryBookRecords(Page<EnyanBook> page, EnyanBook record, EnyanBookList bookList);

	List<EnyanBook> queryBookRecords(EnyanBookList bookList);

	/**
	 * <p>重置缓存</p>
	 * @param dataId
	 * @return void
	 * @since : 2024-09-14
	 **/
	void resetCache(Long dataId);

	/**
	 * <p>根据是否首页来获取书单书籍</p>
	 * @param index
	 * @return java.util.List<com.aaron.spring.model.EnyanBookList>
	 * @since : 2024-10-03
	 **/
	List<EnyanBookList> findBookListByIndex(Integer index);
}
