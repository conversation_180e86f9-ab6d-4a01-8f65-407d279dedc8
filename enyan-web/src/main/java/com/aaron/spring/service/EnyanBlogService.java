package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.model.EnyanBlogExample;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/1/21
 * @Modified By:
 */
public interface EnyanBlogService extends IService<EnyanBlog, EnyanBlogExample>{
	/**
	 * <p></p>
	 * @param page
	 * @param record
	 * @return com.aaron.mybatis.dao.pojo.Page
	 * @since : 2022/1/21
	 **/
	Page queryRecordsBasic(Page<EnyanBlog> page, EnyanBlog record);

	/**
	 * <p>增加喜欢量</p>
	 * @param id
	 * @return int
	 * @since : 2022/1/21
	 **/
	int updateBlogLikeCountById(Long id);

	/**
	 * <p>增加阅读量</p>
	 * @param id
	 * @return int
	 * @since : 2022/1/21
	 **/
	int updateBlogReadCountById(Long id);
}
