package com.aaron.spring.service;

import com.aaron.spring.model.EnyanSpirit;
import com.aaron.spring.model.EnyanSpiritExample;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/13/20
 * @Modified By:
 */
public interface EnyanSpiritService extends IService<EnyanSpirit, EnyanSpiritExample>{

    List<EnyanSpirit> findAllSpirits();

    /**
     * <p>上架的所有书籍</p>
     * @param
     * @return: java.util.List<com.aaron.spring.model.EnyanSpirit>
     * @since : 12/7/20
     */
    List<EnyanSpirit> findAllSpiritsOnShelf();

    /**
     * <p>根据bookId获取灵修书籍</p>
     * @param bookId
     * @return: com.aaron.spring.model.EnyanSpirit
     * @since : 12/2/20
     */
    EnyanSpirit getSpiritByBookId(Long bookId);

    /**
     *
     * 根据主键修改个别属性
     * @param enyanSpirit
     * @Date: 2018/1/3
     */
    void updateByPrimaryKeySelective(EnyanSpirit enyanSpirit, Long bookId);
}
