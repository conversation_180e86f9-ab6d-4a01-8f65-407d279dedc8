package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EmailHistoryMapper;
import com.aaron.spring.mapper.custom.EmailHistoryCustomMapper;
import com.aaron.spring.model.EmailHistory;
import com.aaron.spring.model.EmailHistoryExample;
import com.aaron.spring.service.EmailHistoryService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: Aaron Ha<PERSON>
 * @Date: Created in  2018/3/2
 * @Modified By:
 */
@Service
public class EmailHistoryServiceImpl implements EmailHistoryService{

    @Resource
    private EmailHistoryMapper emailHistoryMapper;

    @Resource
    private EmailHistoryCustomMapper emailHistoryCustomMapper;

    @Override
    public Page queryRecords(Page<EmailHistory> page, EmailHistory record) {
        if (null == record){
            record = new EmailHistory();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EmailHistoryExample example = new EmailHistoryExample();
            EmailHistoryExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            long count = page.getTotalRecord();
            if (count<=0){
                count = emailHistoryMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EmailHistory> list = emailHistoryMapper.selectByExample(example);

            if (null == page){
                page = new Page<>();
            }

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EmailHistory> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EmailHistory> result = new ExecuteResult<>();
        try {
            EmailHistory record = emailHistoryMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EmailHistory();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EmailHistory> addRecord(EmailHistory record) {
        ExecuteResult<EmailHistory> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }

            int saveFlag = emailHistoryMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EmailHistory> updateRecord(EmailHistory record) {
        ExecuteResult<EmailHistory> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = emailHistoryMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = emailHistoryMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EmailHistory record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EmailHistory record) {
        return null;
    }

    @Override
    public ExecuteResult<String> refreshEmailHistory(long id) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = emailHistoryCustomMapper.refreshEmailHistory(id,System.currentTimeMillis());
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EmailHistory> getEmailHistoryByName(String email, Integer emailType, Integer yearMonthDay) {

        ExecuteResult<EmailHistory> result = new ExecuteResult<>();
        try {
            EmailHistoryExample example = new EmailHistoryExample();
            EmailHistoryExample.Criteria criteria = example.createCriteria();

            criteria.andEmailEqualTo(email);
            if (null != emailType){
                criteria.andEmailTypeEqualTo(emailType);
            }
            if (null != yearMonthDay){
                criteria.andYearMonthDayEqualTo(yearMonthDay);
            }

            example.setOrderByClause("send_at desc");
            List<EmailHistory> list = emailHistoryMapper.selectByExample(example);

            if (!list.isEmpty()){
                if (list.size()>0){
                    result.setResult(list.get(0));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }
}
