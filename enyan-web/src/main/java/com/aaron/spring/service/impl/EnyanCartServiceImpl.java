package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.Money;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanCartMapper;
import com.aaron.spring.mapper.custom.EnyanCartCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanCartService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @Author: Aaron Ha<PERSON>
 * @Description:
 * @Date: Created in  2021/4/28
 * @Modified By:
 */
@Service
public class EnyanCartServiceImpl implements EnyanCartService {

    @Resource
    private EnyanCartMapper enyanCartMapper;

    @Resource
    private EnyanCartCustomMapper enyanCartCustomMapper;

    @Override
    public ExecuteResult<String> deleteByEmailAndBookId(String email, Long bookId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanCartCustomMapper.deleteByEmailAndBookId(email, bookId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteByExample(EnyanCart record) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            EnyanCartExample example = new EnyanCartExample();
            EnyanCartExample.Criteria criteria = example.createCriteria();

            criteria.andBookIdEqualTo(record.getBookId());
            criteria.andUserEmailEqualTo(record.getUserEmail());

            int deleteFlag = enyanCartMapper.deleteByExample(example);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public Page queryCarts(Page<EnyanBook> page, EnyanCart enyanCart) {
        if (null == enyanCart){
            enyanCart = new EnyanCart();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanCartExample example = new EnyanCartExample();
            //EnyanWishExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanCartCustomMapper.searchCountByExample(enyanCart,example);
                page.setTotalRecord(count);
            }
            List<EnyanBook> list = enyanCartCustomMapper.searchByExample(enyanCart,example);

            for (EnyanBook enyanBook:list){
                if (null == enyanBook.getPriceHkd()){
                    continue;
                }
                Money money = Money.hkds(enyanBook.getPriceHkd());
                //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
                if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                            &&enyanBook.getDiscountSingleValue()>0){
                    //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                    //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                    enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
                }
            }

            if (!list.isEmpty()){
                page.setRecords(list);
                page.setTotalRecord(count);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public List<EnyanBook> searchAllByEmail(String email) {
        List<EnyanBook> list =  enyanCartCustomMapper.searchAllByEmail(email);
        for (EnyanBook enyanBook:list){
            if (null == enyanBook.getPriceHkd()){
                continue;
            }
            Money money = Money.hkds(enyanBook.getPriceHkd());
            //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
            if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                        &&enyanBook.getDiscountSingleValue()>0){
                //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
            }
        }
        return list;
    }

    @Override
    public int deleteCartsAllByEmail(String email) {
        return enyanCartCustomMapper.deleteCartsAllByEmail(email);
    }

    @Override
    public int deleteCarts(String email, List<Long> ids) {
        return enyanCartCustomMapper.deleteCarts(email, ids.toArray(new Long[0]));
    }

    @Override
    public long countOfCartByEmail(String email) {
        return enyanCartCustomMapper.countOfCartByEmail(email);
    }

    @Override
    public long countOfCartByEmailAndBookId(String email, Long bookId) {
        return enyanCartCustomMapper.countOfCartByEmailAndBookId(email, bookId);
    }

    @Override
    public ExecuteResult<String> revokeUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = enyanCartCustomMapper.deleteCartsAllByEmail(email);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public Page queryRecords(Page<EnyanCart> page, EnyanCart record) {
        if (null == record){
            record = new EnyanCart();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanCartExample example = new EnyanCartExample();
            EnyanCartExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            criteria.andBookIdEqualTo(record.getBookId());
            criteria.andUserEmailEqualTo(record.getUserEmail());
            //example.setOrderByClause("category_order desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanCartMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanCart> list = enyanCartMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanCart> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanCart> result = new ExecuteResult<>();
        try {
            EnyanCart record= enyanCartMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanCart();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanCart> addRecord(EnyanCart record) {
        ExecuteResult<EnyanCart> result = new ExecuteResult<>();
        try {
            if (StringUtils.isBlank(record.getCartId())){
                record.setCartId(UUID.randomUUID().toString());
            }
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag=0;
            try {
                saveFlag = enyanCartCustomMapper.addCart(record);
                if (saveFlag>0){
                    result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                    result.setResult(record);
                }else {
                    result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
                }
            } catch (DuplicateKeyException e) {
                //result.addErrorMessage("合同名称不能重复");
                /*
                if (saveFlag>0){
                    result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                    result.setResult(record);
                }else {
                    result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
                }*/
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanCart> updateRecord(EnyanCart record) {
        ExecuteResult<EnyanCart> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanCartMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanCartMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanCart record) {
     return null;
    }

    @Override
    public String checkUpdateRecord(EnyanCart record) {
     return null;
    }
}
