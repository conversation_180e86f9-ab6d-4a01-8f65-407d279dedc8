package com.aaron.spring.service;

import com.aaron.drm.model.DrmInfo;
import com.aaron.drm.model.LcpInfo;
import com.aaron.drm.model.Licenses;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.*;
import com.aaron.util.ExecuteResult;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/23
 * @Modified By:
 */
public interface EnyanBookBuyService extends IService<EnyanBookBuy, EnyanBookBuyExample> {

    /**
     *
     * 分离某订单
     * @param enyanOrder
     * @Date: 2021-08-02
     */
    void splitOrder(EnyanOrder enyanOrder);

    /**
     * @param record
     *
     *
     * @Date: 2018/11/30
     */
    List<EnyanBookBuy> searchRecordsByUserList(EnyanBookBuy record);

    /**
     * <p></p>
     * @param bookBuy
     * @return java.util.List<com.aaron.spring.model.EnyanBookBuy>
     * @since : 2023/1/6
     **/
    List<EnyanBookBuy> findRecordsByBookBuy(EnyanBookBuy bookBuy);

    /**
     * @param page
     * @param record
     *
     * 按照订单方式查看明细
     * @Date: 2018/4/26
     */
    Page queryRecordsByOrder(Page<EnyanBookBuy> page, EnyanBookBuy record);

    /**
     *
     *
     * @param page
     * @param record
     * @Date: 2019-10-29
     */
    Page searchRecordsByUserAndBookAndAuthorList(Page<EnyanBookBuy> page, EnyanBookBuy record);

    /**
     * @param
     *
     *
     * @Date: 2019-06-09
     */
    void resetOrderDetailAndPurchaseInfo();

    /**
     * @param bookBuy
     *
     * 获取所有的订单详情
     * @Date: 2019-06-09
     */
    //List<EnyanBookBuy> findAllOrderDetailList(EnyanBookBuy bookBuy);

    /**
     * @param purchaseId
     *
     *
     * @Date: 2019-06-10
     */
    Licenses getLicenseByPurchaseId(int purchaseId);
    /**
     *
     * 根据LicenseID获取License JSON信息
     * @param licenseId
     * @Date: 2019-09-24
     */
    String getLicenseStringByLicenseId(String licenseId);

    /**
     *
     *  根据 licenseId 下载文件
     * @param drmInfo
     * @Date: 2019-06-10
     */
    byte[] downloadLcpFiles(DrmInfo drmInfo);

    /**
     * <p>根据 licenseId, userId 下载文件</p>
     * @param lcpInfo
     * @return byte[]
     * @since : 2022/12/13
     **/
    byte[] downloadLcpFiles(LcpInfo lcpInfo);

    /**
     * <p>根据email获取bookId及bookName，，用于我的图书</p>
     * @param email
     * @return: java.util.List<com.aaron.spring.model.EnyanOrderDetail>
     * @since : 2020-11-05
     */
    List<EnyanBookBuy> findBookIDAndNameByEmail(String email);

    /**
     * <p>根据email获取bookId及bookName，App图片链接，分组，用于app的书架</p>
     * @param email
     * @return: java.util.List<com.aaron.spring.model.EnyanOrderDetail>
     * @since : 2021-12-08
     */
    List<EnyanBookBuy> findBookBaseInfoByEmail(String email);

    /**
     * <p>根据bookId获取bookId、bookName及email，用于预售</p>
     *  * @param bookId
     * @return: java.util.List<com.aaron.spring.model.EnyanOrderDetail>
     * @since : 2021/3/16
     */
    List<EnyanBookBuy> findBookIDAndNameAndEmailByBookID(Long bookId);

    /**
     * <p>根据email及bookId获取书籍信息，用于灵修书籍是否购买</p>
     * @param email
     * @param bookId
     * @return: com.aaron.spring.model.EnyanOrderDetail
     * @since : 12/7/20
     */
    List<EnyanBookBuy> getBookIDAndNameByEmailAndBookId(String email, Long bookId);

    /**
     * <p>根据email及bookId获取书籍信息</p>
     * @param email
     * @param bookId
     * @return java.util.List<com.aaron.spring.model.EnyanBookBuy>
     * @since : 2021/6/3
     **/
    List<EnyanBookBuy> findBookBuyListByEmailAndBookId(String email, Long bookId);

    /**
     * <p></p>
     * @param orderNum
     * @param bookId
     * @return java.util.List<com.aaron.spring.model.EnyanBookBuy>
     * @since : 2022/5/10
     **/
    List<EnyanBookBuy> findBookBuyListByOrderNumAndBookId(String orderNum, Long bookId);
    /**
     * <p>是否有这本书</p>
     * @param email
     * @param bookId
     * @return long
     * @since : 2021/6/10
     **/
    long countOfBookByEmailAndBookId(String email, Long bookId);

    int updateReadInfoByEmailAndBookId(EnyanBookBuy bookBuy);

    int updateGroupNameByEmailAndBookId(EnyanBookBuy bookBuy);

    int updateGroupNameByEmailAndBookIds(EnyanBookBuy bookBuy, List<Long> idList);

    int updateReadInfoAndGroupNameByEmailAndBookId(EnyanBookBuy bookBuy);

    /**
     * <p>回收书籍</p>
     * @param orderNum
     * @param bookId
     * @return int
     * @since : 2022/5/10
     **/
    int recycleBookByOrderNumAndBookId(String orderNum, Long bookId);

    /**
     * <p>在bookbuy删除，并清空license</p>
     * @param bookBuy
     * @return int
     * @since : 2023/1/6
     **/
    int delBookBuyAndLicenseByBookBuy(EnyanBookBuy bookBuy);

    /**
     * <p>注销用户</p>
     * @param email
     * @param revokedEmail
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2022/8/26
     **/
    ExecuteResult<String> revokeUser(String email, String revokedEmail);

    /**
     * <p>从订单信息导入书籍阅读列表</p>
     * @param
     * @return void
     * @since : 2023/1/4
     **/
    void importDataFromOrderDetail(EnyanOrderDetail orderDetail);

    /**
     * <p>从兑换码信息导入到书籍</p>
     * @param redeemCode
     * @return void
     * @since : 2023/1/6
     **/
    void importDataFromRedeemCode(EnyanRedeemCode redeemCode);

    /**
     * <p>ids 中已经购买的书籍</p>
     * @param email
     * @param ids
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-12-23
     **/
    List<EnyanBook> findBookListHasBuyByEmailAndIds(String email, Long[] ids);
}
