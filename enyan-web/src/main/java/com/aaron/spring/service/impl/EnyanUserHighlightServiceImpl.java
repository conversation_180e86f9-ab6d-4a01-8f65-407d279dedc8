package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanUserHighlightsMapper;
import com.aaron.spring.mapper.custom.EnyanUserHighlightsCustomMapper;
import com.aaron.spring.model.EnyanUserHighlights;
import com.aaron.spring.model.EnyanUserHighlightsExample;
import com.aaron.spring.service.EnyanUserHighlightService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: Aaron Ha<PERSON>
 * @Date: Created in  2019-09-04
 * @Modified By:
 */
@Service
public class EnyanUserHighlightServiceImpl extends BaseService<EnyanUserHighlights, EnyanUserHighlightsExample> implements EnyanUserHighlightService {
    @Resource
    private EnyanUserHighlightsMapper enyanUserHighlightsMapper;

    @Resource
    private EnyanUserHighlightsCustomMapper enyanUserHighlightsCustomMapper;

    @Override
    public List<EnyanUserHighlights> findEnyanUserHighlightsList(EnyanUserHighlights record) {
        if (null == record){
            record = new EnyanUserHighlights();
        }
        try {
            EnyanUserHighlightsExample example = new EnyanUserHighlightsExample();
            EnyanUserHighlightsExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            criteria.andUpdateTimeGreaterThanOrEqualTo(record.getUpdateTime());

            List<EnyanUserHighlights> list = enyanUserHighlightsMapper.selectByExample(example);

            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Page findUserHighlightsGTUpdateTime(Page<EnyanUserHighlights> page, EnyanUserHighlights record) {
        if (null == record){
            record = new EnyanUserHighlights();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanUserHighlightsExample example = new EnyanUserHighlightsExample();
            EnyanUserHighlightsExample.Criteria criteria = example.createCriteria();
            example.setPage(page);
            example.setOrderByClause("update_time asc");

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            criteria.andUpdateTimeGreaterThan(record.getUpdateTime());

            List<EnyanUserHighlights> list = enyanUserHighlightsMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public Page queryRecords(Page<EnyanUserHighlights> page, EnyanUserHighlights record) {
        if (null == record){
            record = new EnyanUserHighlights();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanUserHighlightsExample example = new EnyanUserHighlightsExample();
            EnyanUserHighlightsExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanUserHighlightsMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanUserHighlights> list = enyanUserHighlightsMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanUserHighlights> queryRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanUserHighlights> queryRecordByPrimaryKey(String pkId) {
        ExecuteResult<EnyanUserHighlights> result = new ExecuteResult<>();
        try {
            EnyanUserHighlights record= enyanUserHighlightsMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanUserHighlights();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<Boolean> addHighlights(List<EnyanUserHighlights> list) {
        ExecuteResult<Boolean> result = new ExecuteResult<>();
        for (EnyanUserHighlights tmp : list){
            if (StringUtils.isBlank(tmp.getUserEmail())){
                continue;
            }
            EnyanUserHighlights highlight = enyanUserHighlightsMapper.selectByPrimaryKey(tmp.getHighlightId());
            if (null == highlight){
                try {
                    enyanUserHighlightsCustomMapper.add(tmp);
                }catch (DuplicateKeyException e){//因已添加索引

                }
            }else {
                if (!tmp.getUserEmail().equals(highlight.getUserEmail())){
                    continue;
                }
                EnyanUserHighlights newHighlight = new EnyanUserHighlights();
                newHighlight.setIsDeleted(tmp.getIsDeleted());
                newHighlight.setHighlightId(tmp.getHighlightId());
                newHighlight.setUpdateTime(tmp.getUpdateTime());
                newHighlight.setNoteForHighlight(tmp.getNoteForHighlight());
                newHighlight.setRangy(tmp.getRangy());
                enyanUserHighlightsMapper.updateByPrimaryKeySelective(newHighlight);
            }
        }
        return result;
    }
}
