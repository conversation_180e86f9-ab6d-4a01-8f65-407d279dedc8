/**
 * 
 */
package com.aaron.spring.service.page;

import java.util.List;

/**
 *<p>
 * Title: QST Teach
 * </p>
 * 
 * <p>
 * Description: QST
 * </p>
 * 
 * <p>
 * Copyright: Copyright (c) 2015
 * </p>
 * 
 * <p>
 * Company: QST
 * </p>
 *
 * <AUTHOR>
 *
 */
public class PageList {
	private List objectList;
	private Pages pages;

	public PageList() {
	}

	public void setObjectList(List objectList) {
		this.objectList = objectList;
	}

	public void setPages(Pages pages) {
		this.pages = pages;
	}

	public List getObjectList() {
		return objectList;
	}

	public Pages getPages() {
		return pages;
	}

	/**
	 * @param args
	 */
	public static void main(String[] args) {

	}

}
