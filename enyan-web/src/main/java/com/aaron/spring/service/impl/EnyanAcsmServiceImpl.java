package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanAcsmMapper;
import com.aaron.spring.model.EnyanAcsm;
import com.aaron.spring.model.EnyanAcsmExample;
import com.aaron.spring.service.EnyanAcsmService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: Aaron <PERSON>
 * @Date: Created in  2018/3/13
 * @Modified By:
 */
@Service
public class EnyanAcsmServiceImpl implements EnyanAcsmService {

    @Resource
    private EnyanAcsmMapper enyanAcsmMapper;

    @Override
    public Page queryRecords(Page<EnyanAcsm> page, EnyanAcsm record) {
        if (null == record){
            record = new EnyanAcsm();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanAcsmExample example = new EnyanAcsmExample();
            EnyanAcsmExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            //example.setOrderByClause("category_order desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanAcsmMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanAcsm> list = enyanAcsmMapper.selectByExample(example);

            if (!list.isEmpty()){
                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }
    @Override
    public List<EnyanAcsm> findEnyanAcsmList(EnyanAcsm enyanAcsm){
        if (null == enyanAcsm){
            enyanAcsm = new EnyanAcsm();
        }

        try {
            EnyanAcsmExample example = new EnyanAcsmExample();
            EnyanAcsmExample.Criteria criteria = example.createCriteria();

            if (0 != enyanAcsm.getBookId()){
                criteria.andBookIdEqualTo(enyanAcsm.getBookId());
            }
            if (null != enyanAcsm.getOrderNum()){
                criteria.andOrderNumEqualTo(enyanAcsm.getOrderNum());
            }
            if (0 != enyanAcsm.getUserId()){
                criteria.andUserIdEqualTo(enyanAcsm.getUserId());
            }
            if (null != enyanAcsm.getIsFulfilled()){
                criteria.andIsFulfilledEqualTo(enyanAcsm.getIsFulfilled());
            }
            if (null != enyanAcsm.getTransactionId()){
                criteria.andTransactionIdEqualTo(enyanAcsm.getTransactionId());
            }
            //example.setOrderByClause("category_order desc");
            if (null != enyanAcsm.getOrderObjList()){
                for (OrderObj orderObj:enyanAcsm.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            List<EnyanAcsm> list = enyanAcsmMapper.selectByExample(example);

            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    @Override
    public ExecuteResult<EnyanAcsm> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanAcsm> result = new ExecuteResult<>();
        try {
            EnyanAcsm record= enyanAcsmMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanAcsm();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanAcsm> addRecord(EnyanAcsm record) {
        ExecuteResult<EnyanAcsm> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag;
            try {
                saveFlag = enyanAcsmMapper.insert(record);
                if (saveFlag>0){
                    result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                    result.setResult(record);
                }else {
                    result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
                }
            } catch (DuplicateKeyException e) {
                //result.addErrorMessage("合同名称不能重复");
                /*
                if (saveFlag>0){
                    result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                    result.setResult(record);
                }else {
                    result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
                }*/
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanAcsm> updateRecord(EnyanAcsm record) {
        ExecuteResult<EnyanAcsm> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanAcsmMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanAcsmMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanAcsm record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanAcsm record) {
        return null;
    }
}
