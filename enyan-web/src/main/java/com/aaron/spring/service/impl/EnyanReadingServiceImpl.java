package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBanner;
import com.aaron.spring.api.RestReading;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanReadingMapper;
import com.aaron.spring.mapper.custom.EnyanReadingCustomMapper;
import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanReading;
import com.aaron.spring.model.EnyanReading;
import com.aaron.spring.model.EnyanReadingExample;
import com.aaron.spring.service.EnyanReadingService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/6/23
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanReadingServiceImpl implements EnyanReadingService {

	@Resource
	private EnyanReadingMapper enyanReadingMapper;

	@Resource
	private EnyanReadingCustomMapper enyanReadingCustomMapper;

	@Override
	public Page queryRecords(Page<EnyanReading> page, EnyanReading record) {
		if (null == record){
			record = new EnyanReading();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanReadingExample example = new EnyanReadingExample();
			EnyanReadingExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			if (StringUtils.isNotBlank(record.getBlogTitle())){
				criteria.andBlogTitleLike("%"+record.getBlogTitle()+"%");
			}*/
			if (StringUtils.isNotBlank(record.getDataName())){
				criteria.andDataNameLike("%"+record.getDataName()+"%");
			}
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			criteria.andIsDeletedEqualTo(0);
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanReadingMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanReading> list;
			if (count > 0){
				list = enyanReadingMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<EnyanReading> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanReading> result = new ExecuteResult<>();
		try {
			EnyanReading record = enyanReadingMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanReading();
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanReading> addRecord(EnyanReading record) {
		ExecuteResult<EnyanReading> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = enyanReadingMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanReading> updateRecord(EnyanReading record) {
		ExecuteResult<EnyanReading> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanReadingMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = EnyanReadingMapper.deleteByPrimaryKey(pkId);
			int deleteFlag = enyanReadingCustomMapper.updateRecordToDeletedById(pkId);
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanReading record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanReading record) {
		return null;
	}

	@Override
	public void initReadings() {
		/*
		List<EnyanReading> readingList = enyanReadingCustomMapper.findRecords();
		//Date currentDate = new Date();
		List<RestReading> readings = new ArrayList<>();
		for (EnyanReading obj:readingList){
			if (null == obj.getEndAt()){
				continue;
			}
//			if (obj.getEndAt().compareTo(currentDate) < 0){
//				continue;
//			}
			RestReading rest = new RestReading();
			rest.initFrom(obj);
			if (obj.getDataReadShow() == 1){
				readings.add(rest);
			}
		}
		Constant.DEFAULT_REST_CONFIG.setReadings(readings);*/
	}

	@Override
	public void resetReadings() {
		enyanReadingCustomMapper.updateRecordToExpired();
		this.initReadings();
	}
}
