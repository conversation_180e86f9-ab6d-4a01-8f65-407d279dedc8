package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.StuEnrollMapper;
import com.aaron.spring.mapper.custom.StuEnrollCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.StuEnrollService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/2/13
 * @Modified By:
 */
@Slf4j
@Service
public class StuEnrollServiceImpl implements StuEnrollService {
	@Resource
	private StuEnrollMapper stuEnrollMapper;

	@Resource
	private StuEnrollCustomMapper stuEnrollCustomMapper;

	@Override
	public Page queryRecords(Page<StuEnroll> page, StuEnroll record) {
		if (null == record){
			record = new StuEnroll();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			StuEnrollExample example = new StuEnrollExample();
			StuEnrollExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			*/
			criteria.andTermsEqualTo(EBookConstant.CheckIn.TERM);
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			long count = page.getTotalRecord();
			if (count<=0){
				count = stuEnrollMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<StuEnroll> list;
			if (count > 0){
				list = stuEnrollMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}
			if (list.isEmpty() == false){
				Long current = System.currentTimeMillis();
				for (StuEnroll obj:list){
					if (StringUtils.isNotBlank(obj.getCheckin())){
						StuCheckinInfo stuCheckinInfo = JSON.parseObject(obj.getCheckin(),StuCheckinInfo.class);
						stuCheckinInfo.resetData(current);
						obj.setStuCheckinInfo(stuCheckinInfo);
					}
				}
			}
			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<StuEnroll> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<StuEnroll> result = new ExecuteResult<>();
		try {
			StuEnroll record = stuEnrollMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new StuEnroll();
			}
			Long current = System.currentTimeMillis();
			if (StringUtils.isNotBlank(record.getCheckin())){
				StuCheckinInfo stuCheckinInfo = JSON.parseObject(record.getCheckin(),StuCheckinInfo.class);
				stuCheckinInfo.resetData(current);
				record.setStuCheckinInfo(stuCheckinInfo);
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<StuEnroll> addRecord(StuEnroll record) {
		ExecuteResult<StuEnroll> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = stuEnrollMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<StuEnroll> updateRecord(StuEnroll record) {
		ExecuteResult<StuEnroll> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = stuEnrollMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = StuEnrollCustomMapper.updateRecordToDeletedById(pkId);
			int deleteFlag = 0;
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(StuEnroll record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(StuEnroll record) {
		return null;
	}

	@Override
	public List<StuEnroll> findRecordsInCurrentTerms() {
		List<StuEnroll> list = stuEnrollCustomMapper.findRecordByTerms(EBookConstant.CheckIn.TERM);
		if (list.isEmpty() == false){
			Long current = System.currentTimeMillis();
			for (StuEnroll obj:list){
				if (StringUtils.isNotBlank(obj.getCheckin())){
					StuCheckinInfo stuCheckinInfo = JSON.parseObject(obj.getCheckin(),StuCheckinInfo.class);
					stuCheckinInfo.resetData(current);
					obj.setStuCheckinInfo(stuCheckinInfo);
				}
			}
		}
		return list;
	}

	@Override
	public StuEnroll getRecordByEmail(String email) {
		List<StuEnroll> list = stuEnrollCustomMapper.getRecordByEmail(EBookConstant.CheckIn.TERM,email);
		if (list.isEmpty() == false){
			Long current = System.currentTimeMillis();
			StuEnroll obj = list.get(0);
			if (StringUtils.isNotBlank(obj.getCheckin())){
				StuCheckinInfo stuCheckinInfo = JSON.parseObject(obj.getCheckin(),StuCheckinInfo.class);
				stuCheckinInfo.resetData(current);
				obj.setStuCheckinInfo(stuCheckinInfo);
			}
			return obj;
		}
		return null;
	}

	@Override
	public StuEnroll getRecordByEnrollCode(String enrollCode) {
		List<StuEnroll> list = stuEnrollCustomMapper.getRecordByEnrollCode(EBookConstant.CheckIn.TERM,enrollCode);
		if (list.isEmpty() == false){
			Long current = System.currentTimeMillis();
			StuEnroll obj = list.get(0);
			if (StringUtils.isNotBlank(obj.getCheckin())){
				StuCheckinInfo stuCheckinInfo = JSON.parseObject(obj.getCheckin(),StuCheckinInfo.class);
				stuCheckinInfo.resetData(current);
				obj.setStuCheckinInfo(stuCheckinInfo);
			}
			return obj;
		}
		return null;
	}

	@Override
	public void updateRecordToEnrollByEmail(StuEnroll enroll, Integer status, Date enrollAt) {
//		StuEnroll enroll = new StuEnroll();
//		enroll.setEmail(email);
		enroll.setEnrollStatus(1);
		enroll.setEnrollAt(enrollAt);

		List<StuCheckin> checkinList = enroll.getStuCheckinInfo().getCheckinList().stream().filter(obj -> obj.getType() == Integer.parseInt(EBookConstant.CheckIn.Type.ENROLL)).toList();
		if (checkinList.isEmpty() == false){
			StuCheckin checkin = checkinList.get(0);
			checkin.setTime(enrollAt.getTime());
			checkin.setStatus(status);
		}
		String json = JSON.toJSONString(enroll.getStuCheckinInfo());
		enroll.setCheckin(json);

		stuEnrollCustomMapper.updateRecordToEnrollByEmail(EBookConstant.CheckIn.TERM,enroll);
	}

	@Override
	public void updateRecordToEnrollByEnrollCode(StuEnroll enroll, Integer status, Date enrollAt) {
//		StuEnroll enroll = new StuEnroll();
//		enroll.setEnrollCode(enrollCode);
		enroll.setEnrollStatus(1);
		enroll.setEnrollAt(enrollAt);

		List<StuCheckin> checkinList = enroll.getStuCheckinInfo().getCheckinList().stream().filter(obj -> obj.getType() == Integer.parseInt(EBookConstant.CheckIn.Type.ENROLL)).toList();
		if (checkinList.isEmpty() == false){
			StuCheckin checkin = checkinList.get(0);
			checkin.setTime(enrollAt.getTime());
			checkin.setStatus(status);
		}
		String json = JSON.toJSONString(enroll.getStuCheckinInfo());
		enroll.setCheckin(json);

		stuEnrollCustomMapper.updateRecordToEnrollByEnrollCode(EBookConstant.CheckIn.TERM,enroll);
	}

	@Override
	public void updateRecordToCheckinByEmail(StuEnroll enroll, Integer type, Integer status, Long time) {
//		StuEnroll enroll = new StuEnroll();
//		enroll.setEmail(email);

		List<StuCheckin> checkinList = enroll.getStuCheckinInfo().getCheckinList().stream().filter(obj -> obj.getType() == type).toList();
		if (checkinList.isEmpty() == false){
			StuCheckin checkin = checkinList.get(0);
			checkin.setTime(time);
			checkin.setStatus(status);
		}
		String json = JSON.toJSONString(enroll.getStuCheckinInfo());
		enroll.setCheckin(json);

		stuEnrollCustomMapper.updateRecordToCheckinByEmail(EBookConstant.CheckIn.TERM,enroll);
	}

	@Override
	public void updateRecordToCheckinByEnrollCode(StuEnroll enroll, Integer type, Integer status, Long time) {
//		StuEnroll enroll = new StuEnroll();
//		enroll.setEnrollCode(enrollCode);

		List<StuCheckin> checkinList = enroll.getStuCheckinInfo().getCheckinList().stream().filter(obj -> obj.getType() == type).toList();
		if (checkinList.isEmpty() == false){
			StuCheckin checkin = checkinList.get(0);
			checkin.setTime(time);
			checkin.setStatus(status);
		}
		String json = JSON.toJSONString(enroll.getStuCheckinInfo());
		enroll.setCheckin(json);

		stuEnrollCustomMapper.updateRecordToCheckinByEmail(EBookConstant.CheckIn.TERM,enroll);
	}

}
