package com.aaron.spring.service;

import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanBannerExample;
import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.model.EnyanBlogExample;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/6/23
 * @Modified By:
 */
public interface EnyanBannerService extends IService<EnyanBanner, EnyanBannerExample>{
	/**
	 * <p>初始化banner</p>
	 * @param
	 * @return void
	 * @since : 2022/6/27
	 **/
	void initBanners();

	void resetBanners();
}
