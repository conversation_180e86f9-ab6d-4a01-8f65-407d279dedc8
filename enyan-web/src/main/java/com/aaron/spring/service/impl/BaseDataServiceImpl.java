package com.aaron.spring.service.impl;

import com.aaron.common.NameAndValue;
import com.aaron.spring.common.Constant;
import com.aaron.spring.service.BaseDataService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/16
 * @Modified By:
 */
@Service
public class BaseDataServiceImpl implements BaseDataService{
    @Override
    public String getCategoryNameById(String id) {
        if (StringUtils.isBlank(id)){
            return null;
        }
        String categoryName = null;
        for (NameAndValue valueAndName : Constant.categoriesAllList){
            if (id.equals(valueAndName.getValue())){
                categoryName = valueAndName.getName();
                break;
            }
        }
        return categoryName;
    }

    @Override
    public String getPublisherNameById(String id) {
        if (StringUtils.isBlank(id)){
            return null;
        }
        String value = null;
        for (NameAndValue valueAndName : Constant.publishersList){
            if (id.equals(valueAndName.getValue())){
                value = valueAndName.getName();
                break;
            }
        }
        return value;
    }
}
