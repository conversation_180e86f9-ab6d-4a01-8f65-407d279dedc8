package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.data.DataInterface;
import com.aaron.drm.util.DRMUtil;
import com.aaron.exception.BusinessException;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.security.function.encoder.SaltPasswordEncoder;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.AuthUserMapper;
import com.aaron.spring.mapper.UserInfoMapper;
import com.aaron.spring.mapper.UserMapper;
import com.aaron.spring.mapper.custom.UserCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.AuthUserService;
import com.aaron.util.ExecuteResult;
import com.aaron.util.PasswordUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.keygen.KeyGenerators;
import org.springframework.security.crypto.keygen.StringKeyGenerator;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/10/31
 * @Modified By:
 */
@Service
public class AuthUserServiceImpl  implements AuthUserService{

    @Resource
    private AuthUserMapper authUserMapper;

    @Resource
    private JdbcTemplate userJdbcTemplate;

    @Resource
    private JdbcTemplate eBookJdbcTemplate;

    @Autowired
    private SaltPasswordEncoder saltPasswordEncoder;

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserCustomMapper userCustomMapper;

    @Resource
    private DataInterface dataInterface;

    private final StringKeyGenerator saltGenerator = KeyGenerators.string();

    //INSERT INTO bg.oc_user (user_group_id, username, password, salt, firstname, lastname, email, code, ip, status, date_added) VALUES
    // (1, 'admin', 'e1c21037a34f95b877b1bd6e88dbf8a5d891c48b', 'wwwEnyanE', 'admin', 'a', '<EMAIL>', '11', '11', 1, '2017-12-28 10:29:10.008000');
    //private static final String insertUser = "INSERT INTO oc_user (user_group_id, username, password, salt, firstname, lastname, email, code, ip, status, date_added) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static final String insertUser = "INSERT INTO oc_customer (store_id, firstname, lastname, email, telephone, fax, password, salt, cart, wishlist, newsletter, address_id, customer_group_id, ip, status, approved, token, date_added) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String selectUserByName = "select * from oc_customer where firstname = ?";

    private static final String selectUserByEmail = "select * from oc_customer where email = ?";

    private static final String selectUserByID = "select * from oc_customer where customer_id = ?";

    private static final String activeUserByEmail = "UPDATE oc_customer SET approved = 1, verified = 1 where email = ?";

    private static final String updateUserPwdByEmail = "UPDATE oc_customer SET password = ?, salt = ?, approved = 1, verified = 1 where email = ?";

    private static final String updateUserNameByEmail = "UPDATE oc_customer SET firstname = ? where email = ?";

    private static final String updateUserSexByEmail = "UPDATE oc_customer SET sex = ? where email = ?";

    private static final String updateUserBirthdayByEmail = "UPDATE oc_customer SET birthday = ? where email = ?";

    private static final String revokeUserByEmail = "UPDATE oc_customer SET email = ? where email = ?";

    private static final String delLcpPurchaseByLcpUserId = "delete from  purchase where user_id = ?";

    private static final String delLcpLicenseByLcpUserUuid = "delete from  license where user_id = ?";

    private static final String selectUserByVendor = "select * from oc_customer where customer_group_id = 4"; //所有的供应商

    /**
     * 修改用户的登录email
     * */
    private static final String changeUserEmail = "update oc_customer set email = ?, password = 'e1c21037a34f95b877b1bd6e88dbf8a5d891c48b',salt = 'wwwEnyanE' where email = ? ";

    /**
     * 修改用户的Order email
     * */
    private static final String changeUserOrderEmail = "update enyan_order set user_email = ? where user_email = ? ";

    /**
     * 修改用户的Order Detail email
     * */
    private static final String changeUserOrderDetailEmail = "update enyan_order_detail set user_email = ? where user_email = ? ";

    /**
     * 修改用户的BookBuy email
     * */
    private static final String changeUserBookBuyEmail = "update enyan_book_buy set user_email = ? where user_email = ? ";

    /**
     * 修改用户的Lcp user email
     * */
    private static final String changeUserLcpEmail = "update user set email = ?, password = ? where email = ? ";

    /**
     * 修改用户的 userInfo email
     * */
    private static final String changeUserInfoEmail = "update user_info set username = ? where username = ? ";

    /**
     * 修改用户的 rent email
     * */
    private static final String changeUserRentEmail = "update enyan_rent set user_email = ? where user_email = ? ";

    /**
     * 修改用户的 rent detail email
     * */
    private static final String changeUserRentDetailEmail = "update enyan_rent_detail set user_email = ? where user_email = ? ";

    @Override
    public Page queryRecords(Page<AuthUser> page, AuthUser authUser) {
        if (null == authUser){
            authUser = new AuthUser();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            AuthUserExample example = new AuthUserExample();
            AuthUserExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(authUser.getUserName())){
                criteria.andUserNameLike(authUser.getUserName());
            }
            if (StringUtils.isNotBlank(authUser.getEmail())){
                criteria.andEmailLike(authUser.getEmail());
            }
            /*if (null != authUser.getRoleType()){
                criteria.andRoleTypeEqualTo(authUser.getRoleType());
            }*/
            if (null != authUser.getIsStaff()){
                criteria.andIsStaffEqualTo(authUser.getIsStaff());
            }
            if (null != authUser.getIsActive()){
                criteria.andIsStaffEqualTo(authUser.getIsActive());
            }

            long count = authUserMapper.countByExample(example);
            List<AuthUser> list = authUserMapper.selectByExample(example);
            if (!list.isEmpty()){
                page.setRecords(list);
                page.setTotalRecord(count);
            }else {
                page.setRecords(new ArrayList<>());
            }
        }catch (Exception e){
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<AuthUser> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        try {
            AuthUser authUser = authUserMapper.selectByPrimaryKey(pkId);
            if (null == authUser){
                authUser = new AuthUser();
            }
            result.setResult(authUser);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<AuthUser> addRecord(AuthUser record) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            //record.setIsActive(new Byte("0"));
            record.setDateJoined(new Date());

            int saveFlag = authUserMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return result;
    }

    @Override
    public ExecuteResult<AuthUser> updateRecord(AuthUser record) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = authUserMapper.updateByPrimaryKeySelective(record);
            if (saveFlag<0){
                result.addErrorMessage("保存用户信息失败");
            }else {
                result.setSuccessMessage("保存用户信息成功");
                result.setResult(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = authUserMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag<0){
                result.addErrorMessage("删除用户失败");
            }else {
                result.setResult("删除用户成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(AuthUser record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (StringUtils.isBlank(record.getUserName())){
            sb.append("用户名不可以为空");
            return sb.toString();
        }
        if (StringUtils.isBlank(record.getUserPassword())||StringUtils.isBlank(record.getUserPasswordAgain())){
            sb.append("密码不可以为空");
            return sb.toString();
        }

        if (!PasswordUtil.isValidPassword(record.getUserPassword())){
            sb.append("密码必须为长度大于8，并且包含特殊字符");
            return sb.toString();
        }
        if (!record.getUserPassword().equals(record.getUserPasswordAgain())){
            sb.append("请确认您输入的密码");
            return sb.toString();
        }
        if (StringUtils.isBlank(record.getEmail())){
            sb.append("email不可以为空");
            return sb.toString();
        }

        if (StringUtils.isBlank(record.getNickName())){
            sb.append("昵称不可以为空");
            return sb.toString();
        }
        if (sb.length()>0){
            return sb.toString();
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(AuthUser record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (null == record.getUserId() || record.getUserId()<=0){
            sb.append("主键信息不可以为空");
            return sb.toString();
        }
        if (sb.length()>0){
            return sb.toString();
        }
        return null;
    }


    @Override
    public ExecuteResult<AuthUser> reg(AuthUser record) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        try {
            //校验保存对象
            /*
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }*/
            record.setEmail(record.getEmail().toLowerCase());////手动调整为全部小写
            String salt = saltGenerator.generateKey().substring(0,9);
            String password = saltPasswordEncoder.encodePassword(record.getUserPassword(),salt);
            //INSERT INTO bg.oc_user (user_group_id, username, password, salt, firstname, lastname, email, code, ip, status, date_added) VALUES
            // (1, 'admin', 'e1c21037a34f95b877b1bd6e88dbf8a5d891c48b', 'wwwEnyanE', 'admin', 'a', '<EMAIL>', '11', '11', 1, '2017-12-28 10:29:10.008000');

            //INSERT INTO oc_customer (store_id, firstname, lastname, email, telephone, fax, password, salt, cart, wishlist, newsletter, address_id, customer_group_id, ip, status, approved, token, date_added) VALUES
            // (0, 'aaron', 'hao', '<EMAIL>', '13918647061', '', 'e1c21037a34f95b877b1bd6e88dbf8a5d891c48b', 'wwwEnyanE', '', '', 1, 0, 1, '', 1, 1, '', '2017-04-16 03:57:28');

            /*
            int saveFlag = userJdbcTemplate.update(insertUser,((PreparedStatement ps)->{
                ps.setInt(1,2);
                ps.setString(2,record.getUserName());
                ps.setString(3,password);
                ps.setString(4,salt);
                ps.setString(5,"");
                ps.setString(6,"");
                ps.setString(7,record.getEmail());
                ps.setString(8,"");//code
                ps.setString(9,"");//ip
                ps.setByte(10,new Byte("1"));
                ps.setTimestamp(11,new Timestamp(new Date().getTime()));

            }));*/
            int saveFlag = userJdbcTemplate.update(insertUser,((PreparedStatement ps)->{
                ps.setInt(1,0);//store_id
                ps.setString(2,record.getUserName());//firstname
                ps.setString(3,"");//lastname
                ps.setString(4,record.getEmail().toLowerCase());//, email
                ps.setString(5,"");// telephone
                ps.setString(6,"");// fax
                ps.setString(7,password);// password
                ps.setString(8,salt);//salt
                ps.setString(9,"");//cart
                ps.setString(10,"");//wishlist
                ps.setByte(11,Constant.BYTE_VALUE_0);//newsletter 0
                ps.setInt(12,0);//address_id
                ps.setInt(13,1);//, customer_group_id
                ps.setString(14,"");//, ip
                ps.setByte(15,Constant.BYTE_VALUE_1);//, status 1
                ps.setByte(16,Constant.BYTE_VALUE_0);//, approved 0
                ps.setString(17,"");//, token
                ps.setTimestamp(18,new Timestamp(new Date().getTime()));// date_added

            }));
            //saveFlag = authUserMapper.updateByPrimaryKeySelective(record);
            if (saveFlag<0){
                result.addErrorMessage("保存用户信息失败");
            }else {
                result.setSuccessMessage("保存用户信息成功");
                result.setResult(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<AuthUser> updateInfo(AuthUser authUser) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();


        return result;
    }

    @Override
    public ExecuteResult<AuthUser> getUserByName(String userName) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        try {
            final List<AuthUser> listUser = new ArrayList();
            userJdbcTemplate.query(selectUserByName,((ResultSet resultSet)->{
                AuthUser u=new AuthUser();
                this.initUserInfo(u,resultSet);
                listUser.add(u);
            }),userName);
            if (!listUser.isEmpty()){
                result.setResult(listUser.get(0));
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<AuthUser> getUserByEmail(String email) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        try {
            final List<AuthUser> listUser = new ArrayList();
            userJdbcTemplate.query(selectUserByEmail,((ResultSet resultSet)->{
                AuthUser u=new AuthUser();
                this.initUserInfo(u,resultSet);
                listUser.add(u);
            }),email);
            if (!listUser.isEmpty()){
                result.setResult(listUser.get(0));
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<AuthUser> getUserByID(Long userId) {
        ExecuteResult<AuthUser> result = new ExecuteResult<>();
        try {
            final List<AuthUser> listUser = new ArrayList();
            userJdbcTemplate.query(selectUserByID,((ResultSet resultSet)->{
                AuthUser u=new AuthUser();
                this.initUserInfo(u,resultSet);
                listUser.add(u);
            }),userId);
            if (!listUser.isEmpty()){
                result.setResult(listUser.get(0));
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String getUserNameByEmail(String email) {
        AuthUser authUser = this.getUserByEmail(email).getResult();
        if (null != authUser){
            return StringUtils.isNotBlank(authUser.getUserName())?authUser.getUserName():email;
        }
        return email;
    }

    @Override
    public List<AuthUser> findAllVendorUsers() {
        final List<AuthUser> result = new ArrayList();
        try {
            userJdbcTemplate.query(selectUserByVendor,((ResultSet resultSet)->{
                AuthUser u=new AuthUser();
                this.initUserInfo(u,resultSet);
                result.add(u);
            }));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public ExecuteResult<String> activeUser(String email) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = userJdbcTemplate.update(activeUserByEmail,new Object[]{email});
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> updatePasswd(AuthUser authUser) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            String salt = saltGenerator.generateKey().substring(0,9);
            String password = saltPasswordEncoder.encodePassword(authUser.getUserPassword(),salt);
            int saveFlag = userJdbcTemplate.update(updateUserPwdByEmail,new Object[]{password,salt,authUser.getEmail()});
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> updateName(AuthUser authUser) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = userJdbcTemplate.update(updateUserNameByEmail,new Object[]{authUser.getNickName(),authUser.getEmail()});
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> updateSex(AuthUser authUser) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = userJdbcTemplate.update(updateUserSexByEmail,new Object[]{authUser.getSex(),authUser.getEmail()});
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> updateBirthday(AuthUser authUser) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = userJdbcTemplate.update(updateUserBirthdayByEmail,new Object[]{authUser.getBirthday(),authUser.getEmail()});
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> revokeUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            userInfoMapper.deleteByPrimaryKey(email);
            dataInterface.delUserInfo(email);
            int saveFlag = userJdbcTemplate.update(revokeUserByEmail,new Object[]{revokedEmail,email});
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> changeUserEmail(String oldEmail, String newEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            dataInterface.delUserInfo(oldEmail);
            result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            /*
            Map valuesMap = new HashMap();
            valuesMap.put("email", email);
            valuesMap.put("bookId", bookId.toString());
            StringSubstitutor sub = new StringSubstitutor(valuesMap);*/

            String passwordLcp = DRMUtil.getDefaultHintPasswd(newEmail);
            userJdbcTemplate.update(changeUserEmail, new Object[]{newEmail,oldEmail});
            eBookJdbcTemplate.update(changeUserOrderEmail, new Object[]{newEmail,oldEmail});
            eBookJdbcTemplate.update(changeUserOrderDetailEmail, new Object[]{newEmail,oldEmail});
            eBookJdbcTemplate.update(changeUserBookBuyEmail, new Object[]{newEmail,oldEmail});
            eBookJdbcTemplate.update(changeUserLcpEmail, new Object[]{newEmail,passwordLcp,oldEmail});
            eBookJdbcTemplate.update(changeUserInfoEmail, new Object[]{newEmail,oldEmail});
            eBookJdbcTemplate.update(changeUserRentEmail, new Object[]{newEmail,oldEmail});
            eBookJdbcTemplate.update(changeUserRentDetailEmail, new Object[]{newEmail,oldEmail});

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> revokeLcpUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            UserExample example = new UserExample();
            UserExample.Criteria criteria = example.createCriteria();

            criteria.andEmailEqualTo(email);
            List<User> list = userMapper.selectByExample(example);
            if (null == list || list.isEmpty()){
                //record = new EnyanBook();
                return result;
            }
            //获取LCP User，Lcp的用户信息就不删除了，之后还可以重复利用；purchase和License信息都删除
            User user = list.get(0);
            int saveFlag = eBookJdbcTemplate.update(delLcpPurchaseByLcpUserId,new Object[]{user.getId()});
            eBookJdbcTemplate.update(delLcpLicenseByLcpUserUuid,new Object[]{user.getUuid()});
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public boolean isPasswordValid(String encPass, String rawPass, Object salt) {
        return saltPasswordEncoder.isPasswordValid(encPass,rawPass,salt);
    }

    @Override
    public UserInfo loadUserInfoByEmail(String email) {
        if (StringUtils.isBlank(email)){
            return null;
        }
        UserInfo info = dataInterface.getUserInfoByEmail(email);
        if (null != info){
            return info;
        }
        info = userInfoMapper.selectByPrimaryKey(email);
        if (info == null){
            return null;
        }
        if (StringUtils.isBlank(info.getInfoText())){
            CustomUserDetail customUserDetail = new CustomUserDetail();
            customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
            customUserDetail.setDeviceLimitList(new ArrayList<>());
            info.setCustomUserDetail(customUserDetail);
        }else {
            CustomUserDetail customUserDetail = JSON.parseObject(info.getInfoText(),CustomUserDetail.class);
            if (null != customUserDetail){
                if (null == customUserDetail.getDeviceLimitList()){
                    customUserDetail.setDeviceLimitList(new ArrayList<>());
                }
                if(null == customUserDetail.getCartInfoGerenal()){
                    customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
                }
            }
            info.setCustomUserDetail(customUserDetail);
        }

        dataInterface.saveUserInfo(info);
        return info;
    }

    @Override
    public void changeUserInfoPubsByEmail(String email) {
        if (StringUtils.isBlank(email)){
            return;
        }
        UserInfo info = dataInterface.getUserInfoByEmail(email);
        if (null == info){
            info = userInfoMapper.selectByPrimaryKey(email);
        }
        if (info == null){
            return;
        }
        if (StringUtils.isBlank(info.getInfoText())){
            CustomUserDetail customUserDetail = new CustomUserDetail();
            customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
            customUserDetail.setDeviceLimitList(new ArrayList<>());
            info.setCustomUserDetail(customUserDetail);
        }else {
            CustomUserDetail customUserDetail = JSON.parseObject(info.getInfoText(),CustomUserDetail.class);
            if (null != customUserDetail){
                if (null == customUserDetail.getDeviceLimitList()){
                    customUserDetail.setDeviceLimitList(new ArrayList<>());
                }
                if(null == customUserDetail.getCartInfoGerenal()){
                    customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
                }
            }
            List<String> publisherIdList = new ArrayList<>();
            if (customUserDetail.getPublisherId() > 0){
                publisherIdList.add(customUserDetail.getPublisherId()+"");
            }
            customUserDetail.setPublisherIdList(publisherIdList);
            info.setInfoText(JSONObject.toJSONString(customUserDetail));
            info.setCustomUserDetail(customUserDetail);
        }

        dataInterface.delUserInfo(email);
        userInfoMapper.updateByPrimaryKeySelective(info);
    }

    @Override
    public List<UserInfo> findUserInfos() {
        UserInfoExample example = new UserInfoExample();
        List<UserInfo> list = userInfoMapper.selectByExampleWithBLOBs(example);
        for (UserInfo info:list){
            if (StringUtils.isBlank(info.getInfoText())){
                CustomUserDetail customUserDetail = new CustomUserDetail();
                customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
                customUserDetail.setDeviceLimitList(new ArrayList<>());
                info.setCustomUserDetail(customUserDetail);
            }else {
                CustomUserDetail customUserDetail = JSON.parseObject(info.getInfoText(),CustomUserDetail.class);
                if (null != customUserDetail){
                    if (null == customUserDetail.getDeviceLimitList()){
                        customUserDetail.setDeviceLimitList(new ArrayList<>());
                    }
                    if(null == customUserDetail.getCartInfoGerenal()){
                        customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
                    }
                }
                info.setCustomUserDetail(customUserDetail);
            }
        }
        return list;
    }

    @Override
    public ExecuteResult<UserInfo> addUserInfo(UserInfo userInfo) {
        ExecuteResult<UserInfo> result = new ExecuteResult<>();
        try {
            CustomUserDetail customUserDetail = userInfo.getCustomUserDetail();
            if (null != customUserDetail){
                if (null == customUserDetail.getCartInfoGerenal()){
                    customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
                }
                if (null == customUserDetail.getDeviceLimitList()){
                    customUserDetail.setDeviceLimitList(new ArrayList<>());
                }
                userInfo.setInfoText(JSONObject.toJSONString(customUserDetail));
            }
            int saveFlag = userInfoMapper.insert(userInfo);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(userInfo);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
            dataInterface.saveUserInfo(userInfo);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return result;
    }

    @Override
    public ExecuteResult<UserInfo> updateUserInfo(UserInfo userInfo) {
        ExecuteResult<UserInfo> result = new ExecuteResult<>();
        try {
            CustomUserDetail customUserDetail = userInfo.getCustomUserDetail();
            if (null != customUserDetail){
                if (null == customUserDetail.getCartInfoGerenal()){
                    customUserDetail.setCartInfoGerenal(new CartInfoGerenal());
                }
                if (null == customUserDetail.getDeviceLimitList()){
                    customUserDetail.setDeviceLimitList(new ArrayList<>());
                }
                userInfo.setInfoText(JSONObject.toJSONString(customUserDetail));
            }
            int saveFlag = userInfoMapper.updateByPrimaryKeySelective(userInfo);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
                result.setResult(userInfo);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
            dataInterface.delUserInfo(userInfo.getUsername());
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<User> addLcpUser(User user) {
        ExecuteResult<User> result = new ExecuteResult<>();
        try {
            //校验保存对象　
//            String checkMsg = this.checkSaveRecord(record);
//            if (StringUtils.isNotBlank(checkMsg)){
//                result.addErrorMessage("保存校验失败："+ checkMsg);
//                return result;
//            }
            //record.setIsActive(new Byte("0"));
            //record.setDateJoined(new Date());
            if (StringUtils.isNotBlank(user.getName())){
                user.setName(user.getName().toLowerCase());
            }
            if (StringUtils.isNotBlank(user.getEmail())){
                user.setEmail(user.getEmail().toLowerCase());
            }
            int saveFlag = userMapper.insert(user);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(user);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }

        return result;
    }

    @Override
    public ExecuteResult<User> getLcpUserById(int id) {
        ExecuteResult<User> result = new ExecuteResult<>();
        try {

            User record = userMapper.selectByPrimaryKey(id);
            if (null == record){
                //record = new EnyanBook();
                return result;
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public ExecuteResult<User> getLcpUserByEmail(String email) {
        ExecuteResult<User> result = new ExecuteResult<>();
        try {
            UserExample example = new UserExample();
            UserExample.Criteria criteria = example.createCriteria();

            criteria.andEmailEqualTo(email);
            List<User> list = userMapper.selectByExample(example);
            if (null == list || list.isEmpty()){
                //record = new EnyanBook();
                return result;
            }
            result.setResult(list.get(0));
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public User getLcpUserByLicenseId(String licenseId) {
        try {
            User record = userCustomMapper.selectByLicenseId(licenseId);
            return record;
        } catch (Exception e) {
            e.printStackTrace();
            //result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return null;
    }

    @Override
    public ExecuteResult<User> updateLcpUserPasswd(User record) {
        ExecuteResult<User> result = new ExecuteResult<>();
        try {
            //校验保存对象　
//            String checkMsg = this.checkUpdateRecord(record);
//            if (StringUtils.isNotBlank(checkMsg)){
//                result.addErrorMessage("保存校验失败："+ checkMsg);
//                return result;
//            }
            int saveFlag = userMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    @Override
    public List<User> findAllLCPUsers() {
        try {
            UserExample example = new UserExample();

            List<User> list = userMapper.selectByExample(example);
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     *
     * 将ResultSet 转为AuthUser
     * @param u
     * @param resultSet
     * @Date: 2017/12/29
     */
    private void initUserInfo(AuthUser u,ResultSet resultSet) throws SQLException {
        u.setUserId((long) resultSet.getInt("customer_id"));
        u.setUserName(resultSet.getString("firstname"));
        u.setSex(resultSet.getInt("sex"));
        u.setBirthday(resultSet.getString("birthday"));

        String email = resultSet.getString("email");
        email = email == null ? null : email.trim().toLowerCase();
        u.setEmail(email);
        u.setUserPassword(resultSet.getString("password"));
        u.setSalt(resultSet.getString("salt"));
        int groupId = resultSet.getInt("customer_group_id");
        if (1 == groupId){
            u.setRoleType(Constant.BYTE_VALUE_1);
        }else {
            u.setRoleType(Constant.BYTE_VALUE_2);
        }
        u.setIsActive(resultSet.getByte("approved"));
    }
}
