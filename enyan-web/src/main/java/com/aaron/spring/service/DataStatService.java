package com.aaron.spring.service;

import com.aaron.spring.model.DataStat;
import com.aaron.spring.model.DataStatExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/9/26
 * @Modified By:
 */
public interface DataStatService extends IService<DataStat, DataStatExample>{
	/**
	 * <p>存储昨天的数据(不包含兑换码兑换订单)</p>
	 * @param
	 * @return void
	 * @since : 2021/9/26
	 **/
	DataStat saveDataYesterday();

	/**
	 * <p>修改昨天的数据</p>
	 * @param yyyyMMdd
	 * @return void
	 * @since : 2021/9/26
	 **/
	void updateDataInDay(String yyyyMMdd);


	/**
	 * <p>获取根据Day展示的列表数据</p>
	 * @param beginTime
	 * @param endTime
	 * @return java.util.List<com.aaron.spring.model.DataStat>
	 * @since : 2021/9/27
	 **/
	List<DataStat> findDataByDay(String beginTime, String endTime);

	/**
	 * <p>获取根据Month展示的列表数据</p>
	 * @param beginTime
	 * @param endTime
	 * @return java.util.List<com.aaron.spring.model.DataStat>
	 * @since : 2021/9/27
	 **/
	List<DataStat> findDataByMonth(String beginTime, String endTime);

	/**
	 * <p>获取根据Year展示的列表数据</p>
	 * @param beginTime
	 * @param endTime
	 * @return java.util.List<com.aaron.spring.model.DataStat>
	 * @since : 2021/9/27
	 **/
	List<DataStat> findDataByYear(String beginTime, String endTime);

	/**
	 * <p>基础数据（总注册用户数，总下单数）</p>
	 * @param
	 * @return com.aaron.spring.model.DataStat
	 * @since : 2021/9/27
	 **/
	DataStat basicInfo();
}
