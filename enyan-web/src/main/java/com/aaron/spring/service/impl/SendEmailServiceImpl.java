package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.entity.Mail;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.EnyanRent;
import com.aaron.spring.service.EmailService;
import com.aaron.spring.service.SendEmailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/11/11
 * @Modified By:
 */
@Slf4j
@Service
public class SendEmailServiceImpl implements SendEmailService {
	@Resource
	protected MessageSource messageSource;

	@Resource
	protected EmailService emailService;

	//final Locale locale_EN = org.springframework.util.StringUtils.parseLocale(InterfaceContant.LocaleLang.ENG);
	//final Locale locale_SC = org.springframework.util.StringUtils.parseLocale(InterfaceContant.LocaleLang.SC);
	//final Locale locale_TC = org.springframework.util.StringUtils.parseLocale(InterfaceContant.LocaleLang.TC);
	@Override
	public void sendMailOfRedeemCode(EnyanRedeemCode redeemCode, String lang) {

	}

	@Override
	public void sendMailOfRent(EnyanRent rent, String mailCode) {
		if (null == rent.getBeginAt() || StringUtils.isBlank(mailCode)){//开始的时间
			return;
		}
		log.debug("sendMailOfRent mailCode={}, orderNum={}",mailCode,rent.getOrderNum());
		Map<String,Object> model = new HashMap<>();
		Mail mail = new Mail();
		mail.setModel(model);
		mail.setTo(rent.getUserEmail());
		mail.setFrom(Constant.EMAIL_FROM);

		if (null != rent.getRentLang()){//语言类型，1：简体；2：繁体；3：英语；
			switch (rent.getRentLang()){
				case EBookConstant.RentLang.Hans:
					mail.setLocale(Locale.SIMPLIFIED_CHINESE);
					break;
				case EBookConstant.RentLang.Hant:
					mail.setLocale(Locale.TRADITIONAL_CHINESE);
					//mail.setLocale(Locale.forLanguageTag(InterfaceContant.LocaleLang.TC));
					break;
				case EBookConstant.RentLang.Eng:
					mail.setLocale(Locale.US);
					break;
			}
		}

		model.put("emailToName",rent.getUserEmail());

		String rentName = BookUtil.getBookNameInRentType(rent.getRentType(), rent.getRentLang());
		model.put("rentName",rentName);

		String rentAuto = BookUtil.getRentAutoByAuto(rent.getIsAuto());
		model.put("rentAuto",rentAuto);

		String rentFee = BookUtil.getRentPrice(rent.getRentType(), rent.getIsAuto()).setScale(0).toString();
		model.put("rentFee",rentFee);

		if (null != rent.getExpiredAt()){
			model.put("expiredAt", DateFormatUtils.format(rent.getExpiredAt(), InterfaceContant.DateFormatCustom.DATE));
		}

		Date purchasedAt = rent.getBeginAt();
		Date currentDate = new Date();
		Date next;
		switch (mailCode){
			case EBookConstant.MailCode.RENT_0101:
				mail.setFtl("email/mail_rent_0101");
				mail.setSubject("先租后买订阅即将到期");
				break;
			case EBookConstant.MailCode.RENT_0102:
				mail.setSubject("先租后买自动扣费提醒");
				mail.setFtl("email/mail_rent_0102");
				//获取自动扣费下次扣费时间
				next = DateUtils.addDays(rent.getExpiredAt(), -1);
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0103:
				mail.setSubject("先租后买订阅即将到期");
				mail.setFtl("email/mail_rent_0103");
				break;
			case EBookConstant.MailCode.RENT_0201:
				mail.setSubject("先租后买即将自动退订");
				mail.setFtl("email/mail_rent_0201");
				//续订截止日期
				next = DateUtils.addDays(rent.getExpiredAt(), 3);
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0202:
				mail.setSubject("先租后买自动扣费失败");
				mail.setFtl("email/mail_rent_0202");
				//续订截止日期
				next = DateUtils.addDays(rent.getExpiredAt(), 3);
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0301:
				mail.setSubject("先租后买订阅已到期");
				mail.setFtl("email/mail_rent_0301");
				//获取使用优惠期限
				next = DateUtils.addMonths(rent.getLeaveAt(), 1);//
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0302:
				mail.setSubject("先租后买订阅已到期");
				mail.setFtl("email/mail_rent_0302");
				//获取使用优惠期限
				next = DateUtils.addMonths(rent.getLeaveAt(), 1);//
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0401:
				mail.setSubject("先租后买订单详情");
				mail.setFtl("email/mail_rent_0401");
				//${orderNum},${orderTime},${totalMonths},${expiredAt},${totalFee}
				model.put("orderNum", rent.getOrderNum());
				model.put("orderTime", DateFormatUtils.format(rent.getBeginAt(), InterfaceContant.DateFormatCustom.DATE));
				//将detail里的这两个数据填充到rent里
				model.put("totalMonths", rent.getTotalMonths());
				model.put("totalFee", rent.getTotalFee().setScale(2));
				break;
			case EBookConstant.MailCode.RENT_0402:
				mail.setSubject("先租后买订单详情");
				mail.setFtl("email/mail_rent_0402");
				//${orderNum},${orderTime},${totalMonths},${expiredAt},${totalFee}
				model.put("orderNum", rent.getOrderNum());
				model.put("orderTime", DateFormatUtils.format(rent.getBeginAt(), InterfaceContant.DateFormatCustom.DATE));
				//将detail里的这两个数据填充到rent里
				model.put("totalMonths", rent.getTotalMonths());
				model.put("totalFee", rent.getTotalFee().setScale(2));
				//获取自动扣费下次扣费时间
				next = DateUtils.addDays(rent.getExpiredAt(), -1);
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0501:
				mail.setSubject("先租后买续订详情");
				mail.setFtl("email/mail_rent_0501");
				//${orderNum},${orderTime},${totalMonths},${expiredAt},${totalFee}
				model.put("orderNum", rent.getOrderNum());
				model.put("orderTime", DateFormatUtils.format(rent.getBeginAt(), InterfaceContant.DateFormatCustom.DATE));
				//将detail里的这两个数据填充到rent里
				model.put("totalMonths", rent.getTotalMonths());
				model.put("totalFee", rent.getTotalFee().setScale(2));
				break;
			case EBookConstant.MailCode.RENT_0502:
				mail.setSubject("先租后买续订详情");
				mail.setFtl("email/mail_rent_0502");
				//${orderNum},${orderTime},${totalMonths},${expiredAt},${totalFee}
				model.put("orderNum", rent.getOrderNum());
				model.put("orderTime", DateFormatUtils.format(rent.getBeginAt(), InterfaceContant.DateFormatCustom.DATE));
				//将detail里的这两个数据填充到rent里
				model.put("totalMonths", rent.getTotalMonths());
				model.put("totalFee", rent.getTotalFee().setScale(2));
				//获取自动扣费下次扣费时间
				next = DateUtils.addDays(rent.getExpiredAt(), -1);
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_06:
				mail.setSubject("先租后买已退订，请尽快使用购书优惠");
				mail.setFtl("email/mail_rent_06");
				//获取使用优惠期限
				next = DateUtils.addMonths(rent.getExpiredAt(), 1);//
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0701:
				mail.setSubject("先租后买购书优惠即将到期");
				mail.setFtl("email/mail_rent_0701");
				//获取使用优惠期限
				next = DateUtils.addMonths(rent.getExpiredAt(), 1);//
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
			case EBookConstant.MailCode.RENT_0702:
				mail.setSubject("先租后买购书优惠即将到期");
				mail.setFtl("email/mail_rent_0702");
				//获取使用优惠期限
				next = DateUtils.addMonths(rent.getExpiredAt(), 1);//
				model.put("nextAt", DateFormatUtils.format(next, InterfaceContant.DateFormatCustom.DATE));
				break;
		}
		emailService.sendMessageUsingThymeleafTemplate(mail);
	}
}
