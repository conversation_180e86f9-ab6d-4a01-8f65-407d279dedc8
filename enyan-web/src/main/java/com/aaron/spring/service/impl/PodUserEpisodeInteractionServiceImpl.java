package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.PodUserEpisodeInteractionMapper;
import com.aaron.spring.model.PodUserEpisodeInteraction;
import com.aaron.spring.model.PodUserEpisodeInteractionExample;
import com.aaron.spring.service.PodUserEpisodeInteractionService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description: 用户播客单集交互服务实现类
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Slf4j
@Service
public class PodUserEpisodeInteractionServiceImpl implements PodUserEpisodeInteractionService {

    @Resource
    private PodUserEpisodeInteractionMapper podUserEpisodeInteractionMapper;

    @Override
    public Page queryRecords(Page<PodUserEpisodeInteraction> page, PodUserEpisodeInteraction record) {
        if (null == record){
            record = new PodUserEpisodeInteraction();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            PodUserEpisodeInteractionExample example = new PodUserEpisodeInteractionExample();
            PodUserEpisodeInteractionExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            if (null != record.getUserId()){
                criteria.andUserIdEqualTo(record.getUserId());
            }
            if (null != record.getEpisodeId()){
                criteria.andEpisodeIdEqualTo(record.getEpisodeId());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (null != record.getIsLiked()){
                criteria.andIsLikedEqualTo(record.getIsLiked());
            }
            if (null != record.getIsCompleted()){
                criteria.andIsCompletedEqualTo(record.getIsCompleted());
            }
            
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            } else {
                // 默认按最后播放时间倒序排序
                example.setOrderByClause("last_played_at DESC");
            }
            
            long count = page.getTotalRecord();
            if (count<=0){
                count = podUserEpisodeInteractionMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<PodUserEpisodeInteraction> list;
            if (count > 0){
                list = podUserEpisodeInteractionMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }

            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<PodUserEpisodeInteraction> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<PodUserEpisodeInteraction> result = new ExecuteResult<>();
        try {
            PodUserEpisodeInteraction record = podUserEpisodeInteractionMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new PodUserEpisodeInteraction();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<PodUserEpisodeInteraction> addRecord(PodUserEpisodeInteraction record) {
        ExecuteResult<PodUserEpisodeInteraction> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            
            // 设置创建时间为当前时间
            record.setCreatedAt(new Date());

            int saveFlag = podUserEpisodeInteractionMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<PodUserEpisodeInteraction> updateRecord(PodUserEpisodeInteraction record) {
        ExecuteResult<PodUserEpisodeInteraction> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = podUserEpisodeInteractionMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = podUserEpisodeInteractionMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(PodUserEpisodeInteraction record) {
        if (null == record) {
            return "交互数据不能为空";
        }
        if (null == record.getUserId()) {
            return "用户ID不能为空";
        }
        if (null == record.getEpisodeId()) {
            return "单集ID不能为空";
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(PodUserEpisodeInteraction record) {
        if (null == record) {
            return "交互数据不能为空";
        }
        if (null == record.getInteractionId()) {
            return "交互ID不能为空";
        }
        return null;
    }

    @Override
    public ExecuteResult<PodUserEpisodeInteraction> recordPlayProgress(Long userId, Long episodeId, Integer progressSeconds) {
        ExecuteResult<PodUserEpisodeInteraction> result = new ExecuteResult<>();
        
        // 参数校验
        if (null == userId || null == episodeId || null == progressSeconds) {
            result.addErrorMessage("用户ID、单集ID和播放进度不能为空");
            return result;
        }
        
        try {
            // 查询是否存在记录
            PodUserEpisodeInteractionExample example = new PodUserEpisodeInteractionExample();
            PodUserEpisodeInteractionExample.Criteria criteria = example.createCriteria();
            criteria.andUserIdEqualTo(userId);
            criteria.andEpisodeIdEqualTo(episodeId);
            
            List<PodUserEpisodeInteraction> interactions = podUserEpisodeInteractionMapper.selectByExample(example);
            
            PodUserEpisodeInteraction interaction;
            if (interactions != null && !interactions.isEmpty()) {
                // 更新已有记录
                interaction = interactions.get(0);
                
                // 记录累计播放时间
                int oldProgress = interaction.getPlaybackProgressSeconds() != null ? interaction.getPlaybackProgressSeconds() : 0;
                int cumulativeSeconds = interaction.getCumulativePlaybackSeconds() != null ? 
                        interaction.getCumulativePlaybackSeconds() : 0;
                
                // 如果新的进度大于旧的进度，增加累计播放时间
                if (progressSeconds > oldProgress) {
                    cumulativeSeconds += (progressSeconds - oldProgress);
                }
                
                interaction.setPlaybackProgressSeconds(progressSeconds);
                interaction.setCumulativePlaybackSeconds(cumulativeSeconds);
                interaction.setLastPlayedAt(new Date());
                
                // 根据业务需求判断是否完成播放（例如进度超过90%）
                if (progressSeconds > 0) {
                    interaction.setIsCompleted(1);
                }
                
                return this.updateRecord(interaction);
            } else {
                // 创建新记录
                interaction = new PodUserEpisodeInteraction();
                interaction.setUserId(userId);
                interaction.setEpisodeId(episodeId);
                interaction.setPlaybackProgressSeconds(progressSeconds);
                interaction.setCumulativePlaybackSeconds(progressSeconds);
                interaction.setLastPlayedAt(new Date());
                interaction.setIsLiked(0);
                interaction.setIsCompleted(0);
                
                return this.addRecord(interaction);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        
        return result;
    }

    @Override
    public Page queryUserPlayHistory(Page<PodUserEpisodeInteraction> page, Long userId) {
        if (null == userId){
            page.setErrorMessage("用户ID不能为空");
            return page;
        }
        
        PodUserEpisodeInteraction record = new PodUserEpisodeInteraction();
        record.setUserId(userId);
        
        // 按最后播放时间倒序排序
        OrderObj orderObj = new OrderObj("last_played_at", InterfaceContant.OrderBy.DESC);
        record.addOrder(orderObj);
        
        return this.queryRecords(page, record);
    }

    @Override
    public ExecuteResult<PodUserEpisodeInteraction> getUserPlayProgress(Long userId, Long episodeId) {
        ExecuteResult<PodUserEpisodeInteraction> result = new ExecuteResult<>();
        
        // 参数校验
        if (null == userId || null == episodeId) {
            result.addErrorMessage("用户ID和单集ID不能为空");
            return result;
        }
        
        try {
            // 查询是否存在记录
            PodUserEpisodeInteractionExample example = new PodUserEpisodeInteractionExample();
            PodUserEpisodeInteractionExample.Criteria criteria = example.createCriteria();
            criteria.andUserIdEqualTo(userId);
            criteria.andEpisodeIdEqualTo(episodeId);
            
            List<PodUserEpisodeInteraction> interactions = podUserEpisodeInteractionMapper.selectByExample(example);
            
            if (interactions != null && !interactions.isEmpty()) {
                result.setResult(interactions.get(0));
            } else {
                PodUserEpisodeInteraction interaction = new PodUserEpisodeInteraction();
                interaction.setUserId(userId);
                interaction.setEpisodeId(episodeId);
                interaction.setPlaybackProgressSeconds(0);
                interaction.setCumulativePlaybackSeconds(0);
                interaction.setIsLiked(0);
                interaction.setIsCompleted(0);
                result.setResult(interaction);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        
        return result;
    }
}
