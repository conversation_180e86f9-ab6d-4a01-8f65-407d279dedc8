package com.aaron.spring.service;

import com.aaron.common.NameAndValue;
import com.aaron.data.model.SearchBook;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookExample;
import com.aaron.spring.model.EnyanCategory;
import com.aaron.util.ExecuteResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.data.elasticsearch.core.SearchHits;

import java.util.List;

/**
 * Book service
 * @Author: <PERSON>
 * @Date: Created in  2017/11/1
 * @Modified By:
 */
public interface EnyanBookService extends IService<EnyanBook,EnyanBookExample>{

    /**
     * <p>只是添加书籍</p>
     * @param record
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.model.EnyanBook>
     * @since : 2023/4/19
     **/
    ExecuteResult<EnyanBook> addRecordOnly(EnyanBook record);

    /**
     *
     * 按照时间顺序获取书籍
     * @param enyanBook
     * @Date: 2017/12/2
     */
    Page findBookOrderByTime(EnyanBook enyanBook);
    /**
     *
     * 按照销量顺序获取书籍
     * @param enyanBook
     * @Date: 2017/12/2
     */
    Page findBookOrderBySales(EnyanBook enyanBook);
    /**
     *
     * 按照销量价格获取书籍
     * @param enyanBook
     * @Date: 2017/12/2
     */
    Page findBookOrderByPrice(EnyanBook enyanBook);

    /**
     *
     * 更新首页分类信息
     * @param categoryId 为空，则更新所有
     * @Date: 2017/12/4
     */
    void initIndexCategoryBooks(Long categoryId);


    /**
     * <p>初始化所有的首页推荐（老版本）</p>
     * @param
     * @return void
     * @since : 2021/8/6
     **/
    void initIndexAllRecommended();

    /**
     * <p>初始化所有的首页信息</p>
     * @param
     * @return void
     * @since : 2024-10-09
     **/
    void initIndexAllInfo();

    /**
     * 只获取书籍列表, pageSize=-1 是所有数据
     * @param enyanBook 非空
     * @Date: 2017/12/5
     */
    List<EnyanBook> findBooks(EnyanBook enyanBook);

    /**
     *  获取书籍全部信息, pageSize=-1 是所有数据
     * @param enyanBook 非空
     * @Date: 2017/12/5
     */
    List<EnyanBook> findBooksWithBlob(EnyanBook enyanBook);

    /**
     * 基础信息的book
     *
     * @param bookIdList
     * @Date: 2018/3/12
     */
    List<EnyanBook> findBookByIds(List<Long> bookIdList);

    /**
     * 基础信息的book
     *
     * @param bookIds
     * @Date: 2018/3/12
     */
    List<EnyanBook> findBookByIdsArray(Long[] bookIds);

    /**
     * ID and Name基础信息的book
     *
     * @param bookIdList
     */
    List<EnyanBook> findBookIDAndNameByIds(List<Long> bookIdList);

    /**
     * ID and Name基础信息的book
     *
     * @param bookIds
     */
    List<EnyanBook> findBookIDAndNameByIdsArray(Long[] bookIds);


    /**
     * ID and Name基础信息的book
     *
     * @param bookIds ","区隔
     */
    List<EnyanBook> findBookIDAndNameByIdsString(String bookIds);

    /**
     * ID and Name and Price基础信息的book
     *
     * @param bookIds ","区隔
     */
    List<EnyanBook> findBookIDAndNameAndPriceByIdsString(String bookIds);

    /**
     * 获取所有书籍的基本信息：
     * book.book_id, book.book_title, book.publisher_id, book.publisher_name
     * @param saleStatus 在上架状态 (null： 所有；1：上架；0非上架)
     * @Date: 2020-05-13
     */
    List<EnyanBook> findBookBasicInfo(Integer saleStatus);

    /**
     * <p>特价书籍列表</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2021/9/1
     **/
    List<EnyanBook> findBookBasicInfoSpecialOffer();

    /**
     * <p>更新相应书籍到 特价状态与否</p>
     * @param ids
     * @param specialOfferOrNO
     * @return int
     * @since : 2021/9/2
     **/
    int updateBooksToSpecialOffer(String[] ids, Integer specialOfferOrNO);

    /**
     * <p>所有使用单个折扣或N件折扣的书籍</p>
     * @param
     * @return: java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2020-08-18
     */
    List<EnyanBook> findBooksByDiscountValid();

    /**
     * <p>获取书籍ID的匹配信息(所有书籍)</p>
     * @param
     * @return: java.util.List<com.aaron.common.NameAndValue>
     * @since : 2020-07-20
     */
    List<NameAndValue> getBookIDsList();

    /**
     * <p>根据discountId获取NameAndValue</p>
     * @param discountId
     * @return: java.util.List<com.aaron.common.NameAndValue>
     * @since : 2020-07-30
     */
    List<NameAndValue> findBookNameAndValueByDiscountId(Long discountId);

    /**
     * <p>根据discountSingleId获取NameAndValue</p>
     * @param discountSingleId
     * @return: java.util.List<com.aaron.common.NameAndValue>
     * @since : 2020-07-30
     */
    List<NameAndValue> findBookNameAndValueByDiscountSingleId(Long discountSingleId);

    /**
     * <p>根据setId获取NameAndValue</p>
     * @param setId
     * @return java.util.List<com.aaron.common.NameAndValue>
     * @since : 2023/5/23
     **/
    List<NameAndValue> findBookNameAndValueBySetId(Long setId);

    List<EnyanBook> findBookIdAndNameListBySetId(Long setId);

    /**
     * <p>根据setId获取NameAndValueAndPrice</p>
     * @param setId
     * @return java.util.List<com.aaron.common.NameAndValue>
     * @since : 2023/5/23
     **/
    List<EnyanBook> findBookIdAndNameAndPriceListBySetId(Long setId);

    /**
     * <p>初始化书籍的NameAndValue，书籍列表里添加了这个重置功能</p>
     * @param
     * @return: void
     * @since : 2020-07-30
     */
    void initBookNameAndValue(Boolean shouldReset);

    /**
     * <p>修改书籍的折扣信息</p>
     * @param book
     * @param ids
     * @param idsOld
     * @param isSingle
     * @return: int
     * @since : 2020-07-30
     */
    int updateBookDiscount(EnyanBook book, String[] ids, String[] idsOld, boolean isSingle);

    /**
     * <p>修改书籍的套装信息</p>
     * @param book
     * @param ids
     * @param idsOld
     * @return int
     * @since : 2023/5/23
     **/
    int updateBookSet(EnyanBook book, String[] ids, String[] idsOld);

    /**
     * <p>添加所有N件折商品（discountId=1）到单件折（在<code>book</code>的属性中）</p>
     * @param book
     * @return: int
     * @since : 2020-09-22
     */
    int updateBookSingleDiscountAssociateNDiscount(EnyanBook book);

    /**
     *
     *  包含大字段查询
     * @param page
     * @param record
     * @Date: 2017/12/5
     */
    Page queryRecordsUseBlob(Page<EnyanBook> page, EnyanBook record);

    /**
     *
     * 搜索书籍
     * @param enyanBook
     * @Date: 2017/12/20
     */
    Page searchBook(EnyanBook enyanBook);

    /**
     *
     * 搜索书籍
     * @param enyanBook
     * @Date: 2017/12/20
     */
    Page searchBookByTitleOrAuthor(EnyanBook enyanBook);

    /**
     *
     * 搜索书籍
     * @param enyanBook
     * @Date: 2017/12/20
     */
    Page searchBookByAuthor(EnyanBook enyanBook);

    /**
     *
     * 搜索书籍
     * @param enyanBook
     * @Date: 2023/3/28
     */
    Page searchBookByTitleOrAuthorWithElasticSearch(EnyanBook enyanBook);


    /**
     * <p>在ElasticSearch中搜索书籍</p>
     * @param keyword
     * @return org.springframework.data.elasticsearch.core.SearchHits<com.aaron.data.model.SearchBook>
     * @since : 2023/3/28
     **/
    SearchHits<SearchBook> searchBookByKeywordWithElasticSearch(String keyword);
    /**
     *
     * 书籍销量+1
     * @param bookIdList
     * @Date: 2017/12/21
     */
    void updateBookSaleVolumeAdd(List<Long> bookIdList);
    /**
     *
     * 书籍销量-1
     * @param bookIdList
     * @Date: 2017/12/21
     */
    void updateBookSaleVolumeMinus(List<Long> bookIdList);

    /**
     * <p>修改书籍分类的名称</p>
     * @param categoryName
     * @param categoryId
     * @return void
     * @since : 2023/6/15
     **/
    void updateBookCategoryName(String categoryName, Long categoryId);
    /**
     *
     * 根据主键修改个别属性
     * @param enyanBook
     * @Date: 2018/1/3
     */
    void updateByPrimaryKeySelective(EnyanBook enyanBook);
    /**
     *
     * 还没有付成本费的书籍
     * @param publisherId
     * @Date: 2018/6/7
     */
    List<EnyanBook> findBookNotCost(Long publisherId);

    /**
     * <p>初始化Elasticsearch数据</p>
     * @param
     * @return void
     * @since : 2023/3/28
     **/
    void initElasticsearchInfo();


    /**
     * <p>每天初始化Elasticsearch数据</p>
     * @param
     * @return void
     * @since : 2023/3/28
     **/
    void initElasticsearchInfoDay();

    /**
     * <p></p>
     * @param bookId
     * @return void
     * @since : 2023/3/28
     **/
    void resetBookElasticSearch(Long bookId);

    /**
     * <p>重置书籍的星级</p>
     * @param bookId
     * @return void
     * @since : 2023/4/24
     **/
    void resetBookStar(Long bookId);

    /**
     * <p>获取某大于Id的最近一条基本数据</p>
     * @param bookId
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-02-03
     **/
    List<EnyanBook> findTop1BasicBookGTBookId(Long bookId);
    /**
     * <p>重制书籍的拼音</p>
     * @param bookId
     * @return void
     * @since : 2024-02-03
     **/
    void resetBookPinyin(Long bookId, String bookTitle);

    /**
     * <p>根据作者查询推荐的书籍</p>
     * @param author
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-04-22
     **/
    List<EnyanBook> findBookRecommendByAuthor(String author);

    /**
     * <p>根据书系查询推荐的书籍</p>
     * @param setId
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-04-22
     **/
    List<EnyanBook> findBookRecommendBySetId(Long setId);

    /**
     * <p>根据类型查询同类型畅销书推荐的书籍</p>
     * @param categoryId
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-04-22
     **/
    List<EnyanBook> findBookRecommendTopByCategory(Long categoryId);

    /**
     * <p>根据类型查询同类型随机推荐的书籍</p>
     * @param categoryId
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-04-22
     **/
    List<EnyanBook> findBookRecommendRandomByCategory(Long categoryId);

    /**
     * <p>根据书籍获取推荐的书籍列表</p>
     * @param bookId
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-08-08
     **/
    List<EnyanBook> findBookRecommendByBook(Long bookId);
}
