package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.PodPodcastMapper;
import com.aaron.spring.mapper.custom.PodEpisodeCustomMapper;
import com.aaron.spring.mapper.custom.PodPodcastCustomMapper;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.model.PodPodcastExample;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description: 栏目服务实现类
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Slf4j
@Service
public class PodPodcastServiceImpl implements PodPodcastService {

    @Resource
    private PodPodcastMapper podPodcastMapper;

    @Resource
    private PodPodcastCustomMapper podPodcastCustomMapper;
    
    @Resource
    private PodEpisodeCustomMapper podEpisodeCustomMapper;

    @Override
    public Page queryRecords(Page<PodPodcast> page, PodPodcast record) {
        if (null == record){
            record = new PodPodcast();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            PodPodcastExample example = new PodPodcastExample();
            example.setPage(page);  // 标准分页设置
            PodPodcastExample.Criteria criteria = example.createCriteria();

            // 标准条件方法
            if (null != record.getIsPublished()){
                criteria.andIsPublishedEqualTo(record.getIsPublished());
            }
            if (null != record.getIsDeleted()){
                criteria.andIsDeletedEqualTo(record.getIsDeleted());
            }

            // 处理搜索条件
            if (null != record.getPodcastId()){
                criteria.andPodcastIdEqualTo(record.getPodcastId());
            } else {
                // 只有在没有指定ID的情况下才进行模糊搜索
                if (StringUtils.isNotBlank(record.getTitle())){
                    criteria.andTitleLike("%"+record.getTitle()+"%");
                }
                if (StringUtils.isNotBlank(record.getAuthorName())){
                    criteria.andAuthorNameLike("%"+record.getAuthorName()+"%");
                }
            }
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            }

            long count = podPodcastMapper.countByExample(example);
            page.setTotalRecord(count);
            
            List<PodPodcast> list = podPodcastMapper.selectByExample(example);
            page.setRecords(list);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return page;
    }

    @Override
    public ExecuteResult<PodPodcast> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<PodPodcast> result = new ExecuteResult<>();
        try {
            PodPodcast record = podPodcastMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new PodPodcast();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<PodPodcast> addRecord(PodPodcast record) {
        ExecuteResult<PodPodcast> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }

            int saveFlag = podPodcastMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

//    @Override
//    public ExecuteResult<PodPodcast> updateRecord(PodPodcast record) {
//        ExecuteResult<PodPodcast> result = new ExecuteResult<>();
//        try {
//            //校验保存对象　
//            String checkMsg = this.checkUpdateRecord(record);
//            if (StringUtils.isNotBlank(checkMsg)){
//                result.addErrorMessage("保存校验失败："+ checkMsg);
//                return result;
//            }
//            int saveFlag = podPodcastMapper.updateByPrimaryKeySelective(record);
//            if (saveFlag>0){
//                result.setResult(record);
//                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
//            }else {
//                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
//        }
//        return result;
//    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = podPodcastCustomMapper.updatePodcastToDeletedById(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(PodPodcast record) {
        if (null == record) {
            return "播客数据不能为空";
        }
        if (StringUtils.isBlank(record.getTitle())) {
            return "播客标题不能为空";
        }
        if (StringUtils.isBlank(record.getDescription())) {
            return "播客描述不能为空";
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(PodPodcast record) {
        if (null == record) {
            return "播客数据不能为空";
        }
        if (null == record.getPodcastId()) {
            return "播客ID不能为空";
        }
        return null;
    }

    @Override
    public Page queryRecordsBasic(Page<PodPodcast> page, PodPodcast record) {
        if (null == record){
            record = new PodPodcast();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            PodPodcastExample example = new PodPodcastExample();
            example.setPage(page);  // 标准分页设置
            PodPodcastExample.Criteria criteria = example.createCriteria();
            
            // 基本查询默认只查询未删除的数据
            criteria.andIsDeletedEqualTo(0);
            
            if (null != record.getIsPublished()){
                criteria.andIsPublishedEqualTo(record.getIsPublished());
            }
            if (StringUtils.isNotBlank(record.getTitle())){
                criteria.andTitleLike("%"+record.getTitle()+"%");
            }
            if (StringUtils.isNotBlank(record.getAuthorName())){
                criteria.andAuthorNameLike("%"+record.getAuthorName()+"%");
            }
            
            // 默认按照显示顺序和创建时间降序排序
            if (null == record.getOrderObjList() || record.getOrderObjList().isEmpty()) {
                example.setOrderByClause("display_order DESC, created_at DESC");
            } else {
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            }
            
            long count = podPodcastMapper.countByExample(example);
            page.setTotalRecord(count);
            
            List<PodPodcast> list = podPodcastMapper.selectByExample(example);
            page.setRecords(list);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public int updatePodcastPlayCountById(Long id) {
        try {
            if (null != id) {
                 // TODO 更新播放次数
//                return podPodcastCustomMapper.updatePodcastPlayCountById(id);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
    
    @Override
    public int updatePodcastLikeCountById(Long id) {
        try {
            if (null != id) {
                return podPodcastCustomMapper.updatePodcastLikeCountById(id);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //log.error("更新播客喜欢量失败，id={}", id, e);
        }
        return 0;
    }

    @Override
    public ExecuteResult<Boolean> updateAllEpisodesCoverImage(Long podcastId, String coverImageUrl, String coverImageUrl2) {
        ExecuteResult<Boolean> result = new ExecuteResult<>();
        try {
            if (podcastId == null || StringUtils.isBlank(coverImageUrl)) {
                result.addErrorMessage("参数错误：播客ID和封面URL不能为空");
                return result;
            }
            
            int updated = podEpisodeCustomMapper.updateEpisodesCoverImageByPodcastId(podcastId, coverImageUrl, coverImageUrl2);
            result.setResult(true);
            result.setSuccessMessage("成功更新" + updated + "个单集的封面图片");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public ExecuteResult<PodPodcast> updateRecord(PodPodcast record) {
        ExecuteResult<PodPodcast> result = new ExecuteResult<>();
        
        try {
            // 检查记录是否存在
            PodPodcast existingRecord = podPodcastMapper.selectByPrimaryKey(record.getPodcastId());
            if (existingRecord == null) {
                result.addErrorMessage("找不到ID为 " + record.getPodcastId() + " 的播客记录");
                return result;
            }
            
            // 更新记录
            int updated = podPodcastMapper.updateByPrimaryKeySelective(record);
            if (updated > 0) {
                // 获取更新后的记录
                PodPodcast updatedRecord = podPodcastMapper.selectByPrimaryKey(record.getPodcastId());
                result.setResult(updatedRecord);
                
                // 如果封面URL有变化，则更新该播客下所有单集的封面
                if ((record.getCoverImageUrl() != null && 
                     !record.getCoverImageUrl().equals(existingRecord.getCoverImageUrl())) ||
                    (record.getCoverImageUrl2() != null && 
                     !record.getCoverImageUrl2().equals(existingRecord.getCoverImageUrl2()))) {
                    this.updateAllEpisodesCoverImage(record.getPodcastId(), 
                                                    record.getCoverImageUrl(), 
                                                    record.getCoverImageUrl2());
                }
            } else {
                result.addErrorMessage("更新播客记录失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage("更新播客记录失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Page queryPublishedPodcasts(Page<PodPodcast> page, PodPodcast record) {
        if (null == record){
            record = new PodPodcast();
        }
        // 设置只查询已发布的播客
        record.setIsPublished(1);
        record.setIsDeleted(0);
        return this.queryRecords(page, record);
    }
}
