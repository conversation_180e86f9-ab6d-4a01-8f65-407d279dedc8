package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.aspect.OnProductionCondition;
import com.aaron.common.ListUtils;
import com.aaron.common.Money;
import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.data.DataInterface;
import com.aaron.data.model.SearchBook;
import com.aaron.data.repository.elasticsearch.BookSearchRepository;
import com.aaron.exception.BusinessException;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBanner;
import com.aaron.spring.common.AaronJF;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.common.RegexpUtil;
import com.aaron.spring.mapper.EnyanBookMapper;
import com.aaron.spring.mapper.custom.*;
import com.aaron.spring.model.*;
import com.aaron.spring.service.*;
import com.aaron.util.DateUtil;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import com.github.houbb.pinyin.constant.enums.PinyinStyleEnum;
import com.github.houbb.pinyin.util.PinyinHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/1
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanBookServiceImpl implements EnyanBookService{
    @Resource
    private EnyanBookMapper enyanBookMapper;

    @Resource
    private EnyanBookCustomMapper enyanBookCustomMapper;

    @Resource
    private EnyanCategoryService enyanCategoryService;

    @Resource
    private EnyanConfigService enyanConfigService;

    @Resource
    private DataInterface dataInterface;

    //@Resource
    //private EnyanOrderDetailCustomMapper enyanOrderDetailCustomMapper;

    @Resource
    private EnyanBookBuyCustomMapper enyanBookBuyCustomMapper;

    @Resource
    private PurchaseCustomMapper purchaseCustomMapper;

    @Autowired
    private ExceptionService exceptionService;

    @Lazy
    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Lazy
    @Resource
    private BookSearchRepository bookSearchRepository;


    @Resource
    private EnyanBookSetService enyanBookSetService;

    @Resource
    private EnyanBookListService enyanBookListService;

	@Resource
	private EnyanCommentCustomMapper enyanCommentCustomMapper;

    @Resource
    private EnyanBannerService enyanBannerService;

    @Override
    public Page queryRecords(Page<EnyanBook> page, EnyanBook record) {

        return this.queryRecords(page, record, true,false);
    }
    @Override
    public Page queryRecordsUseBlob(Page<EnyanBook> page, EnyanBook record){
        return this.queryRecords(page, record, true,true);
    }

    @Override
    public Page searchBook(EnyanBook record) {
        if (null == record){
            record = new EnyanBook();
        }
        Page page = record.getPage();
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanBookExample example = new EnyanBookExample();
            EnyanBookExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            if (StringUtils.isNotBlank(record.getSearchText())){
                String searchText = record.getSearchText();
                if (StringUtils.isBlank(searchText)){
                    page.setRecords(new ArrayList());
                    page.setTotalRecord(0);
                    return page;
                }
                String[] searchTextArray = searchText.split("（");
                if (searchTextArray.length > 0){
                    searchText = searchTextArray[0];
                }
                searchText = RegexpUtil.stringFilter(searchText).toLowerCase();
                if (StringUtils.isBlank(searchText)){
                    page.setRecords(new ArrayList());
                    page.setTotalRecord(0);
                    return page;
                }

                String pinYin = PinyinHelper.toPinyin(searchText, PinyinStyleEnum.INPUT,"-");

                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;

                criteria.andBookPinyinRegexp(pinYin);
                criteria.andAuthorRegexp(searchText);
                criteria.andPublisherNameRegexp(searchText);
                //criteria.andPublisherNameLike("%"+record.getSearchText()+"%");
            }
            //criteria.andShelfStatusEqualTo(Constant.BYTE_VALUE_1);
            //example.setOrderByClause("category_order desc");
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                    if ("sales_volume".equals(orderObj.getOrderBy())
                                && orderObj.getAscOrDesc().equals(InterfaceContant.OrderBy.DESC)){//销量的降序排列，排除免费书
                        criteria.andPriceHkdGreaterThan(Constant.VALUE_0);
                    }
                }
                example.setOrderByClause(buffer.toString());
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanBookCustomMapper.searchCountByExample(example);
            }
            List<EnyanBook> list;
            if (count > 0){
                list = enyanBookCustomMapper.searchByExample(example);
            }else {
                list = new ArrayList<>();
            }

            if (!list.isEmpty()){
                for (EnyanBook enyanBook:list){
                    if (null == enyanBook.getPriceHkd()){
                        continue;
                    }
                    Money money = Money.hkds(enyanBook.getPriceHkd());
                    //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
                    if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                            &&enyanBook.getDiscountSingleValue()>=0){
                        //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                        //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                        enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
                    }
                }
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    public Page searchBookByTitleOrAuthor(EnyanBook record){
        if (null == record){
            record = new EnyanBook();
        }
        Page page = record.getPage();
        if (null == page){
            page = new Page<>();
        }
        if (StringUtils.isBlank(record.getSearchText())){
            page.setRecords(new ArrayList<EnyanBook>());
            return page;
        }
        try {
            String searchText = record.getSearchText();
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList<EnyanBook>());
                return page;
            }

            if (StringUtils.indexOf(record.getSearchText(), "*" ) != -1
                        || StringUtils.indexOf(record.getSearchText(), "$" ) != -1
                        || StringUtils.indexOf(record.getSearchText(), "=" ) != -1){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }
            String[] searchTextArray = record.getSearchText().split("（");
            if (searchTextArray.length > 0){
                searchText = searchTextArray[0];
            }
            searchText = RegexpUtil.stringFilter(searchText).toLowerCase();
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }

            String pinYin = PinyinHelper.toPinyin(searchText, PinyinStyleEnum.INPUT,"-");

            String searchTextSc = AaronJF.f2j(searchText);
            String searchTextTc = AaronJF.j2f(searchText);
            searchText = searchTextSc+"|"+searchTextTc;
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                record.setOrderByClause(buffer.toString());
            }
            record.setBookTitle(searchText);
            record.setBookPinyin(pinYin);
            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanBookCustomMapper.searchCountByTitleOrAuthor(record,page);
            }
            List<EnyanBook> list;
            if (count > 0){
                list = enyanBookCustomMapper.searchByTitleOrAuthor(record,page);
            }else {
                list = new ArrayList<>();
            }

            if (!list.isEmpty()){
                for (EnyanBook enyanBook:list){
                    if (null == enyanBook.getPriceHkd()){
                        continue;
                    }
                    Money money = Money.hkds(enyanBook.getPriceHkd());
                    //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
                    if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                            &&enyanBook.getDiscountSingleValue()>=0){
                        //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                        //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                        enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
                    }
                }
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
            exceptionService.handleException(new BusinessException(101, null, record.getSearchText(),"搜索失败", e));
        }
        return page;
    }

    @Override
    public Page searchBookByAuthor(EnyanBook record) {
        if (null == record){
            record = new EnyanBook();
        }
        Page page = record.getPage();
        if (null == page){
            page = new Page<>();
        }
        if (StringUtils.isBlank(record.getSearchText())){
            page.setRecords(new ArrayList<EnyanBook>());
            return page;
        }
        try {
            String searchText = record.getSearchText();
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList<EnyanBook>());
                return page;
            }

            if (StringUtils.indexOf(record.getSearchText(), "*" ) != -1
                        || StringUtils.indexOf(record.getSearchText(), "$" ) != -1
                        || StringUtils.indexOf(record.getSearchText(), "=" ) != -1){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }
            String[] searchTextArray = record.getSearchText().split("（");
            if (searchTextArray.length > 0){
                searchText = searchTextArray[0];
            }
            searchText = RegexpUtil.stringFilter(searchText).toLowerCase();
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }

            String searchTextSc = AaronJF.f2j(searchText);
            String searchTextTc = AaronJF.j2f(searchText);
            searchText = searchTextSc+"|"+searchTextTc;
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                record.setOrderByClause(buffer.toString());
            }
            record.setAuthor(searchText);
            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanBookCustomMapper.searchCountByAuthor(record,page);
            }
            List<EnyanBook> list;
            if (count > 0){
                list = enyanBookCustomMapper.searchByAuthor(record,page);
            }else {
                list = new ArrayList<>();
            }

            if (!list.isEmpty()){
                for (EnyanBook enyanBook:list){
                    if (null == enyanBook.getPriceHkd()){
                        continue;
                    }
                    Money money = Money.hkds(enyanBook.getPriceHkd());
                    //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
                    if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                                &&enyanBook.getDiscountSingleValue()>=0){
                        //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                        //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                        enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
                    }
                }
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
            exceptionService.handleException(new BusinessException(101, null, record.getSearchText(),"搜索失败", e));
        }
        return page;
    }

    public Page searchBookByTitleOrAuthorWithElasticSearch(EnyanBook record){
        if (Constant.IS_LOCAL == true){
            return null;
        }
        if (null == record){
            record = new EnyanBook();
        }
        Page page = record.getPage();
        if (null == page){
            page = new Page<>();
        }
        if (StringUtils.isBlank(record.getSearchText())){
            page.setRecords(new ArrayList<EnyanBook>());
            return page;
        }
        try {
            String searchText = record.getSearchText();
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList<EnyanBook>());
                return page;
            }
            if (StringUtils.indexOf(record.getSearchText(), "*" ) != -1
                        || StringUtils.indexOf(record.getSearchText(), "$" ) != -1
                        || StringUtils.indexOf(record.getSearchText(), "=" ) != -1){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }
            searchText = RegexpUtil.stringFilter(record.getSearchText());
            if (StringUtils.isBlank(searchText)){
                page.setRecords(new ArrayList());
                page.setTotalRecord(0);
                return page;
            }

            SearchHits<SearchBook> retList = bookSearchRepository.findDistinctTop10ByBookTitleOrOrAuthorOrBookDescriptionOrPublisherName(searchText,searchText,searchText,searchText);

            long count = retList.stream().count();

            List<EnyanBook> list = new ArrayList<>();
            for (SearchHit tmp :   retList) {
                SearchBook book = (SearchBook) tmp.getContent();
                EnyanBook enyanBook = this.queryRecordByPrimaryKey(book.getBookId()).getResult();
                list.add(enyanBook);
            }

            if (!list.isEmpty()){
                for (EnyanBook enyanBook:list){
                    if (null == enyanBook.getPriceHkd()){
                        continue;
                    }
                    Money money = Money.hkds(enyanBook.getPriceHkd());
                    //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
                    if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                                &&enyanBook.getDiscountSingleValue()>=0){
                        //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                        //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                        enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
                    }
                }
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return page;
    }

    @Override
    public SearchHits<SearchBook> searchBookByKeywordWithElasticSearch(String keyWord) {
        if (Constant.IS_LOCAL == true){
            return null;
        }
        return bookSearchRepository.findDistinctTop10ByBookTitleOrOrAuthorOrBookDescriptionOrPublisherName(keyWord,keyWord,keyWord,keyWord);
    }

    @Override
    public void updateBookSaleVolumeAdd(List<Long> bookIdList) {
        enyanBookCustomMapper.updateBookSaleVolumeAdd(bookIdList);
    }

    @Override
    public void updateBookSaleVolumeMinus(List<Long> bookIdList) {
        enyanBookCustomMapper.updateBookSaleVolumeMinus(bookIdList);
    }

    @Override
    public void updateBookCategoryName(String categoryName, Long categoryId) {
        enyanBookCustomMapper.updateBookCategoryName(categoryName,categoryId);
    }

    @Override
    public void updateByPrimaryKeySelective(EnyanBook enyanBook) {
        enyanBookMapper.updateByPrimaryKeySelective(enyanBook);
        dataInterface.delBook(enyanBook.getBookId());
    }

    @Override
    public List<EnyanBook> findBookNotCost(Long publisherId) {
        return enyanBookCustomMapper.findBookNotCost(publisherId);
    }

    @Override
    public void initElasticsearchInfo() {
        if (Constant.IS_PRODUCT == false){
            return;
        }
        EnyanBook search = new EnyanBook();
        search.setShelfStatus(Constant.BYTE_VALUE_1);
        search.setPage(new Page<>());
        search.getPage().setPageSize(-1);
        List<EnyanBook> list = this.findBooksWithBlob(search);
        for (EnyanBook book:list) {
            SearchBook searchBook = new SearchBook();
            searchBook.initWithEBook(book);
            elasticsearchOperations.save(searchBook);
        }
    }

    @Override
    public void initElasticsearchInfoDay() {
        if (Constant.IS_PRODUCT == false){
            return;
        }
        SearchBook book = bookSearchRepository.findFirstByOrderByBookIdDesc();
        log.debug("book:{}",book.getBookId());
        List<EnyanBook> list = enyanBookCustomMapper.findBookGTBookId(book.getBookId());
        for (EnyanBook enyanBook:list) {
            SearchBook searchBook = new SearchBook();
            searchBook.initWithEBook(enyanBook);
            elasticsearchOperations.save(searchBook);
        }
    }

    @Override
    public void resetBookElasticSearch(Long bookId) {
        if (Constant.IS_PRODUCT == false){
            return;
        }
        EnyanBook book = this.queryRecordByPrimaryKey(bookId).getResult();
        SearchBook searchBook = new SearchBook();
        searchBook.initWithEBook(book);
        elasticsearchOperations.save(searchBook);
    }

    @Override
    public void resetBookStar(Long bookId) {
        List<EnyanComment> resultList = enyanCommentCustomMapper.findStarDataByBook(bookId);
        if (resultList.isEmpty() == true){
            return;
        }
        EnyanComment comment = resultList.get(0);
        String star = comment.getStar();
        Long count = comment.getCount();
        if (StringUtils.isBlank(star) || null == count){
            star = "";
            count = 0L;
        }
        enyanBookCustomMapper.updateBookStarByBookId(bookId,star,count.intValue());
        dataInterface.delBook(bookId);
    }

    @Override
    public List<EnyanBook> findTop1BasicBookGTBookId(Long bookId) {
        return enyanBookCustomMapper.findTop1BasicBookGTBookId(bookId);
    }

    @Override
    public void resetBookPinyin(Long bookId, String bookTitle) {
        String pinYin = PinyinHelper.toPinyin(bookTitle, PinyinStyleEnum.INPUT,"-").toLowerCase();
        if (pinYin.length() > 200){
            pinYin = pinYin.substring(0, 199);
        }
        EnyanBook update = new EnyanBook();
        update.setBookId(bookId);
        update.setBookPinyin(pinYin);
        enyanBookMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public List<EnyanBook> findBookRecommendByAuthor(String author) {
        long count = enyanBookCustomMapper.countBookRecommendByAuthor(author);
        if (count == 0){
            return List.of();
        }
        int pageNO = this.getCurrentPageOfRecommendToday(count);
        return enyanBookCustomMapper.findBookRecommendByAuthor(author, this.getRecordIndex(pageNO));
    }

    @Override
    public List<EnyanBook> findBookRecommendBySetId(Long setId) {
        List<EnyanBook> list = dataInterface.getBookRecommendBySetId(setId);
        if (null != list){
            return list;
        }
        long count = enyanBookCustomMapper.countBookRecommendBySetId(setId);
        if (count == 0){
            return List.of();
        }
        int pageNO = this.getCurrentPageOfRecommendToday(count);
        list = enyanBookCustomMapper.findBookRecommendBySetId(setId, this.getRecordIndex(pageNO));
        dataInterface.saveBookRecommendBySetId(list, setId);
        return list;
    }

    @Override
    public List<EnyanBook> findBookRecommendTopByCategory(Long categoryId) {
        List<EnyanBook> list = dataInterface.getBookRecommendTopByCategory(categoryId);
        if (null != list){
            return list;
        }
        list = enyanBookCustomMapper.findBookRecommendTopByCategory(categoryId);
        dataInterface.saveBookRecommendTopByCategory(list, categoryId);
        return list;
    }

    @Override
    public List<EnyanBook> findBookRecommendRandomByCategory(Long categoryId) {
        List<EnyanBook> list = dataInterface.getBookRecommendRandomByCategory(categoryId);
        if (null != list){
            return list;
        }
        long count = enyanBookCustomMapper.countBookRecommendByCategory(categoryId);
        if (count == 0){
            return List.of();
        }
        int pageNO = this.getCurrentPageOfRecommendToday(count);
        list = enyanBookCustomMapper.findBookRecommendByCategory(categoryId, this.getRecordIndex(pageNO));
        dataInterface.saveBookRecommendRandomByCategory(list, categoryId);
        return list;
    }

    @Override
    public List<EnyanBook> findBookRecommendByBook(Long bookId) {
        List<EnyanBook> enyanBookRet = new ArrayList<>();

        ExecuteResult<EnyanBook> bookExecuteResult = this.queryRecordByPrimaryKey(bookId);
        EnyanBook enyanBook = bookExecuteResult.getResult();
        if (null == enyanBook){
            return null;
        }

        Set<Long> bookIdSet = new HashSet<>();
        bookIdSet.add(enyanBook.getBookId());//排除本书

        //A、同作者书籍至多12本
        List<EnyanBook> authorList = this.findBookRecommendByAuthor(enyanBook.getAuthor());//随机展示2本

        this.collectRecommendBooks(enyanBookRet,authorList,bookIdSet,2);

        //B、同书系书籍至多12本
        List<EnyanBook> setList = this.findBookRecommendBySetId(enyanBook.getSetId());

        this.collectRecommendBooks(enyanBookRet,setList,bookIdSet,2);

        //C、同类型畅销书TOP12
        List<EnyanBook> categoryTopList = this.findBookRecommendTopByCategory(enyanBook.getCategoryId());

        this.collectRecommendBooks(enyanBookRet,categoryTopList,bookIdSet,1);

        //D、同类型书籍12本
        List<EnyanBook> categoryList = this.findBookRecommendRandomByCategory(enyanBook.getCategoryId());

        this.collectRecommendBooks(enyanBookRet,categoryList,bookIdSet,6-enyanBookRet.size());

        Collections.shuffle(enyanBookRet);

        return enyanBookRet;
    }

    /**
     * <p></p>
     * @param retList 整合完成的List
     * @param toBeCollectList 将要整合的List
     * @param toBeIdSet 保存id的Set
     * @param toBeCount 需要整合的个数
     * @return void
     * @since : 2024-05-06
     **/
    private void collectRecommendBooks(List<EnyanBook> retList, List<EnyanBook> toBeCollectList, Set<Long> toBeIdSet, int toBeCount){
        List<EnyanBook> listNew = new ArrayList<>();//过滤前面已经有的书籍
        for (EnyanBook book:toBeCollectList){
            if (toBeIdSet.contains(book.getBookId().longValue())){
                continue;
            }
            listNew.add(book);
        }
        List<EnyanBook> listReturn = ListUtils.randomList(listNew, toBeCount);
        for (EnyanBook book:listReturn){
            toBeIdSet.add(book.getBookId());
        }
        retList.addAll(listReturn);
    }

    /**
     * 获取记录的总数获取当前的页数
     * */
    private int getCurrentPageOfRecommendToday(long count){
        if (count == 0 ){
            return 0;
        }
        int pageSize = 12;
        int totalPages =  (int)(count / pageSize);
        if (count % pageSize != 0){
            totalPages++;
        }

        int day = (int)DateUtils.getFragmentInDays(new Date(), Calendar.MONTH);
	    return day % totalPages + 1;
    }

    private Integer getRecordIndex(Integer currentPage) {
        return currentPage > 0 ? (currentPage - 1) * 12 : 0;
    }

    public Page queryRecords(Page<EnyanBook> page, EnyanBook record, boolean withCount, boolean useBlob) {
        if (null == record){
            record = new EnyanBook();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanBookExample example = new EnyanBookExample();
            EnyanBookExample.Criteria criteria = example.createCriteria();

            example.setPage(page);
            if (page.getPageSize() == -1){//不需要分页
                example.setPage(null);
            }

            if (StringUtils.isNotBlank(record.getBookTitle())){
                String searchText = record.getBookTitle();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andBookTitleRegexp(searchText);
            }

            if (StringUtils.isNotBlank(record.getAuthor())){
                String searchText = record.getAuthor();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andAuthorRegexp(searchText);
            }

            if (StringUtils.isNotBlank(record.getPublisherName())){
                String searchText = record.getPublisherName();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andPublisherNameRegexp(searchText);
            }

            if (StringUtils.isNotBlank(record.getCategoryName())){
                String searchText = record.getCategoryName();
                String searchTextSc = AaronJF.f2j(searchText);
                String searchTextTc = AaronJF.j2f(searchText);
                searchText = searchTextSc+"|"+searchTextTc;
                criteria.andCategoryNameRegexp(searchText);
            }

            if (record.getCategoryId() != null){
                criteria.andCategoryIdEqualTo(record.getCategoryId());
            }

            if (null != record.getIsInCn()){
                criteria.andIsInCnEqualTo(record.getIsInCn());
            }

            if (null != record.getDiscountSingleIsValid()){
                criteria.andDiscountSingleIsValidEqualTo(record.getDiscountSingleIsValid());
            }

            if (null != record.getDiscountIsValid()){
                criteria.andDiscountIsValidEqualTo(record.getDiscountIsValid());
            }

            if (null != record.getShelfStatus()){
                criteria.andShelfStatusEqualTo(record.getShelfStatus());
            }

            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }

            if (null != record.getSetId()){
                criteria.andSetIdEqualTo(record.getSetId());
            }

            if (null != record.getPrice()){
                criteria.andPriceEqualTo(record.getPrice());
            }

            if (null != record.getSalesModel()){
                criteria.andSalesModelEqualTo(record.getSalesModel());
            }

            if (null != record.getSpecialOffer()){
                criteria.andSpecialOfferEqualTo(record.getSpecialOffer());
            }

            if (null != record.getSetId()){
                criteria.andSetIdEqualTo(record.getSetId());
            }

            //example.setOrderByClause("category_order desc");
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                    if ("sales_volume".equals(orderObj.getOrderBy())
                                && orderObj.getAscOrDesc().equals(InterfaceContant.OrderBy.DESC)){//销量的降序排列，排除免费书
                        criteria.andPriceHkdGreaterThan(Constant.VALUE_0);
                    }
                }
                example.setOrderByClause(buffer.toString());
            }

            List<EnyanBook> list = null;

            if (useBlob){
                list = enyanBookCustomMapper.selectByExampleWithBLOBs(example);
            }else {
                list = enyanBookMapper.selectByExample(example);
            }

            if (!list.isEmpty()){
                if (withCount){
                    long count = page.getTotalRecord();
                    if (count==0){
                        count = enyanBookMapper.countByExample(example);
                    }
                    page.setTotalRecord(count);
                }
                for (EnyanBook enyanBook:list){
                    if (null == enyanBook.getPriceHkd()){
                        continue;
                    }
                    Money money = Money.hkds(enyanBook.getPriceHkd());
                    //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
                    if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                            &&enyanBook.getDiscountSingleValue()>=0){
                        //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                        //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                        enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
                    }
                    if (StringUtils.isNotBlank(record.getBookWeb())){
                        String bookWeb = record.getBookWeb();
                        BookWebInfo bookWebInfo = JSON.parseObject(bookWeb,BookWebInfo.class);
                        record.setBookWebInfo(bookWebInfo);
                    }
                }

                page.setRecords(list);
            }else {
                page.setRecords(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanBook> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanBook> result = new ExecuteResult<>();
        try {
            EnyanBook book = dataInterface.getBookByID(pkId);
            if (null != book){
                result.setResult(book);
                return result;
            }
            EnyanBook record = enyanBookMapper.selectByPrimaryKey(pkId);
            if (null == record){
                //record = new EnyanBook();
                return result;
            }
            Money money = Money.hkds(record.getPriceHkd());
            //record.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
            if (Constant.BYTE_VALUE_1.equals(record.getDiscountSingleIsValid())
                    &&record.getDiscountSingleValue()>=0){
                //record.setPriceCnyDiscount(money.getCNYDiscountAmount(record.getDiscountSingleValue()));
                //record.setPriceUSDDiscount(money.getUSDDiscountAmount(record.getDiscountSingleValue()));
                record.setPriceHKDDiscount(money.getHKDDiscountAmount(record.getDiscountSingleValue()));
            }
            if (StringUtils.isNotBlank(record.getBookWeb())){
                String bookWeb = record.getBookWeb();
                BookWebInfo bookWebInfo = JSON.parseObject(bookWeb,BookWebInfo.class);
                record.setBookWebInfo(bookWebInfo);
            }
            /*
            if (null != record.getBookId()){
                EnyanBookSet obj = enyanBookSetService.queryRecordByPrimaryKey(record.getBookId()).getResult();
                if (null == obj){
                    record.setSetName(obj.getSetName());
                }
            }*/
            result.setResult(record);
            dataInterface.saveBook(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanBook> addRecord(EnyanBook record) {
        ExecuteResult<EnyanBook> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            String pinYin = PinyinHelper.toPinyin(record.getBookTitle(), PinyinStyleEnum.INPUT,"-").toLowerCase();
            if (pinYin.length() > 200){
                pinYin = pinYin.substring(0, 199);
            }
            record.setBookPinyin(pinYin);
            int saveFlag = enyanBookMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);

            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
            //this.initBookNameAndValue();
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanBook> addRecordOnly(EnyanBook record) {
        ExecuteResult<EnyanBook> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            String pinYin = PinyinHelper.toPinyin(record.getBookTitle(), PinyinStyleEnum.INPUT,"-").toLowerCase();
            if (pinYin.length() > 200){
                pinYin = pinYin.substring(0, 199);
            }
            record.setBookPinyin(pinYin);
            int saveFlag = enyanBookMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);

            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanBook> updateRecord(EnyanBook record) {
        ExecuteResult<EnyanBook> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            boolean updateDrm = false;
            boolean updateName = false;

            EnyanBook oldBook = this.queryRecordByPrimaryKey(record.getBookId()).getResult();

            if (StringUtils.isNotBlank(record.getBookDrmRef())){
                if (StringUtils.isBlank(oldBook.getBookDrmRef())){
                    updateDrm = true;
                }else if (!oldBook.getBookDrmRef().equals(record.getBookDrmRef())){
                    updateDrm = true;
                }
            }
            int oldPublicationId = Integer.parseInt(oldBook.getBookDrmRef());
            if (oldPublicationId < 0){//仅用于预上架 不需要更新DRM
                updateDrm = false;
            }
            if (null == record.getBookType()
                    || EBookConstant.BookType.EBOOK_SET == oldBook.getBookType()
                    || EBookConstant.BookType.EBOOK_SET == record.getBookType()){//修改前或后如果是套装 不修改更新书籍
                updateDrm = false;
            }
            if (StringUtils.isNotBlank(record.getBookTitle()) && !oldBook.getBookTitle().equals(record.getBookTitle())){
                updateName = true;//修改名称需要更新记录
            }
            if (updateName){
                String pinYin = PinyinHelper.toPinyin(record.getBookTitle(), PinyinStyleEnum.INPUT,"-").toLowerCase();
                if (pinYin.length() > 200){
                    pinYin = pinYin.substring(0, 199);
                }
                record.setBookPinyin(pinYin);
            }
            int saveFlag = enyanBookMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);

                enyanConfigService.updateSystemConfigDate();

                if (updateDrm){//因为更换了DRM，更新购买记录和购买记录的DRM匹配
                    int newPublictionId =  Integer.parseInt(record.getBookDrmRef());

                    enyanBookBuyCustomMapper.updateOrderDetailPublicationIdByBookId(Integer.parseInt(record.getBookDrmRef()),record.getBookId());
                    purchaseCustomMapper.updatePurchaseToNewPublicationId(newPublictionId,oldPublicationId);
                }

            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
            if (updateName){
                //this.initBookNameAndValue();
            }
            dataInterface.delBook(record.getBookId());
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanBookMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
                enyanConfigService.updateSystemConfigDate();
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
            dataInterface.delBook(pkId);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanBook record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (StringUtils.isBlank(record.getBookTitle())){
            sb.append("书名不可以为空");
            return sb.toString();
        }

        if (sb.length()>0){
            return sb.toString();
        }
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanBook record) {
        StringBuffer sb = new StringBuffer();
        if (null == record){
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (record.getBookId()==0){
            sb.append("主键信息不可以为空");
            return sb.toString();
        }
        return null;
    }



    @Override
    public Page findBookOrderByTime(EnyanBook enyanBook) {
        enyanBook.addOrder(new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC));
        return this.queryRecords(enyanBook.getPage(),enyanBook);
    }

    @Override
    public Page findBookOrderBySales(EnyanBook enyanBook) {
        return null;
    }

    @Override
    public Page findBookOrderByPrice(EnyanBook enyanBook) {
        enyanBook.addOrder(new OrderObj("price",InterfaceContant.OrderBy.DESC));
        return this.queryRecords(enyanBook.getPage(),enyanBook);
    }

    @Override
    public List<EnyanBook> findBooks(EnyanBook enyanBook) {
        return this.queryRecords(enyanBook.getPage(),enyanBook,false,false).getRecords();
    }

    @Override
    public List<EnyanBook> findBooksWithBlob(EnyanBook enyanBook){
        return this.queryRecords(enyanBook.getPage(),enyanBook,false,true).getRecords();
    }

    @Override
    public List<EnyanBook> findBookByIds(List<Long> bookIdList) {
        if (bookIdList.isEmpty()){
            return new ArrayList<>();
        }
        List<EnyanBook> list = enyanBookCustomMapper.findBookBasicInfoByIds(bookIdList);
        for (EnyanBook enyanBook:list){
            if (null == enyanBook.getPriceHkd()){
                continue;
            }
            Money money = Money.hkds(enyanBook.getPriceHkd());
            //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
            if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                    &&enyanBook.getDiscountSingleValue()>=0){
                //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
            }
        }
        return list;
    }

    @Override
    public List<EnyanBook> findBookByIdsArray(Long[] bookIds) {
        if (null == bookIds || bookIds.length<1){
            return List.of();
        }
        List<EnyanBook> list = enyanBookCustomMapper.findBookBasicInfoByIdsArray(bookIds);
        for (EnyanBook enyanBook:list){
            if (null == enyanBook.getPriceHkd()){
                continue;
            }
            Money money = Money.hkds(enyanBook.getPriceHkd());
            //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
            if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                        &&enyanBook.getDiscountSingleValue()>=0){
                //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
            }
        }
        return list;
    }

    @Override
    public List<EnyanBook> findBookIDAndNameByIds(List<Long> bookIdList) {
        return enyanBookCustomMapper.findBookIDAndNameByIds(bookIdList);
    }

    @Override
    public List<EnyanBook> findBookIDAndNameByIdsArray(Long[] bookIds) {
        return enyanBookCustomMapper.findBookIDAndNameByIdsArray(bookIds);
    }

    @Override
    public List<EnyanBook> findBookIDAndNameByIdsString(String bookIds) {
        List<Long> idsLong = Arrays.stream(bookIds.split(","))
                                     .map(Long::parseLong)
                                     .collect(Collectors.toList());
        return enyanBookCustomMapper.findBookIDAndNameByIdsList(idsLong);
    }

    @Override
    public List<EnyanBook> findBookIDAndNameAndPriceByIdsString(String bookIds) {
        List<Long> idsLong = Arrays.stream(bookIds.split(","))
                                     .map(Long::parseLong)
                                     .collect(Collectors.toList());
        List<EnyanBook> list = enyanBookCustomMapper.findBookIDAndNameAndPriceByIdsList(idsLong);
        for (EnyanBook enyanBook:list){
            if (null == enyanBook.getPriceHkd()){
                continue;
            }
            Money money = Money.hkds(enyanBook.getPriceHkd());
            //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
            if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                        &&enyanBook.getDiscountSingleValue()>=0){
                //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
            }
        }
        return list;
    }

    @Override
    public List<EnyanBook> findBookBasicInfo(Integer saleStatus) {
        return enyanBookCustomMapper.findBookBasicInfo(saleStatus);
    }

    @Override
    public List<EnyanBook> findBookBasicInfoSpecialOffer() {
        return enyanBookCustomMapper.findBookBasicInfoSpecialOffer();
    }

    @Override
    public int updateBooksToSpecialOffer(String[] ids, Integer specialOfferOrNO) {
        if (ids.length == 0){
            return 0;
        }
        return enyanBookCustomMapper.updateBooksToSpecialOffer(ids, specialOfferOrNO);
    }

    @Override
    public List<EnyanBook> findBooksByDiscountValid() {
        return enyanBookCustomMapper.findBooksByDiscountValid();
    }

    @Override
    public List<NameAndValue> getBookIDsList() {
        return Constant.booksList;
    }

    @Override
    public List<NameAndValue> findBookNameAndValueByDiscountId(Long discountId) {
        List<EnyanBook> bookList =  enyanBookCustomMapper.findBookIDAndNameByDiscountId(discountId);
        List<NameAndValue> bookIDsList = new ArrayList<>();
        for (EnyanBook book : bookList){
            NameAndValue nameAndValue = new NameAndValue();
            nameAndValue.setName(book.getBookTitle());
            nameAndValue.setValue(""+  book.getBookId());
            bookIDsList.add(nameAndValue);
        }
        return bookIDsList;
    }

    @Override
    public List<NameAndValue> findBookNameAndValueByDiscountSingleId(Long discountSingleId) {
        List<EnyanBook> bookList =  enyanBookCustomMapper.findBookIDAndNameByDiscountSingleId(discountSingleId);
        List<NameAndValue> bookIDsList = new ArrayList<>();
        for (EnyanBook book : bookList){
            NameAndValue nameAndValue = new NameAndValue();
            nameAndValue.setName(book.getBookTitle());
            nameAndValue.setValue(""+  book.getBookId());
            bookIDsList.add(nameAndValue);
        }
        return bookIDsList;
    }

    @Override
    public List<NameAndValue> findBookNameAndValueBySetId(Long setId) {
        List<EnyanBook> bookList =  enyanBookCustomMapper.findBookIDAndNameBySetId(setId);
        List<NameAndValue> bookIDsList = new ArrayList<>();
        for (EnyanBook book : bookList){
            NameAndValue nameAndValue = new NameAndValue();
            nameAndValue.setName(book.getBookTitle());
            nameAndValue.setValue(""+  book.getBookId());
            bookIDsList.add(nameAndValue);
        }
        return bookIDsList;
    }

    @Override
    public List<EnyanBook> findBookIdAndNameListBySetId(Long setId){
        return enyanBookCustomMapper.findBookIDAndNameBySetId(setId);
    }

    @Override
    public List<EnyanBook> findBookIdAndNameAndPriceListBySetId(Long setId) {
        List<EnyanBook> list = enyanBookCustomMapper.findBookIdAndNameAndPriceListBySetId(setId);
        for (EnyanBook enyanBook:list){
            if (null == enyanBook.getPriceHkd()){
                continue;
            }
            Money money = Money.hkds(enyanBook.getPriceHkd());
            //enyanBook.setPriceUsd(money.getDollarAmount(Constant.CNY_RATE));
            if (Constant.BYTE_VALUE_1.equals(enyanBook.getDiscountSingleIsValid())
                        &&enyanBook.getDiscountSingleValue()>=0){
                //enyanBook.setPriceCnyDiscount(money.getCNYDiscountAmount(enyanBook.getDiscountSingleValue()));
                //enyanBook.setPriceUSDDiscount(money.getUSDDiscountAmount(enyanBook.getDiscountSingleValue()));
                enyanBook.setPriceHKDDiscount(money.getHKDDiscountAmount(enyanBook.getDiscountSingleValue()));
            }
        }
        return list;
    }

    @Override
    public void initBookNameAndValue(Boolean shouldReset) {
        if (shouldReset == true){
            dataInterface.delBookNameAndValueList();
        }
        List<NameAndValue> bookIDsList = dataInterface.getBookNameAndValueList();
        if (null != bookIDsList){
            Constant.booksList = bookIDsList;
            return;
        }
        List<EnyanBook> bookList =  enyanBookCustomMapper.findBookIDAndName();
        bookIDsList = new ArrayList<>();
        for (EnyanBook book : bookList){
            NameAndValue nameAndValue = new NameAndValue();
            nameAndValue.setName(book.getBookTitle());
            nameAndValue.setValue(""+  book.getBookId());
            bookIDsList.add(nameAndValue);
        }
        Constant.booksList = bookIDsList;
        dataInterface.saveBookNameAndValueList(bookIDsList);
    }

    @Override
    public int updateBookDiscount(EnyanBook book, String[] ids, String[] idsOld, boolean isSingle) {
        int flag=0;
        //String idString = String.join(",",ids);
        List<String> idsRemoveList = new ArrayList<>();//旧的被取消的id List
        Set<String> idSet = new HashSet<>();
        //String[] idsLong = new String[ids.length];
        for (int i = 0; i < ids.length; i++) {
            //idsLong[i] = ids[i];
            idSet.add(ids[i]);
        }
        //Long[] idsOldLong = new Long[idsOld.length];
        for (int i = 0; i < idsOld.length; i++) {
            //idsOldLong[i] = Long.parseLong(idsOld[i]);
            if (!idSet.contains(idsOld[i])){//如果新的id列表没有这个id，则加入取消的列表
                idsRemoveList.add(idsOld[i]);
            }
            idSet.add(idsOld[i]);
        }
        String[] idsRemove = idsRemoveList.toArray(new String[idsRemoveList.size()]);

        EnyanBook bookDefault = new EnyanBook();
        bookDefault.setDiscountSingleId(-1L);
        bookDefault.setDiscountSingleType(Constant.BYTE_VALUE_0);
        bookDefault.setDiscountSingleIsValid(Constant.BYTE_VALUE_0);
        bookDefault.setDiscountSingleValue(100);
        bookDefault.setDiscountSingleEndTime(new Date());
        bookDefault.setDiscountSingleStartTime(new Date());
        bookDefault.setDiscountSingleDescription("");
        bookDefault.setDiscountId(-1L);
        bookDefault.setDiscountDescription("");
        bookDefault.setDiscountIsValid(Constant.BYTE_VALUE_0);
        if (isSingle){
            if (ids.length>0){
                flag = enyanBookCustomMapper.updateBookDiscountSingle(book,ids);
            }
            if (idsRemove.length > 0){
                enyanBookCustomMapper.updateBookDiscountSingle(bookDefault,idsRemove);
            }
        }else {
            if (ids.length>0){
                flag = enyanBookCustomMapper.updateBookDiscount(book,ids);
            }
            if (idsRemove.length > 0){
                flag = enyanBookCustomMapper.updateBookDiscount(bookDefault,idsRemove);
            }
        }
        Iterator<String> it = idSet.iterator();
        while (it.hasNext()){
            Long bookId = Long.parseLong(it.next());
            dataInterface.delBook(bookId);
        }
        return flag;
    }

    @Override
    public int updateBookSet(EnyanBook book, String[] ids, String[] idsOld){
        int flag=0;
        //String idString = String.join(",",ids);
        List<String> idsRemoveList = new ArrayList<>();//旧的被取消的id List
        Set<String> idSet = new HashSet<>();
        //String[] idsLong = new String[ids.length];
        for (int i = 0; i < ids.length; i++) {
            //idsLong[i] = ids[i];
            idSet.add(ids[i]);
        }
        //Long[] idsOldLong = new Long[idsOld.length];
        for (int i = 0; i < idsOld.length; i++) {
            //idsOldLong[i] = Long.parseLong(idsOld[i]);
            if (!idSet.contains(idsOld[i])){//如果新的id列表没有这个id，则加入取消的列表
                idsRemoveList.add(idsOld[i]);
            }
            idSet.add(idsOld[i]);
        }
        String[] idsRemove = idsRemoveList.toArray(new String[idsRemoveList.size()]);

        EnyanBook bookDefault = new EnyanBook();

        bookDefault.setSetId(-1L);
        bookDefault.setSetName("");

        if (ids.length>0){
            flag = enyanBookCustomMapper.updateBookSet(book,ids);
        }
        if (idsRemove.length > 0){
            flag = enyanBookCustomMapper.updateBookSet(bookDefault,idsRemove);
        }

        Iterator<String> it = idSet.iterator();
        while (it.hasNext()){
            Long bookId = Long.parseLong(it.next());
            dataInterface.delBook(bookId);
        }
        return flag;
    }

    @Override
    public int updateBookSingleDiscountAssociateNDiscount(EnyanBook book) {
        int flag = enyanBookCustomMapper.updateBookSingleDiscountAssociateNDiscount(book);
        if (flag > 0){
            dataInterface.delAllBooks();
        }
        return flag;
    }

    @Override
    public void initIndexAllRecommended() {
        this.initIndexRecommendedList();
        this.initIndexCategoryBooks(null);
    }

    @Override
    public void initIndexAllInfo() {
        this.initIndexRecommendedList();
        this.initIndexCategoryBooks(null);
        this.initShopIndex();
    }

    /**
     * <p>初始化首页的缓存信息</p>
     * @param
     * @return void
     * @since : 2024-09-17
     **/
    private void initShopIndex(){
        Constant.shopIndexSet = new HashMap<>();

        ShopIndex shopIndex = new ShopIndex();
        shopIndex.setInit(true);
        Constant.shopIndex = shopIndex;

        shopIndex.setShopTopCategories(Constant.indexCategoryList);
        shopIndex.setEditorList(Constant.indexRecommendedList);

        this.enyanBannerService.resetBanners();
        shopIndex.setShopBanners(Constant.DEFAULT_REST_CONFIG.getMiddleBanners());
        if (null != Constant.DEFAULT_REST_CONFIG.getIndexBanners() && Constant.DEFAULT_REST_CONFIG.getIndexBanners().isEmpty() == false){
            if (Constant.DEFAULT_REST_CONFIG.getIndexBanners().size() > 2){
                int size = Constant.DEFAULT_REST_CONFIG.getIndexBanners().size();
                int topSize = size - 2;
                List<RestBanner> topBanners = Constant.DEFAULT_REST_CONFIG.getIndexBanners().subList(0, topSize);//有图片的
                List<RestBanner> topBlogs = Constant.DEFAULT_REST_CONFIG.getIndexBanners().subList(size - 2, Constant.DEFAULT_REST_CONFIG.getIndexBanners().size());//没有图片
                shopIndex.setTopBanners(topBanners);
                shopIndex.setTopBlogs(topBlogs);
            }else {
                shopIndex.setTopBanners(Constant.DEFAULT_REST_CONFIG.getIndexBanners());
                shopIndex.setTopBlogs(Constant.DEFAULT_REST_CONFIG.getIndexBanners());
            }
        }

        List<EnyanBookList> bookListInList = enyanBookListService.findBookListByIndex(1);//列表中展示的书单
        if (bookListInList == null || bookListInList.size() < 2){//不符合要求，直接回退
            List<EnyanCategory> emptyList = List.of();
            shopIndex.setShopTopMenu(emptyList);
            shopIndex.setShopMenu(emptyList);
            shopIndex.setShopSet(emptyList);
            shopIndex.setShopBottomMenu(emptyList);
            return;
        }

        List<EnyanBookList> shopTopMenuList = bookListInList.subList(0, 2);//需要在首页展示的在套装书上方的书单书籍
        List<EnyanBookList> shopBottomMenuList = bookListInList.subList(2, bookListInList.size());//需要在首页展示的在套装书下方的书单书籍

        shopIndex.setShopTopMenu(this.bookListToCategory(shopTopMenuList,true));
        shopIndex.setShopBottomMenu(this.bookListToCategory(shopBottomMenuList,true));

        List<EnyanBookList> bookListInSet = enyanBookListService.findBookListByIndex(2);//套装书中需展示的书单
        List<EnyanBookSet> bookSetInSet = enyanBookSetService.findBookSetByIndex(2);//套装书中需展示的书系

        List<EnyanCategory> shopSet = this.bookSetToCategory(bookSetInSet,false);
        //shopSet.addAll(this.bookListToCategory(bookListInSet));
        shopIndex.setShopSet(shopSet);

        List<EnyanCategory> shopMenu = this.bookListToCategory(bookListInSet,false);
        shopIndex.setShopMenu(shopMenu);
    }

    /**
     * <p>EnyanBookList转换EnyanCategory</p>
     * @param bookListList
     * @return java.util.List<com.aaron.spring.model.EnyanCategory>
     * @since : 2024-10-04
     **/
    private List<EnyanCategory> bookListToCategory(List<EnyanBookList> bookListList, boolean shouldInitRecords){
        List<EnyanCategory> list = new ArrayList<>();
        for (EnyanBookList obj : bookListList){
            int maxInt = obj.getBookIDs().length > 10 ? 10 : obj.getBookIDs().length;
            EnyanCategory category = new EnyanCategory();
            category.setCategoryId(obj.getSetId());
            category.setCategoryName(obj.getSetName());
            category.setCategoryNameTc(obj.getSetNameTc());
            category.setCategoryNameEn(obj.getSetNameEn());
            category.setBannerUrl(obj.getBannerUrl());
            category.setBookCoverUrl(obj.getBookCover());
            category.setRecords(new ArrayList<>());
            if (shouldInitRecords == true){
                for (int i = 0; i < maxInt; i++) {
                    EnyanBook book = this.queryRecordByPrimaryKey(Long.parseLong(obj.getBookIDs()[i])).getResult();
                    category.getRecords().add(book);
                }
            }

            list.add(category);
        }
        return list;
    }

    /**
     * <p>EnyanBookSet转换EnyanCategory</p>
     * @param bookSetList
     * @return java.util.List<com.aaron.spring.model.EnyanCategory>
     * @since : 2024-10-05
     **/
    private List<EnyanCategory> bookSetToCategory(List<EnyanBookSet> bookSetList, boolean shouldInitRecords){
        List<EnyanCategory> list = new ArrayList<>();
        for (EnyanBookSet obj : bookSetList){
            EnyanCategory category = new EnyanCategory();
            category.setCategoryId(obj.getSetId());
            category.setCategoryName(obj.getSetName());
            category.setCategoryNameTc(obj.getSetNameTc());
            category.setCategoryNameEn(obj.getSetNameEn());
            category.setBannerUrl(obj.getBannerUrl());
            category.setBookCoverUrl(obj.getBookCover());
            category.setRecords(new ArrayList<>());
            if (shouldInitRecords == true){
                List<EnyanBook> bookList = enyanBookCustomMapper.findBookBasicInfoBySetIdTopN(obj.getSetId());
                for (EnyanBook book : bookList) {
                    category.getRecords().add(book);
                }
            }
            list.add(category);
        }
        return list;
    }

    /**
     *
     * 初始化编辑推荐的书籍
     * @Date: 2017/12/27
     */
    private void initIndexRecommendedList(){
        /*
        EnyanBook enyanBook = new EnyanBook();
        enyanBook.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
        //enyanBook.addOrder(new OrderObj("opensale_at", InterfaceContant.OrderBy.DESC));
        enyanBook.setIsRecommended(Constant.BYTE_VALUE_2);
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);

        Page page = new Page();
        page.setPageSize(6);
        enyanBook.setPage(page);

        Constant.indexRecommendedList = this.findBooks(enyanBook);*/
        EnyanCategory categoryRecommended = enyanCategoryService.queryRecordByPrimaryKey(-1L).getResult();
        if (null == categoryRecommended || StringUtils.isBlank(categoryRecommended.getBookRecommended())){
            return;
        }
        List<Long> idsLong = Arrays
                .stream(categoryRecommended.getBookRecommended().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<EnyanBook> list = this.findBookByIds(idsLong);
        Map<Integer, EnyanBook> tree = new TreeMap<>();
        for (EnyanBook book:list){
            if (Constant.BYTE_VALUE_1.equals(book.getShelfStatus()) == false){//没有上架，则直接pass
                continue;
            }
            tree.put(idsLong.indexOf(book.getBookId().longValue()), book);
        }
        Constant.indexRecommendedList = new ArrayList(tree.values());
    }

    @Override
    public void initIndexCategoryBooks(Long categoryId) {
        EnyanCategory enyanCategory = new EnyanCategory();
        enyanCategory.setIsIndex(Constant.BYTE_VALUE_1);
        enyanCategory.setIsHidden(0);
        enyanCategory.addOrder(new OrderObj("category_order",InterfaceContant.OrderBy.DESC));
        List<EnyanCategory> categories = enyanCategoryService.findCategories(enyanCategory);

        for (EnyanCategory category:categories){
            if (null == categoryId){
                this.initIndexCategory(category);
            }else {
                if (category.getCategoryId() == categoryId){//重新更新
                    this.initIndexCategory(category);
                }else {//重复利用
                    this.reuseIndexCategory(category);
                }
            }
        }

        Constant.indexCategoryList = categories;
    }

    /**
     *
     * 初始化分类里的书籍
     * @param category
     * @Date: 2017/12/27
     */
    private void initIndexCategory(EnyanCategory category){
        Page page = new Page();
        page.setPageSize(6);

        /*
        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setCategoryId(category.getCategoryId());
        enyanBook.setIsRecommended(Constant.BYTE_VALUE_1);
        enyanBook.setShelfStatus(Constant.BYTE_VALUE_1);
        enyanBook.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
        //enyanBook.addOrder(new OrderObj("opensale_at",InterfaceContant.OrderBy.DESC));
        enyanBook.setPage(page);
        */
        if (null == category || StringUtils.isBlank(category.getBookRecommended())){
            return;
        }
        List<Long> idsLong = Arrays
                .stream(category.getBookRecommended().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<EnyanBook> list = this.findBookByIds(idsLong);
        Map<Integer, EnyanBook> tree = new TreeMap<>();
        for (EnyanBook book:list){
            if (Constant.BYTE_VALUE_1.equals(book.getShelfStatus()) == false){//没有上架，则直接pass
                continue;
            }
            if (book.getCategoryId().equals(category.getCategoryId()) == false){
                continue;
            }
            tree.put(idsLong.indexOf(book.getBookId().longValue()), book);
        }
        page.setRecords(new ArrayList(tree.values()));
        category.setPage(page);
    }

    /**
     *
     * 重复利用现在分类里的书籍
     * @param category
     * @Date: 2017/12/27
     */
    private void reuseIndexCategory(EnyanCategory category){
        List<EnyanCategory> categoriesOld = Constant.indexCategoryList;
        for (EnyanCategory categoryOld:categoriesOld){
            if (categoryOld.getCategoryId().equals(category.getCategoryId())){
                category.setPage(categoryOld.getPage());
            }
        }
    }
}