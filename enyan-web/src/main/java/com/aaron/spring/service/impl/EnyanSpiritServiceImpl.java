package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.data.DataInterface;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanSpiritMapper;
import com.aaron.spring.mapper.custom.EnyanSpiritCustomMapper;
import com.aaron.spring.model.EnyanSpirit;
import com.aaron.spring.model.EnyanSpiritExample;
import com.aaron.spring.service.EnyanSpiritService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  11/13/20
 * @Modified By:
 */
@Service
public class EnyanSpiritServiceImpl implements EnyanSpiritService {
    @Autowired
    protected EnyanSpiritMapper enyanSpiritMapper;

    @Resource
    private DataInterface dataInterface;

    @Autowired
    protected EnyanSpiritCustomMapper enyanSpiritCustomMapper;

    @Override
    public Page queryRecords(Page<EnyanSpirit> page, EnyanSpirit record) {
        if (null == record){
            record = new EnyanSpirit();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanSpiritExample example = new EnyanSpiritExample();
            EnyanSpiritExample.Criteria criteria = example.createCriteria();

            example.setPage(page);
            if (page.getPageSize() == -1){//不需要分页
                example.setPage(null);
            }
            /*if (StringUtils.isNotBlank(record.getPublisherName())){
                criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
            }*/
            if (null != record.getBookId()){
                criteria.andBookIdEqualTo(record.getBookId());
            }
            if (null != record.getOrderObjList()){
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < record.getOrderObjList().size(); i++) {
                    OrderObj orderObj = record.getOrderObjList().get(i);
                    if (i!=0){
                        buffer.append(",");
                    }
                    buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
                example.setOrderByClause(buffer.toString());
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanSpiritMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanSpirit> list;
            if (count > 0){
                list = enyanSpiritMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }

            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanSpirit> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanSpirit> result = new ExecuteResult<>();
        try {

            EnyanSpirit record = enyanSpiritMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanSpirit();
            }
            result.setResult(record);

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanSpirit> addRecord(EnyanSpirit record) {
        ExecuteResult<EnyanSpirit> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanSpiritMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanSpirit> updateRecord(EnyanSpirit record) {
        ExecuteResult<EnyanSpirit> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanSpiritMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
            dataInterface.delSpiritByBookId(record.getBookId());
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            EnyanSpirit record = enyanSpiritMapper.selectByPrimaryKey(pkId);
            int deleteFlag = enyanSpiritMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }
            dataInterface.delSpiritByBookId(record.getBookId());
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanSpirit record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanSpirit record) {
        return null;
    }

    @Override
    public List<EnyanSpirit> findAllSpirits() {
        return enyanSpiritCustomMapper.findAllSpirits();
    }

    @Override
    public List<EnyanSpirit> findAllSpiritsOnShelf() {
        return enyanSpiritCustomMapper.findAllSpiritsOnShelf();
    }

    @Override
    public EnyanSpirit getSpiritByBookId(Long bookId) {
        EnyanSpirit spirit = dataInterface.getSpiritByBookId(bookId);
        if (null != spirit){
            return spirit;
        }

        EnyanSpiritExample example = new EnyanSpiritExample();
        EnyanSpiritExample.Criteria criteria = example.createCriteria();

        criteria.andBookIdEqualTo(bookId);

        List<EnyanSpirit> list = enyanSpiritMapper.selectByExample(example);
        if (list.isEmpty()){
            return null;
        }
        EnyanSpirit record = list.get(0);
        dataInterface.saveSpiritByBook(record);
        return record;
    }

    @Override
    public void updateByPrimaryKeySelective(EnyanSpirit enyanSpirit, Long bookId) {
        enyanSpiritMapper.updateByPrimaryKeySelective(enyanSpirit);
        dataInterface.delSpiritByBookId(bookId);
    }
}
