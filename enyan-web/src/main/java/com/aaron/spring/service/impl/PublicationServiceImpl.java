package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.drm.model.Licenses;
import com.aaron.drm.model.PublicationLcp;
import com.aaron.drm.util.DRMUtil;
import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.http.HttpResult;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.controller.BookController;
import com.aaron.spring.mapper.EnyanPublisherMapper;
import com.aaron.spring.mapper.PublicationMapper;
import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.model.EnyanPublisherExample;
import com.aaron.spring.model.Publication;
import com.aaron.spring.model.PublicationExample;
import com.aaron.spring.service.PublicationService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2019-06-09
 * @Modified By:
 */
@Service
public class PublicationServiceImpl extends BaseService<Publication, PublicationExample> implements PublicationService {
    private final Logger logger = LoggerFactory.getLogger(PublicationServiceImpl.class);
    @Autowired
    protected PublicationMapper mapper;

    @Override
    public List<Publication> findAllPublicationList(Publication publication) {
        if (null == publication) {
            publication = new Publication();
        }
        List<Publication> list = null;
        try {
            PublicationExample example = new PublicationExample();
            PublicationExample.Criteria criteria = example.createCriteria();

            example.setOrderByClause("id desc");
            if (StringUtils.isNotBlank(publication.getTitle())) {
                criteria.andTitleEqualTo(publication.getTitle());
            }

            if (StringUtils.isNotBlank(publication.getUuid())) {
                criteria.andUuidEqualTo(publication.getUuid());
            }

            list = mapper.selectByExample(example);


        } catch (Exception e) {
            e.printStackTrace();
//            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return list;
    }

    @Override
    public List<NameAndValue> findAllPublicationNameAndValues() {
        List<Publication> list = this.findAllPublicationList(null);

        List<NameAndValue> valueAndNameList = new ArrayList<>();
        NameAndValue first = new NameAndValue();
        first.setValue("-1");
        first.setName("仅用于预上架");
        valueAndNameList.add(first);
        for (Publication publication : list) {
            NameAndValue valueAndName = new NameAndValue();
            valueAndName.setValue(String.valueOf(publication.getId()));
            valueAndName.setName(publication.getTitle());

            valueAndNameList.add(valueAndName);
        }
        return valueAndNameList;
    }

    @Override
    public Page queryRecords(Page<Publication> page, Publication record) {
        if (null == record) {
            record = new Publication();
        }
        if (null == page) {
            page = new Page<>();
        }
        try {
            PublicationExample example = new PublicationExample();
            PublicationExample.Criteria criteria = example.createCriteria();

            example.setPage(page);

            if (StringUtils.isNotBlank(record.getTitle())) {
                criteria.andTitleLike("%" + record.getTitle() + "%");
            }

            long count = page.getTotalRecord();
            if (count <= 0) {
                count = mapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<Publication> list;
            if (count > 0){
                list = mapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }

            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<Publication> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<Publication> result = new ExecuteResult<>();
        try {
            Publication record = mapper.selectByPrimaryKey(pkId.intValue());
            if (null == record) {
                record = new Publication();
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public List<Publication> findPublicationReally(Publication publication) {
        if (null == publication) {
            publication = new Publication();
        }
        List<Publication> list = null;
        try {
            PublicationExample example = new PublicationExample();
            PublicationExample.Criteria criteria = example.createCriteria();

            if (StringUtils.isNotBlank(publication.getTitle())) {
                criteria.andTitleEqualTo(publication.getTitle());
            }

            list = mapper.selectByExample(example);


        } catch (Exception e) {
            e.printStackTrace();
//            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return list;
    }

    @Override
    public ExecuteResult<Publication> addRecordByLcp(PublicationLcp record) {
        ExecuteResult<Publication> result = new ExecuteResult<>();
        String url = DRMUtil.getApiPostPublication();
        try {
            String jsonString = JSONObject.toJSONString(record);
            logger.debug("jsonString:"+jsonString);

            HttpResult httpResult = HttpProtocolHandler.execute(new HashMap<>(), url, HttpMethod.POST, null, jsonString);
            if (!httpResult.isSuccess()) {
                return null;
            }

            //Licenses licenses = JSONObject.parseObject(httpResult.getStringResult(),Licenses.class);
            result.setResult(record);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    @Override
    public ExecuteResult<Publication> addRecord(Publication record) {
        ExecuteResult<Publication> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)) {
                result.addErrorMessage("保存校验失败：" + checkMsg);
                return result;
            }
            int saveFlag = mapper.insert(record);
            if (saveFlag > 0) {
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            } else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<Publication> updateRecord(Publication record) {
        ExecuteResult<Publication> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)) {
                result.addErrorMessage("保存校验失败：" + checkMsg);
                return result;
            }
            int saveFlag = mapper.updateByPrimaryKeySelective(record);
            if (saveFlag > 0) {
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            } else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkUpdateRecord(Publication record) {
        StringBuffer sb = new StringBuffer();
        if (null == record) {
            sb.append("传入对象不可以为空");
            return sb.toString();
        }
        if (record.getId() == 0) {
            sb.append("主键信息不可以为空");
            return sb.toString();
        }
        return null;
    }
}
