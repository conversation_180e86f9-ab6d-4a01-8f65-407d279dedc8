package com.aaron.spring.interceptor;

import com.aaron.annotation.GeoIPRequired;
import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.common.APIConstant;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.DeviceLimit;
import com.aaron.spring.model.UserInfo;
import com.aaron.spring.service.AuthUserService;
import com.aaron.spring.service.GeoIPLocationService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2021/4/9
 * @Modified By:
 */
@Slf4j
public class APIInterceptor implements HandlerInterceptor {
    @Resource
    private AuthUserService authUserService;

    @Resource
    private GeoIPLocationService geoIPLocationService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.debug("APIInterceptor");
        String token = request.getHeader(Constant.TOKEN_NAME);
        String email = request.getHeader(Constant.ACCESS_EMAIL);
        String currency = request.getHeader(Constant.ACCESS_CURRENCY);
        String lang = request.getHeader(Constant.ACCESS_LANG);
        String deviceFrom = request.getHeader(Constant.ACCESS_DEVICE_FROM);
        String deviceVersion = request.getHeader(Constant.ACCESS_DEVICE_VERSION);

        if (!(handler instanceof HandlerMethod)) {
            response.setStatus(500);
            return false;
        }

        if (APIConstant.DeviceFrom.pc.equals(deviceFrom)) {//PC必须访问front接口
            this.sendUpdateErrorResponse(response);
            return false;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        //RestBaseDTO baseDTO = handlerMethod.getMethodAnnotation(RestBaseDTO.class);
        /*
        MethodParameter[] parameters= handlerMethod.getMethodParameters();

        if (null != parameters && parameters.length > 0){
            if (parameters[0].getParameterType())

            if (parameters[0].getParameterType() instanceof RestBaseDTO){

            }
        }*/

        request.setAttribute(Constant.CURRENT_USER_ID, 11L);
        request.setAttribute(Constant.ACCESS_EMAIL, email);
        request.setAttribute(Constant.ACCESS_CURRENCY, currency);
        request.setAttribute(Constant.ACCESS_LANG, lang);
        request.setAttribute(Constant.ACCESS_DEVICE_FROM, deviceFrom);
        request.setAttribute(Constant.ACCESS_DEVICE_VERSION, deviceVersion);

        if (!handlerMethod.getMethod().getDeclaringClass().getName().startsWith(APIConstant.PACKAGE_USE_BEGIN)) {//必须是当前版本
            this.sendUpdateErrorResponse(response);
            return false;
        }

        // 设置接口的IP归属地
        GeoIPRequired geoIPAnnotation = handlerMethod.getMethod().getAnnotation(GeoIPRequired.class);
        if (null != geoIPAnnotation){
            String area = request.getHeader(Constant.ACCESS_AREA);
            if (StringUtils.isBlank(area)){
                String ip = geoIPLocationService.getClientIpAddress(request);
                area = geoIPLocationService.getLocationAreaByIP(ip);
            }
            request.setAttribute(Constant.ACCESS_AREA, area);
        }

        // 判断接口是否不需要登录
        LoginAnonymous loginAnnotation = handlerMethod.getMethod().getAnnotation(LoginAnonymous.class);
        // 有 @LoginAnonymous 注解，不需要认证
        if (loginAnnotation != null) {
            return true;
        }

        if ("licenses".equals(handlerMethod.getMethod().getName())) {
            log.debug("is DRM getLicense");
            return true;
        }

        if ("login".equals(handlerMethod.getMethod().getName())
                || "passwdForget".equals(handlerMethod.getMethod().getName())
                || "launch".equals(handlerMethod.getMethod().getName())) {
            log.debug("token:[{}] is login()",token);
            return true;
        }

        /*//
        if (StringUtils.isBlank(email)){//暂时不启用
            if ("downloadEpub".equals(handlerMethod.getMethod().getName())){//下载的接口需要返回单独的值
                this.sendDownloadErrorResponse(response);
                return false;
            }
            this.sendLoginErrorResponse(response); //
            return false;
            //return true;
        }*/
        if (Constant.IS_LOCAL || Constant.IS_TEST) {
            return true;
        }
        if (StringUtils.isBlank(email) || StringUtils.isBlank(token)) {
            if ("downloadEpub".equals(handlerMethod.getMethod().getName())) {//下载的接口需要返回单独的值
                this.sendDownloadErrorResponse(response);
                return false;
            }
            this.sendTokenErrorResponse(response);
            return false;
        }
        log.debug("token:[{}] is not login()",token);

        if (Constant.TEST_TOKEN_VALUE.equals(token)) { //测试用的token，直接pass
            return true;
        }

        UserInfo userInfo = authUserService.loadUserInfoByEmail(email);
        boolean isTokenValid = false;
        if (null != userInfo && null != userInfo.getCustomUserDetail().getDeviceLimitList()) {
            for (DeviceLimit deviceLimit : userInfo.getCustomUserDetail().getDeviceLimitList()) {
                if (deviceLimit.getToken().equals(token)) {//有当前的token
                    isTokenValid = true;
                    break;
                }
            }
        }
        if (isTokenValid == false) {
            if ("downloadEpub".equals(handlerMethod.getMethod().getName())) {//下载的接口需要返回单独的值
                this.sendDownloadErrorResponse(response);
                return false;
            }
            this.sendTokenErrorResponse(response);
            return false;
        }

        /*if(token == null) {
            response.setStatus(500);
            return false;
        }*/

        return true;
    }

    /**
     * Token失效（设备解绑等）
     *
     * @param response
     * @Date: 2020-02-26
     */
    private void sendTokenErrorResponse(HttpServletResponse response) throws Exception {
        ExecuteResult<String> result = new ExecuteResult<>();
        result.addErrorMessage(InterfaceContant.ApiErrorConfig.TOKEN_ERROR_CODE);

        response.setContentType(APPLICATION_JSON_VALUE);
        PrintWriter writer = response.getWriter();
        writer.print(JSONObject.toJSONString(result, JSONWriter.Feature.WriteMapNullValue));//WriteDateUseDateFormat
        writer.close();
        response.flushBuffer();
    }

    /**
     * 需要登录(计划用到access_token过期)
     *
     * @param response
     * @Date: 2020-03-04
     */
    private void sendLoginErrorResponse(HttpServletResponse response) throws Exception {
        ExecuteResult<String> result = new ExecuteResult<>();
        result.addErrorMessage(InterfaceContant.ApiErrorConfig.LOGIN_ERROR_CODE);

        response.setContentType(APPLICATION_JSON_VALUE);
        PrintWriter writer = response.getWriter();
        writer.print(JSONObject.toJSONString(result, JSONWriter.Feature.WriteMapNullValue));
        writer.close();
        response.flushBuffer();
    }

    /**
     * 非法请求
     *
     * @param response
     * @Date: 2020-02-26
     */
    private void sendRequestErrorResponse(HttpServletResponse response) throws Exception {
        ExecuteResult<String> result = new ExecuteResult<>();
        result.addErrorMessage(InterfaceContant.ApiErrorConfig.ILLEGAL_REQUEST_PARAMETERS_CODE);

        response.setContentType(APPLICATION_JSON_VALUE);
        PrintWriter writer = response.getWriter();
        writer.print(JSONObject.toJSONString(result, JSONWriter.Feature.WriteMapNullValue));
        writer.close();
        response.flushBuffer();
    }

    /**
     * 必须升级的描述 API失效
     *
     * @param response
     * @Date: 2020-02-26
     */
    private void sendUpdateErrorResponse(HttpServletResponse response) throws Exception {
        ExecuteResult<String> result = new ExecuteResult<>();
        result.addErrorMessage(InterfaceContant.ApiErrorConfig.UPDATE_ERROR_CODE);

        response.setContentType(APPLICATION_JSON_VALUE);
        PrintWriter writer = response.getWriter();
        writer.print(JSONObject.toJSONString(result, JSONWriter.Feature.WriteMapNullValue));
        writer.close();
        response.flushBuffer();
    }

    private void sendDownloadErrorResponse(HttpServletResponse response) throws Exception {
        ExecuteResult<String> result = new ExecuteResult<>();
        result.addErrorMessage(InterfaceContant.ApiErrorConfig.UPDATE_ERROR_CODE);

        response.setContentType(APPLICATION_JSON_VALUE);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        PrintWriter writer = response.getWriter();
        writer.print(JSONObject.toJSONString(result, JSONWriter.Feature.WriteMapNullValue));
        writer.close();
        response.flushBuffer();
    }
}
