package com.aaron.spring.interceptor;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.util.CookieUtil;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-04-24
 * @Modified By:
 */
public class EnyanLocaleChangeInterceptor extends LocaleChangeInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws ServletException {

        String newLocale = request.getParameter(getParamName());

        if (newLocale != null) {
            if (checkHttpMethod(request.getMethod())) {
                LocaleResolver localeResolver = RequestContextUtils.getLocaleResolver(request);
                if (localeResolver == null) {
                    throw new IllegalStateException(
                            "No LocaleResolver found: not in a DispatcherServlet request?");
                }
                try {
                    CookieUtil.addCookie(InterfaceContant.CookieName.LANGUAGE, newLocale, response,false,false);
                    Locale locale = enyanParseLocaleValue(newLocale);
                    localeResolver.setLocale(request, response, locale);
                }
                catch (IllegalArgumentException ex) {
                    if (isIgnoreInvalidLocale()) {
                        if (logger.isDebugEnabled()) {
                            logger.debug("Ignoring invalid locale value [" + newLocale + "]: " + ex.getMessage());
                        }
                    }
                    else {
                        throw ex;
                    }
                }
            }
        }
        // Proceed in any case.
        return true;
    }
    private boolean checkHttpMethod(String currentMethod) {
        String[] configuredMethods = getHttpMethods();
        if (ObjectUtils.isEmpty(configuredMethods)) {
            return true;
        }
        for (String configuredMethod : configuredMethods) {
            if (configuredMethod.equalsIgnoreCase(currentMethod)) {
                return true;
            }
        }
        return false;
    }
    Locale enyanParseLocaleValue(String locale) {
        locale = locale.replaceAll("/","");
        if (InterfaceContant.LocaleLang.SC_SHORT.equals(locale)){
            locale = InterfaceContant.LocaleLang.SC;
        }else if (InterfaceContant.LocaleLang.TC_SHORT.equals(locale)){
            locale = InterfaceContant.LocaleLang.TC;
        }else if (InterfaceContant.LocaleLang.ENG_SHORT.equals(locale)){
            locale = InterfaceContant.LocaleLang.ENG;
        }else if (InterfaceContant.LocaleLang.SC.equals(locale) || InterfaceContant.LocaleLang.TC.equals(locale)
                    || InterfaceContant.LocaleLang.ENG.equals(locale)){

        }else{
            locale = InterfaceContant.LocaleLang.SC;
        }
        return super.parseLocaleValue(locale);
    }
}
