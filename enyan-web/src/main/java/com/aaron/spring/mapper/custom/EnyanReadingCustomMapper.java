package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanReading;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanReadingCustomMapper {
    @Update({"update enyan_reading set is_deleted = 1 where data_id = #{dataId}"})
    int updateRecordToDeletedById(@Param("dataId") Long dataId);

    @Update({"update enyan_reading set data_status = 0 where is_deleted = 0  and end_at < now()"})
    int updateRecordToExpired();

    @Select("select * from enyan_reading where is_deleted = 0  order by data_priority desc")
    List<EnyanReading> findRecords();

    @Select("select * from enyan_reading where is_deleted = 0  and end_at < now()")
    List<EnyanReading> findRecordsExpired();
}