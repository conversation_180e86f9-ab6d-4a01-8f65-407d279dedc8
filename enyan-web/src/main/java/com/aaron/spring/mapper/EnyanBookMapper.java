package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanBookMapper {
    long countByExample(EnyanBookExample example);

    int deleteByExample(EnyanBookExample example);

    int deleteByPrimaryKey(Long bookId);

    int insert(EnyanBook record);

    int insertSelective(EnyanBook record);

    List<EnyanBook> selectByExampleWithBLOBs(EnyanBookExample example);

    List<EnyanBook> selectByExample(EnyanBookExample example);

    EnyanBook selectByPrimaryKey(Long bookId);

    int updateByExampleSelective(@Param("record") EnyanBook record, @Param("example") EnyanBookExample example);

    int updateByExampleWithBLOBs(@Param("record") EnyanBook record, @Param("example") EnyanBookExample example);

    int updateByExample(@Param("record") EnyanBook record, @Param("example") EnyanBookExample example);

    int updateByPrimaryKeySelective(EnyanBook record);

    int updateByPrimaryKeyWithBLOBs(EnyanBook record);

    int updateByPrimaryKey(EnyanBook record);
}