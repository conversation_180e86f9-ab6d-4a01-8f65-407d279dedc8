package com.aaron.spring.mapper.custom;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanBookCustomMapper {

    List<EnyanBook> selectByExampleWithBLOBs(EnyanBookExample example);

    long searchCountByExample(EnyanBookExample example);

    List<EnyanBook> searchByExampleWithBLOBs(EnyanBookExample example);

    List<EnyanBook> searchByExample(EnyanBookExample example);

    List<EnyanBook> searchByTitleOrAuthor(@Param("record") EnyanBook record, @Param("page")Page page);

    long searchCountByTitleOrAuthor(@Param("record") EnyanBook record,  @Param("page")Page page);

    List<EnyanBook> searchByAuthor(@Param("record") EnyanBook record, @Param("page")Page page);

    long searchCountByAuthor(@Param("record") EnyanBook record,  @Param("page")Page page);

    int updateBookSaleVolumeAdd(@Param("bookIdList") List<Long> bookIdList);

    int updateBookSaleVolumeMinus(@Param("bookIdList") List<Long> bookIdList);

    List<EnyanBook> findBookByIds(@Param("bookIdList") List<Long> bookIdList);

    List<EnyanBook> findBookBasicInfoByIds(@Param("bookIdList") List<Long> bookIdList);

    List<EnyanBook> findBookBasicInfoByIdsArray(@Param("bookIdList") Long[] bookIds);

    List<EnyanBook> findBookIDAndNameByIds(@Param("bookIdList") List<Long> bookIdList);

    List<EnyanBook> findBookIDAndNameByIdsArray(@Param("bookIdList") Long[] bookIds);

    @Select({"<script>select book_id,book_title from enyan_book " +
                     "where book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    List<EnyanBook> findBookIDAndNameByIdsList(@Param("ids") List<Long> bookIdList);

    @Select({"<script>select book_id,book_title,author,price,price_hkd,category_id,publisher_id,publisher_name,book_cover,book_cover_app,discount_single_id,discount_single_type,discount_single_value,discount_single_is_valid,discount_id,discount_description,discount_is_valid,show_publisher,sales_model from enyan_book " +
                     "where book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    List<EnyanBook> findBookIDAndNameAndPriceByIdsList(@Param("ids") List<Long> bookIdList);

    int updateByExample(@Param("record") EnyanBook record, @Param("example") EnyanBookExample example);

    List<EnyanBook> findBookNotCost(@Param("publisherId") Long publisherId);

    List<EnyanBook> findBookBasicInfo(@Param("saleStatus")Integer saleStatus);

    List<EnyanBook> findBookBasicInfoBySetIdTopN(@Param("setId") Long setId);

    /**
     * <p>特价书籍列表</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2021/9/2
     **/
    @Select("select book_id,book_title,publisher_id,publisher_name,special_offer from enyan_book where special_offer = 1")
    List<EnyanBook> findBookBasicInfoSpecialOffer();


    /**
     * <p>获取上架的大于bookId的书籍列表</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2021/9/2
     **/
    @Select("select * from enyan_book where shelf_status =1 and book_Id > #{bookId}")
    List<EnyanBook> findBookGTBookId(@Param("bookId")Long bookId);

    @Select("select book_id, book_title, book_pinyin, author, author_bio, translator, word_count, product_web from enyan_book where book_Id > #{bookId} limit 1")
    List<EnyanBook> findTop1BasicBookGTBookId(@Param("bookId")Long bookId);

    /**
     * <p>更新相应书籍到 特价状态与否</p>
     * @param ids
     * @param specialOfferOrNO
     * @return int
     * @since : 2021/9/2
     **/
    @Update({"<script>update enyan_book set special_offer = #{specialOfferOrNO} " +
                     "where book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    int updateBooksToSpecialOffer(@Param("ids")String[] ids, @Param("specialOfferOrNO")Integer specialOfferOrNO);
//    List<EnyanBook> findBookBasicInfoByIdString(@Param("idsString")String ids);

    /**
     * <p>根据书系ID获取书籍列表</p>
     * @param setId
     * @return java.util.List<com.aaron.spring.model.EnyanBook>
     * @since : 2024-09-21
     **/
    List<EnyanBook> findBookBasicInfoBySetId(@Param("setId")Long setId);

    @Select("select book_id,book_title from enyan_book")
    List<EnyanBook> findBookIDAndName();

    //@Select("select * from enyan_book where is_recommended <> 0 order by recommended_order desc")
    //List<EnyanBook> findBooksRecommended();

    @Select("select book_id,book_title from enyan_book where discount_id = #{discountId}")
    List<EnyanBook> findBookIDAndNameByDiscountId(@Param("discountId")Long discountId);

    @Select("select book_id,book_title from enyan_book where discount_single_id = #{discountSingleId}")
    List<EnyanBook> findBookIDAndNameByDiscountSingleId(@Param("discountSingleId")Long discountSingleId);

    @Select("select book_id,book_title from enyan_book where set_id = #{setId}")
    List<EnyanBook> findBookIDAndNameBySetId(@Param("setId")Long setId);

    @Select("select book_id,book_title,author,price,price_hkd,category_id,publisher_id,publisher_name,book_cover,book_cover_app,discount_single_id,discount_single_type,discount_single_value,discount_single_is_valid,discount_id,discount_description,discount_is_valid,show_publisher,sales_model from enyan_book where set_id = #{setId}")
    List<EnyanBook> findBookIdAndNameAndPriceListBySetId(@Param("setId")Long setId);

    @Update({"<script>update enyan_book set discount_single_id = #{book.discountSingleId}, " +
            "discount_single_type=#{book.discountSingleType}, discount_single_value=#{book.discountSingleValue}, " +
            "discount_single_description=#{book.discountSingleDescription}, discount_single_is_valid=#{book.discountSingleIsValid}, " +
            "discount_single_start_time=#{book.discountSingleStartTime}, discount_single_end_time=#{book.discountSingleEndTime} " +
            "where book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>"})
    int updateBookDiscountSingle(@Param("book")EnyanBook book, @Param("ids")String[] ids);

    @Update({"<script>update enyan_book set discount_id = #{book.discountId}, " +
            "discount_description=#{book.discountDescription}, discount_is_valid=#{book.discountIsValid} " +
            "where book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>"})
    int updateBookDiscount(@Param("book")EnyanBook book, @Param("ids")String[] ids);

    @Update({"<script>update enyan_book set set_id = #{book.setId},set_name=#{book.setName} " +
                     "where book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    int updateBookSet(@Param("book")EnyanBook book, @Param("ids")String[] ids);

    @Select("select book_id,discount_single_id,discount_id from enyan_book where (discount_single_is_valid = 1 or discount_is_valid = 1)")
    List<EnyanBook> findBooksByDiscountValid();

    @Update({"update enyan_book set discount_single_value=#{book.discountSingleValue}, " +
            "discount_single_description=#{book.discountSingleDescription}, discount_single_is_valid=#{book.discountSingleIsValid}, " +
            "discount_single_start_time=#{book.discountSingleStartTime}, discount_single_end_time=#{book.discountSingleEndTime} " +
            "where discount_single_id = #{discountId}"})
    int updateBookDiscountSingleByDiscountId(@Param("book")EnyanBook book, @Param("discountId")Long discountId);

    @Update({"update enyan_book set discount_description=#{book.discountDescription}, discount_is_valid=#{book.discountIsValid} " +
            "where discount_id=#{discountId}"})
    int updateBookDiscountByDiscountId(@Param("book")EnyanBook book, @Param("discountId")Long discountId);

    @Update({"update enyan_book set category_name=#{categoryName} where category_id=#{categoryId}"})
    int updateBookCategoryName(@Param("categoryName")String categoryName, @Param("categoryId")Long categoryId);

    @Update({"update enyan_book set set_name=#{setName} where set_id=#{setId}"})
    int updateBookSetName(@Param("setName")String setName, @Param("setId")Long setId);

    @Update({"update enyan_book set discount_single_id = #{book.discountSingleId}, discount_single_value=#{book.discountSingleValue}, " +
            "discount_single_description=#{book.discountSingleDescription}, discount_single_is_valid=#{book.discountSingleIsValid}, " +
            "discount_single_start_time=#{book.discountSingleStartTime}, discount_single_end_time=#{book.discountSingleEndTime} " +
            "where discount_id = 1"})
    int updateBookSingleDiscountAssociateNDiscount(@Param("book")EnyanBook book);

    @Update({"update enyan_book set star = #{star}, star_Count=#{starCount} where book_id = #{bookId}"})
    int updateBookStarByBookId(@Param("bookId")Long bookId, @Param("star")String star, @Param("starCount")Integer starCount);

    @Select("select book_id,book_title,price_hkd,category_id,publisher_id,publisher_name,book_cover,book_cover_app,discount_single_id,discount_single_type,discount_single_value,discount_single_is_valid,discount_id,discount_description,discount_is_valid,show_publisher,sales_model from enyan_book where shelf_status = 1 and author = #{author} and price > 0 LIMIT 12 OFFSET #{recordIndex}")
    List<EnyanBook> findBookRecommendByAuthor(@Param("author")String author, @Param("recordIndex")Integer recordIndex);

    @Select("select count(1) from enyan_book where shelf_status = 1 and  author = #{author} and price > 0")
    long countBookRecommendByAuthor(@Param("author")String author);

    @Select("select book_id,book_title,price_hkd,category_id,publisher_id,publisher_name,book_cover,book_cover_app,discount_single_id,discount_single_type,discount_single_value,discount_single_is_valid,discount_id,discount_description,discount_is_valid,show_publisher,sales_model from enyan_book where shelf_status = 1 and set_id=#{setId} and price > 0 LIMIT 12 OFFSET #{recordIndex}")
    List<EnyanBook> findBookRecommendBySetId(@Param("setId")Long setId, @Param("recordIndex")Integer recordIndex);

    @Select("select count(1) from enyan_book where shelf_status = 1 and  set_id=#{setId} and price > 0")
    long countBookRecommendBySetId(@Param("setId")Long setId);

    @Select("select book_id,book_title,price_hkd,category_id,publisher_id,publisher_name,book_cover,book_cover_app,discount_single_id,discount_single_type,discount_single_value,discount_single_is_valid,discount_id,discount_description,discount_is_valid,show_publisher,sales_model from enyan_book where shelf_status = 1 and  category_id=#{categoryId} and price > 0 order by recommended_order desc LIMIT 12")
    List<EnyanBook> findBookRecommendTopByCategory(@Param("categoryId")Long categoryId);

    @Select("select book_id,book_title,price_hkd,category_id,publisher_id,publisher_name,book_cover,book_cover_app,discount_single_id,discount_single_type,discount_single_value,discount_single_is_valid,discount_id,discount_description,discount_is_valid,show_publisher,sales_model from enyan_book where shelf_status = 1 and  category_id=#{categoryId} and price > 0 LIMIT 12 OFFSET #{recordIndex}")
    List<EnyanBook> findBookRecommendByCategory(@Param("categoryId")Long categoryId, @Param("recordIndex")Integer recordIndex);

    @Select("select count(1) from enyan_book where shelf_status = 1 and category_id=#{categoryId} and price > 0")
    long countBookRecommendByCategory(@Param("categoryId")Long categoryId);
}