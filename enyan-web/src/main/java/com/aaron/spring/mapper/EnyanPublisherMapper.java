package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.model.EnyanPublisherExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanPublisherMapper {
    long countByExample(EnyanPublisherExample example);

    int deleteByExample(EnyanPublisherExample example);

    int deleteByPrimaryKey(Long publisherId);

    int insert(EnyanPublisher record);

    int insertSelective(EnyanPublisher record);

    List<EnyanPublisher> selectByExampleWithBLOBs(EnyanPublisherExample example);

    List<EnyanPublisher> selectByExample(EnyanPublisherExample example);

    EnyanPublisher selectByPrimaryKey(Long publisherId);

    int updateByExampleSelective(@Param("record") EnyanPublisher record, @Param("example") EnyanPublisherExample example);

    int updateByExampleWithBLOBs(@Param("record") EnyanPublisher record, @Param("example") EnyanPublisherExample example);

    int updateByExample(@Param("record") EnyanPublisher record, @Param("example") EnyanPublisherExample example);

    int updateByPrimaryKeySelective(EnyanPublisher record);

    int updateByPrimaryKeyWithBLOBs(EnyanPublisher record);

    int updateByPrimaryKey(EnyanPublisher record);
}