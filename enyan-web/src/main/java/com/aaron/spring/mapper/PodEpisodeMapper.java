package com.aaron.spring.mapper;

import com.aaron.spring.model.PodEpisode;
import com.aaron.spring.model.PodEpisodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PodEpisodeMapper {
    long countByExample(PodEpisodeExample example);

    int deleteByExample(PodEpisodeExample example);

    int deleteByPrimaryKey(Long episodeId);

    int insert(PodEpisode record);

    int insertSelective(PodEpisode record);

    List<PodEpisode> selectByExample(PodEpisodeExample example);

    PodEpisode selectByPrimaryKey(Long episodeId);

    int updateByExampleSelective(@Param("record") PodEpisode record, @Param("example") PodEpisodeExample example);

    int updateByExample(@Param("record") PodEpisode record, @Param("example") PodEpisodeExample example);

    int updateByPrimaryKeySelective(PodEpisode record);

    int updateByPrimaryKey(PodEpisode record);
}
