package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanBookBuy;
import com.aaron.spring.model.EnyanBookBuyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanBookBuyMapper {
    long countByExample(EnyanBookBuyExample example);

    int deleteByExample(EnyanBookBuyExample example);

    int deleteByPrimaryKey(Long bookBuyId);

    int insert(EnyanBookBuy record);

    int insertSelective(EnyanBookBuy record);

    List<EnyanBookBuy> selectByExample(EnyanBookBuyExample example);

    EnyanBookBuy selectByPrimaryKey(Long bookBuyId);

    int updateByExampleSelective(@Param("record") EnyanBookBuy record, @Param("example") EnyanBookBuyExample example);

    int updateByExample(@Param("record") EnyanBookBuy record, @Param("example") EnyanBookBuyExample example);

    int updateByPrimaryKeySelective(EnyanBookBuy record);

    int updateByPrimaryKey(EnyanBookBuy record);
}