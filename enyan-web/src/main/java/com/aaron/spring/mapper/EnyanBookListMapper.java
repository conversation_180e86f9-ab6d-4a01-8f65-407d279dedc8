package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanBookList;
import com.aaron.spring.model.EnyanBookListExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanBookListMapper {
    long countByExample(EnyanBookListExample example);

    int deleteByExample(EnyanBookListExample example);

    int deleteByPrimaryKey(Long setId);

    int insert(EnyanBookList record);

    int insertSelective(EnyanBookList record);

    List<EnyanBookList> selectByExample(EnyanBookListExample example);

    EnyanBookList selectByPrimaryKey(Long setId);

    int updateByExampleSelective(@Param("record") EnyanBookList record, @Param("example") EnyanBookListExample example);

    int updateByExample(@Param("record") EnyanBookList record, @Param("example") EnyanBookListExample example);

    int updateByPrimaryKeySelective(EnyanBookList record);

    int updateByPrimaryKey(EnyanBookList record);
}