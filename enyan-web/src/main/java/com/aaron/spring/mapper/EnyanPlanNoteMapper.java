package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanPlanNote;
import com.aaron.spring.model.EnyanPlanNoteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanPlanNoteMapper {
    long countByExample(EnyanPlanNoteExample example);

    int deleteByExample(EnyanPlanNoteExample example);

    int deleteByPrimaryKey(Long id);

    int insert(EnyanPlanNote record);

    int insertSelective(EnyanPlanNote record);

    List<EnyanPlanNote> selectByExample(EnyanPlanNoteExample example);

    EnyanPlanNote selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") EnyanPlanNote record, @Param("example") EnyanPlanNoteExample example);

    int updateByExample(@Param("record") EnyanPlanNote record, @Param("example") EnyanPlanNoteExample example);

    int updateByPrimaryKeySelective(EnyanPlanNote record);

    int updateByPrimaryKey(EnyanPlanNote record);
}