package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanBannerExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanBannerMapper {
    long countByExample(EnyanBannerExample example);

    int deleteByExample(EnyanBannerExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(EnyanBanner record);

    int insertSelective(EnyanBanner record);

    List<EnyanBanner> selectByExample(EnyanBannerExample example);

    EnyanBanner selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") EnyanBanner record, @Param("example") EnyanBannerExample example);

    int updateByExample(@Param("record") EnyanBanner record, @Param("example") EnyanBannerExample example);

    int updateByPrimaryKeySelective(EnyanBanner record);

    int updateByPrimaryKey(EnyanBanner record);
}