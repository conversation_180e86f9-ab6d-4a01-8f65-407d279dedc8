package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanBannerExample;
import com.aaron.spring.model.EnyanBookBuy;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanBannerCustomMapper {
    @Update({"update enyan_banner set is_deleted = 1 where data_id = #{dataId}"})
    int updateRecordToDeletedById(@Param("dataId") Long dataId);

    @Update({"update enyan_banner set data_status = 0 where is_deleted = 0 and data_status = 1 and end_at < now()"})
    int updateRecordToExpired();

    @Select("select * from enyan_banner where is_deleted = 0 and data_status = 1 order by data_priority desc")
    List<EnyanBanner> findRecords();

    @Select("select * from enyan_banner where is_deleted = 0 and data_status = 1 and end_at < now()")
    List<EnyanBanner> findRecordsExpired();
}