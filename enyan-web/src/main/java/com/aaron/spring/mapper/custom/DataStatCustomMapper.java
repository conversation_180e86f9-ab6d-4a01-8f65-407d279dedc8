package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.DataStat;
import com.aaron.spring.model.DataStatExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DataStatCustomMapper {

    @Select("select COUNT(1) from enyan_wish where user_email = #{email} and book_id = #{bookId}")
    long countOfWishByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Update({"update data_stat set sales_volume = #{dataStat.salesVolume}, income_total = #{dataStat.incomeTotal}, " +
                     "order_count = #{dataStat.orderCount}, order_fee_count = #{dataStat.orderFeeCount}, order_free_count = #{dataStat.orderFreeCount}, " +
                     "user_buy_count = #{dataStat.userBuyCount} where DATE_FORMAT(create_time,'%Y%m%d') = #{yyyyMMdd}"})
    int updateDataStatByDay(@Param("dataStat") DataStat dataStat, @Param("yyyyMMdd")String yyyyMMdd);

    /**
     * <p>获取根据Day展示的列表数据</p>
     * @param beginTime
     * @param endTime
     * @return java.util.List<com.aaron.spring.model.DataStat>
     * @since : 2021/9/27
     **/
    @Select("select sales_volume as 'sales_volume', income_total as 'income_total', order_count as 'order_count', " +
                    "order_fee_count as 'order_fee_count', order_free_count as 'order_free_count', user_buy_count as 'user_buy_count', " +
                    "user_new_count as 'user_new_count', DATE_FORMAT(create_time,'%Y%m%d') as dateString from data_stat " +
                    "where DATE_FORMAT(create_time,'%Y%m%d') between #{beginTime} and #{endTime}" +
                    " order by dateString desc")
    List<DataStat> findDataByDay(@Param("beginTime")String beginTime, @Param("endTime")String endTime);

    /**
     * <p>获取根据Month展示的列表数据</p>
     * @param beginTime
     * @param endTime
     * @return java.util.List<com.aaron.spring.model.DataStat>
     * @since : 2021/9/27
     **/
    @Select("select sum(sales_volume) as sales_volume, sum(income_total) as income_total, sum(order_count) as order_count, " +
                    "sum(order_fee_count) as order_fee_count, sum(order_free_count) as order_free_count, sum(user_buy_count) as user_buy_count, " +
                    "sum(user_new_count) as user_new_count, DATE_FORMAT(create_time,'%Y%m') as dateString from data_stat " +
                    "where DATE_FORMAT(create_time,'%Y%m') between #{beginTime} and #{endTime} " +
                    "group by dateString order by dateString desc")
    List<DataStat> findDataByMonth(@Param("beginTime")String beginTime, @Param("endTime")String endTime);

    /**
     * <p>获取根据Year展示的列表数据</p>
     * @param beginTime
     * @param endTime
     * @return java.util.List<com.aaron.spring.model.DataStat>
     * @since : 2021/9/27
     **/
    @Select("select sum(sales_volume) as sales_volume, sum(income_total) as income_total, sum(order_count) as order_count, " +
                    "sum(order_fee_count) as order_fee_count, sum(order_free_count) as order_free_count, sum(user_buy_count) as user_buy_count, " +
                    "sum(user_new_count) as user_new_count, DATE_FORMAT(create_time,'%Y') as dateString from data_stat " +
                    "where DATE_FORMAT(create_time,'%Y') between #{beginTime} and #{endTime} " +
                    "group by dateString order by dateString desc")
    List<DataStat> findDataByYear(@Param("beginTime")String beginTime, @Param("endTime")String endTime);
}