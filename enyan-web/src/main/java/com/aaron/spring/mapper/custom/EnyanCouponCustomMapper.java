package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCoupon;
import com.aaron.spring.model.EnyanCouponExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanCouponCustomMapper {

    /**
     * <p>根据优惠码获取列表</p>
     * @param code
     * @return java.util.List<com.aaron.spring.model.EnyanCoupon>
     * @since : 2021/9/1
     **/
    @Select("select * from enyan_coupon where coupon_code = #{code} and is_deleted = 0")
    List<EnyanCoupon> getCouponRecordByCode(@Param("code")String code);


    @Update({"update enyan_coupon set buy_count = buy_count+1 where data_id=#{dataId}"})
    int updateBookDiscountByCouponId(@Param("dataId")Long dataId);

    @Update({"update enyan_coupon set buy_count = buy_count+1 where coupon_code = #{code}"})
    int updateCouponBuyCountByCode(@Param("code")String code);

    int updateByExampleSelective(@Param("record") EnyanCoupon record, @Param("example") EnyanCouponExample example);

    int updateByExample(@Param("record") EnyanCoupon record, @Param("example") EnyanCouponExample example);

}