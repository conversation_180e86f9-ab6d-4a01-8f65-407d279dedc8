package com.aaron.spring.mapper;

import com.aaron.spring.model.Publication;
import com.aaron.spring.model.PublicationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PublicationMapper {
    long countByExample(PublicationExample example);

    int deleteByExample(PublicationExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(Publication record);

    int insertSelective(Publication record);

    List<Publication> selectByExample(PublicationExample example);

    Publication selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") Publication record, @Param("example") PublicationExample example);

    int updateByExample(@Param("record") Publication record, @Param("example") PublicationExample example);

    int updateByPrimaryKeySelective(Publication record);

    int updateByPrimaryKey(Publication record);
}