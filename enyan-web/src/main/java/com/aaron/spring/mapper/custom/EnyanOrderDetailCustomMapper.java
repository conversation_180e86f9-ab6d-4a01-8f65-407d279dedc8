package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanOrderDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanOrderDetailCustomMapper {

    List<EnyanOrderDetail> queryOrderDetailByBook(@Param("record") EnyanOrderDetail record);

    long countByBook(@Param("record") EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderDetailByVendor(@Param("record") EnyanOrderDetail record);

    long countByVendor(@Param("record") EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderDetailByDay(@Param("record") EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderDetailByWeek(@Param("record") EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderDetailByMonth(@Param("record") EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderDetailByYear(@Param("record") EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderDetailBySpecialMonth(@Param("record") EnyanOrderDetail record);

    List<EnyanOrderDetail> searchRecordsByUserAndBookAndAuthorList(@Param("record") EnyanOrderDetail record);

    long countBySpecialMonth(@Param("record") EnyanOrderDetail record);

    long countRecordsByUserAndBookAndAuthorList(@Param("record") EnyanOrderDetail record);

    long countByUser(@Param("record") EnyanOrderDetail record);

    int updateByOrderAndBookKeySelective(EnyanOrderDetail record);

    /**
     *
     * 因为更换了DR吗，更新订单明细的DRM匹配
     * @param newPublicationId
     * @param bookId
     * @Date: 2020-06-22
     */
    int updateOrderDetailPublicationIdByBookId(@Param("newPublicationId") Integer newPublicationId, @Param("bookId") Long bookId);

    @Select("select book_id,book_title from enyan_order_detail where user_email = #{email}")
    List<EnyanOrderDetail> findBookIDAndNameByEmail(@Param("email")String email);

    @Select("select book_id,book_title,user_email from enyan_order_detail where book_id = #{bookId}")
    List<EnyanOrderDetail> findBookIDAndNameAndEmailByBookID(@Param("bookId")Long bookId);

    @Select("select book_id,book_title from enyan_order_detail where user_email = #{email} and book_id = #{bookId}")
    List<EnyanOrderDetail> getBookIDAndNameByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Update({"update enyan_order_detail set is_deleted = 1 where order_detail_id = #{orderDetailId}"})
    int updateOrderDetailDeletedByBookId( @Param("orderDetailId") Long orderDetailId);

    /**
     * <p>Day的销量(salesVolume)</p>
     * @param
     * @return long
     * @since : 2021/9/26
     **/
    @Select("select count(1) from enyan_order_detail where is_deleted = 0 and DATE_FORMAT(purchased_at,'%Y%m%d') = #{yyyyMMdd}")
    long countOfBooksByDay(@Param("yyyyMMdd")String yyyyMMdd);

    @Update({"update enyan_order_detail set user_email = #{revokedEmail} where user_email = #{email}"})
    int revokeUser(@Param("email")String email, @Param("revokedEmail")String revokedEmail);

    /**
     * <p>购买的兑换码订单</p>
     * @param
     * @return java.util.List<com.aaron.spring.model.EnyanOrderDetail>
     * @since : 2023/1/6
     **/
    @Select("select book_id,book_title,user_email from enyan_order_detail where order_type = 1")
    List<EnyanOrderDetail> findBookIDAndNameAndEmailByRedeem();

    /**
     * <p>直接购买的电子书数量</p>
     * @param email
     * @param bookId
     * @return long
     * @since : 2023/1/6
     **/
    @Select("select count(1) from enyan_order_detail where order_type = 0 and is_deleted = 0 and user_email = #{email} and book_id = #{bookId}")
    long countOfBookBuyByEmail(@Param("email")String email, @Param("bookId")Long bookId);
}