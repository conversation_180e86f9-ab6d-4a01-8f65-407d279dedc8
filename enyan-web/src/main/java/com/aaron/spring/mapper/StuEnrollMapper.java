package com.aaron.spring.mapper;

import com.aaron.spring.model.StuEnroll;
import com.aaron.spring.model.StuEnrollExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StuEnrollMapper {
    long countByExample(StuEnrollExample example);

    int deleteByExample(StuEnrollExample example);

    int deleteByPrimaryKey(Long dataId);

    int insert(StuEnroll record);

    int insertSelective(StuEnroll record);

    List<StuEnroll> selectByExample(StuEnrollExample example);

    StuEnroll selectByPrimaryKey(Long dataId);

    int updateByExampleSelective(@Param("record") StuEnroll record, @Param("example") StuEnrollExample example);

    int updateByExample(@Param("record") StuEnroll record, @Param("example") StuEnrollExample example);

    int updateByPrimaryKeySelective(StuEnroll record);

    int updateByPrimaryKey(StuEnroll record);
}