package com.aaron.spring.mapper;

import com.aaron.spring.model.PodUserEpisodeInteraction;
import com.aaron.spring.model.PodUserEpisodeInteractionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PodUserEpisodeInteractionMapper {
    long countByExample(PodUserEpisodeInteractionExample example);

    int deleteByExample(PodUserEpisodeInteractionExample example);

    int deleteByPrimaryKey(Long interactionId);

    int insert(PodUserEpisodeInteraction record);

    int insertSelective(PodUserEpisodeInteraction record);

    List<PodUserEpisodeInteraction> selectByExample(PodUserEpisodeInteractionExample example);

    PodUserEpisodeInteraction selectByPrimaryKey(Long interactionId);

    int updateByExampleSelective(@Param("record") PodUserEpisodeInteraction record, @Param("example") PodUserEpisodeInteractionExample example);

    int updateByExample(@Param("record") PodUserEpisodeInteraction record, @Param("example") PodUserEpisodeInteractionExample example);

    int updateByPrimaryKeySelective(PodUserEpisodeInteraction record);

    int updateByPrimaryKey(PodUserEpisodeInteraction record);
}
