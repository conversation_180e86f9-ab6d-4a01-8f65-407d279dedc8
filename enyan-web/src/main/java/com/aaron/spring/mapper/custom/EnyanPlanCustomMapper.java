package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanPlan;
import com.aaron.spring.model.EnyanPlanExample;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanPlanCustomMapper {
    long countByExample(EnyanPlanExample example);

    int deleteByExample(EnyanPlanExample example);

    int deleteByPrimaryKey(Long id);

    int insert(EnyanPlan record);

    int insertSelective(EnyanPlan record);

    List<EnyanPlan> selectByExampleWithBLOBs(EnyanPlanExample example);

    List<EnyanPlan> selectByExample(EnyanPlanExample example);

    EnyanPlan selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") EnyanPlan record, @Param("example") EnyanPlanExample example);

    int updateByExampleWithBLOBs(@Param("record") EnyanPlan record, @Param("example") EnyanPlanExample example);

    int updateByExample(@Param("record") EnyanPlan record, @Param("example") EnyanPlanExample example);

    int updateByPrimaryKeySelective(EnyanPlan record);

    int updateByPrimaryKeyWithBLOBs(EnyanPlan record);

    int updateByPrimaryKey(EnyanPlan record);

    @Select("select plan_id,user_email,book_id,name,start_from,has_buy,is_deleted,update_time from enyan_plan where user_email = #{email} and book_id = #{bookId}")
    EnyanPlan getBasicInfoByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Select("select * from enyan_plan where user_email = #{email} and book_id = #{bookId}")
    EnyanPlan getFullInfoByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Update({"update enyan_plan set name = #{plan.name}, is_deleted=0, start_from=#{plan.startFrom}, finished_bit_set = null, update_time = #{plan.updateTime}, has_buy = #{plan.hasBuy}" +
            " where user_email = #{plan.userEmail} and book_id = #{plan.bookId}"})
    int resetPlan(@Param("plan")EnyanPlan plan);

    @Update({"update enyan_plan set is_deleted=0, start_from=#{plan.startFrom}, finished_bit_set = #{plan.finishedBitSet}, update_time = #{plan.updateTime}" +
            " where user_email = #{plan.userEmail} and book_id = #{plan.bookId}"})
    int adjustPlan(@Param("plan")EnyanPlan plan);

    @Update({"update enyan_plan set is_deleted=1, update_time = #{plan.updateTime}" +
            " where user_email = #{plan.userEmail} and book_id = #{plan.bookId}"})
    int removePlan(@Param("plan")EnyanPlan plan);

    @Update({"<script>update enyan_plan set has_buy = 1, update_time = #{updateTime} " +
            " where user_email = #{email} and book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>"})
    int updatePlanHasBuyByEmailAndIds(@Param("email")String email,@Param("updateTime")Long updateTime, @Param("ids")String[] ids);

    int updateSyncPlan(@Param("userEmail")String userEmail, @Param("list")List<EnyanPlan> list);

    @Update({"update enyan_plan set is_deleted=#{plan.isDeleted}, finished_bit_set = #{plan.finishedBitSet}, update_time = #{plan.updateTime}" +
            " where user_email = #{plan.userEmail} and book_id = #{plan.bookId}"})
    int updateSyncPlanSingle(@Param("plan")EnyanPlan plan);

    @Delete({"delete from enyan_plan where user_email = #{email}"})
    int deleteAllByEmail(@Param("email")String email);
}