package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanPayRate;
import com.aaron.spring.model.EnyanPayRateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanPayRateMapper {
    long countByExample(EnyanPayRateExample example);

    int deleteByExample(EnyanPayRateExample example);

    int deleteByPrimaryKey(Long payRateId);

    int insert(EnyanPayRate record);

    int insertSelective(EnyanPayRate record);

    List<EnyanPayRate> selectByExample(EnyanPayRateExample example);

    EnyanPayRate selectByPrimaryKey(Long payRateId);

    int updateByExampleSelective(@Param("record") EnyanPayRate record, @Param("example") EnyanPayRateExample example);

    int updateByExample(@Param("record") EnyanPayRate record, @Param("example") EnyanPayRateExample example);

    int updateByPrimaryKeySelective(EnyanPayRate record);

    int updateByPrimaryKey(EnyanPayRate record);
}