package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanReaderHighlights;
import com.aaron.spring.model.EnyanReaderHighlightsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanReaderHighlightsMapper {
    long countByExample(EnyanReaderHighlightsExample example);

    int deleteByExample(EnyanReaderHighlightsExample example);

    int deleteByPrimaryKey(String id);

    int insert(EnyanReaderHighlights record);

    int insertSelective(EnyanReaderHighlights record);

    List<EnyanReaderHighlights> selectByExample(EnyanReaderHighlightsExample example);

    EnyanReaderHighlights selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") EnyanReaderHighlights record, @Param("example") EnyanReaderHighlightsExample example);

    int updateByExample(@Param("record") EnyanReaderHighlights record, @Param("example") EnyanReaderHighlightsExample example);

    int updateByPrimaryKeySelective(EnyanReaderHighlights record);

    int updateByPrimaryKey(EnyanReaderHighlights record);
}