package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanAcsm;
import com.aaron.spring.model.EnyanAcsmExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanAcsmMapper {
    long countByExample(EnyanAcsmExample example);

    int deleteByExample(EnyanAcsmExample example);

    int deleteByPrimaryKey(Long acsmId);

    int insert(EnyanAcsm record);

    int insertSelective(EnyanAcsm record);

    List<EnyanAcsm> selectByExample(EnyanAcsmExample example);

    EnyanAcsm selectByPrimaryKey(Long acsmId);

    int updateByExampleSelective(@Param("record") EnyanAcsm record, @Param("example") EnyanAcsmExample example);

    int updateByExample(@Param("record") EnyanAcsm record, @Param("example") EnyanAcsmExample example);

    int updateByPrimaryKeySelective(EnyanAcsm record);

    int updateByPrimaryKey(EnyanAcsm record);
}