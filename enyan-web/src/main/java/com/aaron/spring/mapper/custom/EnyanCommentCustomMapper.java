package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.DataRentStat;
import com.aaron.spring.model.EnyanComment;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanCommentCustomMapper {
    @Update({"update enyan_comment set like_count = like_count + 1 where data_id = #{dataId}"})
    int updateCommentLikeCountById(@Param("dataId") Long dataId);

    /**
     * <p>删除评论，另外，如果没有子评论则直接设置为不可统计（不管是父评论，还是子评论--子评论默认comment_count是0）</p>
     * @param dataId
     * @param email
     * @return int
     * @since : 2023/5/29
     **/
    @Update({"update enyan_comment set is_deleted = 1,can_show = IF(comment_count = 0, 0, can_show) where data_id = #{dataId} and email = #{email}"})
    int updateCommentToDeletedById(@Param("dataId") Long dataId, @Param("email") String email);

    /**
     * <p>因添加了子评论，所以需要增加评论书，并且需要设置为“可以统计”</p>
     * @param dataId
     * @return int
     * @since : 2023/5/29
     **/
    @Update({"update enyan_comment set comment_count = comment_count + 1, can_show = 1 where data_id = #{dataId}"})
    int updateCommentToPlusById(@Param("dataId") Long dataId);

    /**
     * <p>删除子评论，如果父评论里已经没有了评论，则本评论也需要被隐藏</p>
     * @param dataId
     * @return int
     * @since : 2023/5/29
     **/
    @Update({"update enyan_comment set comment_count = comment_count - 1, can_show = IF(comment_count = 0 and is_deleted = 1, 0, can_show) where data_id = #{dataId}"})
    int updateCommentToMinusById(@Param("dataId") Long dataId);

    @Select("select round(avg(star),1) as star,count(1) as Count from enyan_comment where parent_id = 0 and book_id = #{bookId} and is_deleted = 0 and star > '0' ")
    List<EnyanComment> findStarDataByBook(@Param("bookId") Long bookId);

}