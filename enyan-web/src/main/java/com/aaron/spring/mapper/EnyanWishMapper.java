package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanWish;
import com.aaron.spring.model.EnyanWishExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanWishMapper {
    long countByExample(EnyanWishExample example);

    int deleteByExample(EnyanWishExample example);

    int deleteByPrimaryKey(Long wishId);

    int insert(EnyanWish record);

    int insertSelective(EnyanWish record);

    List<EnyanWish> selectByExample(EnyanWishExample example);

    EnyanWish selectByPrimaryKey(Long wishId);

    int updateByExampleSelective(@Param("record") EnyanWish record, @Param("example") EnyanWishExample example);

    int updateByExample(@Param("record") EnyanWish record, @Param("example") EnyanWishExample example);

    int updateByPrimaryKeySelective(EnyanWish record);

    int updateByPrimaryKey(EnyanWish record);
}