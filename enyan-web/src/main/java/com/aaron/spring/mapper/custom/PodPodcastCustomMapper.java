package com.aaron.spring.mapper.custom;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: <PERSON>
 * @Description: 播客自定义Mapper接口
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Repository
public interface PodPodcastCustomMapper {
    
    /**
     * <p>逻辑删除播客</p>
     * @param podcastId
     * @return int
     * @since : 2025/5/13
     **/
    int updatePodcastToDeletedById(@Param("podcastId") Long podcastId);
    
    /**
     * <p>增加播客喜欢量</p>
     * @param podcastId
     * @return int
     * @since : 2025/5/13
     **/
    int updatePodcastLikeCountById(@Param("podcastId") Long podcastId);
    
//    /**
//     * <p>增加播客播放量</p>
//     * @param podcastId
//     * @return int
//     * @since : 2025/5/13
//     **/
//    int updatePodcastPlayCountById(@Param("podcastId") Long podcastId);
}
