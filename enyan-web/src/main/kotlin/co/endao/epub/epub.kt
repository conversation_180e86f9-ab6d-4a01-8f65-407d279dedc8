package co.endao.epub

import org.w3c.dom.Document
import org.w3c.dom.Element
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream
import java.util.zip.ZipFile


sealed class DataSource{
    class Zip(val zip:ZipFile):DataSource(){
        override fun getInputStream(p:String): InputStream? {
            return zip.getInputStream(zip.getEntry(p))
        }
    }
    class Folder(val path:String):DataSource(){
        override fun getInputStream(p:String): InputStream? {
            val f = File("$path/$p")
            if(f.isFile) return FileInputStream(f)
            return null
        }
    }

    abstract fun getInputStream(p:String): InputStream?
}

fun ZipFile.decompress(p:String){
    val dir = File(p)
    dir.mkdirs()
    for(it in entries())    {
        val strm = getInputStream(it) ?: continue
        val out = "$p/${it.name}"
        File(out).parentFile.mkdirs()
        strm.copyTo(FileOutputStream(out))
    }
}

//参考资料：
// https://www.zhihu.com/question/20569146/answer/32817316
open class OpenDocument(val dataSource:DataSource){
    val docCache = mutableMapOf<String, Document>()
    val dataCache = mutableMapOf<String,ByteArray>()
/*
    var zip: ZipFile? = null
    constructor(z: ZipFile){
        zip = z
    }
  */
    open fun canCompress(s:String):Boolean{
        if(s=="mimetype") return false
        if(s=="META-INF/container.xml") return false
        return true
    }

    fun getInputStream(p:String){

    }
    fun rootFilePath(): String? {
        val cont = loadXml("META-INF/container.xml")!!
        val nd = cont.selectSingleNode("//*[local-name()='rootfile']") as Element
        return nd.getAttribute("full-path")
    }
    fun loadXml(p:String): Document?{
        if(docCache[p]!=null) return docCache[p]
        /*
        val ent = zip?.getEntry(p) ?: return null
        val strm = zip?.getInputStream(ent) ?: return null
         */
        val strm = dataSource.getInputStream(p) ?: return null
        val res = loadW3CDom(strm)
        docCache[p] = res
        return res
    }

    fun loadBytes(p:String):ByteArray?{
        if(dataCache[p]!=null) return dataCache[p]
        /*
        val ent = zip?.getEntry(p)
        val strm = zip?.getInputStream(ent)
        */
        val strm = dataSource.getInputStream(p)
        val res = strm?.readBytes() ?: return null
        dataCache[p] = res
        return res
    }
}
open class EpubBook:OpenDocument{
    class ResourceItem{
        var id = ""
        var href = ""
        var mediaType = ""
        var properties:String? = null
    }

    var opf:Document
    var opfPath = ""
    var identifier = ""
    var language = "zh-CN"
    val resources = mutableListOf<ResourceItem>()
    val order = mutableListOf<ResourceItem>()

    constructor(inp: DataSource):super(inp){
        opfPath = rootFilePath()!!
        opf = loadXml(opfPath)!!
        processOpf()
    }
    fun processOpf(){
        val doc = opf
        val metadata = doc.selectSingleNode("//*[local-name()='metadata']") as Element
        for(el in metadata.elements()){
            val name = el.name
            when(name){
                "language"->{
                    language = el.textTrim
                }
                "identifier"->{
                    identifier = el.textTrim
                }
                else->{

                }
            }
        }
        val manifest = doc.selectSingleNode("//*[local-name()='manifest']") as Element
        for(ch in manifest.elements("item")){
            val lnk = ResourceItem()
            lnk.href = ch.getAttribute("href")
            lnk.id = ch.getAttribute("id")
            lnk.mediaType = ch.getAttribute("media-type")
            lnk.properties = ch.getAttribute("properties")
            resources.add(lnk)
        }
        val spine = doc.selectSingleNode("//*[local-name()='spine']") as Element
        for(ch in spine.elements("itemref")){
            val id = ch.getAttribute("idref")
            val rsc = getResourceById(id)
            if(rsc==null) continue
            order.add(rsc)
        }
    }
    fun getResourceById(id:String): ResourceItem? {
        for(r in resources){
            if(r.id==id) return r
        }
        return null
    }
    companion object{
        fun resolve(pathA:String,b:String): String {
            val arrB = b.split("/")
            val arrA = pathA.split('/').toMutableList()
            arrA.removeAt(arrA.size-1)
            for(a in arrB){
                if(a==".."){
                    arrA.removeAt(arrA.size-1)
                }else if(a=="." || a=="") {
                    continue
                } else{
                    arrA.add(a)
                }
            }
            return arrA.joinToString("/")
        }
    }
}