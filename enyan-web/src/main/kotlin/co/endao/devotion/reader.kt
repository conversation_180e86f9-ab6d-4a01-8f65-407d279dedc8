package co.endao.devotion

import co.endao.epub.*
import org.w3c.dom.Document
import org.w3c.dom.Element
import java.util.*

class DevotionItem{
    var title = ""
    var href = ""
    var day = 0
}
/**
 *
 * book = DevotionBook(DataSource.Folder(dir.absolutePath))
 *
 * */
class DevotionBook: EpubBook {
    var navDoc:Document? = null
    var navPath = ""


    val dayToHref = mutableMapOf<Int,String>()
    val devotionItems = mutableListOf<DevotionItem>()

    constructor(z:DataSource):super(z){
        val n = getNav()
        if(n!=null){
            navPath = resolve(opfPath,n.href)
            navDoc =  loadXml(navPath)
        }
        navDoc?.let {
            val nav = it.selectNodes("//*[local-name()='nav']")

            var navEl:Element? = null
            for(nd in nav){
                val el = nd as Element
                val at = el.attrs()
                if(at["type"]=="devotion"){
                    navEl = el
                    parseNav(navEl)
                    break
                }
            }
        }
    }
    fun parseNav(el:Element){
        val els = el.child("ol")!!

        for(ch in els.elements()){
            val txt = ch.textContent
            val id = ch.getAttribute("id")
            val href = ch.child("a")!!.getAttribute("href")
            val item = DevotionItem()
            item.title = txt
            item.day=id.substringAfter('-').toInt()
            item.href = href
            devotionItems.add(item)
            dayToHref[item.day] = href
        }

    }
    fun getNav(): ResourceItem? {
        for(it in resources){
            if(it.properties=="nav"){
                return it
            }
        }
        return null
    }

    fun getPathForDay(d:Int): String? {
        val href = dayToHref[d+1] ?: return null
        return resolve(navPath,href)
    }
    fun getDay(d:Int): ByteArray? {
        val href = dayToHref[d+1] ?: return null
        val path = resolve(navPath,href)
        return loadBytes(path)
    }
    fun dataBits():BitSet{
        val res = BitSet(devotionItems.last().day)
        for(it in devotionItems){
            res.set(it.day-1)
        }
        return res
    }
}